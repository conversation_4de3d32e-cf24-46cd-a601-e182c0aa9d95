using Dapper;
using LazyCache;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using NLog;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace Voucher.APIHelper
{
    public class Utility
    {
        #region appsettings
        public static string ConnWrite { get { return get_connectionstrings("sqlConnectionWrite"); } }
        public static string ConnRead { get { return get_connectionstrings("sqlConnectionRead"); } }
        public static string ConnIPTV { get { return get_connectionstrings("sqlConnectionIPTV"); } }

        public static string environment { get { return get_appsettings_environment(); } }

        public static string Jwt_Issuer { get { return get_appsettings("Jwt_Issuer"); } }
        public static string Jwt_Key { get { return get_appsettings("Jwt_Key"); } }

        public static string http_client_proxy { get { return get_appsettings("http_client_proxy"); } }

        public static string bpapi_fpt_vn { get { return get_appsettings("bpapi_fpt_vn"); } }

        public static string myFpt_fpt_vn { get { return get_appsettings("my_fpt_api"); } }
        public static string my_fpt_api_client_id { get { return get_appsettings("my_fpt_api_client_id"); } }
        public static string my_fpt_api_client_secret { get { return get_appsettings("my_fpt_api_client_secret"); } }
        public static string my_fpt_api_username { get { return get_appsettings("my_fpt_api_username"); } }
        public static string my_fpt_api_password { get { return get_appsettings("my_fpt_api_password"); } }

        public static string urlFPTplay { get { return get_appsettings("urlFPTplay"); } }
        public static string FptPlay_APP_ID { get { return get_appsettings("FptPlay_APP_ID"); } }
        public static string FptPlay_APP_KEY { get { return get_appsettings("FptPlay_APP_KEY"); } }
        public static string FptPlay_KEY { get { return get_appsettings("FptPlay_KEY"); } }

        public static string UrlVPN { get { return get_appsettings("UrlVPN"); } }
        public static string vpn_endpoint_create { get { return get_appsettings("vpn_endpoint_create"); } }
        public static string vpn_endpoint_status { get { return get_appsettings("vpn_endpoint_status"); } }
        public static string vpn_token { get { return get_appsettings("vpn_token"); } }
        public static string vpn_publickey { get { return get_appsettings("vpn_publickey"); } }

        public static string loyalty_FRTurl { get { return get_appsettings("loyalty_FRTurl"); } }
        public static string loyalty_FRT_authen { get { return get_appsettings("loyalty_FRT_authen"); } }
        public static string loyalty_FRT_create_endpoint { get { return get_appsettings("loyalty_FRT_create_endpoint"); } }
        public static string loyalty_FRT_book_endpoint { get { return get_appsettings("loyalty_FRT_book_endpoint"); } }

        public static string loyaltyapi { get { return get_appsettings("loyaltyapi"); } }
        public static string loyaltyapi_username { get { return get_appsettings("loyaltyapi_username"); } }
        public static string loyaltyapi_password { get { return get_appsettings("loyaltyapi_password"); } }

        public static string Token_key_GTBB_loy { get { return get_appsettings("Token_key_GTBB_loy"); } }

        public static string foxpaykey { get { return get_appsettings("foxpaykey"); } }

        public static string VoucherOBS { get { return get_appsettings("VoucherOBS"); } }

        public static string sourceId { get { return get_appsettings("sourceId"); } }
        public static string roleId { get { return get_appsettings("roleId"); } }

        public static string OS6_ReferalProgram_EmailFrom { get { return get_appsettings("OS6_ReferalProgram_EmailFrom"); } }

        public static string cssapi_fpt_net { get { return get_appsettings("cssapi_fpt_net"); } }

        public static string iam_fpt_vn { get { return get_appsettings("iam_fpt_vn"); } }

        public static string apis_fpt_net { get { return get_appsettings("apis_fpt_net"); } }

        public static string camera_api { get { return get_appsettings("camera_api"); } }

        public static string apiOtt { get { return get_appsettings("apiOtt"); } }

        public static string apiPromotion { get { return get_appsettings("apiPromotion"); } }

        public static string hrapi_fpt_vn { get { return get_appsettings("hrapi_fpt_vn"); } }

        public static string parapiora_fpt_api { get { return get_appsettings("parapiora_fpt_api"); } }

        public static string BillPaymentFreeUrl { get { return get_appsettings("BillPaymentFreeUrl"); } }

        public static string hi_fpt_api { get { return get_appsettings("hi_fpt_api"); } }

        public static string apiHiFptcustomer { get { return get_appsettings("apiHiFptcustomer"); } }

        public static string manapi_fpt_vn { get { return get_appsettings("manapi_fpt_vn"); } }
        public static string deviceReplace { get { return get_appsettings("deviceReplace"); } }

        public static string DigitalSignature { get { return get_appsettings("DigitalSignature"); } }

        public static string HiFPTFG { get { return get_appsettings("HiFPTFG"); } }


        public static string systemapi_fpt_vn { get { return get_appsettings("systemapi_fpt_vn"); } }

        public static string apiSmsworld { get { return get_appsettings("apiSmsworld"); } }

        public static string emailApi { get { return get_appsettings("emailApi"); } }

        #endregion

        #region environment
        public static string get_connectionstrings(string key)
        {
            try
            {
                IAppCache cache = new CachingService();
                string cache_key = "utility-get_connectionstrings-" + key;

                return cache.GetOrAdd(cache_key, () =>
                {
                    string appsettings_environment = get_appsettings_environment();
                    if (string.IsNullOrEmpty(appsettings_environment))
                    {
                        return ConfigurationManager.ConnectionStrings[key]?.ConnectionString ?? "";
                    }
                    else
                    {
                        string setting = "";
                        switch (appsettings_environment)
                        {
                            case "local":
                                setting = ConfigurationManager.ConnectionStrings["local__" + key]?.ConnectionString ?? "";
                                if (!string.IsNullOrEmpty(setting))
                                    break;
                                else
                                    goto case "dev";
                            case "dev":
                                setting = ConfigurationManager.ConnectionStrings["dev__" + key]?.ConnectionString ?? "";
                                if (!string.IsNullOrEmpty(setting))
                                    break;
                                else
                                    goto case "stag";
                            case "stag":
                                setting = ConfigurationManager.ConnectionStrings["stag__" + key]?.ConnectionString ?? "";
                                if (!string.IsNullOrEmpty(setting))
                                    break;
                                else
                                    goto default;
                            default:
                                setting = ConfigurationManager.ConnectionStrings[key]?.ConnectionString ?? "";
                                break;
                        }
                        return setting;
                    }
                }, DateTimeOffset.Now.AddHours(24));
            }
            catch (Exception ex)
            {
                Utility.LogError(ex.ToString());
            }
            return "";
        }

        public static string get_appsettings(string key)
        {
            try
            {
                IAppCache cache = new CachingService();
                string cache_key = "utility-get_appsettings-" + key;

                return cache.GetOrAdd(cache_key, () =>
                {
                    string appsettings_environment = get_appsettings_environment();
                    if (string.IsNullOrEmpty(appsettings_environment))
                    {
                        return ConfigurationManager.AppSettings[key] ?? "";
                    }
                    else
                    {
                        string setting = "";
                        switch (appsettings_environment)
                        {
                            case "local":
                                setting = ConfigurationManager.AppSettings["local__" + key] ?? "";
                                if (!string.IsNullOrEmpty(setting))
                                    break;
                                else
                                    goto case "dev";
                            case "dev":
                                setting = ConfigurationManager.AppSettings["dev__" + key] ?? "";
                                if (!string.IsNullOrEmpty(setting))
                                    break;
                                else
                                    goto case "stag";
                            case "stag":
                                setting = ConfigurationManager.AppSettings["stag__" + key] ?? "";
                                if (!string.IsNullOrEmpty(setting))
                                    break;
                                else
                                    goto default;
                            default:
                                setting = ConfigurationManager.AppSettings[key] ?? "";
                                break;
                        }
                        return setting;
                    }
                }, DateTimeOffset.Now.AddHours(24));
            }
            catch (Exception ex)
            {
                Utility.LogError(ex.ToString());
            }
            return "";
        }

        // local dev stag prod
        public static string get_appsettings_environment()
        {
            try
            {
                IAppCache cache = new CachingService();
                string key = "utility-get_appsettings_environment";

                return cache.GetOrAdd(key, () =>
                {
                    try
                    {
                        string sys_environment = Environment.GetEnvironmentVariable("environment") ?? "";
                        if (!string.IsNullOrEmpty(sys_environment) && (sys_environment == "dev" || sys_environment == "stag"))
                            return sys_environment;
                    }
                    catch { }

#if DEBUG
                    return "local";
#endif

                    string path = System.Web.Hosting.HostingEnvironment.MapPath("~/appsettings.json");
                    if (System.IO.File.Exists(path))
                    {
                        JObject settings = JObject.Parse(System.IO.File.ReadAllText(path));
                        return (string)settings["environment"];
                    }

                    return "";

                }, DateTimeOffset.Now.AddHours(24));
            }
            catch (Exception ex)
            {
                Utility.LogError(ex.ToString());
            }
            return "";
        }
        #endregion

        #region http_client
        public static async Task<ResponseModel<string>> GetAsync(string uri, Dictionary<string, string> headers = null, int timeout = 10)
        {
            var res = new ResponseModel<string>();
            try
            {
                using (var httpClient = new HttpClient())
                {
                    // timeout
                    httpClient.Timeout = TimeSpan.FromSeconds(timeout);
                    // header
                    if (headers != null && headers.Count > 0)
                    {
                        foreach (var header in headers)
                            httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
                    }
                    // deadlock ConfigureAwait(false)
                    using (var response = await httpClient.GetAsync(uri).ConfigureAwait(false))
                    {
                        // will throw an exception if not successful
                        response.EnsureSuccessStatusCode();
                        res.data = await response.Content.ReadAsStringAsync();
                        res.result = 1;
                        return res;
                    }
                }
            }
            catch (HttpRequestException hre)
            {
                string message = hre.Message;
                if (hre.InnerException != null && !string.IsNullOrEmpty(hre.InnerException.Message))
                    message += " >> " + hre.InnerException.Message;
                res.result = -1;
                res.error = message;
                Utility.LogError(uri + "|" + hre.ToString());
                return res;
            }
            catch (Exception ex)
            {
                string message = ex.Message;
                if (ex.InnerException != null && !string.IsNullOrEmpty(ex.InnerException.Message))
                    message += " >> " + ex.InnerException.Message;
                res.result = -1;
                res.error = message;
                Utility.LogError(uri + "|" + ex.ToString());
                return res;
            }
        }

        public static async Task<ResponseModel<string>> PostAsync(string uri, string data, Dictionary<string, string> headers = null, int timeout = 10, string mediaType = "application/json", bool useProxy = false)
        {
            var res = new ResponseModel<string>();
            try
            {
                // content
                var content = new StringContent(data == null ? string.Empty : data, Encoding.UTF8, mediaType);

                // proxy
                var handler = new HttpClientHandler();
                if (useProxy)
                {
                    handler = new HttpClientHandler
                    {
                        Proxy = new System.Net.WebProxy(http_client_proxy),
                        UseProxy = true
                    };
                }

                using (var httpClient = new HttpClient(handler))
                {
                    // timeout
                    httpClient.Timeout = TimeSpan.FromSeconds(timeout);
                    // header
                    if (headers != null && headers.Count > 0)
                    {
                        foreach (var header in headers)
                            httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
                    }
                    // deadlock ConfigureAwait(false)
                    using (var response = await httpClient.PostAsync(uri, content).ConfigureAwait(false))
                    {
                        response.EnsureSuccessStatusCode();
                        res.data = await response.Content.ReadAsStringAsync();
                        res.result = 1;
                        return res;
                    }
                }
            }
            catch (HttpRequestException hre)
            {
                string message = hre.Message;
                if (hre.InnerException != null && !string.IsNullOrEmpty(hre.InnerException.Message))
                    message += " >> " + hre.InnerException.Message;
                res.result = -1;
                res.error = message;
                Utility.LogError(uri + "|" + hre.ToString());
                return res;
            }
            catch (Exception ex)
            {
                string message = ex.Message;
                if (ex.InnerException != null && !string.IsNullOrEmpty(ex.InnerException.Message))
                    message += " >> " + ex.InnerException.Message;
                res.result = -1;
                res.error = message;
                Utility.LogError(uri + "|" + ex.ToString());
                return res;
            }
        }
        #endregion

        #region nlog
        private static readonly Logger _log = get_logger();

        public static Logger get_logger()
        {
            try
            {
                string configFile = "nlog.config";
                if (environment == "local" || environment == "dev")
                    configFile = "nlog.Development.config";
                else if (environment == "stag")
                    configFile = "nlog.Staging.config";
                return NLog.LogManager.Setup().LoadConfigurationFromFile(configFile).GetCurrentClassLogger();
            }
            catch { }
            return LogManager.GetCurrentClassLogger();
        }

        public static void LogError(string message, object obj = null)
        {
            try
            {
                if (obj != null)
                    message = message + "<<" + JsonConvert.SerializeObject(obj);
                LogEventInfo theEvent = new LogEventInfo(LogLevel.Error, "", message);
                theEvent.Properties["local_addr"] = Regex.Match(GetLocalIPAddress(), @"(\d+)(?!.*\d)").Value;
                _log.Log(theEvent);
            }
            catch (Exception ex)
            {
                _log.Error(ex.Message + ">>" + message);
            }
        }

        public static void LogTrace(string message, object obj = null)
        {
            try
            {
                if (obj != null)
                    message = message + "<<" + JsonConvert.SerializeObject(obj);
                LogEventInfo theEvent = new LogEventInfo(LogLevel.Trace, "", message);
                theEvent.Properties["local_addr"] = Regex.Match(GetLocalIPAddress(), @"(\d+)(?!.*\d)").Value;
                _log.Log(theEvent);
            }
            catch (Exception ex)
            {
                _log.Error(ex.Message + ">>" + message);
            }
        }

        public static void LogInfo(string dir, string message, object obj = null)
        {
            try
            {
                if (obj != null)
                    message = message + "<<" + JsonConvert.SerializeObject(obj);
                LogEventInfo theEvent = new LogEventInfo(LogLevel.Info, "", message);
                theEvent.Properties["local_addr"] = Regex.Match(GetLocalIPAddress(), @"(\d+)(?!.*\d)").Value;
                theEvent.Properties["dir"] = dir;
                _log.Log(theEvent);
            }
            catch (Exception ex)
            {
                _log.Error(ex.Message + ">>" + message);
            }
        }

        public static void LogDebug(string message, object obj = null)
        {
            try
            {
                if (obj != null)
                    message = message + "<<" + JsonConvert.SerializeObject(obj);
                LogEventInfo theEvent = new LogEventInfo(LogLevel.Debug, "", message);
                theEvent.Properties["local_addr"] = Regex.Match(GetLocalIPAddress(), @"(\d+)(?!.*\d)").Value;
                _log.Log(theEvent);
            }
            catch (Exception ex)
            {
                _log.Error(ex.Message + ">>" + message);
            }
        }

        public static void LogFull(bool log, string message, object obj = null)
        {
            try
            {
                if (log)
                    LogInfo("Z", "log_full : " + message, obj);
            }
            catch (Exception ex)
            {
                _log.Error(ex.Message + ">>" + message);
            }
        }
        #endregion

        #region dapper
        public static IEnumerable<dynamic> Query(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.Query(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static IEnumerable<T> Query<T>(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.Query<T>(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static dynamic QueryFirstOrDefault(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.QueryFirstOrDefault(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static T QueryFirstOrDefault<T>(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.QueryFirstOrDefault<T>(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static int Execute(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.Execute(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static IEnumerable<dynamic> Query(SqlConnection connection, string sql, object param = null,
            IDbTransaction transaction = null, CommandType commandType = CommandType.StoredProcedure)
        {
            return connection.Query(sql, param, transaction, commandType: commandType, commandTimeout: 300);
        }

        public static IEnumerable<T> Query<T>(SqlConnection connection, string sql, object param = null,
            IDbTransaction transaction = null, CommandType commandType = CommandType.StoredProcedure)
        {
            return connection.Query<T>(sql, param, transaction, commandType: commandType, commandTimeout: 300);
        }

        public static dynamic QueryFirstOrDefault(SqlConnection connection, string sql, object param = null,
            IDbTransaction transaction = null, CommandType commandType = CommandType.StoredProcedure)
        {
            return connection.QueryFirstOrDefault(sql, param, transaction, commandType: commandType, commandTimeout: 300);
        }

        public static T QueryFirstOrDefault<T>(SqlConnection connection, string sql, object param = null,
            IDbTransaction transaction = null, CommandType commandType = CommandType.StoredProcedure)
        {
            return connection.QueryFirstOrDefault<T>(sql, param, transaction, commandType: commandType, commandTimeout: 300);
        }

        public static int Execute(SqlConnection connection, string sql, object param = null,
            IDbTransaction transaction = null, CommandType commandType = CommandType.StoredProcedure)
        {
            return connection.Execute(sql, param, transaction, commandType: commandType, commandTimeout: 300);
        }
        #endregion

        #region converter
        public static string ConvertToUnsign(string str)
        {
            Regex regex = new Regex("\\p{IsCombiningDiacriticalMarks}+");
            string temp = str.Normalize(NormalizationForm.FormD);
            return regex.Replace(temp, String.Empty).Replace('\u0111', 'd').Replace('\u0110', 'D');
        }

        public static T CloneObject<T>(T obj)
        {
            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(obj));
        }

        public static decimal PriceNoVAT(decimal price_with_vat, int? vat_rate)
        {
            try
            {
                return Math.Round(price_with_vat * 100 / (100 + (vat_rate ?? 10)), 0);
            }
            catch
            {
                return 0;
            }
        }

        public static class Converter
        {
            public static T FromRow<T>(DataRow row, string columName)
            {
                if (row.Table.Columns.Contains(columName))
                    return To<T>(row[columName]);
                else
                    return To<T>(null);
            }

            public static T FromRow<T>(DataRow row, string columName, T defaultValue)
            {
                if (row.Table.Columns.Contains(columName))
                    return To<T>(row[columName]);
                else
                    return defaultValue;
            }

            public static T To<T>(object obj)
            {
                try
                {
                    Type conversionType = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
                    return (T)System.Convert.ChangeType(obj, conversionType);
                }
                catch
                {
                    return default(T);
                }
            }

            public static T To<T>(object obj, T defaultValue)
            {
                try
                {
                    Type conversionType = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
                    return (T)System.Convert.ChangeType(obj, conversionType);
                }
                catch
                {
                    return defaultValue;
                }
            }

            public static string ToXml<T>(T obj)
            {
                try
                {
                    if (obj == null)
                        return "";

                    var type = obj.GetType();
                    string typeName = type.Name;
                    if (type.GenericTypeArguments != null && type.GenericTypeArguments.Count() > 0)
                        typeName = type.GenericTypeArguments[0].Name;

                    XmlSerializer xs = new XmlSerializer(typeof(T));
                    using (var sw = new StringWriter())
                    {
                        using (XmlWriter xw = XmlWriter.Create(sw))
                        {
                            xs.Serialize(xw, obj);
                            return sw.ToString().Replace(typeName, "Model");
                        }
                    }
                }
                catch
                {
                    return "";
                }
            }

            #region To
            public static int ToInt(object obj)
            {
                return To<int>(obj);
            }

            public static long ToLong(object obj)
            {
                return To<long>(obj);
            }

            public static string ToString(object obj)
            {
                return To<string>(obj) ?? "";
            }

            public static DateTime? ToDateTime(object obj)
            {
                return To<DateTime?>(obj);
            }
            #endregion
        }

        #endregion

        public static string GetLocalIPAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                        return ip.ToString();
                }
            }
            catch (Exception ex)
            {
                Utility.LogError(ex.ToString());
            }
            return string.Empty;
        }

        public static List<T> GetConfig<T>(int groupID)
        {
            try
            {
                return Utility.Query<T>(
                    Utility.ConnRead,
                    "PowerInside.dbo.OS6_DXBusinessPolicy_Config_Get",
                    new { GroupID = groupID }
                ).ToList();
            }
            catch (Exception ex)
            {
                Utility.LogError(ex.ToString());
            }
            return default(List<T>);
        }

        public static List<BaseConfig> GetBaseConfig(int groupID, string groupName)
        {
            try
            {
                return Utility.Query<BaseConfig>(
                    Utility.ConnRead,
                    "PowerInside.dbo.OS6_DXBusinessPolicy_BaseConfig_Get",
                    new { GroupID = groupID }
                ).ToList();
            }
            catch (Exception ex)
            {
                Utility.LogError(ex.ToString());
            }
            return null;
        }

        public class BaseConfig
        {
            public int FValue { get; set; }
            public string FName { get; set; }
            public int TValue { get; set; }
            public string TName { get; set; }
            public int GroupID { get; set; }
            public string GroupName { get; set; }
            public int Status { get; set; }
        }

        public static class Operator
        {
            public static double Division(double x, double y)
            {
                if (y > 0)
                    return x / y;
                else
                    return 0;
            }
        }

        // NoJsonProperty
        // new JsonSerializer { ContractResolver = new NoJsonPropertyContractResolver() }
        // new JsonSerializerSettings { ContractResolver = new NoJsonPropertyContractResolver() }
        public class NoJsonPropertyContractResolver : DefaultContractResolver
        {
            protected override JsonProperty CreateProperty(System.Reflection.MemberInfo member, MemberSerialization memberSerialization)
            {
                JsonProperty property = base.CreateProperty(member, memberSerialization);
                property.PropertyName = property.UnderlyingName;
                return property;
            }
        }
    }

    public class WebAPIHelper : Utility
    { }
}