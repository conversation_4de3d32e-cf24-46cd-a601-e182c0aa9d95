using log4net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace Voucher.APIHelper.Log4net
{
    public static class L
    {
        public static void Mes(Level Level, string Message, string APIPart = null)
        {
            try
            {
                if (!string.IsNullOrEmpty(APIPart))
                    Message = APIPart + ": " + Message;
                switch (Level)
                {
                    case Level.REQUEST:
                        WebAPIHelper.LogInfo("L", "REQ: " + Message);
                        break;
                    case Level.RESPONSE:
                        WebAPIHelper.LogInfo("L", "RES: " + Message);
                        break;
                    case Level.ENDREQUEST:
                        WebAPIHelper.LogInfo("L", "END: " + Message);
                        break;
                    case Level.ERROR:
                        WebAPIHelper.LogInfo("L", "ERR: " + Message);
                        break;
                    default:
                        WebAPIHelper.LogInfo("L", Message);
                        break;
                }
            }
            catch { }
        }
    }
}