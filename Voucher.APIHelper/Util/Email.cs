using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.ShareModel;

namespace Voucher.APIHelper.Util
{
    public class Email
    {
        public static void SendMail(MailModelInput input)
        {
            var logid = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " + JsonConvert.SerializeObject(input), " SendMail ");
            
            try
            {
                string url = string.Concat(Utility.emailApi, "/api/SendMailSMTP/InsertInfoSendMailSMTP");
                string data = JsonConvert.SerializeObject(input);
                string res = callapiNoHeader(url, "POST", data);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " + JsonConvert.SerializeObject(res), " SendMail res: ");                
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " + JsonConvert.SerializeObject(ex.Message), " SendMail error: ");    
            }
        }
        public static string callapiNoHeader(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }
    }
}