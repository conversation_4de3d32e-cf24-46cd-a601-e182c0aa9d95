using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.UI.WebControls;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.ShareModel;
using System.Data.SqlClient;
using System.Data;
using Dapper;
using System.Security.Policy;

namespace Voucher.APIHelper.Util
{
    public class FuncShared
    {
        static AuthorizationInforShared _auth;

        #region const
        private static string OS6_FPTVoucher_LoyaltyCallBack = "PowerInside..OS6_FPTVoucher_LoyaltyCallBack";
        private static string OS6_FPTVoucher_RedeemLoyaltyActionSubmit = "PowerInside..OS6_FPTVoucher_RedeemLoyaltyActionSubmit";
        #endregion

        static AuthorizationInforShared getAuthorInforV2(LoginInforShared login, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                   JsonConvert.SerializeObject(_auth) + JsonConvert.SerializeObject("start to getAuthorInfor"));
            if (_auth != null)
            {
                long timeExpire = Convert.ToInt32(_auth.expires_in) + Convert.ToInt32(_auth.iat);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                  JsonConvert.SerializeObject(timeExpire) + JsonConvert.SerializeObject("timeExpire"));
                DateTime datetimeNow = DateTime.Now.AddMinutes(30);

                long timeNow = ConvertToTimestamp(datetimeNow);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                  JsonConvert.SerializeObject(timeNow) + JsonConvert.SerializeObject("timeNow"));
                if (timeExpire > timeNow)
                    return _auth;
            }
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject("start to getAuthorInfor"));

            try
            {
                string authInfo = login.username + ":" + login.password;
                authInfo = Convert.ToBase64String(Encoding.Default.GetBytes(authInfo));

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(WebAPIHelper.loyaltyapi + "/auth/oauth/token?grant_type=client_credentials");
                request.Method = "POST";
                request.Accept = "application/json; charset=utf-8";

                request.Headers["Authorization"] = "Basic " + authInfo;

                var response = (HttpWebResponse)request.GetResponse();

                string strResponse = "";
                using (var sr = new StreamReader(response.GetResponseStream()))
                {
                    strResponse = sr.ReadToEnd();
                }
                _auth = JsonConvert.DeserializeObject<AuthorizationInforShared>(strResponse);

            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " getAuthorInforV2 Error:");
                return null;
            }
            return _auth;
        }

        private static long ConvertToTimestamp(DateTime value)
        {
            long epoch = (value.Ticks - 621355968000000000) / 10000000;
            return epoch;
        }
        public static AuthorizationInforShared GetTokenv2(LoginInforShared login, string logId)
        {
            return getAuthorInforV2(login, logId);
        }
        
        public static bool NewChangeStatusLoyaltyV2(SendLoyaltyShare sl, int privateCodeID, string LogId, SqlConnection conn = null, SqlTransaction trans = null)
        {
            bool flag = false;
            string responeseAPI = "";
            string request = JsonConvert.SerializeObject(sl);
            string exception = "";
            string endpoint = "/loyalty-services/api/integration-evoucher/redeem-device";
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(sl), "ChangeStatusLoyalty req ");
            LoyaltyRedeemOutput lro = new LoyaltyRedeemOutput();
            try
            {
                LoginInforShared login = new LoginInforShared() { username = Utility.loyaltyapi_username, password = Utility.loyaltyapi_password };
                AuthorizationInforShared aut = getAuthorInforV2(login, LogId);

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(WebAPIHelper.loyaltyapi), " NewChangeStatusLoyalty urlAPI " + sl.voucherCode);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var data = new StringContent(JsonConvert.SerializeObject(sl), Encoding.UTF8, "application/json");
                    var response = client.PostAsync(endpoint, data).Result;
                    string result = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), " NewChangeStatusLoyalty APIres " + sl.voucherCode);
                    lro = JsonConvert.DeserializeObject<LoyaltyRedeemOutput>(result);
                    responeseAPI = JsonConvert.SerializeObject(lro);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " NewChangeStatusLoyalty APIres Error " + sl.voucherCode);
                flag = false;
                exception = ex.Message;
            }

            if (lro.statusCode != null && lro.statusCode.ToUpper().Equals("SUCCESS"))
            {
                flag = true;
            }
            Insert_LogRequest_LogResponset(privateCodeID, sl.voucherCode, request, flag, responeseAPI, exception, endpoint, conn, trans);
            return flag;
        }

        public static void Insert_LogRequest_LogResponset(
            int privateCodeID,
            string voucherCode,
            string request,
            bool isSuccess,
            string responseAPI,
            string exception,
            string endpoint,
            SqlConnection conn = null,
            SqlTransaction trans = null)
        {
            bool isExternalConnection = conn != null;
            using (var connection = isExternalConnection ? conn : new SqlConnection(SqlHelper.ConnWrite()))
            {
                if (!isExternalConnection)
                    connection.Open(); // Mở kết nối nếu là kết nối nội bộ

                // Insert Log Request
                var idRequest = connection.Query<int>(OS6_FPTVoucher_LoyaltyCallBack,
                    new
                    {
                        actionName = "InsertLogRequest",
                        req = request,
                        idPrivateCode = privateCodeID,
                        voucherCode = voucherCode,
                        isSuccess = isSuccess,
                        endpoint = endpoint
                    },
                    transaction: trans,
                    commandType: CommandType.StoredProcedure).FirstOrDefault();

                // Insert Log Response
                connection.Query(OS6_FPTVoucher_LoyaltyCallBack,
                    new
                    {
                        actionName = "InsertLogResponset",
                        res = responseAPI,
                        idReq = idRequest,
                        exception = exception
                    },
                    transaction: trans,
                    commandType: CommandType.StoredProcedure);

                if (!isExternalConnection)
                    connection.Close();
            }
        }

        public static string LoyaltyActionSubmit(
            string prefixPrivateCode,
            string privateCode,
            string typeSubmit,
            string logId,
            SqlConnection conn = null,
            SqlTransaction trans = null)
        {
            bool isExternalConnection = conn != null;
            try
            {
                using (var connection = isExternalConnection ? conn : new SqlConnection(SqlHelper.ConnWrite()))
                {
                    if (!isExternalConnection)
                        connection.Open();

                    var privateCodeConfig = connection.Query<PrivateCode>(
                        OS6_FPTVoucher_RedeemLoyaltyActionSubmit,
                        new
                        {
                            ActionName = "CheckConfig",
                            PrivateCode = privateCode
                        },
                        transaction: trans,
                        commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (privateCodeConfig == null)
                        return "Not in config loyalty submit";

                    var request = new SendLoyaltyShare
                    {
                        voucherCode = prefixPrivateCode,
                        voucherPaymentStatus = typeSubmit
                    };

                    // Gửi loyalty và xử lý kết quả
                    var isLoyaltySent = NewChangeStatusLoyaltyV2(request, privateCodeConfig.Id, logId, conn, trans);
                    return isLoyaltySent ? "" : prefixPrivateCode;
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

    }
}