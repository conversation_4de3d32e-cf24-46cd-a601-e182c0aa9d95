using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace Voucher.APIHelper.Util
{
    public static class Util
    {
        public static string JoinStringNET(this int input, int percent)
        {
            if (input != 0 && input != -1 && percent == 100) return string.Format(", T{0}", input.ToString());
            if (input != 0 && input != -1 && percent < 100) return string.Format(", T{0}/{1}%", input.ToString(), percent);
            return "";
        }


        public static int VATDiscount(this int input)
        {
            return RoundingTo((int)(input *1.1), 1000);
        }

        public static int RoundUp(double number, int decimalPlaces)
        {
            return (int)(Math.Ceiling(number * Math.Pow(10, decimalPlaces)) / Math.Pow(10, decimalPlaces));
        }

        public static long RoundingTo(long myNum, long roundTo)
        {
            if (roundTo <= 0) return myNum;
            return (myNum + roundTo / 2) / roundTo * roundTo;
        }

        public static long RoundingTo(long myNum, int roundTo)
        {
            return RoundingTo(myNum, (long)roundTo);
        }

        public static int RoundingTo(int myNum, int roundTo)
        {
            return (int)RoundingTo((long)myNum, (long)roundTo);
        }



        public static DateTime FirstDayOfMonth(this DateTime dateTime)
        {
            return new DateTime(dateTime.Year, dateTime.Month, 1);
        }


        public static IEnumerable<TSource> DistinctBy<TSource, TKey>
        (this IEnumerable<TSource> source, Func<TSource, TKey> keySelector)
        {
            HashSet<TKey> seenKeys = new HashSet<TKey>();
            foreach (TSource element in source)
            {
                if (seenKeys.Add(keySelector(element)))
                {
                    yield return element;
                }
            }
        }

        public static string CreateMD5(this string input)
        {
            using (System.Security.Cryptography.MD5 md5 = System.Security.Cryptography.MD5.Create())
            {
                byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);

                // Convert the byte array to hexadecimal string
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("X2"));
                }
                return sb.ToString().ToLower();
            }
        }
    }
}