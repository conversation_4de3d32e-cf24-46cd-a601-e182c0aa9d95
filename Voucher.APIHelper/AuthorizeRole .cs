using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Voucher.APIHelper
{
    public static class AuthorizeRole
    {
        public const string Admin = "Admin";
        public const string SuperUser = "SuperUser";
        public const string AdminRF = "AdminRF";
        public const string Loyalty = "Loyalty";
        public const string Foxpay = "Foxpay";
        public const string Hifpt = "Hifpt";
        public const string ToolQuay = "ToolQuay";
        public const string Mobinet = "Mobinet";
        public const string Telesale = "Telesale";
        public const string Dkol = "Dkol";        
        public const string Frtvoucher = "Frtvoucher";
        public const string Hifg = "Hifg";
        public const string Fpt = "Fpt";
        public const string ReportGTBB = "ReportGTBB";//
        public const string IssueInvoice = "IssueInvoice";
        public const string RegisterWeb = "RegisterWeb";
        public const string SaleClub = "SaleClub";
        public const string RegisterWebAndSaleClub = "RegisterWeb,SaleClub";
        public const string SuperUserAndSaleClub = "SuperUser,SaleClub";
        public const string FPTPlay = "FPTPlay";
        public const string PTC = "PTC";
        public const string LoyaltyService_Check = "Hifpt,SM,Hifg";
        public const string LoyaltyService_Redeem = "Hifpt,SM,Hifg";
        public const string SM = "SM";
        public const string fptvn = "fptvn";
        public const string qlcs = "qlcs";
        public const string SaleClub_qlcs = "SaleClub,qlcs";
    }
}