using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper.Func.Constant;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.ShareModel;
using Dapper;

namespace Voucher.APIHelper.Func
{
    public class Func
    {

        /// <summary>
        /// get total quota really to use
        /// </summary>
        /// <param name="PromotionEventID"></param>
        /// <param name="IDSalesMan"></param>
        /// <param name="TotalQuotaUsed"></param>
        /// <returns></returns>
        public  int GetQuota(int PromotionEventID, int IDSalesMan, int TotalQuotaUsed)
        {
            SalesMan SalesInfo = new SalesMan();
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    SalesInfo = GetSalesman(connection, transaction, IDSalesMan);
                    if (SalesInfo == null) return -1;

                    Dictionary<string, int> QuotaAssign = new Dictionary<string, int>{
                        { ConstantAction.SALESMAN, 0},{ConstantAction.DEPARTMENT,0},{ConstantAction.BRANCHCODE,0},{ConstantAction.LOCATION,0},{ConstantAction.REGION,0}
                    };

                    for (int i = 0; i < QuotaAssign.Count(); i++)
                    {
                        SalesInfo.Quota = GetQuotaNode(QuotaAssign.ElementAt(i).Key, SalesInfo, PromotionEventID, ConstantAction.GetQuota);
                        L.Mes(Level.QUOTAINNODE, string.Format("{0} - {1}", QuotaAssign.ElementAt(i).Key, SalesInfo.Quota));


                        if (i == 0 && SalesInfo.Quota > 0) break;
                        if (SalesInfo.Quota > 0 && (QuotaAssign.ElementAt(i - 1).Value == 0))
                        {
                            int SumQuota = GetQuotaNode(QuotaAssign.ElementAt(i - 1).Key,
                                                SalesInfo,
                                                PromotionEventID, ConstantAction.GetQuotaParallelNode);

                            SalesInfo.Quota = SalesInfo.Quota - SumQuota;
                            SalesInfo.Quota = SalesInfo.Quota - TotalQuotaUsed;
                            break;
                        }
                        if ((SalesInfo.Quota > 0) && (QuotaAssign.ElementAt(i - 1).Value == -1))
                        {
                            int CountRow = GetQuotaNode(QuotaAssign.ElementAt(i - 1).Key,
                                                SalesInfo,
                                                PromotionEventID, ConstantAction.CheckContainChildNode);
                            if (CountRow > 0)
                            {
                                return -1;
                            }
                            else
                            {
                                SalesInfo.Quota = SalesInfo.Quota - TotalQuotaUsed;
                                break;
                            }
                            break;
                        }

                        if (i == 4 && SalesInfo.Quota == -1)
                            return -1;
                        QuotaAssign[QuotaAssign.ElementAt(i).Key] = SalesInfo.Quota;
                    }
                }
            }
            return SalesInfo.Quota;
        }

        /// <summary>
        /// Get quota of node assign
        /// </summary>
        /// <param name="AssignPosition"></param>
        /// <param name="user"></param>
        /// <param name="PromotionID"></param>
        /// <param name="ActionName"></param>
        /// <returns></returns>
        public int GetQuotaNode(string AssignPosition, SalesMan user, int PromotionID, string ActionName)
        {
            int Quota = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    int Result = connection.Query<int>(API.spAPI_SalesMan, new
                    {
                        ActionName = ActionName,
                        QuotaAction = AssignPosition,
                        AccountName = user.Name,
                        REGION = user.REGION,
                        LOCATION = user.LOCATION,
                        BRANCH = user.BRANCHCODE,
                        DEPARTMENT = user.DEPARTMENT,
                        SALESMAN = user.SALESMAN,
                        PromotionID = PromotionID
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    Quota = Result;
                }
            }
            return Quota;
        }


        /// <summary>
        /// Get Total Quota will be used
        /// </summary>
        /// <param name="PromotionEventID"></param>
        /// <returns></returns>
        public int GetQuotaUsed(int PromotionEventID)
        {
            int quota = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    quota = connection.Query<int>(API.spAPi_MBS, new
                    {
                        ActionName = "GetQuotaGeneralCodeUsed",
                        PromotionEvent = PromotionEventID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return quota;

        }

        /// <summary>
        /// Get Info Salesman
        /// </summary>
        /// <param name="connection"></param>
        /// <param name="transaction"></param>
        /// <param name="ObjSalesman"></param>
        /// <returns></returns>
        public SalesMan GetSalesman(SqlConnection connection, SqlTransaction transaction, int ObjSalesman)
        {
            SalesMan SalesInfo = new SalesMan();
            SalesInfo = connection.Query<SalesMan>(API.spAPI_SalesMan, new
            {
                ActionName = "SelectByID",
                ID = ObjSalesman
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            return SalesInfo;
        }

        public String GetContract(int Obj) {
            string GetContract = "";
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    GetContract = connection.Query<string>(API.spAPI_CS, new
                    {
                        ActionName = ConstantAction.GetContract,
                        ObjID = Obj,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return GetContract;
            
        }
    }
}