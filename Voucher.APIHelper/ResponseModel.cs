using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Voucher.APIHelper
{
    public class ResponseEmptyModel
    {
        public int result { get; set; }
        public string error { get; set; }
        public ResponseEmptyModel()
        {
            error = string.Empty;
        }
    }
    public class ResponseModel<T> : ResponseEmptyModel
    {
        public T data { get; set; }
    }
    public class ResponseModel : ResponseModel<object> { }
    public class ResponseModels<T> : ResponseModel<List<T>>
    {
        public ResponseModels()
            : base()
        {
            data = new List<T>();
        }
    }
    public class ResponseModels : ResponseModels<object> { }
}