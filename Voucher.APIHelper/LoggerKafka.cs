using Confluent.Kafka;
using System;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using Newtonsoft.Json;

namespace Voucher.APIHelper
{    
    public class LoggerKafka 
    {
        public static void InsertLog(string data)
        {
            Utility.LogInfo("Kafka", data);
        }

        public static void WriteLogKafka(object input, string mes, string logId, HttpContext context = null)
        {
            InsertLog(string.Format("{1} : {0}", input, mes));
        }

        public static string JoinStringToWriteLog(string Name, object Value)
        {
            return string.Format("{0} : {1} : {2}", DateTime.Now.ToString(), Name, JsonConvert.SerializeObject(Value));
        }
    }
}
