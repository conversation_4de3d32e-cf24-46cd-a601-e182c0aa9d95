using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Voucher.APIHelper.ShareModel
{
    public class AuthorizationInforShared
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string scope { set; get; }
        public string iat { set; get; }
        public string expires_in { set; get; }
        public string jti { set; get; }
    }

    public class LoginInforShared
    {
        public string username { set; get; }
        public string password { set; get; }
    }

    public class LoyaltyRedeemOutput
    {
        public string statusCode { set; get; }
        public string message { set; get; }
    }

    public class SendLoyaltyShare
    {
        public string mobileHiFpt { get; set; }
        public string redeemDate { get; set; }
        public int? shopCode { set; get; }
        public string shopName { set; get; }
        public string transCode { set; get; }
        public string saleOrder { set; get; }
        public int? voucherActualUse { set; get; }
        public string voucherCode { set; get; }
        public string billContent { set; get; }
        public string billFromDate { set; get; }
        public string billNumber { set; get; }
        public int? billRemain { set; get; }
        public string billToDate { set; get; }
        public int? billTotalDebt { set; get; }
        public string voucherPaymentStatus { set; get; }
        public string contractNo { set; get; }
    }

    public class PrivateCode 
    {
        public int Id { set; get; }
        public string Code { set; get; }
    }

    #region loyPendingStatus
    public class VoucherAccessPending
    {
        public string VoucherCode { set; get; }
    }
    public class VoucherAccessPendingInput
    {
        public List<VoucherAccessPending> Vouchers { set; get; }
        public int ObjId { set; get; }
        public string RegCode { set; get; }
        public string OrderCode { set; get; }
        public string OrderType { set; get; }
        public DateTime? PaymentDate { set; get; }
    }
    #endregion

    #region cskh to fgold
    public class GetGold
    {
        public GetGoldData Data { get; set; }
        public int StatusCode { get; set; }
        public bool Success { get; set; }
    }
    public class GetGoldData
    {
        public string ActionCode { get; set; }
        public int LocationId { get; set; }
        public int BranchCode { get; set; }
        public int Coins { get; set; }
        public List<string> EventCodes { get; set; }
    }
    #endregion
}