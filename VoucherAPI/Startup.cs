using Microsoft.IdentityModel.Tokens;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.Jwt;
using Owin;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Http;
using Voucher.APIHelper;

namespace Voucher.API
{
    public class Startup
    {
        public void Configuration(IAppBuilder appBuilder)
        {
            HttpConfiguration config = new HttpConfiguration();
            WebApiConfig.Register(config);

            appBuilder.UseJwtBearerAuthentication(new JwtBearerAuthenticationOptions
            {
                AuthenticationMode = AuthenticationMode.Active,
                TokenValidationParameters = new TokenValidationParameters()
                {
                    ValidateLifetime = true,
                    //ClockSkew = TimeSpan.Zero,// If you want to allow a certain amount of clock drift
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = WebAPIHelper.Jwt_Issuer,
                    ValidAudience = WebAPIHelper.Jwt_Issuer,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(WebAPIHelper.Jwt_Key))
                }
            });
            appBuilder.UseWebApi(config);
        }
    }
}