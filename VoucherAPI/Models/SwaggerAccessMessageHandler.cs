using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Voucher.API.Models
{
    public class SwaggerAccessMessageHandler : DelegatingHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            if (SwaggerHelper.IsSwagger(request))
            {
                try
                {
                    string RequestIpAddress = SwaggerHelper.GetClientIPAddress(HttpContext.Current.Request);
                    if (!SwaggerHelper.CheckPermission(RequestIpAddress))
                    {
                        HttpResponseMessage response = request.CreateResponse(HttpStatusCode.BadRequest, RequestIpAddress);
                        return Task.FromResult(response);
                    }
                }
                catch { }
            }
            return base.SendAsync(request, cancellationToken);
        }
    }
}