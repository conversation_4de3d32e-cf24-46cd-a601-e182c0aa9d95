using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace Voucher.API.Models
{
    public class ErrorMessageHandler : DelegatingHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            return base.SendAsync(request, cancellationToken).ContinueWith<HttpResponseMessage>((responseToCompleteTask) =>
            {
                HttpResponseMessage response = responseToCompleteTask.Result;
                HttpError error = null;

                if ((!response.IsSuccessStatusCode) && (response.TryGetContentValue(out error)))
                {
                    // Build new custom from underlying HttpError object.
                    var errorResp = new MyErrorResponse { result = -1, error = error.Message, errorDetail = error.MessageDetail ?? error.ExceptionMessage };
                    // Replace outgoing response's content with our custom response
                    // while keeping the requested MediaType [formatter].
                    var content = (ObjectContent)response.Content;
                    response.Content = new ObjectContent(typeof(MyErrorResponse), errorResp, content.Formatter);
                }

                return response;
            });
        }
    }

    public class MyErrorResponse
    {
        public int result { get; set; }
        public string error { get; set; }
        public string errorDetail { get; set; }
    }
}