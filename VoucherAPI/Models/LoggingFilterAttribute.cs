using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;

namespace Voucher.API.Models
{
    public class LoggingFilterAttribute : ActionFilterAttribute
    {
        private readonly Logger _log = LogManager.GetCurrentClassLogger();
        public override void OnActionExecuting(HttpActionContext filterContext)
        {
            try
            {
                HttpContext.Current.Request.Headers.Add("logkey", Guid.NewGuid().ToString("N").Substring(0, 16));
            }
            catch { }

            if (filterContext.Request.Method.Method == "GET")
                APIHelper.Utility.LogTrace(filterContext.Request.Method.Method);
            else
                APIHelper.Utility.LogTrace(filterContext.Request.Method.Method, filterContext.ActionArguments);
        }
    }
}