using LazyCache;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Web;

namespace Voucher.API.Models
{
    public static class SwaggerHelper
    {
        // check ip in limit.json
        public static bool CheckPermission(string RequestIpAddress)
        {
            try
            {
                if (APIHelper.Utility.environment == "dev" || HttpContext.Current.Request.IsLocal)
                    return true;

                IAppCache cache = new CachingService();
                string cache_key = "swagger_" + RequestIpAddress;

                var cache_results = cache.Get<bool?>(cache_key);
                if (cache_results == true)
                    return true;

                var lst = APIHelper.Utility.Query(
                    APIHelper.Utility.ConnRead,
                    "PowerInside.dbo.OS6_DXBusinessPolicy_Config_Get",
                    new { GroupID = 11 }
                ).ToList();

                bool result = lst.Exists(c => RequestIpAddress.Contains(c.Value));

                if (result)
                {
                    cache.Add(cache_key, true);

                    return true;
                }
                return false;
            }
            catch { }
            return true;
        }

        // get xml comment
        public static System.IO.FileInfo[] GetXmlCommentsPath()
        {
            var path = System.Web.Hosting.HostingEnvironment.MapPath("~/Json");
            if (System.IO.Directory.Exists(path))
            {
                var dir = new System.IO.DirectoryInfo(path);
                return dir.GetFiles("*.xml");
            }
            return new System.IO.FileInfo[0];
        }

        // get local ip
        public static string GetLocalIPAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                        return ip.ToString();
                }
            }
            catch { }
            return string.Empty;
        }

        // get client ip
        public static string GetClientIPAddress(HttpRequest request)
        {
            // Look for a proxy address first
            string userIpAddress = request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            // If there is no proxy, get the standard remote address
            if (string.IsNullOrEmpty(userIpAddress) || userIpAddress.ToLower() == "unknown")
                userIpAddress = request.ServerVariables["REMOTE_ADDR"];
            return userIpAddress;
        }

        public static bool IsSwagger(HttpRequestMessage request)
        {
            return request.RequestUri.PathAndQuery.Contains("/swagger");
        }

        // get route after api
        public static string SplitRouteTemplate(string RouteTemplate)
        {
            if (!string.IsNullOrEmpty(RouteTemplate))
            {
                try
                {
                    return System.Text.RegularExpressions.Regex.Replace(RouteTemplate, "api/", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase).Split('/')[0];
                }
                catch { }
            }
            return string.Empty;
        }
    }

    // order by path
    public class CustomDocumentFilter : Swashbuckle.Swagger.IDocumentFilter
    {
        public void Apply(Swashbuckle.Swagger.SwaggerDocument swaggerDoc, Swashbuckle.Swagger.SchemaRegistry schemaRegistry, System.Web.Http.Description.IApiExplorer apiExplorer)
        {
            // make operations alphabetic
            var paths = swaggerDoc.paths.OrderBy(e => e.Key).ToList();
            swaggerDoc.paths = paths.ToDictionary(e => e.Key, e => e.Value);


        }
    }
}