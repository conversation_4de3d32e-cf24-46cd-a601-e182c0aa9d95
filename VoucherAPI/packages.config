<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Confluent.Kafka" version="1.5.1" targetFramework="net45" />
  <package id="Dapper" version="1.50.2" targetFramework="net45" />
  <package id="LazyCache" version="0.7.1.44" targetFramework="net45" />
  <package id="librdkafka.redist" version="1.5.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.Cors" version="5.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net45" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.7.1" targetFramework="net45" />
  <package id="Microsoft.IdentityModel.Logging" version="6.7.1" targetFramework="net45" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.7.1" targetFramework="net45" />
  <package id="Microsoft.Owin" version="4.1.0" targetFramework="net45" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.1.0" targetFramework="net45" />
  <package id="Microsoft.Owin.Security" version="4.1.0" targetFramework="net45" />
  <package id="Microsoft.Owin.Security.Jwt" version="4.1.0" targetFramework="net45" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.1.0" targetFramework="net45" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net45" />
  <package id="Newtonsoft.Json" version="10.0.1" targetFramework="net45" />
  <package id="NLog" version="4.7.4" targetFramework="net45" />
  <package id="NLog.Targets.KafkaAppender" version="1.0.8" targetFramework="net45" />
  <package id="NLog.Web" version="4.9.0" targetFramework="net45" />
  <package id="Oracle.ManagedDataAccess" version="18.3.0" targetFramework="net45" />
  <package id="Owin" version="1.0" targetFramework="net45" />
  <package id="Swashbuckle" version="5.6.0" targetFramework="net45" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net45" />
  <package id="Swashbuckle.Core.Extension" version="1.0.0" targetFramework="net45" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net45" />
  <package id="System.IdentityModel.Tokens.Jwt" version="6.7.1" targetFramework="net45" />
  <package id="System.Memory" version="4.5.0" targetFramework="net45" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.0" targetFramework="net45" />
  <package id="WebActivatorEx" version="2.0" targetFramework="net45" />
</packages>