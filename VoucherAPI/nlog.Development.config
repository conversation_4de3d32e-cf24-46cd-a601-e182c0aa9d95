<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  autoReload="true">
  <targets async="true">
    <target xsi:type="File"
      name="logfile-error"
      layout="${longdate}|${level:uppercase=true}|${aspnet-request:serverVariable=LOCAL_ADDR}|${aspnet-request-ip}|${aspnet-request:serverVariable=HTTP_X_FORWARDED_FOR}|${aspnet-request-headers:HeaderNames=logkey:ValuesOnly=true}|${aspnet-request-url:IncludeQueryString=true}|${message}"
      encoding="utf-8"
      archiveAboveSize="52428800"
      maxArchiveFiles="2"
      archiveFileName="${basedir}/logs/${event-properties:item=local_addr}/error/log.{#}.txt"
      archiveNumbering="DateAndSequence"
      archiveDateFormat="yyMM"
      archiveEvery="Month"
      fileName="${basedir}/logs/${event-properties:item=local_addr}/error/logfile.txt"
      concurrentWrites="true"
      keepFileOpen="false"
    />
    <target xsi:type="File"
      name="logfile-trace"
      layout="${longdate}|${level:uppercase=true}|${aspnet-request:serverVariable=LOCAL_ADDR}|${aspnet-request-ip}|${aspnet-request:serverVariable=HTTP_X_FORWARDED_FOR}|${aspnet-request-headers:HeaderNames=logkey:ValuesOnly=true}|${aspnet-request-url:IncludeQueryString=true}|${message}"
      encoding="utf-8"
      archiveAboveSize="52428800" 
      maxArchiveFiles="5"
      archiveFileName="${basedir}/logs/${event-properties:item=local_addr}/trace/log.{#}.txt"
      archiveNumbering="DateAndSequence"
      archiveDateFormat="yyMMdd"
      archiveEvery="Day" 
      fileName="${basedir}/logs/${event-properties:item=local_addr}/trace/logfile.txt"
      concurrentWrites="true"
      keepFileOpen="false" 
    />
    <target xsi:type="File"
      name="logfile-info"
      layout="${longdate}|${level:uppercase=true}|${aspnet-request:serverVariable=LOCAL_ADDR}|${aspnet-request-ip}|${aspnet-request:serverVariable=HTTP_X_FORWARDED_FOR}|${aspnet-request-headers:HeaderNames=logkey:ValuesOnly=true}|${aspnet-request-url:IncludeQueryString=true}|${message}"
      encoding="utf-8"
      archiveAboveSize="52428800"
      maxArchiveFiles="5"
      archiveFileName="${basedir}/logs/${event-properties:item=local_addr}/info/${event-properties:item=dir}/log.{#}.txt"
      archiveNumbering="DateAndSequence"
      archiveDateFormat="yyMMdd"
      archiveEvery="Day"
      fileName="${basedir}/logs/${event-properties:item=local_addr}/info/${event-properties:item=dir}/logfile.txt"
      concurrentWrites="true"
      keepFileOpen="false"
    />
    <!-- kafka -->
  </targets>
  <rules>
    <logger name="*" level="Trace" writeTo="logfile-trace" />
    <logger name="*" level="Error" writeTo="logfile-error" />
    <logger name="*" level="Info" writeTo="logfile-info" />
    <!-- kafka -->
  </rules>
</nlog>