<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
    <section name="nlog" type="NLog.Config.ConfigSectionHandler, NLog" />
  </configSections>
  <connectionStrings>
    <add name="sqlConnectionWrite" connectionString="Server=ISP-PRDB;Database=Internet;Trusted_Connection=Yes;Max Pool Size=500" />
    <add name="sqlConnectionRead" connectionString="Server=ISP-PRDB-READ;Database=Internet;Trusted_Connection=Yes;Max Pool Size=500" />
    <add name="sqlConnectionIPTV" connectionString="Data Source=ISP-IPTV;Initial Catalog=IPTV;Trusted_Connection=Yes;" />

    <add name="dev__sqlConnectionWrite" connectionString="Data Source=***********;Initial Catalog=Internet;User ID=isc;Password=**********" />
    <add name="dev__sqlConnectionRead" connectionString="Data Source=***********;Initial Catalog=Internet;User ID=isc;Password=**********" />
    <add name="dev__sqlConnectionIPTV" connectionString="Data Source=***********;Initial Catalog=Internet;User ID=isc;Password=**********" />
  </connectionStrings>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="PreserveLoginUrl" value="true" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />

    <add key="Jwt_Key" value="Qmk5iHD21QiIZStgRTMd" />
    <add key="Jwt_Issuer" value="localhost" />

    <add key="stag__Jwt_Key" value="Thisismy!SecretKey12345!@#abc" />
    <add key="stag__Jwt_Issuer" value="localhost" />

    <add key="http_client_proxy" value="http://proxy.hcm.fpt.vn:80" />

    <add key="bpapi_fpt_vn" value="http://bpapi.fpt.vn" />

    <add key="stag__bpapi_fpt_vn" value="http://bpapi-stag.fpt.vn" />

    <add key="my_fpt_api" value="https://myfpt.fpt-software.vn" />
    <add key="my_fpt_api_client_id" value="98d79f23-1a27-4fe7-a48e-93e23397e518" />
    <add key="my_fpt_api_client_secret" value="aqE0aEY3BhdeU60FeP53YRDcdeIwxwCQnxg9bcbG" />
    <add key="my_fpt_api_username" value="ftel_myfpt" />
    <add key="my_fpt_api_password" value="y7}DF}sxqBL46n3C" />

    <add key="stag__my_fpt_api" value="https://myfpt-dev.fpt-demo.com" />
    <add key="stag__my_fpt_api_client_id" value="1e86577a-5023-414c-928c-0ee72752e1e1" />
    <add key="stag__my_fpt_api_client_secret" value="v56vtQTB0dTvfzfR5Kj3AwIOQP4sVqByB79v38SX" />
    <add key="stag__my_fpt_api_username" value="ftel_myfpt" />
    <add key="stag__my_fpt_api_password" value="uidvnkzas1" />

    <add key="urlFPTplay" value="https://inhouse.fptplay.net" />
    <add key="FptPlay_APP_ID" value="APP0060" />
    <add key="FptPlay_APP_KEY" value="76f6a2a4d14012cc286cb128eb967d8f8ec84ea5" />
    <add key="FptPlay_KEY" value="c1b4b3dba4146a88b36cdebab0caa3ce" />

    <add key="stag__urlFPTplay" value="https://inhouse-staging.fptplay.net" />
    <add key="stag__FptPlay_APP_ID" value="APP0019" />
    <add key="stag__FptPlay_APP_KEY" value="bdd37d82ed119adf559c20a5857f83f19f110302" />
    <add key="stag__FptPlay_KEY" value="c1b4b3dba4146a88b36cdebab0caa3ce" />

    <add key="UrlVPN" value="https://gold.fptvpn.com" />
    <add key="vpn_endpoint_create" value="/extends/users/create" />
    <add key="vpn_endpoint_status" value="/extends/users/status" />
    <add key="vpn_token" value="eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.sGlsv7K8eY0rCGft7GVOMoBeJ3Gkqq8f4qvR_EjpnUalrJdUM9d2f19nXIx1leiRwJgiG3527tY6iSPKeEJo4qmB7BVq5mSQKly5R66-PI6EpvW6qI9iCWp_QsseHmtsjuuhHbY0Qjw2nGwzvyUyz4zw7afGfYXhuuRWTg2wsmv_N6JuOianff028_5sZvOpWMP0gLmpoADr4Slxem9n3v8kio0P9mFGdQ90vzftTYmoZOzfgU6pyH8guDrTJDtuRVfwNkTmXB3eNP5mQNXKewRsPGxA43TiI7PZrC2yKHeqQcMt9Yt0CMtUG6G-ZqQM3bBCpITV1KN9HGcsFTHDDg" />
    <add key="vpn_publickey" value="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyia4Epc9KsKydUneAFF5kpV0OWZy0TROmWkhJYrpHbg4gYiJbaE070hhhmToW5SF2Tddu1rnY0uKyBzh9LGaod5qMG2yTHqum/zw+ml6vqdl5nccOOfnWuEOxYOpZ+F4o2wctH8tbhhMGgYHsgcf/HVcB4hASIc8HNp7jjm3E9yVmHi+B5OdT1tfvGYGftMQWNEdF8MMd7RjJ5Fv577vWpqzF5qrgxrYy0m3W1gKDzYnHbBOpxNmf5CkLLrdnC3FpqOiw/NT79ohqFjj6wpVtXACQyPeLlT9wlA8oPNdNMWL5pypnUYOCM9tTHYjR5slL4MxD8RsOydxikpjHPzDawIDAQAB" />

    <add key="loyalty_FRTurl" value="http://internalgw.fptshop.com.vn" />
    <add key="loyalty_FRT_authen" value="frt_prod_env0d0c3d0ce7344dd8ad3cf3adcd6d6c80" />
    <add key="loyalty_FRT_create_endpoint" value="/prod/fpt/loyalty/create-evoucher-type" />
    <add key="loyalty_FRT_book_endpoint" value="/prod/fpt/loyalty/convert-points-to-seri-voucher" />

    <add key="stag__loyalty_FRTurl" value="http://internalgw.fptshop.com.vn" />
    <add key="stag__loyalty_FRT_authen" value="frt_beta_env0c5fa45191d348abb5cecff865b7aa78" />
    <add key="stag__loyalty_FRT_create_endpoint" value="/beta/fpt/loyalty/create-evoucher-type" />
    <add key="stag__loyalty_FRT_book_endpoint" value="/beta/fpt/loyalty/convert-points-to-seri-voucher" />

    <add key="loyaltyapi" value="https://loyaltyapi.fpt.net" />
    <add key="loyaltyapi_username" value="evoucher" />
    <add key="loyaltyapi_password" value="YM53R2p5zWSTRfpmL5UAy2NpykLtaccN" />

    <add key="stag__loyaltyapi" value="https://stag-loyaltyapi.fpt.net" />
    <add key="stag__loyaltyapi_username" value="evoucher" />
    <add key="stag__loyaltyapi_password" value="dzNJZFYw9zES7u2gHwhTPF4tAs84jJTg" />

    <add key="Token_key_GTBB_loy" value="cmVmZXJyYWwtaHViOkdRZUpHWFZ3WVAya1MzSlJwUU5GOVRXZlEzMm4yUVhm" />

    <add key="stag__Token_key_GTBB_loy" value="cmVmZXJyYWwtaHViOlFVcEhyNjNTekNBYTlWVFVLTjZ5SzhKeEc4R0R6dTR5" />

    <add key="VoucherOBS" value="OBS" />

    <add key="sourceId" value="23" />
    <add key="roleId" value="2149" />

    <add key="stag__sourceId" value="24" />
    <add key="stag__roleId" value="36" />

    <add key="foxpaykey" value="FOXPAY@@*#GHD23HJ5D@AA2" />

    <add key="OS6_ReferalProgram_EmailFrom" value="<EMAIL>" />

    <add key="stag__OS6_ReferalProgram_EmailFrom" value="<EMAIL>" />

    <add key="stag__foxpaykey" value="IDIWD@(*DJDWH" />

    <add key="iam_fpt_vn" value="https://iam.fpt.vn" />

    <add key="apis_fpt_net" value="https://apis.fpt.net" />

    <add key="stag__apis_fpt_net" value="https://apis-stag.fpt.net" />

    <add key="cssapi_fpt_net" value="http://cssapi.fpt.net" />

    <add key="stag__cssapi_fpt_net" value="http://cssapi-stag.fpt.net" />

    <add key="camera_api" value="http://cameraapi.fpt.vn" />

    <add key="stag__camera_api" value="http://cameraapistag.fpt.vn" />

    <add key="apiOtt" value="https://id.fptplay.net" />

    <add key="stag__apiOtt" value="https://id-staging.fptplay.net" />

    <add key="apiPromotion" value="http://promotionapi.fpt.vn" />

    <add key="stag__apiPromotion" value="http://stg.promotionapi.fpt.vn" />

    <add key="hrapi_fpt_vn" value="http://hrapi.fpt.vn" />

    <add key="stag__hrapi_fpt_vn" value="http://hrapistag.fpt.vn" />

    <add key="parapiora_fpt_api" value="http://parapiora.fpt.vn" />

    <add key="stag__parapiora_fpt_api" value="http://stagparapiora.fpt.vn" />

    <add key="BillPaymentFreeUrl" value="http://apiiptv.fpt.vn" />

    <add key="stag__BillPaymentFreeUrl" value="http://apiiptvtag.fpt.vn" />
    
    <add key="hi_fpt_api" value="http://hi-customer.fpt.vn" />

    <add key="stag__hi_fpt_api" value="http://hi-customer-stag.fpt.vn" />

    <add key="apiHiFptcustomer" value="http://hi-customer.fpt.vn" />

    <add key="stag__apiHiFptcustomer" value="http://hi-customer-stag.fpt.vn" />

    <add key="manapi_fpt_vn" value="http://manapi.fpt.vn" />

    <add key="deviceReplace" value="http://manapi.fpt.vn" />

    <add key="stag__DigitalSignature" value="http://systemresourcestag.fpt.vn" />

    <add key="stag__HiFPTFG" value="http://hifpt-api-stag.fpt.vn" />
    <add key="stag__HiFPTFG_token_clientId" value="evoucher" />
    <add key="stag__HiFPTFG_token_clientSecret" value="665b72a185cb31b56f52acb6dc4df9f82fa4366a283af2cdb30bd88e64960755" />
    <add key="stag__HiFPTFG_token_endpoint" value="/auth-provider/client/token" />
    <add key="stag__HiFPTFG_noti_endpoint" value="/notify-provider/3rd/send-notify-by-contract" />

    <add key="HiFPTFG" value="http://hifpt-api.fpt.vn" />
    <add key="HiFPTFG_token_clientId" value="evoucher" />
    <add key="HiFPTFG_token_clientSecret" value="665b72a185cb31b56f52acb6dc4df9f82fa4366a283af2cdb30bd88e64960755" />
    <add key="HiFPTFG_token_endpoint" value="/auth-provider/client/token" />
    <add key="HiFPTFG_noti_endpoint" value="/notify-provider/3rd/send-notify-by-contract" />
    
    <add key="systemapi_fpt_vn" value="http://systemapi.fpt.vn" />

    <add key="apiSmsworld" value="http://smsworld.fpt.vn" />

    <add key="emailApi" value="http://systemmailapi.fpt.vn" />

    <add key="stag__nextgenapi" value="http://nextgenapi-stag.fpt.net" />
    <add key="stag__nextgenapi_user" value="paytv" />
    <add key="stag__nextgenapi_pass" value="P4ytv" />
    <add key="nextgenapi" value="http://nextgenapi.fpt.net" />
    <add key="nextgenapi_user" value="paytv" />
    <add key="nextgenapi_pass" value="P4ytv" />

    <add key="stag__fsi_domain" value="http://intapi-stag.fpt.vn" />
    <add key="fsi_domain" value="http://intapi.fpt.vn" />
    
    <add key="fsi_inactive_ep" value="/api/ApiInsideVN/FGame/UltraFastCreateCustomerEVC" />
    <add key="stag__fsi_inactive_ep" value="/api/ApiInsideVN/FGame/UltraFastCreateCustomerEVC" />
    
    
  </appSettings>
  <system.web>
    <compilation targetFramework="4.5" debug="true" />
    <httpRuntime targetFramework="4.5" />
    <customErrors mode="On" defaultRedirect="/Template/NotFound.html">
      <error statusCode="401" redirect="/Template/NotFound.html" />
      <error statusCode="404" redirect="/Template/NotFound.html" />
      <error statusCode="400" redirect="/Template/NotFound.html" />
    </customErrors>
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Routing" />
        <add namespace="System.Web.WebPages" />
      </namespaces>
    </pages>
  </system.web>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />

    
  <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers></system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Core" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <publisherPolicy apply="no" />
        <assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="EntityFramework" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.0.0" newVersion="4.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.7.1.0" newVersion="6.7.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.7.1.0" newVersion="6.7.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.JsonWebTokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.7.1.0" newVersion="6.7.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.7.1.0" newVersion="6.7.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Cors" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.4.0" newVersion="5.2.4.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
