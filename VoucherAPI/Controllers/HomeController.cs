using LazyCache;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using Voucher.API.Models;
using Voucher.APIHelper;

namespace Voucher.API.Controllers
{
    public class HomeController : Controller
    {
        //
        // GET: /Home/
        public ContentResult Index()
        {
            return Content("Hello");
        }

        #region log
        public ContentResult Log()
        {
            string RequestIpAddress = SwaggerHelper.GetClientIPAddress(System.Web.HttpContext.Current.Request);
            if (!SwaggerHelper.CheckPermission(RequestIpAddress))
                return Content(RequestIpAddress, "text/html");

            string html = JsonConvert.SerializeObject(Request.ServerVariables["LOCAL_ADDR"]) + "<hr>";
            var list = GetLogs();
            foreach (var item in list)
            {
                html += item.path + "<br>";
                html += string.Join("<br>", item.logs.Select(l => "<a href='/" + l + "'>" + Path.GetFileName(l) + "</a>"));
                html += "<hr>";
            }
            return Content(html, "text/html");
        }

        public ContentResult DelLogs(string cmd = null)
        {
            string RequestIpAddress = SwaggerHelper.GetClientIPAddress(System.Web.HttpContext.Current.Request);
            if (!SwaggerHelper.CheckPermission(RequestIpAddress))
                return Content(RequestIpAddress, "text/html");

            if (cmd == "del")
            {
                ResponseModel res = new ResponseModel();
                var files = Directory.GetFiles(Server.MapPath("~/logs/"), "*.txt", SearchOption.AllDirectories).ToList();
                files.ForEach(f =>
                {
                    try
                    {
                        if (System.IO.File.GetLastWriteTime(f) < DateTime.Now.AddDays(-30))
                            System.IO.File.Delete(f);
                    }
                    catch (Exception ex) { res.error = ex.ToString(); }
                });
                res.data = files;
                return Content(JsonConvert.SerializeObject(res), "application/json");
            }
            return Content("", "text/html");
        }

        private List<LogModel> GetLogs()
        {
            var files = Directory.GetFiles(Server.MapPath("~/logs/"), "*.txt", SearchOption.AllDirectories).
                Select(f => f.Replace("\\", "/").Substring(f.IndexOf("logs")));
            var list = files.GroupBy(f => f.Substring(0, f.LastIndexOf("/"))).Select(f =>
                new LogModel { path = f.Key.Replace("logs/", ""), logs = f.OrderByDescending(c => c).ToList() });
            return list.OrderBy(c => (c.path.ToLower().Contains("trace") || c.path.ToLower().Contains("error") ? "_" + c.path : c.path)).ToList();
        }

        private static void processDirectory(string startLocation)
        {
            foreach (var directory in Directory.GetDirectories(startLocation))
            {
                processDirectory(directory);
                if (Directory.GetFiles(directory).Length == 0 &&
                    Directory.GetDirectories(directory).Length == 0)
                {
                    Directory.Delete(directory, false);
                }
            }
        }

        public class LogModel
        {
            public string path { get; set; }
            public List<string> logs { get; set; }
        }
        #endregion

        #region cache
        public ContentResult Cache(string key = null, string cmd = null)
        {
            string RequestIpAddress = SwaggerHelper.GetClientIPAddress(System.Web.HttpContext.Current.Request);
            if (!SwaggerHelper.CheckPermission(RequestIpAddress))
                return Content(RequestIpAddress, "text/html");

            List<string> keys = new List<string>();
            if (!string.IsNullOrEmpty(key))
            {
                IAppCache cache = new CachingService();
                keys = cache.ObjectCache.Where(c => c.Key.IndexOf(key, StringComparison.OrdinalIgnoreCase) >= 0).Select(c => c.Key).OrderBy(c => c).ToList();
                if (cmd == "del")
                {
                    foreach (var k in keys)
                    {
                        cache.ObjectCache.Remove(k);
                    }
                }
            }
            return Content(Request.ServerVariables["LOCAL_ADDR"] + "<hr>" + string.Join("<br>", keys), "text/html");
        }
        #endregion
    }
}