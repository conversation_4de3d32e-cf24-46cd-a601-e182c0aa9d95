<log4net>
  <root>
    <level value="INFO"/>
    <appender-ref ref="LogFileAppender"/>
  </root>

  <appender name="LogFileAppender" type="log4net.Appender.RollingFileAppender">
    <file type="log4net.Util.PatternString" value="%property{LogFileName}" />
    <encoding value="utf-8" />
    <appendToFile value="true" />
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="10" />
    <maximumFileSize value="50MB" />
    <staticLogFileName value="true" />
    <layout type="log4net.Layout.PatternLayout">
      <param name="ConversionPattern" value="%date{dd MMM yyyy HH:mm:ss} - %-5level - %message%newline%exception" />
    </layout>
  </appender>

  <logger name="LAAC.UI.WebService">
    <level value="INFO" />
  </logger>

</log4net>