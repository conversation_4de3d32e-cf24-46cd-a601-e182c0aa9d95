using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace GetQuotaOfUser.Models
{
    public class EvoucherBussiness
    {
        public string EventCode { set; get; }
        public string Name { set; get; }
        public DateTime RedeemDate { set; get; }
        public int SaleID { set; get; }
        public string Status { set; get; }
        public int StatusID { set; get; }
    }

    public class EvoucherBussinessSale
    {
        public string EventCode { set; get; }
        public string Name { set; get; }
    }
    public class SalesManInfo
    {
        public string Name { get; set; }
        public string FullName { get; set; }
        public int SALESMAN { get; set; }
        public int DEPARTMENT { get; set; }
        public int BRANCHCODE { get; set; }
        public int LOCATION { get; set; }
        public string REGION { get; set; }
        public int PromotionEvent { get; set; }
        public int Quota { get; set; }
        public int SubCompanyID { get; set; }
    }
    public class PEDiscountModel
    {
        public int ID { get; set; }
        public string EventCode { get; set; }
        public int MoneyPromotionNETID { get; set; }
        public int MoneyPromotionTVID { get; set; }
        // SubsProm
        public int NetPromotionID { get; set; }
        public int IPTVPromotionID { get; set; }
        public int NetInterConnPromotionID { get; set; }
        public int IPTVInterConnPromotionID { get; set; }
        public int GiftPromotionID { get; set; }
        // PromotionNet
        public int PromotionNetID { get; set; }
        public int ContainerID { get; set; }
    }
    public class ApplyVoucherInput
    {
        public string regCode { set; get; }
        public int saleID { set; get; }
        public int objID { set; get; }
        public string voucherGc { set; get; }
        public string user { set; get; }

    }

    public class ClearVoucherInput
    {
        public string regCode { set; get; }
        public int objID { set; get; }
        public string voucherGc { set; get; }
        public string user { set; get; }

    }

    public class DataEventID
    {
        public int generalCodeID { set; get; }
        public int voucherBillID { set; get; }
        public long prePaidID { set; get; }
        public int objPromotionID { set; get; }

    }
    public class PromotionEventModel
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string EventCode { get; set; }
        public int QuotaGeneralCode { get; set; }
        public int QuotaPrivateCode { get; set; }
        public int InviteType { get; set; }

        // Tặng số tháng cước
        //public int FreeMonthNET { get; set; }
        public float ContainerMonth { get; set; }

        public string PromotionNetDiscount { get; set; }
        public int PromotionNetAmount { get; set; }
        public int PromotionNetDuration { get; set; }

        /// ///////////////////////////// Giam tien truc tiep NET
        public int ID1 { get; set; }
        public String Discount1 { get; set; }
        public int Value1 { get; set; }
        public int ServiceCode1 { get; set; }

        ////////////////////////////// Khuyen Mai Tra Truoc NET
        public int ID2 { get; set; }
        public int PrepaidTime { get; set; }
        public String Discount2 { get; set; }
        public int Value2 { get; set; }
        public int DisMonth2 { get; set; }
        public int DisMonth2L2 { get; set; }
        public int DisMonth2L3 { get; set; }
        public int DisMonth2L4 { get; set; }
        public int DisRatio2L2 { get; set; }
        public int DisRatio2L3 { get; set; }
        public int DisRatio2L4 { get; set; }
        public int ServiceCode2 { get; set; }

        /////////////////////////// Khuyen Mai IPTV
        public int ID3 { get; set; }
        public String Discount3 { get; set; }
        public int Value3 { get; set; }
        public int DisMonth3 { get; set; }
        public int DisMonth3L2 { get; set; }
        public int ServiceCode3 { get; set; }

        //////////////////////// Khuyen Mai Hoa Mang Net
        public int ID4 { get; set; }
        public String Discount4 { get; set; }
        public int Value4 { get; set; }
        public int ServiceCode4 { get; set; }

        //////////////////////// Khuyen Mai Hoa Mang IPTV
        public int ID5 { get; set; }
        public String Discount5 { get; set; }
        public int Value5 { get; set; }
        public int ServiceCode5 { get; set; }

        //////////////////////// Khuyen Mai Qua tang
        public int ID6 { get; set; }
        public string Discount6 { get; set; }
        public string Model { get; set; }

        ////////////////////// Khuyen Mai Giam tien truc tiep TV
        public int ID7 { get; set; }
        public String Discount7 { get; set; }
        public int Value7 { get; set; }
        public int ServiceCode7 { get; set; }
    }

    public class MonthPromotionModel
    {
        public string MonthFrom { get; set; }
        public string MonthTo { get; set; }
        public DateTime? ActiveDate { get; set; }
        public int EventMonth { get; set; }
        public int GetAddMonth()
        {
            try
            {
                DateTime? finalMonth;
                DateTime monthFrom, monthTo;
                if (DateTime.TryParse(MonthFrom, out monthFrom))
                {
                    finalMonth = monthFrom;
                    if (DateTime.TryParse(MonthTo, out monthTo))
                    {
                        if (monthTo > finalMonth)
                            finalMonth = monthTo;
                    }
                    if (ActiveDate.HasValue && finalMonth.HasValue)
                    {
                        finalMonth = finalMonth.Value.AddMonths(1);
                        return ((finalMonth.Value.Year - ActiveDate.Value.Year) * 12) + finalMonth.Value.Month - ActiveDate.Value.Month;
                    }
                }
            }
            catch { }
            return 0;
        }
    }
    #region input lien quan API AddVoucher
    public class AddVoucherInput : MBSSalePlatformInputModel
    {
        public List<EvoucherInput> evoucherList { set; get; }
        public string OrderCode { set; get; }
        public string RegCode { set; get; }
        public int objId { set; get; }
    }
    public class MBSSalePlatformInputModel
    {
        public int locationID { set; get; }
        public int saleID { set; get; }

        public List<CustomerTypeModel> CustomerTypes { set; get; }

        public List<ServicePlatform> Services { set; get; }
        public List<ProductPlatform> Products { set; get; }
        public UserBranchPlatformModel Userbranch { set; get; }
        public int channel { set; get; }
    }
    public class CustomerTypeModel
    {
        public int ServiceID { set; get; }
        public int CustomerType { set; get; }
    }
    public class ServicePlatform : Services
    {
        public List<SubServiceTypesModel> SubServiceTypes { set; get; }
    }
    public class Services
    {
        public int ServiceID { set; get; }

    }
    public class ProductPlatform : Services
    {
        public List<SubServiceTypesProModel> SubServiceTypes { set; get; }
    }
    public class UserBranchPlatformModel
    {
        public int sourceId { set; get; }
        public int subcompanyId { set; get; }
        public int branchCode { set; get; }
        public int departmentId { set; get; }
        public string contractGT { set; get; }
    }
    public class SubServiceTypesModel
    {
        public int SubServiceTypeID { set; get; }
        public List<SubServiceModel> SubServices { set; get; }
    }
    public class SubServiceTypesProModel
    {
        public int SubServiceTypeId { set; get; }
        public List<SubServicesPro> SubServices { set; get; }
    }
    public class SubServiceModel
    {
        public int SubServiceID { set; get; }
        public int PrePaid { set; get; }
        public int DeployTypeID { set; get; }
        public int ServiceCode { set; get; }
        public int Qty { set; get; }
    }
    public class SubServicesPro : SubServiceModel
    {
        public int GroupID { set; get; }
        public int ApplySubServiceID { set; get; }
        public int ApplyPrePaid { set; get; }
        public int StatusID { set; get; }
        public int RevokeID { set; get; }
        public int UsesID { set; get; }
    }
    public class DeviceModel : GroupProduct
    {
        public int qty { get; set; }
        public int serviceId { get; set; }
        public int subServiceTypeID { get; set; }
        public int deviceId { get; set; }
        public int serviceCode { get; set; }
    }
    public class GroupProduct
    {
        public int groupID { set; get; }
        public int applyPrePaid { set; get; }
        public int applySubServiceID { set; get; }
        public int statusID { set; get; }
        public int revokeID { set; get; }
        public int usesID { set; get; }
        public int prePaid { set; get; }
        public int deployTypeID { set; get; }
    }
    public class EvoucherInput
    {
        public string evoucherCode { set; get; }
        public int evoucherType { set; get; }
        public int channel { set; get; }
    }
    public class EvoucherService : EvoucherInput
    {
        public int ServiceID { set; get; }
        public int SubServiceType { set; get; }
    }
    public class SalePlatformVoucherValueInfor
    {
        public string evoucherCode { set; get; }
        public string Value { set; get; }
        public decimal Discount { set; get; }
        public decimal DiscountVAT { set; get; }
        public int TypeID { set; get; }
        public int Quota { set; get; }
        public List<Apply> Apply { set; get; }
    }
    public class Apply
    {
        public int ServiceID { set; get; }
        public int SubServiceTypeID { set; get; }
        public int SubServiceID { set; get; }
        public int ServiceCode { set; get; }
    }
    public class OTTReferalProgram
    {
        public validCode data { set; get; }
        public string message { set; get; }
        public int status { set; get; }
    }
    public class validCode
    {
        public string referral_code { set; get; }
        public int valid { set; get; }
    }
    public class InfoRP
    {
        public int ID { get; set; }
        public string EventCode { get; set; }
        public int MoneyPromotionNETID { get; set; }
        public int MoneyPromotionTVID { get; set; }
        public int NetPromotionID { get; set; }
        public int IPTVPromotionID { get; set; }
        public int NetInterConnPromotionID { get; set; }
        public int IPTVInterConnPromotionID { get; set; }
        public int GiftPromotionID { get; set; }

        public int PromotionNetID { get; set; }
        public int ContainerID { get; set; }
    }
    public class FoxGold
    {
        public int ID { set; get; }
        public string EventCode { set; get; }
    }
    public class ItemRequest
    {
        public int PromotionEventID { get; set; }
        public int ObjecInvite { get; set; }
        public int Objectinvited { get; set; }
        public string RegCode { get; set; }
        public string VoucherCode { get; set; }
        public int FreeMonthNet { get; set; }
    }
    public class GeneralCodeInsert
    {
        public string eventCode { set; get; }
        public int id { set; get; }
    }
    public class FoxproUseInviteCode
    {
        public int ID { set; get; }
        public int generalCodeID { set; get; }
        public int res { set; get; }
        public string StaffIDInvite { set; get; }
        public string InviteCode { set; get; }
        public string Description { set; get; }
        public int ObjID { set; get; }
        public string FullName { set; get; }
        public string Contract { set; get; }
        public DateTime? FirstAccess { set; get; }
        public string RealMoneyAmount { set; get; }
        public string EventCode { set; get; }
        public int MoneyPromotionNETID { set; get; }
        public string NET { set; get; }
        public int MoneyPromotionTVID { set; get; }
        public string TV { set; get; }
    }
    public class LoginInfor
    {
        public string username { set; get; }
        public string password { set; get; }
    }
    public class Login
    {
        public const string userName = "<EMAIL>";
        public const string passWord = "!@#Referral123";
    }
    public class AuthorizationInfor
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string refresh_token { set; get; }
        public string accessTokenExpiresOn { set; get; }
        public string refreshTokenExpiresOn { set; get; }
    }
    public class ModelSendNotificationReferralMyFPT
    {
        public string employeeCode { get; set; }
        public decimal Referrer { get; set; }
        public string BookingId { get; set; }
        public string contract_owner { get; set; }
        public string BookingStatus { get; set; }
    }
    public class InfoRFCAM
    {
        public int ID { set; get; }
        public string EventCode { set; get; }
        public int QuotaGeneralCode { set; get; }
        public int InviteType { set; get; }
        public int ID1 { set; get; }
    }
    public class EventCamera
    {
        public string EventCodeCamIn { set; get; }
        public string EventCodeCamOut { set; get; }
    }
    public class RPCodeRedeemModel
    {
        public int PromotionEventID { get; set; }
        public int ObjID { get; set; }
        public string OrderCode { get; set; }
        public string VoucherCode { get; set; }
        public int Location { get; set; }
        public int BranchCode { get; set; }
        public int Department { get; set; }
        public int Salesman { get; set; }
        public int BNET { get; set; }
        public int BTV { get; set; }
        public int IsPrepaidTV { get; set; }
        // status in active in MBS chanel will be have two type:
        // 200 is user invite - 222 is user invited
        // for websilte online chanel : 100 is user invite - 111 is user invited
        public int ActiveChannel { get; set; }
        public int SubCompanyID { get; set; }
        public int LocalTypeID { get; set; }
        public int PaidTimeTypeID { get; set; }
    }

    #region tách link server
    public class In4VoucherTHmodel
    {
        public int amountTv { get; set; }
        public int locationId { get; set; }
        public int addBy { get; set; }
    }

    #endregion
    public class Evoucher
    {
        public string VoucherCode { set; get; }
        public string Description { set; get; }
        public string Note { set; get; }
        public string Todate { set; get; }
    }
    public class SalePlatformGetListVoucher : MBSSalePlatformInputModel
    {

    }
    public class Evoucherid : Evoucher
    {
        public int ID { set; get; }
        public int LocaltypeID { set; get; }
    }
    public class EvoucherServiceCode : Evoucherid
    {
        public int ServiceCodeGTTTnet { set; get; }
        public int ServiceCodeGTTTtv { set; get; }
        public int ServiceCodePHMnet { set; get; }
        public int ServiceCodePHMtv { set; get; }
    }
    #endregion
}