using Dapper;
using GetQuotaOfUser.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;

namespace GetQuotaOfUser.Services
{
    public static class IssueInvoiceService
    {
        private static string OSU6_FPTVoucher_IssueInvoice = "PowerInside.dbo.OSU6_FPTVoucher_IssueInvoice";
        public static List<IssueInvoice> GetIssueInvoice(int objId, int regId)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var result = connection.Query<IssueInvoice>(OSU6_FPTVoucher_IssueInvoice, new
                {
                    Action = "GetInvoice",
                    ObjId = objId,
                    RegId = regId
                }, commandType: System.Data.CommandType.StoredProcedure).ToList();

                return result;
            }
        }
    }
}