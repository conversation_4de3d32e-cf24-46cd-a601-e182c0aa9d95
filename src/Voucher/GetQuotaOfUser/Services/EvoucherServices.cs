using GetQuotaOfUser.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Web.Http.Results;

namespace GetQuotaOfUser.Services
{
    public static class EvoucherServices
    {
        public static List<ValueVoucherOrderCode> GetValueVoucherByOrderCode(List<string> orderCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var result = connection.Query<ValueVoucherOrderCode>("PowerInside.dbo.OSU6_PolicyVoucher_SalePlatform_GetInfor", new
                {
                    Action = "GetValueVoucherByOrderCode",
                    OrderCode = string.Join(",", orderCode)
                }, commandTimeout: 0, commandType: CommandType.StoredProcedure).ToList();

                return result;
            }
        }
    }
}