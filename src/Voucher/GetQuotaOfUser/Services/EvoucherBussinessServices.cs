using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using GetQuotaOfUser.Constant;
using GetQuotaOfUser.Models;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;
using System.Configuration;
using System.Net;
using System.IO;
using Newtonsoft.Json;
using System.Xml.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;

namespace GetQuotaOfUser.Services
{
    public class EvoucherBussinessServices
    {
        public static string RamdoneString()
        {
            Random rd = new Random();
            return rd.Next(1, 10000).ToString();
        }
        public static Boolean CheckValidServiceCode(List<int> SPE, List<int> SIP)
        {
            return (SPE.Where(p => p != 0 && !SIP.Any(p2 => p2 == p)).Count() == 0);
        }

        public static int GetQuotaV2(int promotionID, int salesmanID, int localTypeID, int paidTimeTypeID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(VoucherBussinessConstant.OS6_FPTVoucher_SalesManInfo, new
                {
                    ActionName = VoucherBussinessConstant.GetQuotaV2,
                    ID = salesmanID,
                    PromotionID = promotionID,
                    LocalTypeID = localTypeID,
                    PaidTimeTypeID = paidTimeTypeID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }
        public static void UpdateOanhVK(int ObjID, string OrderCode, int? generalCodeID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@generalCodeID", generalCodeID);
                connection.Execute("PowerInside.dbo.OS6_FPTVoucher_AddVoucherBillV2", p, commandType: CommandType.StoredProcedure);
            }
        }
        public static SalesManInfo GetInfoSalesMan(int ObjSalesman)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<SalesManInfo>("PowerInside.dbo.OS6_FPTVoucher_SalesManInfo", new
                {
                    ActionName = "SelectByID",
                    ID = ObjSalesman
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static int GetPrepaidId(int objID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int res = 0;
                res = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                {
                    actionName = VoucherBussinessConstant.GetPrePaidTimeID,
                    objID = objID,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return res;
            }            
        }

        public static int GetLocaltype(int objID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                {
                    actionName = VoucherBussinessConstant.GetLocalType,
                    objID = objID,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static PEDiscountModel GetPE(string VoucherCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                PEDiscountModel PE = connection.Query<PEDiscountModel>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                {
                    ActionName = VoucherBussinessConstant.GetPEByCode,
                    EventCode = VoucherCode,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return PE;
            }            
        }
        public static void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }

        public static MonthPromotionModel GetMonthPromotion(int ObjID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<MonthPromotionModel>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetMonthPromotion",
                    new { ObjID = ObjID },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        public static int GetPromotionNetByContainerID(int ContainerID, int AddMonth, int IsEvenMonth = 0)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPromotionNetByContainerID",
                    new { ContainerID = ContainerID, AddMonth = AddMonth, IsEvenMonth = IsEvenMonth },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        public static void AddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        public static bool checkInforVoucherClear(string eventCodeGC)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int res = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                {
                    actionName = "checkInforVoucherClearV2",
                    eventCode = eventCodeGC,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return (res ==1);
            }     
        }

        public static int GetStatusrVoucherClear(string eventCodeGC, int objid, string regcode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int res = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                {
                    actionName = "GetStatusVoucherClearV2",
                    eventCode = eventCodeGC,
                    objID = objid,
                    regCode = regcode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return res;
            }    
        }
        #region add voucher
        public static string GetServiceCode(int service)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<string>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetCodeService",
                    @serviceid = service
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static int GetLocaltypeV2(int subServiceID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "getLocaltype",
                    subServiceID = subServiceID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static List<DeviceModel> GetListDeviceCam(List<ProductPlatform> lstProduct)
        {
            List<DeviceModel> lstDevice = new List<DeviceModel>();
            var serviceCams = lstProduct.Where(x => string.Equals(GetServiceCode(x.ServiceID).ToUpper(), "OTT", StringComparison.OrdinalIgnoreCase) || string.Equals(GetServiceCode(x.ServiceID).ToUpper(), "CMR", StringComparison.OrdinalIgnoreCase)).ToList();
            if (serviceCams.Count > 0)
            {
                foreach (var serviceCam in serviceCams)
                {
                    foreach (var SubServiceType in serviceCam.SubServiceTypes)
                    {
                        foreach (var subservice in SubServiceType.SubServices)
                        {
                            DeviceModel de = new DeviceModel();
                            de.applyPrePaid = subservice.ApplyPrePaid;
                            de.applySubServiceID = subservice.ApplySubServiceID;
                            de.deployTypeID = subservice.DeployTypeID;
                            de.deviceId = subservice.SubServiceID;
                            de.groupID = subservice.GroupID;
                            de.prePaid = subservice.PrePaid;
                            de.qty = subservice.Qty;
                            de.revokeID = subservice.RevokeID;
                            de.serviceCode = subservice.ServiceCode;
                            de.serviceId = serviceCam.ServiceID;
                            de.statusID = subservice.StatusID;
                            de.subServiceTypeID = SubServiceType.SubServiceTypeId;
                            de.usesID = subservice.UsesID;
                            lstDevice.Add(de);
                        }
                    }
                }
            }
            return lstDevice;
        }
        public static List<EvoucherService> getListEvcService(Guid logId, int channel, List<EvoucherInput> lstEVC = null, List<EvoucherInput> lstRF = null,
            int localtype = 0, Tuple<int, int> prepaid_net_tv = null, List<DeviceModel> lstDevice = null)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), RamdoneString());
            List<EvoucherService> lstVoucher = new List<EvoucherService>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();

                if (lstEVC.Count > 0)
                {
                    try
                    {
                        foreach (var evc in lstEVC)
                        {
                            List<EvoucherService> lses = new List<EvoucherService>();
                            lses = connection.Query<EvoucherService>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
                            {
                                actionName = "GetServiceVoucher",
                                voucherCode = evc.evoucherCode
                            }, commandType: CommandType.StoredProcedure).ToList();
                            lses.Select(x => x.evoucherType = evc.evoucherType).ToList();
                            if (lses != null)
                                lstVoucher.AddRange(lses);
                        }
                    }
                    catch (Exception ex)
                    {
                        L.Mes(Level.ERROR, ex.Message, keylog + " VoucherBussiness - AddVoucher");
                    }
                }
                if (lstRF.Count > 0)
                {
                    if (lstDevice == null)
                    {
                        lstDevice = new List<DeviceModel>();
                    }
                    foreach (var rf in lstRF)
                    {
                        if (lstDevice.Count > 0)
                        {
                            List<SalePlatformVoucherValueInfor> ls = GetPromotionRFCAM(lstDevice, rf.evoucherCode);
                            if (ls.Count > 0)
                            {
                                foreach (var l in ls)
                                {
                                    if (l.Apply.Count > 0)
                                    {
                                        foreach (var a in l.Apply)
                                        {
                                            EvoucherService es = new EvoucherService { evoucherCode = rf.evoucherCode, ServiceID = a.ServiceID, SubServiceType = a.SubServiceTypeID, evoucherType = rf.evoucherType };
                                            lstVoucher.Add(es);
                                        }
                                    }
                                }
                            }
                            return lstVoucher;
                        }
                        SalePlatformVoucherValueInfor info = GetVoucherCampaignInfo(rf.evoucherCode, localtype, channel, prepaid_net_tv, 1, logId);

                        List<EvoucherService> lses = new List<EvoucherService>();
                        lses = connection.Query<EvoucherService>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
                        {
                            actionName = "GetServiceVoucher",
                            voucherCode = info.evoucherCode
                        }, commandType: CommandType.StoredProcedure).ToList();
                        lses.Select(x => x.evoucherCode = rf.evoucherCode).ToList();
                        lses.Select(x => x.evoucherType = rf.evoucherType).ToList();
                        if (lses != null)
                            lstVoucher.AddRange(lses);
                    }
                }
            }
            return lstVoucher;
        }
        public static List<SalePlatformVoucherValueInfor> GetPromotionRFCAM(List<DeviceModel> devices, string voucherRF)
        {
            List<SalePlatformVoucherValueInfor> lstdataa = new List<SalePlatformVoucherValueInfor>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int checkVoucher = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "CheckStatusRF",
                    VoucherCode = voucherRF
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                if (!checkVoucher.Equals(1))
                {
                    return null;
                }
                foreach (var item in devices)
                {
                    SalePlatformVoucherValueInfor res = new SalePlatformVoucherValueInfor();
                    res = connection.Query<SalePlatformVoucherValueInfor>(VoucherBussinessConstant.OS6_FPTVoucher_SPF_RegisterWeb, new
                    {
                        ActionName = "GetPromotionCam",
                        quantity = item.qty,
                        serviceCode = item.serviceCode,
                        EfId = item.deviceId,
                        serviceId = item.serviceId,
                        subServiceTypeId = item.subServiceTypeID
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (res != null)
                    {
                        res.evoucherCode = voucherRF;
                        res.Apply = new List<Apply>();
                        Apply app = new Apply { ServiceCode = item.serviceCode, ServiceID = item.serviceId, SubServiceID = item.deviceId, SubServiceTypeID = item.subServiceTypeID };
                        res.Apply.Add(app);
                        lstdataa.Add(res);
                    }
                }
            }
            return lstdataa;
        }
        public static SalePlatformVoucherValueInfor GetVoucherCampaignInfo(string voucher, int localtype, int channel, Tuple<int, int> prepaid_net_tv, int typeGetVoucher, Guid logId)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), RamdoneString());
            L.Mes(Level.REQUEST, "", keylog + " VoucherBussiness - AddVoucher");
            SalePlatformVoucherValueInfor voucherinfo = new SalePlatformVoucherValueInfor();
            int PrepaidID = GetPrepaidID(prepaid_net_tv.Item1, prepaid_net_tv.Item2);
            try
            {
                int typeRF = checkReferalProgram(voucher, channel, logId);
                if (typeRF.Equals(2)) // là mã GTBb FPt play
                {
                    voucherinfo = GetInfoVoucherOttRF(voucher, localtype, PrepaidID);
                }
                else
                {
                    bool isMyFFPT = false;
                    if (typeRF.Equals(1)) // là mã GTBb FPt play
                    {
                        isMyFFPT = true;
                    }
                    voucherinfo = GetInfoVoucherRF(voucher, localtype, PrepaidID, isMyFFPT);
                }

                voucherinfo.Apply = getApply(voucherinfo.evoucherCode, prepaid_net_tv.Item1);
                if (typeGetVoucher == 0)
                {
                    voucherinfo.evoucherCode = voucher;
                }
                if (typeGetVoucher == 1)
                {
                    voucherinfo.evoucherCode = voucherinfo.evoucherCode;
                }
                L.Mes(Level.REQUEST, voucherinfo.ToString(), keylog + " GetVoucherCampaignInfo");
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "GetVoucherCampaignInfo Error");
                return null;
            }
            return voucherinfo;
        }
        public static int checkReferalProgram(string voucher, int channel, Guid logId)
        {
            if (channel.Equals(1)) // kênh DKOL thì mới có GTBB FPT play
            {
                bool OTTclient = checkOTTCode(voucher);
                if (OTTclient)
                {
                    return 2; // return 2 nếu mã là GTBB OTT
                }
            }
            bool isMYFPTCode = checkMyFPTCode(voucher, logId.ToString());

            if (isMYFPTCode)
            {
                return 1; // return 1 nếu mã là GTBB MyFPT
            }
            return 0;
        }
        public static int GetPrepaidID(int prepaidnet, int prepaidtv)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetPrepaidID",
                    prepaidnet = prepaidnet,
                    prepaidtv = prepaidtv
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static SalePlatformVoucherValueInfor GetInfoVoucherOttRF(string VoucherRP, int? LocalType, int? PaidTimeTypePE)
        {
            SalePlatformVoucherValueInfor res = new SalePlatformVoucherValueInfor();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.Query<SalePlatformVoucherValueInfor>(VoucherBussinessConstant.OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "GetPromotionOTTCode",
                    localTypeId = LocalType,
                    prepaidId = PaidTimeTypePE
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return res;
        }
        private static SalePlatformVoucherValueInfor GetInfoVoucherRF(string VoucherRP, int? LocalType, int? PaidTimeTypePE, bool isMyFPT)
        {
            string actionCode = "";
            SalePlatformVoucherValueInfor res = new SalePlatformVoucherValueInfor();
            if (isMyFPT)
            {
                actionCode = "GetVoucherRFinfo_MyFPT";
            }
            else
            {
                actionCode = "GetVoucherRFinfo";
            }
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var parameters = new DynamicParameters();
                parameters.Add("@actionName", actionCode);
                parameters.Add("@voucherCode", VoucherRP);
                parameters.Add("@PaidTimeType", PaidTimeTypePE);
                parameters.Add("@LocalType", LocalType);
                res = connection.Query<SalePlatformVoucherValueInfor>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, 
                    parameters, commandType: CommandType.StoredProcedure).DistinctBy(x => x.evoucherCode).FirstOrDefault();
            }
            //res.evoucherCode = VoucherRP;
            return res;
        }
        public static List<Apply> getApply(string voucher, int PrepaidNet)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<Apply>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetApplyVoucher",
                    prepaidnet = PrepaidNet,
                    voucherCode = voucher
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
        }
        public static bool checkOTTCode(string vouchercode)
        {
            try
            {
                string url = Utility.apiOtt + "/api/v1/isc/referral/";
                OTTReferalProgram ott = callapi_CheckOTT(string.Concat(url, vouchercode));
                L.Mes(Level.ERROR, JsonConvert.SerializeObject(ott), "OTT - checkOTTCode");
                if (ott.status == 1)
                {
                    return (ott.data.valid == 1);
                }
                return false;
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "OTT - checkOTTCode");
                return false;
            }
        }
        public static OTTReferalProgram callapi_CheckOTT(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                OTTReferalProgram ott = new OTTReferalProgram { data = null, message = "Fail", status = 0 };
                ott = JsonConvert.DeserializeObject<OTTReferalProgram>(rs);
                return ott;
            }
        }
        public static bool checkMyFPTCode(string vouchercode, string log)
        {
            int flag = 0;
            try
            {
                //kiểm tra loại
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    flag = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_Foxpro_MBS", new
                    {
                        ActionName = "CheckCodeFoxpro",
                        voucherCode = vouchercode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    L.Mes(Level.INFO, flag.ToString(), string.Concat("checkMyFPTCode ", log));
                    return (flag == 1);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), string.Concat("checkMyFPTCode ", log));
                return false;
            }
        }
        public static Tuple<int, int> GetPrepaidtimeNetTV(List<ServicePlatform> lstService)
        {
            int prepaidNet = -1;
            int prepaidTv = -1;
            foreach (var service in lstService)
            {
                string CodeService = GetServiceCode(service.ServiceID);
                if (CodeService.ToUpper().Equals("INT"))
                {
                    prepaidNet = service.SubServiceTypes[0].SubServices[0].PrePaid;
                }
                if (CodeService.ToUpper().Equals("PLAYN"))
                {
                    prepaidTv = service.SubServiceTypes[0].SubServices[0].PrePaid;
                }
            }
            return new Tuple<int, int>(prepaidNet, prepaidTv);
        }
        public static bool CheckAddVoucherGCCode(SqlConnection connection, SqlTransaction transaction, string voucher, int objId, string regcode)
        {
            var keep = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "CheckAddVoucher",
                objid = objId,
                OrderCode = regcode,
                voucherCode = voucher
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // >0 là đã có add trc đó
            return (keep > 0);
        }
        public static bool RedeemVoucherGC(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId, SalesManInfo Salesman,
            int localtype, string regCode, int channel, int locationId, Guid logId, UserBranchPlatformModel userBranch = null)
        {
            int generalCodeID = 0;
            bool res = false;
            try
            {
                PEDiscountModel pdm = new PEDiscountModel();
                pdm = GetPE(connection, transaction, voucherCode);
                WriteToLog(pdm, "AddVoucher RedeemVoucherGC pdm:", logId);
                if (pdm == null) return false;

                int IDPaidTimeType = GetInfoPaidTimeType(prepaid_net_tv.Item1, prepaid_net_tv.Item2);
                WriteToLog(IDPaidTimeType, " RedeemVoucherGC IDPaidTimeType ", logId);

                int status = 0;
                if (channel.Equals(0))
                {
                    if (Salesman == null) return false;
                    var parrams = new
                    {
                        #region
                        ActionName = "RedeemVoucherGeneralCode",
                        EventCode = voucherCode,
                        PromotionEventID = pdm.ID,
                        LocationID = Salesman.LOCATION,
                        DepartmentID = Salesman.DEPARTMENT,
                        SaleID = saleId,
                        ObjID = objid,
                        OrderCode = regCode,
                        ActiveChannel = 1,
                        BNET = 0,
                        BTV = 0,
                        IsPrepaidTV = 0,
                        LocalTypeID = localtype,
                        PaidTimeTypeID = IDPaidTimeType,
                        RowAffected = 0,
                        #endregion
                    };
                    status = AddGCCode(connection, transaction, parrams, VoucherBussinessConstant.OS6_FPTVoucher_GeneralVoucher);
                }
                if (channel.Equals(1))
                {
                    var dataXElement = new
                    {
                        Code = voucherCode,
                        PromotionEventID = pdm.ID,
                        OrderCode = regCode,
                        Source = userBranch.sourceId,
                        SubCompany = userBranch.subcompanyId,
                        LocationID = locationId,
                        BranchCode = userBranch.branchCode,
                        DepartmentID = userBranch.departmentId,
                        SaleID = saleId,
                        ObjID = objid,
                        LocalTypeID = localtype,
                        PaidTimeTypeID = IDPaidTimeType
                    };


                    var parrams = new
                    {
                        ActionName = "InsertGCeRegisterWeb",
                        XML = CreateElementGCCode(dataXElement)
                    };
                    status = AddGCCode(connection, transaction, parrams, VoucherBussinessConstant.OS6_FPTVoucher_SPF_RegisterWeb);
                }
                /*
                int status = connection.QueryFirstOrDefault<int>(VoucherBussinessConstant.OS6_FPTVoucher_GeneralVoucher, new
                {
                    ActionName = "RedeemVoucherGeneralCode",
                    EventCode = voucherCode,
                    PromotionEventID = pdm.ID,
                    LocationID = Salesman.LOCATION,
                    DepartmentID = Salesman.DEPARTMENT,
                    SaleID = saleId,
                    ObjID = objid,
                    OrderCode = regCode,
                    ActiveChannel = 1,
                    BNET = 0,
                    BTV = 0,
                    IsPrepaidTV = 0,
                    LocalTypeID = localtype,
                    PaidTimeTypeID = IDPaidTimeType,
                    RowAffected = 0,
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                */
                WriteToLog(status, "AddVoucher RedeemVoucherGC status ", logId);
                if (status == 0)
                    return false;
                if (status > 0)
                {
                    generalCodeID = status;
                    res = true;
                    #region voucher thuong
                    if (pdm.NetPromotionID > 0)
                    {
                        int updVc = UpdateDiscountNet(
                            objid,  // objID khách hàng
                            pdm.NetPromotionID, //XđồngY tháng NET + Tháng
                            pdm.IPTVPromotionID, //XđồngY tháng TV + Tháng
                            pdm.MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                            pdm.MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                            pdm.EventCode, // Code
                            saleId //Sales -TypeVC:True
                        );
                        WriteToLog(updVc, "AddVoucher RedeemVoucherGC updVc ", logId);
                        if (pdm.MoneyPromotionTVID > 0 || pdm.IPTVPromotionID > 0)
                        {
                            In4VoucherTHmodel voucherTh = new In4VoucherTHmodel();
                            if (pdm.MoneyPromotionTVID > 0)
                            {
                                voucherTh = GetIn4VoucherTH(objid, pdm.MoneyPromotionTVID);
                                WriteToLog(voucherTh, "AddVoucher RedeemVoucherGC voucherTh ", logId);
                            }
                            int updateTH = UpdateDiscountTH(voucherCode, objid, voucherTh.locationId, 0, pdm.IPTVPromotionID, voucherTh.amountTv, voucherTh.addBy, logId);
                            WriteToLog(updateTH, "AddVoucher RedeemVoucherGC updateTH ", logId);
                        }

                    }
                    else
                    {
                        // tang thang
                        int isEvenMonth = 0;
                        if (pdm.ContainerID > 0)
                        {
                            // lay clkm nen
                            var monthPromotion = GetMonthPromotion(objid);
                            if (monthPromotion != null)
                            {
                                // thang chan le
                                isEvenMonth = monthPromotion.EventMonth;
                                // lay promotion net theo thang
                                int promotionNetID = GetPromotionNetByContainerID(pdm.ContainerID, monthPromotion.GetAddMonth(), isEvenMonth);
                                if (promotionNetID > 0)
                                    pdm.PromotionNetID = promotionNetID;
                            }
                        }

                        var datalog = new
                        {
                            ObjID = objid,
                            PromotionNetID = pdm.PromotionNetID,
                            IPTVPromotionID = pdm.IPTVPromotionID,
                            MoneyPromotionNETID = pdm.MoneyPromotionNETID,
                            MoneyPromotionTVID = pdm.MoneyPromotionTVID,
                            EventCode = pdm.EventCode,
                            SaleID = saleId,
                            isEvenMonth = isEvenMonth,
                            generalCodeID = generalCodeID
                        };
                        WriteToLog(datalog, "AddVoucher RedeemVoucherGC - datalog ", logId);

                        int addNet = SPFAddCustomerDiscountV2(
                            objid,
                            pdm.PromotionNetID,
                            pdm.IPTVPromotionID,
                            pdm.MoneyPromotionNETID,
                            pdm.MoneyPromotionTVID,
                            pdm.EventCode,
                            saleId,
                            isEvenMonth,
                            generalCodeID
                        );
                        WriteToLog(addNet, "AddVoucher RedeemVoucherGC addNet ", logId);
                        if (pdm.MoneyPromotionTVID > 0 || pdm.IPTVPromotionID > 0)
                        {
                            In4VoucherTHmodel voucherTh = new In4VoucherTHmodel();
                            if (pdm.MoneyPromotionTVID > 0)
                            {
                                voucherTh = GetIn4VoucherTH(objid, pdm.MoneyPromotionTVID);
                                WriteToLog(voucherTh, "AddVoucher RedeemVoucherGC voucherTh ", logId);
                            }
                            int updateTH = UpdateDiscountTH(voucherCode, objid, voucherTh.locationId, 0, pdm.IPTVPromotionID, voucherTh.amountTv, voucherTh.addBy, logId);
                            WriteToLog(updateTH, "AddVoucher RedeemVoucherGC updateTH ", logId);
                        }
                    }
                    AddVoucherBill(objid, regCode, generalCodeID, IDPaidTimeType);
                    WriteToLog("added voucherbill done", "AddVoucher RedeemVoucherGC", logId);
                    InsertEVCAddClearLog(generalCodeID, voucherCode, saleId, Salesman.Name);
                    WriteToLog("Inserted EVCAddClearLog done", "AddVoucher RedeemVoucherGC Error", logId);
                    #endregion
                }

            }
            catch (Exception ex)
            {
                WriteToLog(ex.Message, " RedeemVoucherGC Error", logId);
                return false;
            }
            return res;
        }

        public static bool InsertEVCAddClearLog(int generalCodeID, string voucherCode, int saleID, string userName)
        {
            try
            {
                using(var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Query(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                    {
                        actionName = "InsertEVCAddClearLog",
                        generalCodeID = generalCodeID,
                        eventCode = voucherCode,
                        saleID = saleID,
                        user = userName
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure);
                }
                return true;
            }
            catch(Exception e)
            {
                return false;
            }
        }

        private static PEDiscountModel GetPE(SqlConnection connection, SqlTransaction transaction, string VoucherCode)
        {
            PEDiscountModel PE = connection.Query<PEDiscountModel>(VoucherBussinessConstant.OS6_FPTVoucher_MBSAPIVoucher, new
            {
                ActionName = "GetPEByCode",
                EventCode = VoucherCode,
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return PE;
        }
        private static int GetInfoPaidTimeType(int IsPerpaidNET, int IsPerpaidTV)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_MBSAPIVoucher, new
                {
                    ActionName = "CheckContaintPaidTimeType",
                    IsPrepaidTV = IsPerpaidTV,
                    IsPrePaidNET = IsPerpaidNET,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        private static int AddGCCode(SqlConnection connection, SqlTransaction transaction, object parram, string store)
        {
            return connection.QueryFirstOrDefault<int>(store, parram, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }
        public static bool CheckRedeemRFCode(SqlConnection connection, SqlTransaction transaction, string voucher, int objId, string orderCode)
        {
            var Redee = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "CheckRedeemRFCode",
                objid = objId,
                OrderCode = orderCode,
                voucherCode = voucher
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // >0 là active trc đó
            return (Redee > 0);
        }
        private static XElement CreateElementGCCode(dynamic data)
        {
            var xmlString = new XElement("N",
                            new XElement("I",
                            new XElement("Code", data.Code),
                            new XElement("PromotionEventID", data.PromotionEventID),
                            new XElement("OrderCode", data.OrderCode),
                            new XElement("Source", data.Source),
                            new XElement("SubCompany", data.SubCompany),
                            new XElement("LocationID", data.LocationID),
                            new XElement("BranchCode", data.BranchCode),
                            new XElement("DepartmentID", data.DepartmentID),
                            new XElement("SaleID", data.SaleID),
                            new XElement("ObjID", data.ObjID),
                            new XElement("ActiveChannel", 2),
                            new XElement("BNET", 0),
                            new XElement("FreeMonthID", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("LocalTypeID", data.LocalTypeID),
                            new XElement("PaidTimeTypeID", data.PaidTimeTypeID)));
            return xmlString;
        }
        public static void AddVoucherBill(int ObjID, string OrderCode, int? generalCodeID, int idprepaid)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@generalCodeID", generalCodeID);
                p.Add("@idprepaid", idprepaid);
                connection.Execute(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatForm_AddVoucherBill, p, commandType: CommandType.StoredProcedure);
            }
        }
        public static bool RedeemVoucherCampaign(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId, SalesManInfo Salesman, int channel, int localtype, string regCode, string contractSuport, List<DeviceModel> devices, Guid logId)
        {
            bool res = false;
            List<PEDiscountModel> rpDiscountUpdate = new List<PEDiscountModel>();
            try
            {
                var idPrepaidTime = GetPrepaidID(prepaid_net_tv.Item1, prepaid_net_tv.Item2);

                if (channel.Equals(1) && !string.IsNullOrEmpty(contractSuport)) // đăng ký hộ
                {
                    // NET và truyền hình
                    if (idPrepaidTime > 0)
                    {
                        return RedeemFriendSellNetTV(connection, transaction, contractSuport, objid, voucherCode, idPrepaidTime, prepaid_net_tv, regCode, logId);
                    }
                    // CMR
                    if (devices.Count > 0)
                    {
                        return AdvocacyCamera(connection, transaction, contractSuport, objid, voucherCode, orderCode, devices, regCode, logId);
                    }

                }
                int typeRF = checkReferalProgram(voucherCode, channel, logId);
                if (typeRF.Equals(1)) // type=1 là GTBB MyFPT
                {
                    return RedeemRFMyFPT(objid, orderCode, localtype, saleId, voucherCode, idPrepaidTime, regCode, logId.ToString());
                }
                if (typeRF.Equals(2)) // type=2 là GTBB FPT play
                {
                    return RedeemRFOTT(objid, orderCode, localtype, saleId, voucherCode, idPrepaidTime, regCode, logId.ToString());
                }
                if (channel.Equals(0))
                {
                    if (idPrepaidTime == 0) return false;
                }
                #region GTBB comment
                /*
                // get ObjOfUserInvite
                int objGT = GetOBJid(connection, transaction,voucherCode);
                if (objGT == 0) return false;
                //GetObjInvite(connection, transaction, ref input);

                // convert Rp to voucher code
                // query with index = 0 is discount for invited user
                // query with index = 1 is discount for invite user
                rpDiscountUpdate = GetPEREF(voucherCode, idPrepaidTime, BuildPackageType(prepaid_net_tv));
                WriteToLog(rpDiscountUpdate, "RedeemVoucherCampaign rpDiscountUpdate", logId);
                if (rpDiscountUpdate.Count != 3) return false;                                
                
                FoxGold EventCodeLoy = ReferalProgramHiFPT.GetEventCodeLoy(objid);
                var request = new List<RPCodeRedeemModel>{
                                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[1].ID, 
                                    VoucherCode = rpDiscountUpdate[1].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objid,
                                    ActiveChannel = 222,
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                },
                                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[2].ID, 
                                    VoucherCode = rpDiscountUpdate[2].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objGT,
                                    ActiveChannel = 200, 
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                }
                            };
                // add GC code cho người đăng ký
                var inforVoucherInvited = new List<RPCodeRedeemModel> { 
                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[1].ID, 
                                    VoucherCode = rpDiscountUpdate[1].EventCode, 
                                    OrderCode =regCode, 
                                    Salesman = saleId, 
                                    ObjID = objid,
                                    ActiveChannel = 222,
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                },
                };

                var inforVoucherInvite = new List<RPCodeRedeemModel> { 
                                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[2].ID, 
                                    VoucherCode = rpDiscountUpdate[2].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objGT,
                                    ActiveChannel = 200, 
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                }
                };
                // add success invite
                bool isAddSuccess = InsertInviteCodeSuccess(connection, transaction, request, EventCodeLoy.EventCode, voucherCode);
                WriteToLog(isAddSuccess, "RedeemVoucherCampaign isAddSuccess", logId);
                if (!isAddSuccess) return false;                    

                generalCodeID = RedeemRPCodeMBS(connection, transaction, inforVoucherInvited, voucherCode);
                WriteToLog(generalCodeID, "RedeemVoucherCampaign generalCodeID", logId);

                var addGCcodeInitve = RedeemRPCodeMBS(connection, transaction, inforVoucherInvite, voucherCode, EventCodeLoy);
                WriteToLog(addGCcodeInitve, "RedeemVoucherCampaign addGCcodeInitve", logId);

                if (generalCodeID <= 0) return false;
                else res = (generalCodeID > 0);
                
                // update status
                UpdateStatusRPCode(connection, transaction, voucherCode);

                
                // add voucher TemRedem cho objid đăng ký
                UpdateDiscount(
                    objid,  // objID user invited
                    rpDiscountUpdate[1].NetPromotionID, //XđồngY tháng NET + Tháng
                    rpDiscountUpdate[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    rpDiscountUpdate[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    rpDiscountUpdate[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    rpDiscountUpdate[1].EventCode, // Code
                    saleId, false //Sales -TypeVC:True.
                );


                // add voucher TemRedem cho objid Giới thiệu
                UpdateDiscount(
                    objGT,  // objID user invite
                    rpDiscountUpdate[2].NetPromotionID, //XđồngY tháng NET + Tháng
                    rpDiscountUpdate[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    rpDiscountUpdate[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    rpDiscountUpdate[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    rpDiscountUpdate[2].EventCode, // Code
                    saleId, false //Sales -TypeVC:True
                );

                AddVoucherBill(objid, regCode, generalCodeID, idPrepaidTime);
                 */
                #endregion
                // GTBB net và truyền hình
                if (idPrepaidTime > 0)
                {
                    Tuple<int, int> objidGT = GetObjIdbyInviteCode(connection, transaction, voucherCode);
                    if (objidGT == null) return false;
                    if (channel.Equals(1) && objidGT.Item2.Equals(219))
                    {
                        return RedeemInviteCodeOwnerCamera(connection, transaction, voucherCode, objid, orderCode, prepaid_net_tv, saleId, Salesman, idPrepaidTime, channel, localtype, regCode, objidGT.Item1, logId);
                    }
                    return RedeemInviteCodeNetTV(connection, transaction, voucherCode, objid, orderCode, prepaid_net_tv, saleId, Salesman, idPrepaidTime, channel, localtype, regCode, contractSuport, logId);
                }
                if (devices.Count > 0 && channel.Equals(1)) // chương trình GTBB cảmera chỉ áp dụng cho kênh DKOL
                {
                    return AdvocacyCamera(connection, transaction, contractSuport, objid, voucherCode, orderCode, devices, regCode, logId);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, " VoucherBussiness - AddVoucher");
                return false;
            }
            return res;
        }
        public static bool RedeemFriendSellNetTV(SqlConnection connection, SqlTransaction transaction, string contractGT, int objid, string voucherCode, int prepaidId, Tuple<int, int> prepaid_net_tv, string regcode, Guid logId)
        {

            try
            {
                #region Active NET hoac TV
                int objidSupport = GetObjIdSupport(connection, transaction, voucherCode, contractGT);
                if (objidSupport.Equals(0))
                {
                    WriteToLog(objidSupport, "RedeemFriendSellNetTV objidSupport", logId);
                    return false;
                }
                List<InfoRP> InviteInfo = new List<InfoRP>();
                InviteInfo = connection.Query<InfoRP>(VoucherBussinessConstant.OS6_FPTVoucher_FriendSellEvent, new
                {
                    code = voucherCode,
                    actionName = "GetPEREF",
                    PackageTypeInvited = BuildPackageType(prepaid_net_tv.Item1, prepaid_net_tv.Item2),
                    PaidTimeType = prepaidId
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                WriteToLog(InviteInfo, "RedeemFriendSellNetTV InviteInfo", logId);
                if (InviteInfo.Count() != 3)
                {
                    return false;
                }
                else if (InviteInfo.Count() == 3)
                {

                }

                // insert data to table InviteSuccess
                // [1] : người được giới thiệu
                // [2] : người giới thiệu
                FoxGold EventCodeLoy = GetEventCodeLoy(objid);
                WriteToLog(EventCodeLoy, "RedeemFriendSellNetTV EventCodeLoy", logId);
                List<ItemRequest> lstItem = new List<ItemRequest> { new ItemRequest { ObjecInvite = objidSupport, Objectinvited = objid, RegCode = regcode, VoucherCode = voucherCode } };
                int InsertInviteSuccess = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_FriendSellEvent, new
                {
                    ActionName = "InsertInfoRedeemRPCode",
                    XML = CreateXMLRedeem(lstItem, InviteInfo[2].EventCode, InviteInfo[1].EventCode, EventCodeLoy.EventCode)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                WriteToLog(EventCodeLoy, "RedeemFriendSellNetTV InsertInviteSuccess", logId);


                if (InsertInviteSuccess != lstItem.Count()) return false;

                UpdateStatusRPCode(connection, transaction, voucherCode);
                // insert data to table generalcode
                List<GeneralCodeInsert> stsAddGeneralCode = InsertGeneralCodeRPgetGCID(connection, transaction, lstItem, InviteInfo);
                WriteToLog(stsAddGeneralCode, "RedeemFriendSellNetTV stsAddGeneralCode", logId);
                // update Discount RpCode User Invited
                UpdateDiscount(
                        objid,  // objID khách hàng
                        InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                        InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                        InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                        InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                        InviteInfo[1].EventCode, // Code
                        objid, false); //Object Invite -TypeVC:True    

                // Update Discount RpCode User Invite
                UpdateDiscount(
                        objidSupport,  // objID khách hàng
                        InviteInfo[2].NetPromotionID, //XđồngY tháng NET + Tháng
                        InviteInfo[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                        InviteInfo[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                        InviteInfo[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                        InviteInfo[2].EventCode, // Code
                        objidSupport, false); //Object Invite -TypeVC:True    

                //L.Mes(Level.INFO, keylog + " " + "Call Voucher Bill");
                // add voucher bill invited
                AddVoucherBill(objid, regcode, stsAddGeneralCode[1].id, prepaidId);
                // add voucher bill invite
                AddVoucherBill(objidSupport, regcode, stsAddGeneralCode[2].id, prepaidId);

                //SenNotifyDKH(ContractGT, input[0].Objectinvited, "successful_service_registration", keylog);                

                #endregion
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public static void WriteToLog(object input, string mes, Guid logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(input), mes);
        }
        public static int GetObjIdSupport(SqlConnection connection, SqlTransaction transaction, string voucherCode, string contractGT)
        {
            return connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_FriendSellEvent, new
            {
                actionName = "GetObjContractSell",
                code = voucherCode,
                contractSell = contractGT
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static string BuildPackageType(int IsPrepaidNET, int IsPrepaidTV)
        {
            if (IsPrepaidNET == -1 && IsPrepaidTV != -1) return "TVONLY";
            else if (IsPrepaidNET != -1 && IsPrepaidTV != -1) return "COMBO";
            else if (IsPrepaidNET != -1 && IsPrepaidTV == -1) return "NETONLY";
            else return "";
        }
        public static FoxGold GetEventCodeLoy(int objInvited)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<FoxGold>("PowerInside.dbo.OS6_ReferralProgram_SucceedInvite", new
                {
                    ActionName = "GetEventLoyaltyInfo",
                    ObjIDInvited = objInvited
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        private static XElement CreateXMLRedeem(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.RegCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite)// object của người giới thiệu
                       ));
            return xmlString;
        }
        public static void UpdateStatusRPCode(SqlConnection connection, SqlTransaction transaction, string InviteCode)
        {
            connection.Query<int>(VoucherBussinessConstant.OS6_ReferralProgram_InviteCode, new
            {
                InviteCode = InviteCode,
                ActionName = "UpdateInvite",
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static List<GeneralCodeInsert> InsertGeneralCodeRPgetGCID(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo)
        {
            List<GeneralCodeInsert> lst = new List<GeneralCodeInsert>();
            for (int i = 0; i < InviteInfo.Count; i++)
            {
                GeneralCodeInsert gc = new GeneralCodeInsert();
                gc.eventCode = InviteInfo[i].EventCode;
                gc.id = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_GeneralCode, new
                {
                    ActionName = "InsertGeneralCodeXMLGCID",
                    XML = CreateXMLUpdateGeneralCodeRPbyGC(input, InviteInfo[i], i),
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                lst.Add(gc);
            }
            return lst;
        }
        public static XElement CreateXMLUpdateGeneralCodeRPbyGC(List<ItemRequest> input, InfoRP promotionEvent, int i)
        {
            if (i.Equals(0))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // Mã loy cho người giới thiệu status = 100
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));
                return xmlString;
            }
            if (i.Equals(1))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 111)));
                return xmlString;
            }
            if (i.Equals(2))
            {
                var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent.EventCode),  // nguoi giới thiệu
                                        new XElement("P", promotionEvent.ID),
                                        new XElement("Or", input[0].RegCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
                return xmlString;
            }
            return null;
        }
        public static bool AdvocacyCamera(SqlConnection connection, SqlTransaction transaction, string contractGT, int objid, string voucherCode, string orderCode, List<DeviceModel> devices, string regcode, Guid logId)
        {
            bool res = false;
            try
            {
                #region Hop dong co CAM
                //int objidSupport = FriendSellServices.GetObjIdSupport(connection, transaction, voucherCode, contractGT);
                Tuple<int, int> objidGT = GetObjIdbyInviteCode(connection, transaction, voucherCode);
                WriteToLog(objidGT, " AdvocacyCamera objidGT", logId);
                if (objidGT == null) return false;
                List<DeviceModel> cameras = connection.Query<DeviceModel>(VoucherBussinessConstant.OS6_FPTVoucher_AdvocacyProgram, new
                {
                    actionName = "GetHistoryTemInfoCam",
                    xml = CreateXMLDevice(voucherCode, orderCode, devices)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                WriteToLog(cameras, " AdvocacyCamera cameras", logId);
                if (cameras.Count > 2 || cameras.Count != devices.Count) return false;

                List<InfoRFCAM> listRFcam = new List<InfoRFCAM>();
                InfoRFCAM camGT = new InfoRFCAM();
                // lấy ưu đãi cho người GT, người đăng ký hộ, chủ hợp đồng camera
                if (objidGT.Item2.Equals(219))
                {//chủ hợp đồng camera
                    var parrams = new
                    {
                        ActionName = "GetPECAM",
                        InviteCode = voucherCode,
                        PackageTypeInvited = "COMBO"
                    };
                    camGT = GetEventCamGT(connection, transaction, "PowerInside.dbo.OS6_ReferralProgram_Camera", parrams);
                }
                else
                {// lấy ưu đãi cho người GT, người đăng ký hộ
                    var parrams = new
                    {
                        ActionName = "GetPromotionEvent",
                        InviteCode = voucherCode,
                        quantity = 0,
                        serviceCode = 0
                    };
                    camGT = GetEventCamGT(connection, transaction, VoucherBussinessConstant.OS6_ReferalProgram_CAM, parrams);
                }

                WriteToLog(camGT, " AdvocacyCamera camGT", logId);

                int insertTemRedeem = connection.Execute(VoucherBussinessConstant.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "AddTemRedeemInvite",
                    vdan = camGT.ID1,
                    objid = objidGT,
                    voucherCode = camGT.EventCode,
                    AddBy = objid
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                WriteToLog(insertTemRedeem, " AdvocacyCamera insertTemRedeem", logId);

                if (camGT == null || camGT.QuotaGeneralCode < 1) return false;
                listRFcam.Add(new InfoRFCAM { EventCode = camGT.EventCode, ID = camGT.ID, InviteType = camGT.InviteType, QuotaGeneralCode = camGT.QuotaGeneralCode });
                EventCamera ec = new EventCamera();
                foreach (var camera in cameras)
                {
                    var camDK = connection.Query<InfoRFCAM>(VoucherBussinessConstant.OS6_ReferalProgram_CAM, new
                    {
                        ActionName = "GetPromotionEvent",
                        InviteCode = voucherCode,
                        quantity = camera.qty,
                        serviceCode = camera.serviceCode,
                        EfId = camera.deviceId
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 5);
                    if (camera.serviceCode == 322) ec.EventCodeCamIn = camDK.EventCode;
                    if (camera.serviceCode == 323 || camera.serviceCode == 443) ec.EventCodeCamOut = camDK.EventCode;
                    listRFcam.Add(new InfoRFCAM { EventCode = camDK.EventCode, ID = camDK.ID, InviteType = camDK.InviteType, QuotaGeneralCode = camDK.QuotaGeneralCode });
                }
                WriteToLog(listRFcam, " AdvocacyCamera listRFcam", logId);

                if (ec == null) return false;
                List<ItemRequest> lstItem = new List<ItemRequest> { new ItemRequest { ObjecInvite = objidGT.Item1, Objectinvited = objid, RegCode = regcode, VoucherCode = voucherCode } };
                int InsertInviteSuccess = connection.Query<int>(VoucherBussinessConstant.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "InsertInfoRedeemRPCodeCAM",
                    XML = CreateXMLRedeemCAM(lstItem, ec, camGT.EventCode, contractGT, "")
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                WriteToLog(InsertInviteSuccess, " AdvocacyCamera InsertInviteSuccess", logId);

                if (InsertInviteSuccess != 1) return false;

                UpdateStatusRPCode(connection, transaction, voucherCode);
                Boolean stsAddGeneralCode = InsertGeneralCodeRPCAM(connection, transaction, lstItem, listRFcam);

                if (!stsAddGeneralCode) return false;
                res = true;
                #endregion
            }
            catch (Exception ex)
            {
                WriteToLog(ex.Message, " AdvocacyCamera Error", logId);
                res = false;
            }
            return res;
        }
        public static bool RedeemRFMyFPT(int objid, string orderCode, int localtype, int saleid, string vouchercode, int paidTimeTypeID, string regCode, string keylog)
        {
            Guid Logid = Guid.Parse(keylog);
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        FoxproUseInviteCode insertInviteCode = connection.Query<FoxproUseInviteCode>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                        {
                            actionName = "InsertInviteCode",
                            objID = objid,
                            orderCode = regCode,
                            //bnet = 0,
                            //btv = 0,
                            inviteCode = vouchercode,
                            PaidTimeTypeID = paidTimeTypeID
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                        if (insertInviteCode.res == 0)
                        {
                            WriteToLog("", "RedeemRFMyFPT Dịch vụ không phải NETONLY hoặc COMBO", Logid);
                            return false;
                        }
                        else
                        {
                            int objID = insertInviteCode.ObjID;
                            int MNET = insertInviteCode.MoneyPromotionNETID;
                            int MTV = insertInviteCode.MoneyPromotionTVID;
                            int StaffID = Convert.ToInt32(insertInviteCode.StaffIDInvite);
                            UpdateDiscount(
                                 objID,  // objID khách hàng
                                 0, //XđồngY tháng NET + Tháng
                                 0, //XđồngY tháng TV + Tháng
                                 MNET, //Giảm Tiền Trực Tiếp NET
                                 MTV, //Giảm Tiền Trực Tiếp TV
                                 insertInviteCode.EventCode, // Code
                                 StaffID, false);

                            AddVoucherBill(objid, regCode, insertInviteCode.generalCodeID, paidTimeTypeID);

                            transaction.Commit();

                            bool statusRedeem = SendNotificationReferralMyFPT(insertInviteCode, keylog);
                            L.Mes(Level.INFO, statusRedeem.ToString(), string.Concat("SendNotificationReferralMyFPT ", keylog));

                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), string.Concat("RedeemRFMyFPT ", keylog));
                return false;
            }
        }
        private static Boolean SendNotificationReferralMyFPT(dynamic input, string keylog)
        {
            int iStatusCode = 0;
            LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
            AuthorizationInfor aut = getAuthorInfor(keylog);
            try
            {
                if (aut == null)
                    return false;
                var request = new ModelSendNotificationReferralMyFPT()
                {
                    employeeCode = input.StaffIDInvite,
                    Referrer = input.RealMoneyAmount,
                    BookingId = input.Contract,
                    contract_owner = input.FullName,
                    BookingStatus = "pending"
                };

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    //client.DefaultRequestHeaders.Add("Authorization", aut.authorization);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/api/oauth-ms/public/auth/integration-supplier", data).Result;
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(response), string.Concat("SendNotificationReferralMyFPT ", keylog));
                    iStatusCode = (int)response.StatusCode;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, string.Concat("SendNotificationReferralMyFPT ", keylog));
                return false;
            }
            //dynamic response = new dynamic();

            if (iStatusCode == 200)
            {
                return true;
            }

            return false;
        }
        private static AuthorizationInfor getAuthorInfor(string keylog)
        {
            L.Mes(Level.INFO, "Start to get token myfpt", string.Concat("getAuthorInfor ", keylog));
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                var uri = WebAPIHelper.myFpt_fpt_vn + "/api/oauth-ms/public/auth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("client_id", Utility.my_fpt_api_client_id));
                keyValues.Add(new KeyValuePair<string, string>("client_secret", Utility.my_fpt_api_client_secret));
                keyValues.Add(new KeyValuePair<string, string>("username", Utility.my_fpt_api_username));
                keyValues.Add(new KeyValuePair<string, string>("password", Utility.my_fpt_api_password));
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "password"));

                var content = new FormUrlEncodedContent(keyValues);

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        response.EnsureSuccessStatusCode();
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, string.Concat("getAuthorInfor ", keylog));
                        res = JsonConvert.DeserializeObject<AuthorizationInfor>(r);
                    }
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, string.Concat("getAuthorInfor ", keylog));
                return null;
            }
            return res;
        }
        public static Tuple<int, int> GetObjIdbyInviteCode(SqlConnection connection, SqlTransaction transaction, string voucherCode)
        {
            return connection.Query<Tuple<int, int>>(VoucherBussinessConstant.OS6_FPTVoucher_AdvocacyProgram, new
            {
                actionName = "GetObjContractSell",
                voucherCode = voucherCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static XElement CreateXMLDevice(string voucherCode, string orderCode, List<DeviceModel> devices)
        {
            var xmlString = new XElement("N",
            from device in devices
            select new XElement("I",
                           new XElement("voucherCode", voucherCode),
                           new XElement("orderCode", orderCode),
                           new XElement("serviceId", device.serviceId),
                           new XElement("subServiceTypeID", device.subServiceTypeID),
                           new XElement("subServiceId", device.deviceId),
                           new XElement("serviceCode", device.serviceCode),
                           new XElement("qty", device.qty)
                       ));
            return xmlString;
        }
        public static InfoRFCAM GetEventCamGT(SqlConnection connection, SqlTransaction transaction, string storeProducer, object parrams)
        {
            return connection.Query<InfoRFCAM>(storeProducer, parrams, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 1);
        }
        public static XElement CreateXMLRedeemCAM(List<ItemRequest> input, EventCamera ec, string GeneralCodeInvite = "", string contractGT = "", string eventCodeLoy = "")
        {
            string typeRF = "HiFPT";
            if (!string.IsNullOrEmpty(contractGT)) typeRF = "DKH";
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited-in", ec.EventCodeCamIn),
                           new XElement("Vinvited-out", ec.EventCodeCamOut),
                           new XElement("Or", item.RegCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite),// object của người giới thiệu
                           new XElement("RF", typeRF)
                       ));
            return xmlString;
        }
        public static Boolean InsertGeneralCodeRPCAM(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRFCAM> camrf)
        {
            // camrf[0] : người đăng ký
            // camrf[1] : người giới thiệu
            int InsertGeneralCode = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_GeneralCode, new
            {
                ActionName = "InsertGeneralCodeXML",
                XML = CreateXMLUpdateGeneralCodeRPCAM(input, camrf),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // check row Affected
            return (InsertGeneralCode <= 3);
        }
        public static XElement CreateXMLUpdateGeneralCodeRPCAM(List<ItemRequest> input, List<InfoRFCAM> camrf)
        {
            int voucherRF = camrf.Count() - 1;
            var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", camrf[0].EventCode),  // nguoi giới thiệu
                                        new XElement("P", camrf[0].ID),
                                        new XElement("Or", input[0].RegCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
            for (int i = 1; i <= voucherRF; i++)
            {
                xmlString.Add(new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", camrf[i].EventCode),
                            new XElement("P", camrf[i].ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 111)));
            }

            return xmlString;
        }
        public static bool RedeemRFOTT(int objid, string orderCode, int localtype, int saleid, string vouchercode, int paidTimeTypeID, string regCode, string keylog)
        {
            Guid Logid = Guid.Parse(keylog);
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        List<InfoRP> InviteInfo = new List<InfoRP>();
                        InviteInfo = connection.Query<InfoRP>(VoucherBussinessConstant.OS6_FPTVoucher_OTTReferralProgram, new
                        {
                            //voucherCode = input[0].VoucherCode,
                            actionName = "GetPromotionEventOTT",
                            PaidTimeType = paidTimeTypeID
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        if (InviteInfo.Count() == 0)
                        {
                            return false;
                        }
                        int? generalCodeID = InsertGeneralCodeOTT(connection, transaction, regCode, objid, InviteInfo, paidTimeTypeID);
                        bool isSuccess = InsertSuccessCodeOTT(connection, transaction, vouchercode, InviteInfo[0].EventCode, objid);

                        if (generalCodeID == null || generalCodeID == 0) return false;

                        if (generalCodeID > 0)
                        {
                            var PEInfo = InviteInfo[0];
                            UpdateDiscount(
                            objid,  // objID khách hàng
                            PEInfo.NetPromotionID, //XđồngY tháng NET + Tháng
                            PEInfo.IPTVPromotionID, //XđồngY tháng TV + Tháng
                            PEInfo.MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                            PEInfo.MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                            PEInfo.EventCode, // Code
                            0, false); //Object Invite -TypeVC:True  

                            L.Mes(Level.INFO, "Call Voucher Bill");
                            // add voucher bill invited
                            AddVoucherBill(objid, regCode, generalCodeID, paidTimeTypeID);
                            L.Mes(Level.INFO, "RedeemReferalOTTCode");
                            return true;
                        }
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), string.Concat("RedeemRFMyFPT ", keylog));
                return false;
            }
        }
        public static int? InsertGeneralCodeOTT(SqlConnection connection, SqlTransaction transaction, string regCode, int objInvited, List<InfoRP> InviteInfo, int paidTimeTypeID)
        {
            try
            {
                var GeneralCodeIDs = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_OTTReferralProgram, new
                {
                    actionName = "InsertGeneralCodeXMLV2",
                    XML = CreateXMLUpdateGeneralCodeOTT(regCode, objInvited, InviteInfo, paidTimeTypeID)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                return GeneralCodeIDs.FirstOrDefault();
            }
            catch (Exception ex)
            {
                return 0;
            }
        }
        private static XElement CreateXMLUpdateGeneralCodeOTT(string regCode, int objInvited, List<InfoRP> promotionEvent, int paidTimeTypeID)
        {

            var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent[0].EventCode),  // nguoi được giới thiệu
                                        new XElement("P", promotionEvent[0].ID),
                                        new XElement("Or", regCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", objInvited),
                                        new XElement("Ac", 111),
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));

            return xmlString;
        }
        private static bool InsertSuccessCodeOTT(SqlConnection connection, SqlTransaction transaction, string invitecode, string voucher, int objidinvited)
        {
            int RowAffected = connection.Execute(VoucherBussinessConstant.OS6_FPTVoucher_OTTReferralProgram, new
            {
                actionName = "InsertSuccessOTTRF",
                InviteCode = invitecode,
                ObjID = objidinvited,
                Code = voucher
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

            // check records update to tbale History_temp
            return (RowAffected > 0);
        }
        public static bool RedeemInviteCodeOwnerCamera(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId, SalesManInfo Salesman, int idPrepaidTime, int channel, int localtype, string regCode, int objidGT, Guid logId)
        {
            try
            {
                WriteToLog(regCode, " RedeemInviteCodeOwnerCamera for reg code ", logId);


                WriteToLog(objidGT, " RedeemInviteCodeOwnerCamera objidGT ", logId);

                List<ItemRequest> lstItem = new List<ItemRequest> { new ItemRequest { ObjecInvite = objidGT, Objectinvited = objid, RegCode = regCode, VoucherCode = voucherCode } };
                WriteToLog(lstItem, " RedeemInviteCodeOwnerCamera lstItem ", logId);

                List<InfoRP> InviteInfo = new List<InfoRP>();

                InviteInfo = connection.Query<InfoRP>(VoucherBussinessConstant.OS6_ReferralProgram_Camera, new
                {
                    ActionName = "GetPEREF",
                    PackageTypeInvited = BuildPackageType(prepaid_net_tv),
                    PaidTimeType = idPrepaidTime
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                WriteToLog(InviteInfo, " RedeemInviteCodeOwnerCamera InviteInfo ", logId);
                if (InviteInfo.Count() != 3)
                {
                    return false;
                }

                int InsertInviteSuccess = connection.Query<int>(VoucherBussinessConstant.OS6_ReferralProgram_Camera, new
                {
                    ActionName = "InsertInfoRedeemRPCode",
                    XML = CreateXMLRedeemOwnerCamera(lstItem, InviteInfo[2].EventCode, InviteInfo[1].EventCode, "")
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                WriteToLog(InsertInviteSuccess, " RedeemInviteCodeOwnerCamera InsertInviteSuccess ", logId);

                if (InsertInviteSuccess != lstItem.Count()) return false;
                UpdateStatusRPCode(connection, transaction, voucherCode);

                List<GeneralCodeInsert> stsAddGeneralCode = InsertGeneralCodeRPgetGCID(connection, transaction, lstItem, InviteInfo, prepaid_net_tv);
                WriteToLog(stsAddGeneralCode, " RedeemInviteCodeOwnerCamera stsAddGeneralCode ", logId);

                if (stsAddGeneralCode.Count() != 2) return false;

                // update Discount RpCode User Invited
                UpdateDiscount(
                     lstItem[0].Objectinvited,  // objID khách hàng
                     InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                     InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                     InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                     InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                     InviteInfo[1].EventCode, // Code
                     lstItem[0].Objectinvited, false); //Object Invite -TypeVC:True   

                AddVoucherBill(objid, regCode, stsAddGeneralCode[1].id, idPrepaidTime);
                return true;
            }
            catch (Exception ex)
            {
                WriteToLog(ex.Message, " RedeemInviteCodeOwnerCamera Error ", logId);
                return false;
            }
        }
        public static string BuildPackageType(Tuple<int, int> input)
        {
            if (input.Item1 == -1 && input.Item2 > -1) return "TVONLY";
            else if (input.Item1 > -1 && input.Item2 > -1) return "COMBO";
            else if (input.Item1 > -1 && input.Item2 == -1) return "NETONLY";
            else return "";
        }
        public static XElement CreateXMLRedeemOwnerCamera(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.RegCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite),// object của người giới thiệu
                           new XElement("TypeRF", "CAMERA")
                       ));
            return xmlString;
        }
        public static List<GeneralCodeInsert> InsertGeneralCodeRPgetGCID(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int> prepaid_net_tv)
        {
            List<GeneralCodeInsert> lst = new List<GeneralCodeInsert>();
            for (int i = 1; i < InviteInfo.Count; i++)
            {
                GeneralCodeInsert gc = new GeneralCodeInsert();
                gc.eventCode = InviteInfo[i].EventCode;
                gc.id = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_GeneralCode, new
                {
                    ActionName = "InsertGeneralCodeXMLGCID",
                    XML = CreateXMLUpdateGeneralCodeRPbyGC(input, InviteInfo[i], prepaid_net_tv, i),
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                lst.Add(gc);
            }
            return lst;
        }
        public static XElement CreateXMLUpdateGeneralCodeRPbyGC(List<ItemRequest> input, InfoRP promotionEvent, Tuple<int, int> prepaid_net_tv, int i)
        {
            if (i.Equals(0))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // Mã loy cho người giới thiệu status = 100
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));
                return xmlString;
            }
            if (i.Equals(1))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", prepaid_net_tv.Item2),
                            new XElement("Ac", 111)));
                return xmlString;
            }
            if (i.Equals(2))
            {
                var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent.EventCode),  // nguoi giới thiệu
                                        new XElement("P", promotionEvent.ID),
                                        new XElement("Or", input[0].RegCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
                return xmlString;
            }
            return null;
        }
        public static bool RedeemInviteCodeNetTV(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId, SalesManInfo Salesman, int idPrepaidTime, int channel, int localtype, string regCode, string contractSuport, Guid logId)
        {
            int generalCodeID = 0;
            bool res = false;
            List<PEDiscountModel> rpDiscountUpdate = new List<PEDiscountModel>();
            try
            {
                // get ObjOfUserInvite
                int objGT = GetOBJid(connection, transaction, voucherCode);
                if (objGT == 0) return false;
                //GetObjInvite(connection, transaction, ref input);

                // convert Rp to voucher code
                // query with index = 0 is discount for invited user
                // query with index = 1 is discount for invite user
                rpDiscountUpdate = GetPEREF(voucherCode, idPrepaidTime, BuildPackageType(prepaid_net_tv));
                WriteToLog(rpDiscountUpdate, "RedeemVoucherCampaign rpDiscountUpdate", logId);
                if (rpDiscountUpdate.Count != 3) return false;

                FoxGold EventCodeLoy = GetEventCodeLoy(objid);
                var request = new List<RPCodeRedeemModel>{
                                new RPCodeRedeemModel
                                {
                                    Location = Salesman.LOCATION,
                                    BranchCode = Salesman.BRANCHCODE,
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[1].ID,
                                    VoucherCode = rpDiscountUpdate[1].EventCode,
                                    OrderCode = regCode,
                                    Salesman = saleId,
                                    ObjID = objid,
                                    ActiveChannel = 222,
                                    BNET = 0,
                                    BTV = 0,
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                },
                                new RPCodeRedeemModel
                                {
                                    Location = Salesman.LOCATION,
                                    BranchCode = Salesman.BRANCHCODE,
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[2].ID,
                                    VoucherCode = rpDiscountUpdate[2].EventCode,
                                    OrderCode = regCode,
                                    Salesman = saleId,
                                    ObjID = objGT,
                                    ActiveChannel = 200,
                                    BNET = 0,
                                    BTV = 0,
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                }
                            };
                // add GC code cho người đăng ký
                var inforVoucherInvited = new List<RPCodeRedeemModel> {
                new RPCodeRedeemModel
                                {
                                    Location = Salesman.LOCATION,
                                    BranchCode = Salesman.BRANCHCODE,
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[1].ID,
                                    VoucherCode = rpDiscountUpdate[1].EventCode,
                                    OrderCode =regCode,
                                    Salesman = saleId,
                                    ObjID = objid,
                                    ActiveChannel = 222,
                                    BNET = 0,
                                    BTV = 0,
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                },
                };

                var inforVoucherInvite = new List<RPCodeRedeemModel> {
                                new RPCodeRedeemModel
                                {
                                    Location = Salesman.LOCATION,
                                    BranchCode = Salesman.BRANCHCODE,
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[2].ID,
                                    VoucherCode = rpDiscountUpdate[2].EventCode,
                                    OrderCode = regCode,
                                    Salesman = saleId,
                                    ObjID = objGT,
                                    ActiveChannel = 200,
                                    BNET = 0,
                                    BTV = 0,
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                }
                };
                // add success invite
                bool isAddSuccess = InsertInviteCodeSuccess(connection, transaction, request, EventCodeLoy.EventCode, voucherCode);
                WriteToLog(isAddSuccess, "RedeemVoucherCampaign isAddSuccess", logId);
                if (!isAddSuccess) return false;

                // add GC code cho người đăng ký
                generalCodeID = RedeemRPCodeMBS(connection, transaction, inforVoucherInvited, voucherCode);
                WriteToLog(generalCodeID, "RedeemVoucherCampaign generalCodeID", logId);
                // add GC code cho người giới thiệu
                var addGCcodeInitve = RedeemRPCodeMBS(connection, transaction, inforVoucherInvite, voucherCode, EventCodeLoy);
                WriteToLog(addGCcodeInitve, "RedeemVoucherCampaign addGCcodeInitve", logId);

                if (generalCodeID <= 0) return false;
                else res = (generalCodeID > 0);

                // update status
                UpdateStatusRPCode(connection, transaction, voucherCode);


                // add voucher TemRedem cho objid đăng ký
                UpdateDiscount(
                    objid,  // objID user invited
                    rpDiscountUpdate[1].NetPromotionID, //XđồngY tháng NET + Tháng
                    rpDiscountUpdate[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    rpDiscountUpdate[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    rpDiscountUpdate[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    rpDiscountUpdate[1].EventCode, // Code
                    saleId, false //Sales -TypeVC:True.
                );


                // add voucher TemRedem cho objid Giới thiệu
                UpdateDiscount(
                    objGT,  // objID user invite
                    rpDiscountUpdate[2].NetPromotionID, //XđồngY tháng NET + Tháng
                    rpDiscountUpdate[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    rpDiscountUpdate[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    rpDiscountUpdate[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    rpDiscountUpdate[2].EventCode, // Code
                    saleId, false //Sales -TypeVC:True
                );

                AddVoucherBill(objid, regCode, generalCodeID, idPrepaidTime);
                return true;
            }
            catch (Exception ex)
            {
                WriteToLog(ex.Message, " RedeemVoucherCampaign Error", logId);
                return false;
            }
            return res;
        }
        public static int GetOBJid(SqlConnection connection, SqlTransaction transaction, string InviteCode)
        {
            return connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                voucherCode = InviteCode,
                actionName = "GetOBJid",
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static List<PEDiscountModel> GetPEREF(string VoucherRP, int PaidTimeTypePE, string PackageTypeInvited)
        {
            List<PEDiscountModel> res = new List<PEDiscountModel>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var parameters = new DynamicParameters();
                parameters.Add("@ActionName", "GetPEREF");
                parameters.Add("@InviteCode", VoucherRP);
                parameters.Add("@PackageTypeInvited", PackageTypeInvited);
                parameters.Add("@RowAffected", 0);
                parameters.Add("@PaidTimeType", PaidTimeTypePE);
                res = connection.Query<PEDiscountModel>(VoucherBussinessConstant.OS6_ReferralProgram_InviteCode, parameters, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
            }
            return res;
        }
        public static bool InsertInviteCodeSuccess(SqlConnection connection, SqlTransaction transaction, List<RPCodeRedeemModel> input, string eventLoyalty, string voucherRP)
        {
            int InsertInviteSuccess = connection.Query<int>(VoucherBussinessConstant.OS6_ReferralProgram_SucceedInvite, new
            {
                ActionName = "InsertInfoRedeemRPCode",
                XML = CreateXMLRedeemIV(input, eventLoyalty, voucherRP),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StatusUpdateInviteSuccess = (InsertInviteSuccess == 1) }));

            return InsertInviteSuccess == 1;
        }
        private static XElement CreateXMLRedeemIV(List<RPCodeRedeemModel> input, string eventLoyalty, string voucherRP)
        {
            var xmlString = new XElement("N",
                new XElement("I",
                    new XElement("RPinvited", voucherRP),  // nguoi giới thiệu
                    new XElement("RPLoyalty", eventLoyalty), // Mã ưu đãi add điểm của Loyalty
                    new XElement("Vinvite", input[1].VoucherCode),
                    new XElement("Vinvited", input[0].VoucherCode),
                    new XElement("Or", input[0].OrderCode),
                    new XElement("Oinvited", input[0].ObjID),
                    new XElement("Oinvite", input[1].ObjID),
                    new XElement("Oinvite", input[1].ObjID)
                )
            );
            return xmlString;
        }
        public static int RedeemRPCodeMBS(SqlConnection connection, SqlTransaction transaction, List<RPCodeRedeemModel> input, string voucherRP, FoxGold eventLoyalty = null)
        {
            XElement xml = null;
            string store = string.Empty;
            if (eventLoyalty == null)
            {
                xml = CreateXMLRedeemRPcode(input);
                store = VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4;

            }
            else
            {
                xml = CreateXMLRedeemRPcode(input, eventLoyalty);
                store = VoucherBussinessConstant.OS6_FPTVoucher_MBSAPIVoucher;
            }
            int idGC = connection.Query<int>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "InsertGeneralCodeXML",
                xml = xml,
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            return idGC;
        }
        private static XElement CreateXMLRedeemRPcode(List<RPCodeRedeemModel> input, FoxGold eventLoy = null)
        {
            int PrepaidTimeTV;
            if (input[0].IsPrepaidTV > 0)
                PrepaidTimeTV = 1;
            else if (input[0].IsPrepaidTV < 0)
                PrepaidTimeTV = -1;
            else
                PrepaidTimeTV = 0;

            var xmlString = new XElement("N",
                new XElement("I",
                    new XElement("L", input[0].Location),
                    new XElement("B", input[0].BranchCode),
                    new XElement("D", input[0].Department),
                    new XElement("S", input[0].Salesman),

                    new XElement("C", input[0].VoucherCode),
                    new XElement("P", input[0].PromotionEventID),
                    new XElement("Or", input[0].OrderCode),

                    new XElement("Obj", input[0].ObjID),

                    new XElement("BNET", input[0].BNET),
                    new XElement("BTV", input[0].BTV),
                    new XElement("IsPrepaidTV", PrepaidTimeTV),

                    new XElement("Ac", input[0].ActiveChannel),

                    new XElement("Su", input[0].SubCompanyID),
                    new XElement("Lo", input[0].LocalTypeID),
                    new XElement("Pa", input[0].PaidTimeTypeID)
                )
            );
            /*
            xmlString.Add(
                new XElement("I",
                    new XElement("L", input[1].Location),
                    new XElement("B", input[1].BranchCode),
                    new XElement("D", input[1].Department),
                    new XElement("S", input[1].Salesman),

                    new XElement("C", input[1].VoucherCode),
                    new XElement("P", input[1].PromotionEventID),
                    new XElement("Or", input[1].OrderCode),

                    new XElement("BNET", input[1].BNET),
                    new XElement("BTV", input[1].BTV),
                    new XElement("IsPrepaidTV", input[1].IsPrepaidTV),

                    new XElement("Obj", input[1].ObjID),
                    new XElement("Ac", input[1].ActiveChannel),

                    new XElement("Su", input[1].SubCompanyID),
                    new XElement("Lo", input[1].LocalTypeID),
                    new XElement("Pa", input[1].PaidTimeTypeID)
                )
            );*/

            if (eventLoy != null)
            {
                xmlString.Add(
                    new XElement("I",
                        new XElement("L", input[0].Location),
                        new XElement("B", input[0].BranchCode),
                        new XElement("D", input[0].Department),
                        new XElement("S", input[0].Salesman),

                        new XElement("C", eventLoy.EventCode),
                        new XElement("P", eventLoy.ID),
                        new XElement("Or", input[0].OrderCode),

                        new XElement("BNET", input[0].BNET),
                        new XElement("BTV", input[0].BTV),
                        new XElement("IsPrepaidTV", input[0].IsPrepaidTV),

                        new XElement("Obj", input[0].ObjID),
                        new XElement("Ac", input[0].ActiveChannel),

                        new XElement("Su", input[0].SubCompanyID),
                        new XElement("Lo", input[0].LocalTypeID),
                        new XElement("Pa", input[0].PaidTimeTypeID)
                    )
                );
            }

            return xmlString;
        }
        public static int SumVoucherInRegcode(string regCode, int objId)
        {
            int sum = 0;
            using(var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                sum = connection.QueryFirstOrDefault<int>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                {
                    actionName = "SumVoucherInRegcode",
                    regCode = regCode,
                    objID = objId
                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
            return sum;
        }

        #region tách link server
        public static int UpdateDiscountNet(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, PNET, PTV, MNET, MTV, Voucher, SalesManID }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_SPF_AddCustomerDiscount",
                    new
                    {
                        actionName = "AddVoucherNet",
                        ObjID = ObjID,
                        PromotionIDNet = PNET,
                        PromotionIDTV = PTV,
                        MoneyPromotionIDNet = MNET,
                        MoneyPromotionIDTV = MTV,
                        VoucherCode = Voucher,
                        AddBy = SalesManID
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        public static In4VoucherTHmodel GetIn4VoucherTH(int ObjID, int MTV)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, MTV, }));
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<In4VoucherTHmodel>(
                    "PowerInside.dbo.OS6_FPTVoucher_SPF_AddCustomerDiscount",
                    new
                    {
                        actionName = "GetIn4VoucherTH",
                        ObjID = ObjID,
                        MoneyPromotionIDTV = MTV
                    },
                    commandType: CommandType.StoredProcedure
                ).FirstOrDefault();
            }
        }

        public static int UpdateDiscountTH(string voucherCode, int objID, int locationID, int iptvid, int promotionIDTV, int amountTV, int addBy, Guid logId)
        {
            var dataparram = new
            {
                VoucherCode = voucherCode,
                ObjID = objID,
                LocationID = locationID,
                IPTVID = iptvid,
                PromotionIDTV = promotionIDTV,
                AmountTV = amountTV,
                AddBy = addBy
            };
            WriteToLog(dataparram, " UpdateDiscountTH ", logId);
            string connIptv = Utility.ConnIPTV;
            using (var connection = new SqlConnection(connIptv))
            {
                return connection.Execute(
                    "IPTV.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    dataparram,
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        private static int SPFAddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_SPF_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        #endregion
        public static List<Evoucher> GetListEvc0(SalePlatformGetListVoucher input, Tuple<int, int> prepaid_net_tv, int PrepaidID, int localtype, Guid logId)
        {
            List<Evoucher> lstEvc = new List<Evoucher>();
            try
            {
                List<Evoucherid> lstdata = new List<Evoucherid>();
                List<EvoucherServiceCode> lstVoucherServiceCode = new List<EvoucherServiceCode>();
                foreach (var service in input.Services)
                {
                    CustomerTypeModel ctm = input.CustomerTypes.Where(x => x.ServiceID == service.ServiceID).FirstOrDefault();
                    foreach (var SubServiceType in service.SubServiceTypes)
                    {
                        List<EvoucherServiceCode> lst = new List<EvoucherServiceCode>();
                        using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                        {
                            if (input.channel.Equals(0))
                            {
                                lst = connection.Query<EvoucherServiceCode>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
                                {
                                    actionName = "GetPromotionEvent",
                                    prepaidnet = prepaid_net_tv.Item1,
                                    PaidTimeType = PrepaidID,
                                    LocalType = localtype,
                                    xml = CreateXMLSubService(SubServiceType.SubServices, service.ServiceID,
                                                                SubServiceType.SubServiceTypeID, ctm.CustomerType, PrepaidID, logId)
                                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                            }
                            if (input.channel.Equals(1))
                            {
                                lst = connection.Query<EvoucherServiceCode>(VoucherBussinessConstant.OS6_FPTVoucher_SPF_RegisterWeb, new
                                {
                                    actionName = "GetPromotionEvent",
                                    prepaidnet = prepaid_net_tv.Item1,
                                    PaidTimeType = PrepaidID,
                                    LocalType = localtype,
                                    xml = CreateXMLSubService(SubServiceType.SubServices, service.ServiceID,
                                                                SubServiceType.SubServiceTypeID, ctm.CustomerType, PrepaidID, logId)
                                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                            }
                        }

                        WriteToLog(lst.Count, "MBSv4 GetListEvoucher lst.Count: ", logId);
                        WriteToLog(lst, "MBSv4 GetListEvoucher lst: ", logId);
                        if (lst.Count > 0)
                            lstVoucherServiceCode.AddRange(lst);
                    }

                    var lstVoucherNonPHM = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet == 0 && x.ServiceCodePHMtv == 0).ToList();
                    if (lstVoucherNonPHM.Count > 0)
                    {
                        lstdata.AddRange(lstVoucherNonPHM);
                    }

                    int countPHMevc = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet > 0 || x.ServiceCodePHMtv > 0).Count();
                    WriteToLog(countPHMevc, "MBSv4 GetListEvoucher countPHMevc: ", logId);

                    if (countPHMevc > 0)
                    {
                        if (input.Products.Count > 0)
                        {
                            var prodbyServiceID = input.Products.Where(s => s.ServiceID == service.ServiceID).ToList();

                            WriteToLog(prodbyServiceID.Count, "MBSv4 prodbyServiceID prodbyServiceID.Count: ", logId);
                            if (prodbyServiceID.Count > 0)
                            {
                                foreach (var pros in prodbyServiceID)
                                {
                                    foreach (var SubServiceTypes in pros.SubServiceTypes)
                                    {
                                        foreach (var SubService in SubServiceTypes.SubServices)
                                        {
                                            var EvcPHM = lstVoucherServiceCode.Where(s => (s.ServiceCodePHMnet == SubService.ServiceCode && GetServiceCode(pros.ServiceID).ToUpper().Equals("INT"))
                                                                                            || (s.ServiceCodePHMtv == SubService.ServiceCode && GetServiceCode(pros.ServiceID).ToUpper().Equals("PLAYN"))).ToList();
                                            WriteToLog(EvcPHM.Count, "MBSv4 GetListEvoucher EvcPHM.Count: ", logId);
                                            if (EvcPHM.Count > 0) lstdata.AddRange(EvcPHM);
                                        }
                                    }
                                }
                            }
                        }
                        //else
                        //{
                        //    lstVoucherServiceCode = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet == 0 && x.ServiceCodePHMtv == 0).ToList();
                        //    if (lstVoucherServiceCode.Count > 0) lstdata.AddRange(lstVoucherServiceCode);
                        //}
                    }

                    WriteToLog(lstdata, "MBSv4 GetListEvoucher lstdata theo service : " + service.ServiceID + " ", logId);
                }

                WriteToLog(lstdata, "MBSv4 GetListEvoucher lstdata all: ", logId);
                var datagroup = lstdata.GroupBy(x => new { x.ID, x.VoucherCode, x.Description }).Select(a => new Evoucherid { ID = a.Key.ID, Description = a.Key.Description, VoucherCode = a.Key.VoucherCode });

                foreach (var evc in datagroup)
                {
                    if (input.channel.Equals(0))
                    {
                        int Quota = GetQuotaV2(evc.ID, input.saleID, localtype, PrepaidID);
                        int quotarKeep = GetQuotaKeep(evc.VoucherCode, input.saleID);

                        WriteToLog(Quota - quotarKeep, "MBSv4 GetListEvoucher Quota - quotarKeep: " + evc.VoucherCode + " ", logId);
                        if ((Quota - quotarKeep) > 0)
                        {
                            lstEvc.Add(new Evoucher { Description = evc.Description, VoucherCode = evc.VoucherCode, Note = evc.Note, Todate = evc.Todate });
                        }
                    }

                    if (input.channel.Equals(1))
                    {
                        int Quota = GetQuotaUnuse(evc.ID, input.locationID, input.saleID, input.Userbranch, localtype, PrepaidID);
                        int quotarKeep = GetQuotaKeepDKOL(input.Userbranch, input.locationID, input.saleID, evc.VoucherCode);

                        WriteToLog(Quota - quotarKeep, "MBSv4 GetListEvoucher Quota - quotarKeep: " + evc.VoucherCode + " ", logId);
                        if ((Quota - quotarKeep) > 0)
                        {
                            lstEvc.Add(new Evoucher { Description = evc.Description, VoucherCode = evc.VoucherCode, Note = evc.Note, Todate = evc.Todate });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteToLog(ex.Message, "MBSv4 GetListEvoucher service error: ", logId);
                return null;
            }

            return lstEvc;
        }
        public static XElement CreateXMLSubService(List<SubServiceModel> input, int serviceID, int SubServiceTypeID, int customerType, int PrepaidID, Guid logId)
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("SubServiceID", item.SubServiceID),
                           new XElement("PrePaid", item.PrePaid),
                           //new XElement("DeployTypeID", item.DeployTypeID), 
                           new XElement("ServiceCode", item.ServiceCode),
                           new XElement("serviceID", serviceID),
                           new XElement("SubServiceTypeID", SubServiceTypeID),
                           new XElement("customerType", customerType),
                           new XElement("PrepaidID", PrepaidID)
                       ));
            WriteToLog(xmlString, " CreateXMLSubService xml serviceID " + serviceID.ToString(), logId);
            return xmlString;
        }
        public static int GetQuotaUnuse(int promotionID, int locationId, int saleid, UserBranchPlatformModel userBranch, int localTypeID, int paidTimeTypeID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(VoucherBussinessConstant.OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "GetQuotaUnused",
                    PromotionID = promotionID,
                    SourceID = userBranch.sourceId,
                    SubCompanyID = userBranch.subcompanyId,
                    LocationID = locationId,
                    BranchCode = userBranch.branchCode,
                    DepartmentID = userBranch.departmentId,
                    SaleID = saleid,
                    prepaidId = paidTimeTypeID,
                    LocalTypeID = localTypeID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }
        public static int GetQuotaKeepDKOL(UserBranchPlatformModel userBranch, int locationId, int saleId, string voucherCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(VoucherBussinessConstant.OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "CountKeepVoucher",
                    @SourceID = userBranch.sourceId,
                    @SubCompanyID = userBranch.subcompanyId,
                    @BranchCode = userBranch.branchCode,
                    @DepartmentID = userBranch.departmentId,
                    @LocationID = locationId,
                    @SaleID = saleId,
                    @VoucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }
        public static int GetQuotaKeep(string VoucherCode, int salesmanID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(VoucherBussinessConstant.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    ActionName = "countQuotaHistoryTem",
                    saleid = salesmanID,
                    voucherCode = VoucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }
        public static List<Evoucher> GetListEvc1(SalePlatformGetListVoucher input, Tuple<int, int> prepaid_net_tv, int PrepaidID, int localtype, Guid logId)
        {
            List<Evoucher> lstEvc = new List<Evoucher>();
            try
            {
                List<Evoucherid> lstdata = new List<Evoucherid>();
                List<EvoucherServiceCode> lstVoucherServiceCode = new List<EvoucherServiceCode>();
                foreach (var service in input.Services)
                {
                    CustomerTypeModel ctm = input.CustomerTypes.Where(x => x.ServiceID == service.ServiceID).FirstOrDefault();
                    foreach (var SubServiceType in service.SubServiceTypes)
                    {
                        List<EvoucherServiceCode> lst = new List<EvoucherServiceCode>();
                        using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                        {
                            lst = connection.Query<EvoucherServiceCode>(VoucherBussinessConstant.OS6_FPTVoucher_SPF_RegisterWeb, new
                            {
                                ActionName = "GetPromotionEvent",
                                prepaidnet = prepaid_net_tv.Item1,
                                prepaidId = PrepaidID,
                                localTypeId = localtype,
                                xml = CreateXMLSubService(SubServiceType.SubServices, service.ServiceID,
                                                            SubServiceType.SubServiceTypeID, ctm.CustomerType, PrepaidID, logId)
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        }

                        WriteToLog(lst.Count, "RegisterWebV5 GetListEvoucher lst.Count: ", logId);
                        WriteToLog(lst, "RegisterWebV5 GetListEvoucher lst: ", logId);
                        if (lst.Count > 0)
                            lstVoucherServiceCode.AddRange(lst);
                    }

                    var lstVoucherNonPHM = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet == 0 && x.ServiceCodePHMtv == 0).ToList();
                    if (lstVoucherNonPHM.Count > 0)
                    {
                        lstdata.AddRange(lstVoucherNonPHM);
                    }

                    int countPHMevc = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet > 0 || x.ServiceCodePHMtv > 0).Count();
                    WriteToLog(countPHMevc, "RegisterWebV5 GetListEvoucher countPHMevc: ", logId);

                    if (countPHMevc > 0)
                    {
                        if (input.Products.Count > 0)
                        {
                            var prodbyServiceID = input.Products.Where(s => s.ServiceID == service.ServiceID).ToList();

                            WriteToLog(prodbyServiceID.Count, "RegisterWebV5 prodbyServiceID prodbyServiceID.Count: ", logId);
                            if (prodbyServiceID.Count > 0)
                            {
                                foreach (var pros in prodbyServiceID)
                                {
                                    foreach (var SubServiceTypes in pros.SubServiceTypes)
                                    {
                                        foreach (var SubService in SubServiceTypes.SubServices)
                                        {
                                            var EvcPHM = lstVoucherServiceCode.Where(s => (s.ServiceCodePHMnet == SubService.ServiceCode && GetServiceCode(pros.ServiceID).ToUpper().Equals("INT"))
                                                                                            || (s.ServiceCodePHMtv == SubService.ServiceCode && GetServiceCode(pros.ServiceID).ToUpper().Equals("PLAYN"))).ToList();
                                            WriteToLog(EvcPHM.Count, "RegisterWebV5 GetListEvoucher EvcPHM.Count: ", logId);
                                            if (EvcPHM.Count > 0) lstdata.AddRange(EvcPHM);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    WriteToLog(lstdata, "RegisterWebV5 GetListEvoucher lstdata theo service : " + service.ServiceID + " ", logId);
                }

                WriteToLog(lstdata, "RegisterWebV5 GetListEvoucher lstdata all: ", logId);
                var datagroup = lstdata.GroupBy(x => new { x.ID, x.VoucherCode, x.Description, x.Note, x.Todate }).Select(a => new Evoucherid { ID = a.Key.ID, Description = a.Key.Description, VoucherCode = a.Key.VoucherCode, Note = a.Key.Note, Todate = a.Key.Todate });
                WriteToLog(datagroup, "RegisterWebV5 GetListEvoucher datagroup: ", logId);
                foreach (var evc in datagroup)
                {
                    WriteToLog(lstdata, "RegisterWebV5 GetListEvoucher StartToGet Quotar: ", logId);
                    WriteToLog("", "RegisterWebV5 GetListEvoucher evc.ID: " + evc.ID + " " + input.locationID + " " + input.saleID + " " + localtype + " " + PrepaidID, logId);
                    WriteToLog(input.Userbranch, "RegisterWebV5 GetListEvoucher input.Userbranch: ", logId);

                    int Quota = GetQuotaUnuse(evc.ID, input.locationID, input.saleID, input.Userbranch, localtype, PrepaidID);
                    WriteToLog(lstdata, "RegisterWebV5 GetListEvoucher Quotar: ", logId);

                    WriteToLog(lstdata, "RegisterWebV5 GetListEvoucher StartToGet quotarKeep: ", logId);
                    int quotarKeep = GetQuotaKeepDKOL(input.Userbranch, input.locationID, input.saleID, evc.VoucherCode);// GetQuotaKeep(evc.VoucherCode, input.saleID);
                    WriteToLog(lstdata, "RegisterWebV5 GetListEvoucher StartToGet quotarKeep: ", logId);

                    WriteToLog(Quota - quotarKeep, "RegisterWebV5 GetListEvoucher Quota - quotarKeep: " + evc.VoucherCode + " ", logId);
                    if ((Quota - quotarKeep) > 0)
                    {
                        lstEvc.Add(new Evoucher { Description = evc.Description, VoucherCode = evc.VoucherCode, Note = evc.Note, Todate = evc.Todate });
                    }
                }
            }
            catch (Exception ex)
            {
                WriteToLog(ex.Message, "RegisterWebV5 GetListEvoucher service error: ", logId);
                return null;
            }

            return lstEvc;
        }
        #endregion
    }
}