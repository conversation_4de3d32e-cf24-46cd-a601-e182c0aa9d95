using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace GetQuotaOfUser.Constant
{
    public class VoucherBussinessConstant
    {
        public const string OS6_FPTVoucher_VoucherBussiness = "PowerInside.dbo.OS6_FPTVoucher_VoucherBussiness";        
        public const string GetListEvoucher = "GetListEvoucher";
        public const string GetPrePaidTimeID = "GetPrePaidTimeID";
        public const string GetLocalType = "GetLocalType";
        public const string GetPromotionEvent = "GetPromotionEvent";
        public const string checkInforVoucherClear = "checkInforVoucherClear";
        public const string GetInforClearVoucher = "GetInforClearVoucher";
        public const string ClearVoucherCuoc = "ClearVoucherCuoc";
        public const string ClearVoucherThang = "ClearVoucherThang";
        public const string ClearPrepaid = "ClearPrepaid";
        public const string GetStatusVoucherClear = "GetStatusVoucherClear";
        public const string GetSalemanInfor = "GetSalemanInfor";
        public const string GetPEByCode = "GetPEByCode";
        public const string RedeemVoucherGeneralCode = "RedeemVoucherGeneralCode";

        public const string OS6_FPTVoucher_SalesManInfo = "PowerInside.dbo.OS6_FPTVoucher_SalesManInfo";
        public const string GetQuotaV2 = "GetQuotaV2";
        public const string OS6_FPTVoucher_SalePlatform_MBSv4 = "PowerInside.dbo.OS6_FPTVoucher_SalePlatform_MBSv4";
        public const string OS6_FPTVoucher_SPF_RegisterWeb = "PowerInside.dbo.OS6_FPTVoucher_SPF_RegisterWeb";
        public const string OS6_FPTVoucher_MBSAPIVoucher = "PowerInside.dbo.OS6_FPTVoucher_MBSAPIVoucher";
        public const string OS6_FPTVoucher_GeneralVoucher = "PowerInside.dbo.OS6_FPTVoucher_GeneralVoucher";
        public const string OS6_FPTVoucher_SalePlatForm_AddVoucherBill = "PowerInside.dbo.OS6_FPTVoucher_SalePlatForm_AddVoucherBill";
        public const string OS6_FPTVoucher_FriendSellEvent = "PowerInside.dbo.OS6_FPTVoucher_FriendSellEvent";
        public const string OS6_ReferralProgram_InviteCode = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";
        public const string OS6_FPTVoucher_GeneralCode = "PowerInside.dbo.OS6_FPTVoucher_GeneralCode";
        public const string OS6_FPTVoucher_AdvocacyProgram = "PowerInside.dbo.OS6_FPTVoucher_AdvocacyProgram";
        public const string OS6_ReferalProgram_CAM = "PowerInside.dbo.OS6_ReferalProgram_CAM";
        public const string OS6_FPTVoucher_OTTReferralProgram = "PowerInside.dbo.OS6_FPTVoucher_OTTReferralProgram";
        public const string OS6_ReferralProgram_Camera = "PowerInside.dbo.OS6_ReferralProgram_Camera";
        public const string OS6_ReferralProgram_SucceedInvite = "PowerInside.dbo.OS6_ReferralProgram_SucceedInvite";
    }

    public class VoucherBussinessErrorConstant
    {
        public const string NoPrepaid = "Không có thông tin Prepaid";
        public const string NoLocalType = "Không có thông tin Gói dịch vụ";
    }
}