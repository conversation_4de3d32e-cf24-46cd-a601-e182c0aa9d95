using GetQuotaOfUser.Models;
using GetQuotaOfUser.Services;
using System;
using System.Collections.Generic;
using System.Web.Http;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper;
using Newtonsoft.Json;

namespace GetQuotaOfUser.Controllers
{
    public class EvoucherInforController : ApiController
    {
        [HttpPost]
        [Route("API/Evoucher/GetValueVoucherByOrderCode")]
        [Authorize(Roles = AuthorizeRole.Admin)]
        public ResponseModel<List<ValueVoucherOrderCode>> GetValueVoucherByOrderCode(EvoucherInforRequest request)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), EvoucherBussinessServices.RamdoneString());
            L.Mes(Level.REQUEST, keylog + " GetValueVoucherByOrderCode: " + JsonConvert.SerializeObject(request));
            try
            {
                var result = EvoucherServices.GetValueVoucherByOrderCode(request.orderCode);

                return new ResponseModel<List<ValueVoucherOrderCode>> { data = result, error = "", result = 1 };

            }
            catch (Exception e)
            {
                L.Mes(Level.ERROR, e.Message, keylog + " GetValueVoucherByOrderCode: ");
                return new ResponseModel<List<ValueVoucherOrderCode>> { data = null, error = e.Message, result = -1 };
            }
        }
    }
}