using GetQuotaOfUser.Models;
using GetQuotaOfUser.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;

namespace GetQuotaOfUser.Controllers
{
    public class IssueInvoiceController : ApiController
    {
        [HttpPost]
        [Route("API/Voucher/IssueInvoice")]
        [Authorize(Roles = AuthorizeRole.IssueInvoice)]
        public ResponseModel<List<IssueInvoice>> IssueInvoice(IssueInvoiceRequest request)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), EvoucherBussinessServices.RamdoneString());
            L.Mes(Level.REQUEST, keylog + " IssueInvoice: " + request.ObjId + " " + request.RegId);
            try
            {
                var result = IssueInvoiceService.GetIssueInvoice(request.ObjId, request.RegId);

                return new ResponseModel<List<IssueInvoice>> { data = result, error = "", result = 1 };

            } catch (Exception e)
            {
                L.Mes(Level.ERROR, e.Message, keylog + " IssueInvoice: ");
                return new ResponseModel<List<IssueInvoice>> { data = null, error = e.Message, result = -1 };
            }
        }
    }
}