using Dapper;
using GetQuotaOfUser.Constant;
using GetQuotaOfUser.Models;
using GetQuotaOfUser.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web.Http;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;

namespace EVCBussiness.Controllers
{
    public class EVCBussinessController : ApiController
    {        
        [HttpGet]
        [Route("API/Voucher/GetListEvoucher")]
        [Authorize(Roles = AuthorizeRole.SuperUser)]
        public ResponseModel<List<EvoucherBussiness>> GetListEvoucher(int objID, string regCode)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), EvoucherBussinessServices.RamdoneString());
            L.Mes(Level.REQUEST, keylog + " VoucherBussiness - GetListEvoucher: " + objID + " " + regCode);                   
            List<EvoucherBussiness> listEVC = new List<EvoucherBussiness>();
            try
            {                
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    listEVC = connection.Query<EvoucherBussiness>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                    {
                        actionName = VoucherBussinessConstant.GetListEvoucher,
                        objID = objID,
                        regCode = regCode,
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                    L.Mes(Level.INFO, listEVC.Count().ToString(), keylog + "VoucherBussiness - GetListEvoucher: " + objID + " " + regCode);       
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, keylog + "VoucherBussiness - GetListEvoucher: ");
                return new ResponseModel<List<EvoucherBussiness>> { data = null, error = ex.Message, result = 0 };
            }
            return new ResponseModel<List<EvoucherBussiness>> { data = listEVC, error = null, result = listEVC.Count };
        }
        
        //[HttpGet]
        //[Route("API/Voucher/GetListEvoucherBySaleID")]
        //public ResponseModel<List<EvoucherBussinessSale>> GetListEvoucherBySaleID(int SaleId, int objID)
        //{
        //    string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), EvoucherBussinessServices.RamdoneString());
        //    L.Mes(Level.REQUEST, keylog + " VoucherBussiness - GetListEvoucherBySaleID: " + SaleId + " for objid " + objID);
        //    List<EvoucherBussinessSale> listEVC = new List<EvoucherBussinessSale>();
        //    try
        //    {
        //        using (var connection = new SqlConnection(SqlHelper.ConnRead()))
        //        {
        //            connection.Open();
        //            int PrepaidInfoID = EvoucherBussinessServices.GetPrepaidId(objID);
        //            if (PrepaidInfoID == 0)
        //            {
        //                return new ResponseModel<List<EvoucherBussinessSale>> { data = null, error = VoucherBussinessErrorConstant.NoPrepaid, result = 0 };
        //            }
        //            L.Mes(Level.INFO, JsonConvert.SerializeObject(PrepaidInfoID), keylog + "VoucherBussiness - GetListEvoucherBySaleID prepaidInfor: " + objID);

        //            int localTypeID = EvoucherBussinessServices.GetLocaltype(objID);
        //            if (localTypeID == 0)
        //            {
        //                return new ResponseModel<List<EvoucherBussinessSale>> { data = null, error = VoucherBussinessErrorConstant.NoLocalType, result = 0 };
        //            }
        //            L.Mes(Level.INFO, localTypeID.ToString(), keylog + "VoucherBussiness - GetListEvoucherBySaleID localTypeID: " + objID.ToString());

        //            List<PromotionEventModel> lst = connection.Query<PromotionEventModel>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
        //            {
        //                ActionName = VoucherBussinessConstant.GetPromotionEvent,
        //                Localtype = localTypeID,
        //                paidTimeID = PrepaidInfoID                        
        //            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

        //            L.Mes(Level.INFO, lst.Count().ToString(), keylog + "VoucherBussiness - GetListEvoucherBySaleID: " + objID + " GetVoucher ");

        //            for (int i = 0; i < lst.Count(); i++)
        //            {
        //                // Check ServiceCode : RULE : the ServiceCode input sent by the customer will be greater or equal the ServiceCode of PE
        //                if (! EvoucherBussinessServices.CheckValidServiceCode(
        //                    new List<int>(){
        //                    lst[i].ServiceCode1,lst[i].ServiceCode2,   // 56 or 47
        //                    lst[i].ServiceCode3,lst[i].ServiceCode4,   // 82 - 58 
        //                    lst[i].ServiceCode5,                            // 153,240,325
        //                    0,                                                  // GIFT
        //                    lst[i].ServiceCode7
        //                },
        //                    new List<int>(){
        //                    56,                           // 56 or 47
        //                    82,                            // 82
        //                    58,                           // 9 or 58
        //                    0 // tạm thời chưa lấy
        //                }
        //               )) continue;

        //                int Quota = EvoucherBussinessServices.GetQuotaV2(lst[i].ID, SaleId, localTypeID, PrepaidInfoID);

        //                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { Code = lst[i].EventCode, Quota = Quota }));
        //                if (Quota <= 0) continue;
        //                listEVC.Add(new EvoucherBussinessSale() { EventCode = lst[i].EventCode, Name = lst[i].Name });
        //            }
                    
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        L.Mes(Level.ERROR, ex.Message, keylog + "VoucherBussiness - GetListEvoucher: ");
        //        return new ResponseModel<List<EvoucherBussinessSale>> { data = null, error = ex.Message, result = 0 };
        //    }
        //    return new ResponseModel<List<EvoucherBussinessSale>> { data = listEVC, error = null, result = listEVC.Count };
        //}

        //[Route("API/Voucher/ApplyVoucher")]
        //[HttpPost]
        //public ResponseModel<bool> ApplyVoucher(ApplyVoucherInput input)
        //{
        //    string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), EvoucherBussinessServices.RamdoneString());
        //    L.Mes(Level.REQUEST, keylog + " VoucherBussiness - ApplyVoucher: saleid " + input.saleID.ToString() + " for objid " + input.objID.ToString());
        //    L.Mes(Level.REQUEST, keylog + " " + JsonConvert.SerializeObject(input), "VoucherBussiness - ApplyVoucher");            
        //    try
        //    {
        //        //get infor saleman
        //        SalesManInfo Salesman = EvoucherBussinessServices.GetInfoSalesMan(input.saleID);
        //        if (Salesman == null)
        //        {
        //            return new ResponseModel<bool>() { data = false, error = "Thông Tin Salesman Không Tồn Tại", result = 0 };
        //        }
        //        int statusClearVC = EvoucherBussinessServices.GetStatusrVoucherClear(input.voucherGc, input.objID, input.regCode);
        //        L.Mes(Level.INFO, keylog + " VoucherBussiness - ClearVoucher: check status voucher " + statusClearVC.ToString());
        //        if (statusClearVC == 0)
        //        {
        //            return new ResponseModel<bool> { data = false, error = input.voucherGc + " voucher đã được clear", result = 0 };
        //        }

        //        PEDiscountModel vcDiscountUpdate = EvoucherBussinessServices.GetPE(input.voucherGc);
        //        if (vcDiscountUpdate == null) return new ResponseModel<bool>() { data = false, error = "Voucher không tồn tại khoặc ngưng kích hoạt", result = 0 };

        //        int PrepaidInfoID = EvoucherBussinessServices.GetPrepaidId(input.objID);
        //        if (PrepaidInfoID == 0) return new ResponseModel<bool>() { data = false, error = "Thông tin áp dụng voucher không hợp lệ", result = 0 };

        //        int localType = EvoucherBussinessServices.GetLocaltype(input.objID);
        //        if (localType == 0) return new ResponseModel<bool>() { data = false, error = "Gói dịch vụ không hợp lệ", result = 0 };

        //        int Quota = EvoucherBussinessServices.GetQuotaV2(vcDiscountUpdate.ID, input.saleID, localType, PrepaidInfoID);
        //        if (Quota <= 0) return new ResponseModel<bool>() { data = false, error = "Đã sử dụng hết số lượng quota phân bổ", result = 0 };
        //        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
        //        {
        //            int generalCodeID = connection.QueryFirstOrDefault<int>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
        //            {
        //                actionName = VoucherBussinessConstant.RedeemVoucherGeneralCode,
        //                eventCode = input.voucherGc,
        //                promotionEventID = vcDiscountUpdate.ID,
        //                saleID = input.saleID,
        //                objID = input.objID,
        //                regCode = input.regCode,
        //                localtype = localType,
        //                paidTimeID = PrepaidInfoID,
        //                user = input.user
        //            },  commandTimeout: null, commandType: CommandType.StoredProcedure);

        //            if (generalCodeID > 0)
        //            {
        //                L.Mes(Level.INFO, keylog + " SP_VUPA_GC");
        //                if (vcDiscountUpdate.NetPromotionID > 0)
        //                {
        //                    EvoucherBussinessServices.UpdateDiscount(
        //                        input.objID,  // objID khách hàng
        //                        vcDiscountUpdate.NetPromotionID, //XđồngY tháng NET + Tháng
        //                        vcDiscountUpdate.IPTVPromotionID, //XđồngY tháng TV + Tháng
        //                        vcDiscountUpdate.MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
        //                        vcDiscountUpdate.MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
        //                        vcDiscountUpdate.EventCode, // Code
        //                        input.saleID, true //Sales -TypeVC:True
        //                    );
        //                }
        //                else
        //                {
        //                    // tang thang
        //                    int isEvenMonth = 0;
        //                    if (vcDiscountUpdate.ContainerID > 0)
        //                    {
        //                        // lay clkm nen
        //                        var monthPromotion = EvoucherBussinessServices.GetMonthPromotion(input.objID);
        //                        if (monthPromotion != null)
        //                        {
        //                            // thang chan le
        //                            isEvenMonth = monthPromotion.EventMonth;
        //                            // lay promotion net theo thang
        //                            int promotionNetID = EvoucherBussinessServices.GetPromotionNetByContainerID(vcDiscountUpdate.ContainerID, monthPromotion.GetAddMonth(), isEvenMonth);
        //                            if (promotionNetID > 0)
        //                                vcDiscountUpdate.PromotionNetID = promotionNetID;
        //                        }
        //                    }

        //                    L.Mes(Level.INFO, keylog +  " AddCustomerDiscountV2: " + JsonConvert.SerializeObject(
        //                        new
        //                        {
        //                            ObjID = input.objID,
        //                            PromotionNetID = vcDiscountUpdate.PromotionNetID,
        //                            IPTVPromotionID = vcDiscountUpdate.IPTVPromotionID,
        //                            MoneyPromotionNETID = vcDiscountUpdate.MoneyPromotionNETID,
        //                            MoneyPromotionTVID = vcDiscountUpdate.MoneyPromotionTVID,
        //                            EventCode = vcDiscountUpdate.EventCode,
        //                            SaleID = input.saleID,
        //                            isEvenMonth = isEvenMonth,
        //                            generalCodeID = generalCodeID
        //                        }
        //                    ));

        //                    EvoucherBussinessServices.AddCustomerDiscountV2(
        //                        input.objID,
        //                        vcDiscountUpdate.PromotionNetID,
        //                        vcDiscountUpdate.IPTVPromotionID,
        //                        vcDiscountUpdate.MoneyPromotionNETID,
        //                        vcDiscountUpdate.MoneyPromotionTVID,
        //                        vcDiscountUpdate.EventCode,
        //                        input.saleID,
        //                        isEvenMonth,
        //                        generalCodeID
        //                    );                            
        //                }
        //                L.Mes(Level.RESPONSE, keylog + " "+ JsonConvert.SerializeObject(generalCodeID != 0));
        //                L.Mes(Level.ENDREQUEST, " End ", keylog + " " + "VoucherBussiness - ApplyVoucher");
        //                EvoucherBussinessServices.UpdateOanhVK(input.objID, input.regCode, generalCodeID);
        //                return new ResponseModel<bool>() { data = (generalCodeID != 0), error = null, result = 0 };
        //            }
        //        }                
        //    }
        //    catch (Exception ex)
        //    {
        //        L.Mes(Level.ERROR, ex.Message,keylog + " VoucherBussiness - ApplyVoucher");
        //        return new ResponseModel<bool>() { data = false, error = ex.Message, result = 0 };
        //    }
        //    return new ResponseModel<bool>() { data = false, error = null, result = 0 };
        //}

        [Route("API/Voucher/ClearVoucher")]
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUser)]
        public ResponseModel<bool> ClearVoucher(ClearVoucherInput input)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), EvoucherBussinessServices.RamdoneString());
            L.Mes(Level.REQUEST, keylog + " VoucherBussiness - ClearVoucher: objid " + input.objID.ToString() + " and Voucher " + input.voucherGc);
            L.Mes(Level.REQUEST, keylog + " " + JsonConvert.SerializeObject(input), "VoucherBussiness - ClearVoucher");
            try
            {
                // kiem tra trang thai cua voucher 
                int statusClearVC = EvoucherBussinessServices.GetStatusrVoucherClear(input.voucherGc, input.objID, input.regCode);
                L.Mes(Level.INFO, keylog + " VoucherBussiness - ClearVoucher: check status voucher " + statusClearVC.ToString());
                if (statusClearVC == 0)
                {
                    return new ResponseModel<bool> { data = false, error = input.voucherGc + " voucher đã được clear", result = 0 };
                }
                // kiem tra voucher dc phep clear gom voucher tang thang cuoc va voucher thang thang MKT
                bool isTrueClear = EvoucherBussinessServices.checkInforVoucherClear(input.voucherGc);
                if (!isTrueClear)
                {
                    L.Mes(Level.INFO, keylog + " VoucherBussiness - ClearVoucher: check clear voucher " + isTrueClear.ToString());
                    return new ResponseModel<bool> { data = false, error = input.voucherGc + " Tính năng clear Voucher Combo đang được cập nhật, vui lòng liên hệ FTQ ", result = 0 };
                }
                int clearVouchcer = 0;
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        DataEventID dataEvent = connection.QueryFirstOrDefault<DataEventID>(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                        {
                            actionName = "GetInforClearVoucherV2",
                            eventCode = input.voucherGc,
                            objID = input.objID,
                            regCode = input.regCode,
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        L.Mes(Level.INFO, keylog + " VoucherBussiness - ClearVoucher: GetInforClearVoucher " + JsonConvert.SerializeObject(dataEvent));
                        if (dataEvent == null)
                        {
                            return new ResponseModel<bool> { data = false, error = "Voucher đã được clear hết!", result = 0 };
                        }
                        else
                        {
                            PEDiscountModel pdm = new PEDiscountModel();
                            pdm = EvoucherBussinessServices.GetPE(input.voucherGc);
                            clearVouchcer = connection.Execute(VoucherBussinessConstant.OS6_FPTVoucher_VoucherBussiness, new
                            {
                                actionName = "ClearVoucherV2",
                                generalCodeID = dataEvent.generalCodeID,
                                voucherBillID = dataEvent.voucherBillID,
                                objPromotionID = dataEvent.objPromotionID,
                                prePaidID = dataEvent.prePaidID,
                                objID = input.objID,
                                regCode = input.regCode,
                                eventCode = input.voucherGc,
                                user = input.user,
                                PromotionIDNet = pdm.PromotionNetID,
                                MoneyPromotionIDNet = pdm.MoneyPromotionNETID
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            L.Mes(Level.INFO, keylog + " VoucherBussiness - ClearVoucher: ClearVoucher res" + JsonConvert.SerializeObject(clearVouchcer));
                            if (clearVouchcer == 0)
                            {
                                return new ResponseModel<bool> { data = false, error = "Clear Voucher không thành công", result = 0 };
                            }
                            transaction.Commit();
                        }                       
                    }
                }
                if (clearVouchcer > 0)
                {
                    L.Mes(Level.INFO, keylog + " VoucherBussiness - ClearVoucher: UpdatePromHistory start");
                    string connIptv = Utility.ConnIPTV;
                    using (var connection = new SqlConnection(connIptv))
                    {
                        connection.Query("IPTV.dbo.OS6_FPTVoucher_UpdatePromHistory", new
                        {
                            objID = input.objID,
                            eventCode = input.voucherGc,
                            user = input.user
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure);
                    }
                    L.Mes(Level.INFO, keylog + " VoucherBussiness - ClearVoucher: UpdatePromHistory done");
                }
                return new ResponseModel<bool> { data = true, error = null, result = 1 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, keylog + " " + ex.Message, "VoucherBussiness - ClearVoucher");
                return new ResponseModel<bool>() { data = false, error = ex.Message, result = 0 };
            }
            return new ResponseModel<bool>() { data = false, error = null, result = 0 };
        }

        [Route("API/Voucher/AddVoucher")]
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUser)]
        public ResponseModel<bool> AddVoucher(AddVoucherInput input)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), EvoucherBussinessServices.RamdoneString());
            var logId = Guid.NewGuid();
            EvoucherBussinessServices.WriteToLog(input, "AddVoucher res:", logId);
            bool status = false;
            int result = 0;
            string error = "";
            var res = new ResponseModel<bool> { data = status, error = error, result = result };
            try
            {
                // lấy thông tin saleman

                SalesManInfo Salesman = EvoucherBussinessServices.GetInfoSalesMan(input.saleID);
                if (Salesman == null && input.channel.Equals(0))
                {
                    return new ResponseModel<bool> { data = false, error = "Thông tin SaleMan không tồn tại", result = 0 };
                }
                //lọc voucher mã chung và voucher RF
                List<EvoucherInput> lstEVCinput = input.evoucherList.Where(a => a.evoucherType == 1).ToList();
                List<EvoucherInput> lstRFinput = input.evoucherList.Where(a => a.evoucherType == 2).ToList();
                if(lstEVCinput.Count > 1 || lstRFinput.Count > 1)
                {
                    return new ResponseModel<bool> { data = false, error = "Chỉ được thêm 1 voucher trên mỗi lần call api", result = 0 };
                }
                //xác định loại gói thanh toán
                Tuple<int, int> prepaid_net_tv = EvoucherBussinessServices.GetPrepaidtimeNetTV(input.Services);
                EvoucherBussinessServices.WriteToLog(prepaid_net_tv, "AddVoucher res prepaid_net_tv:", logId);
                // xác đinh localtype
                List<ServicePlatform> lsp = input.Services.Where(x => EvoucherBussinessServices.GetServiceCode(x.ServiceID).ToUpper() == "INT").ToList();
                int localtype = 0;
                if (lsp.Count > 0)
                {
                    localtype = EvoucherBussinessServices.GetLocaltypeV2(lsp[0].SubServiceTypes[0].SubServices[0].SubServiceID);
                }
                EvoucherBussinessServices.WriteToLog(localtype, "AddVoucher localtype:", logId);
                // xác định voucher thuộc service và subtype
                List<DeviceModel> devices = EvoucherBussinessServices.GetListDeviceCam(input.Products);
                List<EvoucherService> lstEVC = EvoucherBussinessServices.getListEvcService(logId, input.channel, lstEVCinput, lstRFinput, localtype, prepaid_net_tv, devices);
                EvoucherBussinessServices.WriteToLog(lstEVC, "AddVoucher lstEVC:", logId);
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        string contractSuport = string.Empty;
                        if (input.Userbranch != null)
                        {
                            contractSuport = input.Userbranch.contractGT;
                        }

                        #region Phần service
                        if (input.Services.Count > 0)
                        {
                            // chỉ lấy các service iptv và net
                            foreach (var service in input.Services)
                            {
                                List<EvoucherService> lstEVCservice = lstEVC.Where(x => x.ServiceID == service.ServiceID).ToList();
                                foreach (var item in service.SubServiceTypes)
                                {
                                    List<EvoucherService> lstEVCserviceType = lstEVCservice.Where(x => x.ServiceID == service.ServiceID
                                                                                                    && x.SubServiceType == item.SubServiceTypeID).ToList();
                                    EvoucherBussinessServices.WriteToLog(lstEVCserviceType, "AddVoucher lstEVCserviceType:", logId);
                                    foreach (var evc in lstEVCserviceType)
                                    {
                                        if (evc.evoucherType.Equals(1)) // voucher chung
                                        {
                                            #region voucher chung
                                            bool isAdd = EvoucherBussinessServices.CheckAddVoucherGCCode(connection, transaction, evc.evoucherCode, input.objId, input.RegCode);
                                            int checkSumVoucher = EvoucherBussinessServices.SumVoucherInRegcode(input.RegCode, input.objId);
                                            EvoucherBussinessServices.WriteToLog(isAdd.ToString() + "-" + checkSumVoucher.ToString(), "AddVoucher isAdd-checkSumVoucher:", logId);
                                            if (!isAdd && checkSumVoucher == 0)
                                            {
                                                status = EvoucherBussinessServices.RedeemVoucherGC(connection, transaction, evc.evoucherCode,
                                                input.objId, input.OrderCode, prepaid_net_tv, input.saleID, Salesman, localtype, input.RegCode, input.channel, input.locationID, logId, input.Userbranch);
                                                EvoucherBussinessServices.WriteToLog(status, "AddVoucher RedeemVoucherGC-status:", logId);
                                                if (!status)
                                                    break;
                                            }
                                            #endregion
                                        }
                                        if (evc.evoucherType.Equals(2)) // voucher lẻ GTBB
                                        {
                                            //bool isRedeem = EvoucherBussinessServices.CheckRedeemRFCode(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode);
                                            bool isRedeem = EvoucherBussinessServices.CheckAddVoucherGCCode(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode);
                                            EvoucherBussinessServices.WriteToLog(isRedeem, "AddVoucher isRedeem:", logId);
                                            if (!isRedeem)
                                            {
                                                status = EvoucherBussinessServices.RedeemVoucherCampaign(connection, transaction, evc.evoucherCode, input.objId,
                                                        input.OrderCode, prepaid_net_tv, input.saleID, Salesman, input.channel, localtype, input.RegCode, contractSuport, null, logId);
                                                EvoucherBussinessServices.WriteToLog(status, "AddVoucher RedeemVoucherCampaign-status:", logId);
                                                if (!status)
                                                    break;
                                            }
                                        }
                                    }
                                    if (!status)
                                        break;
                                }
                                if (!status)
                                {
                                    res.error = "Không thỏa điều kiện addvoucher RedeemVoucherGC!";
                                    break;
                                }    
                            }
                        }

                        #endregion

                        #region phần thiết bị lẻ
                        if (input.Products.Count > 0)
                        {

                            foreach (var product in input.Products)
                            {
                                List<EvoucherService> lstEVCservice = lstEVC.Where(x => x.ServiceID == product.ServiceID).ToList();
                                foreach (var item in product.SubServiceTypes)
                                {
                                    List<EvoucherService> lstEVCserviceType = lstEVCservice.Where(x => x.ServiceID == product.ServiceID
                                                                                                    && x.SubServiceType == item.SubServiceTypeId).ToList();
                                    foreach (var evc in lstEVCserviceType)
                                    {
                                        if (evc.evoucherType.Equals(1)) // voucher chung
                                        {
                                            #region voucher chung

                                            #endregion
                                        }
                                        if (evc.evoucherType.Equals(2)) // voucher lẻ GTBB
                                        {
                                            bool isRedeem = EvoucherBussinessServices.CheckAddVoucherGCCode(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode);
                                            if (!isRedeem)
                                            {
                                                status = EvoucherBussinessServices.RedeemVoucherCampaign(connection, transaction, evc.evoucherCode, input.objId,
                                                        input.OrderCode, prepaid_net_tv, input.saleID, Salesman, input.channel, localtype, input.RegCode, contractSuport, devices, logId);
                                                EvoucherBussinessServices.WriteToLog(status, "MBSv4 RedeemVoucherRF SaveHistoryTem: " + evc.evoucherCode, logId);
                                                if (!status)
                                                    break;
                                            }
                                        }
                                    }
                                    if (!status)
                                        break;
                                }
                                if (!status)
                                    break;
                            }
                        }
                        #endregion
                        if (status)
                        {
                            transaction.Commit();
                            res.data = status;
                            res.result = 1;
                        }
                        res.data = status;
                    }
                }
            }
            catch (Exception ex)
            {
                EvoucherBussinessServices.WriteToLog(ex.Message, "AddVoucher RedeemVoucher Error: ", logId);
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            return res;
        }
    }
}
