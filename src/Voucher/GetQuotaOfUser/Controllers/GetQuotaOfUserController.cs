
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Data.SqlClient;
using Dapper;
using GetQuotaOfUser.Models;
using GetQuotaOfUser.Constant;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;
using Newtonsoft.Json;

namespace GetQuotaOfUser.Controllers
{
    public class GetQuotaOfUserController : ApiController
    {
        const string InfoSalesMan = "PowerInside.dbo.OS6_FPTVoucher_SalesManInfo";
        [Route("API/Voucher/GetQuotaOfUser")]
        public ResponseModel<UserInfo> Post(AccountUser input)
        {
            UserInfo ResultData = new UserInfo();
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    ResultData = connection.Query<UserInfo>(InfoSalesMan, new
                    {
                        ActionName = "select",
                        AccountName = input.AccountName
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    Dictionary<string, int> QuotaAssign = new Dictionary<string, int>{
                        { ConstantAction.SALESMAN, 0},{ConstantAction.DEPARTMENT,0},{ConstantAction.BRANCHCODE,0},{ConstantAction.LOCATION,0},{ConstantAction.REGION,0}
                    };

                    if (ResultData == null)
                        return new ResponseModel<UserInfo>() { data = null, error = null, result = 0 };



                    for (int i = 0; i < QuotaAssign.Count(); i++)
                    {
                        ResultData.Quota = GetQuota(QuotaAssign.ElementAt(i).Key, ResultData, input.PromotionID, ConstantAction.GetQuota);
                        if (i == 0 && ResultData.Quota > 0) break;
                        if (ResultData.Quota > 0 && (QuotaAssign.ElementAt(i - 1).Value == 0))
                        {
                            int SumQuota = GetQuota(QuotaAssign.ElementAt(i - 1).Key,
                                                ResultData,
                                                input.PromotionID, ConstantAction.GetQuotaParallelNode);

                            ResultData.Quota = ResultData.Quota - SumQuota;
                            ResultData.Quota = ResultData.Quota - GetRedeemQuota(ResultData, input.PromotionID, ConstantAction.GetRedeemQuota);
                            break;
                        }
                        if ((ResultData.Quota > 0) && (QuotaAssign.ElementAt(i - 1).Value == -1))
                        {
                            int CountRow = GetQuota(QuotaAssign.ElementAt(i - 1).Key,
                                                ResultData,
                                                input.PromotionID, ConstantAction.CheckContainChildNode);
                            if (CountRow > 0)
                            {
                                return new ResponseModel<UserInfo>() { data = null, error = null, result = 0 };
                            } break;
                        }

                        if (i == 4 && ResultData.Quota == -1)
                            return new ResponseModel<UserInfo>() { data = null, error = null, result = 0 };
                        QuotaAssign[QuotaAssign.ElementAt(i).Key] = ResultData.Quota;
                    }
                }
            }
            return new ResponseModel<UserInfo>() { data = ResultData, error = null, result = 1 };
        }


        public int GetQuota(string AssignPosition, UserInfo user, int PromotionID, string ActionName)
        {
            int Quota = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    int Result = connection.Query<int>(InfoSalesMan, new
                    {
                        ActionName = ActionName,
                        QuotaAction = AssignPosition,
                        AccountName = user.Name,
                        REGION = user.REGION,
                        LOCATION = user.LOCATION,
                        BRANCH = user.BRANCHCODE,
                        DEPARTMENT = user.DEPARTMENT,
                        SALESMAN = user.SALESMAN,
                        PromotionID = PromotionID
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    Quota = Result;
                }
            }
            return Quota;
        }

        private int GetRedeemQuota(UserInfo user, int PromotionID, string ActionName)
        {
            int Quota = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    int Result = connection.Query<int>(InfoSalesMan, new
                    {
                        ActionName = ActionName,
                        REGION = user.REGION,
                        LOCATION = user.LOCATION,
                        BRANCH = user.BRANCHCODE,
                        DEPARTMENT = user.DEPARTMENT,
                        SALESMAN = user.SALESMAN,
                        PromotionID = PromotionID,
                        Status = 1
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    Quota = Result;
                }
            }
            return Quota;
        }

        [Route("API/Voucher/GetSamSungVoucher")]
        [HttpPost]
        public ResponseModel GetSamSungVoucher(SamSungVoucher input)
        {
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "GetSamSungVoucher");
            dynamic code = null;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        // check Quota
                        code = connection.Query("PowerInside.dbo.OS6_FPTVoucher_MKT_VoucherSamSung", new
                        {   
                            ActionName = "GetVoucherSamSung",
                            contract = input.contractNo
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        transaction.Commit();
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "GetSamSungVoucher");
                return new ResponseModel() { data = null, error = ex.Message, result = 1 };
            }
            return new ResponseModel() { data = code, error = null, result = 1 };
        }
    }
}
