using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Foxpro.InviteFriend.Models
{
    public class HistoryReferral
    {
        public int TotalCount { set; get; }
        public List<ItemHistoryReferral> Items { set; get; }
    }

    public class ItemHistoryReferral
    {
        public string ContractOwner { set; get; }
        public string ContractName { set; get; }
        public string BookingID { set; get; }
        public DateTime? ActiveDate { set; get; }
        public long IncentivesReceived { set; get; }
        public int InviteCodeStatus { set; get; }
    }

}