using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Foxpro.InviteFriend.Models;
using Voucher.APIHelper.Util;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Data;

namespace Foxpro.InviteFriend.Controllers
{
    public class InviteFriendController : ApiController
    {
        [HttpPost]
        [Route("api/foxpro/GetReferralCode")]
        public ResponseModel<ReferralCodeResponse> GetReferralCode(EmployeeModel input)
        {
            var res = new ReferralCodeResponse();
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "GetReferralCode");
            if (string.IsNullOrEmpty(input.EmployeeCode)) return new ResponseModel<ReferralCodeResponse>() { data = null, error = "<PERSON><PERSON><PERSON> nhập mã nhân viên", result = -1 };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    // check Quota
                    res = connection.Query<ReferralCodeResponse>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                    {
                        ActionName = "GetReferralCodeByEmployee",
                        employeeID = input.EmployeeCode,
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                return new ResponseModel<ReferralCodeResponse>() { data = null, error = ex.Message, result = -1 };
            }
            return new ResponseModel<ReferralCodeResponse>() { data = res, error = null, result = 1 };
        }

        [HttpPost]
        [Route("api/foxpro/GetListHistoryReferrals")]
        public ResponseModel<HistoryReferral> GetListHistoryReferrals(EmployeeModel input)
        {
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "GetListHistoryReferrals");
            HistoryReferral hys = new HistoryReferral();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    var lst = connection.Query<ItemHistoryReferral>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                    {
                        ActionName = "GetListHistoryReferrals",
                        employeeID = input.EmployeeCode,
                    }, commandType: CommandType.StoredProcedure).ToList();
                    hys.TotalCount = lst.Count;
                    hys.Items = lst;
                }
            }
            catch (Exception ex)
            {
                return new ResponseModel<HistoryReferral>() { data = null, error = ex.Message, result = -1 };
            }
            return new ResponseModel<HistoryReferral>() { data = hys, error = null, result = 1 };
        }
    }
}
