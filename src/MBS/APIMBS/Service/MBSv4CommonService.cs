using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;
using APIMBS.Models;

namespace APIMBS.Service
{
    public class MBSv4CommonService
    {
        private const string OS6_FPTVoucher_SalePlatForm_AddVoucherBill = "PowerInside.dbo.OS6_FPTVoucher_SalePlatForm_AddVoucherBill";
        public static void WriteToLog(object input, string mes, Guid logId)
        {
            LoggerKafka.InsertLog(string.Format("{1} : {0}", JsonConvert.SerializeObject(input), mes));
        }

        public static void WriteToLogDontSerializeObject(object input, string mes, Guid logId)
        {
            LoggerKafka.InsertLog(string.Format("{1} : {0}", input, mes));
        }

        public static void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, PNET, PTV, MNET, MTV, Voucher, SalesManID }));
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }

        public static int UpdateDiscountNet(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, PNET, PTV, MNET, MTV, Voucher, SalesManID }));            
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_SPF_AddCustomerDiscount",
                    new
                    {
                        actionName = "AddVoucherNet",
                        ObjID = ObjID,
                        PromotionIDNet = PNET,
                        PromotionIDTV = PTV,
                        MoneyPromotionIDNet = MNET,
                        MoneyPromotionIDTV = MTV,
                        VoucherCode = Voucher,
                        AddBy = SalesManID
                    },
                    commandType: CommandType.StoredProcedure
                );
            }            
        }

        public static In4VoucherTHmodel GetIn4VoucherTH(SqlConnection connection, SqlTransaction transaction, int ObjID, int MTV)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, MTV,}));
            return connection.Query<In4VoucherTHmodel>(
                "PowerInside.dbo.OS6_FPTVoucher_SPF_AddCustomerDiscount",
                new
                {
                    actionName = "GetIn4VoucherTH",
                    ObjID = ObjID,
                    MoneyPromotionIDTV = MTV
                },
                transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure
            ).FirstOrDefault();
            
        }

        public static int GetCLKMiptv(SqlConnection connection, SqlTransaction transaction, string regCode, int objid, int prepaidTV)
        {            
            return connection.Query<int>(
                "PowerInside.dbo.OS6_FPTVoucher_GetIPTVID",
                new
                {
                    @objid = objid,
                    @regcode = regCode,
                    @prepaidTV = prepaidTV
                },
                transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure
            ).FirstOrDefault();

        }

        public static int UpdateDiscountTH(string voucherCode, int objID, int locationID, int iptvid, int promotionIDTV, int amountTV, int addBy, Guid logId)
        {
            var dataparram = new
                    {
                        VoucherCode = voucherCode,
                        ObjID = objID,
                        LocationID = locationID,
                        IPTVID = iptvid,
                        PromotionIDTV = promotionIDTV,
                        AmountTV = amountTV,
                        AddBy = addBy
                    };
            WriteToLog(dataparram, " UpdateDiscountTH ", logId);
            string connIptv = Utility.ConnIPTV;
            using (var connection = new SqlConnection(connIptv))
            {
                return connection.Execute(
                    "IPTV.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    dataparram,
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        public static void AddVoucherBill(int ObjID, string OrderCode, int? generalCodeID, int idprepaid)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@generalCodeID", generalCodeID);
                p.Add("@idprepaid", idprepaid);
                connection.Execute(OS6_FPTVoucher_SalePlatForm_AddVoucherBill, p, commandType: CommandType.StoredProcedure);
            }
        }

        public static int NewAddVoucherBill(SqlConnection connection, SqlTransaction transaction, int ObjID, string OrderCode, int? generalCodeID, int idprepaid)
        {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@generalCodeID", generalCodeID);
                p.Add("@idprepaid", idprepaid);

                return connection.Execute(OS6_FPTVoucher_SalePlatForm_AddVoucherBill, p, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
            
        }

        public static void NewUpdateDiscount(SqlConnection connection, SqlTransaction transaction, int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, PNET, PTV, MNET, MTV, Voucher, SalesManID }));
            if (chanelType)
            {
                connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        transaction: transaction,
                        commandType: CommandType.StoredProcedure
                    );
            }
            else
            {
                connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        transaction: transaction,
                        commandType: CommandType.StoredProcedure
                    );
            }
        }
    }
}