using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;
using System.Configuration;
using System.Net;
using System.IO;
using Newtonsoft.Json;
using System.Xml.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using APIMBS.Models;
using APIMBS.Models.MobileSaleV4;

namespace APIMBS.Service
{
    public class FPTClearVoucherServices
    {
        public const string OS6_FPTVoucher_SalesManInfo = "PowerInside.dbo.OS6_FPTVoucher_SalesManInfo";
        public const string OS6_FPTVoucher_FSale = "PowerInside.dbo.OS6_FPTVoucher_FSale";
        public static string RamdoneString()
        {
            Random rd = new Random();
            return rd.Next(1, 10000).ToString();
        }
        public static int GetStatusrVoucherClear(SqlConnection connection, SqlTransaction transaction, string voucherCodeGC, int objid, string regcode)
        {
            int res = connection.Query<int>(OS6_FPTVoucher_FSale, new
            {
                actionName = "GetStatusVoucherClearV2",
                voucherCode = voucherCodeGC,
                objID = objid,
                regCode = regcode
            }, transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return res;
        }
        public static bool checkInforVoucherClear(string voucherCodeGC)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int res = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "checkInforVoucherClearV2",
                    voucherCode = voucherCodeGC,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return (res == 1);
            }
        }
        public static DataBackMoney DataBackMoney(int GeneralCodeID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                DataBackMoney PE = connection.Query<DataBackMoney>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetMoneyInEVC",
                    generalCodeID = GeneralCodeID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return PE;
            }
        }

        public static int UpdateApplyStatus(SqlConnection connection, SqlTransaction transaction, int GeneralCodeID)
        {
            return connection.Execute(OS6_FPTVoucher_FSale, new
            {
                actionName = "UpdateApplyStatus",
                generalCodeID = GeneralCodeID
            }, transaction, commandType: CommandType.StoredProcedure);
        }

        public static int CancelEVCRequest(List<ClearVoucherInput> input, StringBuilder sb)
        {
            try
            {
                var url = Utility.bpapi_fpt_vn + "/api/ins/policy/CancelVoucherRequestOrder";/////

                var jsonData = "{\"OrderCode\": \"" + input.FirstOrDefault().OrderCode + "\"}";

                using (var httpClient = new HttpClient())
                {
                    var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                    var response = httpClient.PostAsync(url, content).Result;

                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = response.Content.ReadAsStringAsync().Result;
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("CancelEVCRequest responseContent", responseContent));
                        return 1;
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("CancelEVCRequest error", e.Message));
                return 2;
            }
            return 0;
        }
    }
}