using APIMBS.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Dapper;
using Voucher.APIHelper;
using System.Data;
using APIMBS.Constant;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;
namespace APIMBS.Service
{
    public class MobiSaleAppService
    {
        public static int GetPrepaidPE(ref PromotionEventRequestV3 input)
        {
            var condition = GetPrepaidConditon(input.NETID, input.LocalType, input.IPTVID);
            // check exit prepaid
            // -100 không có data với ID CLKM nền, 0 Có nhưng trả sau, > 0 trà trước theo số tháng output
            if ((condition.MonthQuantity == -100 && input.IsPrepaidTV == 1) || (condition.PrepaidTime == -100 && input.NETID > 1))
                return 0;

            // convert input
            if (input.NETID == -1) input.IsPrepaidNET = -1;
            if (input.NETID != -1 && input.NETID != 0) input.IsPrepaidNET = condition.PrepaidTime;
            if (input.IsPrepaidTV == 1) input.IsPrepaidTV = condition.MonthQuantity;

            if (condition.DLSType == 2) input.NETServiceCode = 56; else input.NETServiceCode = 47;

            return GetInfoPaidTimeType(input.IsPrepaidNET, input.IsPrepaidTV);
        }
        private static PrepaidConditonSearch GetPrepaidConditon(int NETID, int? LocalType, int IPTVID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<PrepaidConditonSearch>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPrepaidConditon",
                    new { NETID = NETID, IPTVID = IPTVID, LocalType = LocalType },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private static int GetInfoPaidTimeType(int IsPerpaidNET, int IsPerpaidTV)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
                {
                    ActionName = "CheckContaintPaidTimeType",
                    IsPrepaidTV = IsPerpaidTV,
                    IsPrePaidNET = IsPerpaidNET,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        private static string TotalDiscountCLKM(bool CondtionDiscount1, int ValueDiscount1, bool ConditionDiscount2, int ValueDiscount2)
        {
            int value = 0;
            if (CondtionDiscount1) value += ValueDiscount1;
            if (ConditionDiscount2) value += ValueDiscount2;
            return value.ToString();
        }
        private static int GetDismonthValidate(int MonthCLKMVoucher, int MonthCLKMBase)
        {
            return (MonthCLKMVoucher <= MonthCLKMBase) ? MonthCLKMVoucher : MonthCLKMBase;
        }
        
        private static int CheckApplyCLKMTV(int PE, int CLKMTV, Boolean Isprepaid)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@IsPrepaidTV", PE);
                    parameters.Add("@IPTVID", CLKMTV);
                    parameters.Add("@RowAffected", 0);

                    if (Isprepaid) parameters.Add("@ActionName", "GetInfoPrepaidTV");
                    else parameters.Add("@ActionName", "CheckInfoPostPaidTV");
                    res = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, parameters, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return res;
        }

        public static int CheckQuotarSaleman(int saleID, int localTypeID, int PaidTimeTypeID, string voucherCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MobisaleV3, new
                {
                    actionName = "CheckQuotarSalemanMBSv3",
                    localTypeID = localTypeID,
                    paidTimeType = PaidTimeTypeID,
                    voucherCode = voucherCode,
                    saleID=saleID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }            
        }
        public static PromotionEventOutput GetPromotionEventByCondition(int PaidTimeTypeID, int LocalType, string EvenCode)
        {
            
            Promotion pro = new Promotion();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    pro = connection.Query<Promotion>(ConstantAPI.OS6_FPTVoucher_MobisaleV3, new
                    {
                        actionName = "GetPromotionGC",
                        localTypeID = LocalType,
                        paidTimeType = PaidTimeTypeID,
                        voucherCode = EvenCode
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    pro.Services = connection.Query<ServicePromotion>(ConstantAPI.OS6_FPTVoucher_MobisaleV3, new
                    {
                        actionName = "getConditionEVC",
                        localTypeID = LocalType,
                        paidTimeType = PaidTimeTypeID,
                        voucherCode = EvenCode
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();                    
                }
            }
            PromotionEventOutput res = new PromotionEventOutput { Data = pro };
            return res;
        }

        public static Boolean checCodeRF(string vouchercode, string log)
        {
            int flag = 0;
            try
            {
                //kiểm tra loại
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    flag = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MobisaleV3, new
                    {
                        ActionName = "checCodeRF",
                        voucherCode = vouchercode,
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    L.Mes(Level.INFO, flag.ToString(), string.Concat("checCodeRF ", log));
                    return (flag == 1);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), string.Concat("checkMyFPTCode ", log));
                return false;
            }
        }
        public static PromotionEventOutput GetPromotionEventRF(int PaidTimeTypeID, int LocalType, string EvenCode)
        {

            Promotion pro = new Promotion();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();

                pro = connection.Query<Promotion>(ConstantAPI.OS6_FPTVoucher_MobisaleV3, new
                {
                    actionName = "GetPromotionRF",
                    //localTypeID = LocalType,
                    paidTimeType = PaidTimeTypeID,
                    //voucherCode = EvenCode
                },  commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                pro.Services = connection.Query<ServicePromotion>(ConstantAPI.OS6_FPTVoucher_MobisaleV3, new
                {
                    actionName = "getConditionRF",
                    localTypeID = LocalType,
                    paidTimeType = PaidTimeTypeID,
                    voucherCode = pro.VoucherCode
                },  commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
            PromotionEventOutput res = new PromotionEventOutput { Data = pro };
            return res;
        }

        public static PromotionEventOutput GetPromotionEventVoucherPrivate(int PaidTimeTypeID, int LocalType, string EvenCode)
        {

            Promotion pro = new Promotion();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();

                pro = connection.Query<Promotion>(ConstantAPI.OS6_FPTVoucher_MobisaleV3, new
                {
                    actionName = "GetPromotionnPrivateCode",
                    paidTimeType = PaidTimeTypeID,
                    voucherCode = EvenCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                pro.Services = connection.Query<ServicePromotion>(ConstantAPI.OS6_FPTVoucher_MobisaleV3, new
                {
                    actionName = "getConditionEVC",
                    localTypeID = LocalType,
                    paidTimeType = PaidTimeTypeID,
                    voucherCode = pro.VoucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
            PromotionEventOutput res = new PromotionEventOutput { Data = pro };
            return res;
        }
    }
}