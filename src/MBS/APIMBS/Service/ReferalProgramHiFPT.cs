using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using APIMBS.Models;
using APIMBS.Models.MobileSaleV4;
using Voucher.APIHelper.Util;
using APIMBS.Constant;
using System.Xml.Linq;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;

namespace APIMBS.Service
{
    public class ReferalProgramHiFPT
    {
        public static FoxGold GetEventCodeLoy(SqlConnection connection, SqlTransaction transaction, int objInvited)
        {
            return connection.Query<FoxGold>("PowerInside.dbo.OS6_ReferralProgram_SucceedInvite", new
            {
                ActionName = "GetEventLoyaltyInfo",
                ObjIDInvited = objInvited
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static FoxGold GetEventCodeLoyCamera(SqlConnection connection, SqlTransaction transaction)
        {
            return connection.Query<FoxGold>(ConstantAPI.OS6_ReferralProgram_DKOLV5, new
            {
                ActionName = "GetFoxGoldCAM"
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static int CheckRuleIBB(int saleID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_Foxpro_MBS", new
                {
                    ActionName = "CheckRuleIBB",
                    @saleID = saleID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static UserInfo GetInforUser(string voucherCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<UserInfo>(ConstantAPI.OS6_ReferralProgram_InviteCode, new
                {
                    ActionName = "GetInfoUserByInviteCode",
                    InviteCode = voucherCode,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static bool RedeemInviteCodeNetTV(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId, SalesManInfo Salesman, 
            int idPrepaidTime, int channel, int localtype, string regCode, string contractSuport, int objidGT, Guid logId, StringBuilder sb)
        {
            int generalCodeID = 0;
            bool res = false;
            List<PEDiscountModel> rpDiscountUpdate = new List<PEDiscountModel>();
            try
            {
                // get ObjOfUserInvite
                int objGT = MBSv4RedeemVoucherAdvocacyProgram.GetOBJid(connection, transaction, voucherCode);
                if (objGT == 0) return false;
                //GetObjInvite(connection, transaction, ref input);

                // convert Rp to voucher code
                // query with index = 0 is discount for invited user
                // query with index = 1 is discount for invite user
                rpDiscountUpdate = MBSv4RedeemVoucherAdvocacyProgram.GetPEREF(connection, transaction, voucherCode, idPrepaidTime, MBSv4RedeemVoucherAdvocacyProgram.BuildPackageType(prepaid_net_tv), objGT);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign rpDiscountUpdate", rpDiscountUpdate));
                if (rpDiscountUpdate.Count != 3) return false;

                FoxGold EventCodeLoy = new FoxGold() { ID = rpDiscountUpdate[0].ID, EventCode = rpDiscountUpdate[0].EventCode };
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign eventCodeLoy", EventCodeLoy));

                var request = new List<RPCodeRedeemModel>{
                                new RPCodeRedeemModel
                                {
                                    Location = Salesman != null ? Salesman.LOCATION : 0,
                                    BranchCode = Salesman != null ? Salesman.BRANCHCODE : 0,
                                    Department = Salesman != null ? Salesman.DEPARTMENT : 0,
                                    PromotionEventID = rpDiscountUpdate[1].ID, 
                                    VoucherCode = rpDiscountUpdate[1].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objid,
                                    ActiveChannel = 222,
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman != null ? Salesman.SubCompanyID : 0,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                },
                                new RPCodeRedeemModel
                                {
                                    Location = Salesman != null ? Salesman.LOCATION : 0,
                                    BranchCode = Salesman != null ? Salesman.BRANCHCODE : 0,
                                    Department = Salesman != null ? Salesman.DEPARTMENT : 0,
                                    PromotionEventID = rpDiscountUpdate[2].ID, 
                                    VoucherCode = rpDiscountUpdate[2].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objGT,
                                    ActiveChannel = 200, 
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman != null ? Salesman.SubCompanyID : 0,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                }
                            };
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign RPCodeRedeemModel", request));

                // add GC code cho người đăng ký
                var inforVoucherInvited = new List<RPCodeRedeemModel> { 
                new RPCodeRedeemModel
                                {
                                    Location = Salesman != null ? Salesman.LOCATION : 0,
                                    BranchCode = Salesman != null ? Salesman.BRANCHCODE : 0,
                                    Department = Salesman != null ? Salesman.DEPARTMENT : 0,
                                    PromotionEventID = rpDiscountUpdate[1].ID, 
                                    VoucherCode = rpDiscountUpdate[1].EventCode, 
                                    OrderCode =regCode, 
                                    Salesman = saleId, 
                                    ObjID = objid,
                                    ActiveChannel = 222,
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman != null ? Salesman.SubCompanyID : 0,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                },
                };
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign inforVoucherInvited", inforVoucherInvited));

                var inforVoucherInvite = new List<RPCodeRedeemModel> { 
                                new RPCodeRedeemModel
                                {
                                    Location = Salesman != null ? Salesman.LOCATION : 0,
                                    BranchCode = Salesman != null ? Salesman.BRANCHCODE : 0,
                                    Department = Salesman != null ? Salesman.DEPARTMENT : 0,
                                    PromotionEventID = rpDiscountUpdate[2].ID, 
                                    VoucherCode = rpDiscountUpdate[2].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objGT,
                                    ActiveChannel = 200, 
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman != null ? Salesman.SubCompanyID : 0,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                }
                };
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign inforVoucherInvite", inforVoucherInvite));

                // add success invite
                bool isAddSuccess = MBSv4RedeemVoucherAdvocacyProgram.InsertInviteCodeSuccess(connection, transaction, request, EventCodeLoy.EventCode, voucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign rpDiscountUpdate", rpDiscountUpdate));

                if (!isAddSuccess) return false;

                // add GC code cho người đăng ký
                generalCodeID = MBSv4RedeemVoucherAdvocacyProgram.RedeemRPCodeMBS(connection, transaction, inforVoucherInvited, voucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign generalCodeID", generalCodeID));

                // add GC code cho người giới thiệu
                var addGCcodeInitve = MBSv4RedeemVoucherAdvocacyProgram.RedeemRPCodeMBS(connection, transaction, inforVoucherInvite, voucherCode, EventCodeLoy);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign addGCcodeInitve", addGCcodeInitve));

                if (generalCodeID <= 0) return false;
                else res = (generalCodeID > 0);

                // update status
                MBSv4RedeemVoucherAdvocacyProgram.UpdateStatusRPCode(connection, transaction, voucherCode);


                // add voucher TemRedem cho objid đăng ký
                MBSv4CommonService.NewUpdateDiscount(
                    connection,
                    transaction,
                    objid,  // objID user invited
                    rpDiscountUpdate[1].NetPromotionID, //XđồngY tháng NET + Tháng
                    rpDiscountUpdate[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    rpDiscountUpdate[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    rpDiscountUpdate[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    rpDiscountUpdate[1].EventCode, // Code
                    objid, false //objID user invited -TypeVC:True.
                );


                // add voucher TemRedem cho objid Giới thiệu
                MBSv4CommonService.NewUpdateDiscount(
                    connection,
                    transaction,
                    objGT,  // objID user invite
                    rpDiscountUpdate[2].NetPromotionID, //XđồngY tháng NET + Tháng
                    rpDiscountUpdate[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    rpDiscountUpdate[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    rpDiscountUpdate[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    rpDiscountUpdate[2].EventCode, // Code
                    objid, false //objID user invited -TypeVC:True
                );

                //MBSv4CommonService.NewAddVoucherBill(connection, transaction, objid, regCode, generalCodeID, idPrepaidTime);
                return true;
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign Error", ex.Message));
                return false;
            }
            return res;
        }

        public static bool RedeemInviteCodeOwnerCamera(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId, SalesManInfo Salesman, int idPrepaidTime, int channel, int localtype, string regCode,int objidGT, Guid logId, StringBuilder sb)
        {
            try
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera for reg code", regCode));
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera objidGT", objidGT));

                List<ItemRequest> lstItem = new List<ItemRequest> { new ItemRequest { ObjecInvite = objidGT, Objectinvited = objid, RegCode = regCode, VoucherCode = voucherCode } };
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera lstItem", lstItem));

                List<InfoRP> InviteInfo = new List<InfoRP>();

                InviteInfo = connection.Query<InfoRP>(ConstantAPI.OS6_ReferralProgram_Camera, new
                {
                    ActionName = "GetPEREF",
                    PackageTypeInvited = MBSv4RedeemVoucherAdvocacyProgram.BuildPackageType(prepaid_net_tv),
                    PaidTimeType = idPrepaidTime
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera InviteInfo", InviteInfo));

                if (InviteInfo.Count() != 3)
                {
                    return false;
                }

                int InsertInviteSuccess = connection.Query<int>(ConstantAPI.OS6_ReferralProgram_Camera, new
                {
                    ActionName = "InsertInfoRedeemRPCode",
                    XML = CreateXMLRedeemOwnerCamera(lstItem, InviteInfo[2].EventCode, InviteInfo[1].EventCode, InviteInfo[0].EventCode)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera InsertInviteSuccess", InsertInviteSuccess));

                if (InsertInviteSuccess != lstItem.Count()) return false;
                MBSv4RedeemVoucherAdvocacyProgram.UpdateStatusRPCode(connection, transaction, voucherCode);

                List<GeneralCodeInsert> stsAddGeneralCode = InsertGeneralCodeRPgetGCID(connection, transaction, lstItem, InviteInfo, prepaid_net_tv);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera stsAddGeneralCode", stsAddGeneralCode));

                if (stsAddGeneralCode.Count() != 2) return false;

                // update Discount RpCode User Invited
                MBSv4CommonService.NewUpdateDiscount(
                     connection,
                     transaction,
                     lstItem[0].Objectinvited,  // objID khách hàng
                     InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                     InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                     InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                     InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                     InviteInfo[1].EventCode, // Code
                     lstItem[0].Objectinvited, false); //Object Invite -TypeVC:True   

                //MBSv4CommonService.NewAddVoucherBill(connection, transaction, objid, regCode, stsAddGeneralCode[1].id, idPrepaidTime);
                return true;
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera Error", ex.Message));
                return false;
            }
        }
        public static List<GeneralCodeInsert> InsertGeneralCodeRPgetGCID(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int> prepaid_net_tv)
        {
            List<GeneralCodeInsert> lst = new List<GeneralCodeInsert>();
            for (int i = 1; i < InviteInfo.Count; i++)
            {
                GeneralCodeInsert gc = new GeneralCodeInsert();
                gc.eventCode = InviteInfo[i].EventCode;
                gc.id = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_GeneralCode, new
                {
                    ActionName = "InsertGeneralCodeXMLGCID",
                    XML = CreateXMLUpdateGeneralCodeRPbyGC(input, InviteInfo[i], prepaid_net_tv, i),
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                lst.Add(gc);
            }
            return lst;
        }
        public static XElement CreateXMLUpdateGeneralCodeRPbyGC(List<ItemRequest> input, InfoRP promotionEvent, Tuple<int, int> prepaid_net_tv, int i)
        {
            if (i.Equals(0))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // Mã loy cho người giới thiệu status = 100
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));
                return xmlString;
            }
            if (i.Equals(1))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", prepaid_net_tv.Item2),
                            new XElement("Ac", 111)));
                return xmlString;
            }
            if (i.Equals(2))
            {
                var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent.EventCode),  // nguoi giới thiệu
                                        new XElement("P", promotionEvent.ID),
                                        new XElement("Or", input[0].RegCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
                return xmlString;
            }
            return null;
        }
        public static XElement CreateXMLRedeemOwnerCamera(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.RegCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite),// object của người giới thiệu
                           new XElement("TypeRF", "CAMERA")
                       ));
            return xmlString;
        }
        public static Tuple<int,int> GetObjIdbyInviteCode(SqlConnection connection, SqlTransaction transaction, string voucherCode)
        {
            return connection.Query<Tuple<int, int>>(FriendSellServices.OS6_FPTVoucher_AdvocacyProgram, new
            {
                actionName = "GetObjIDGT",
                voucherCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static InfoRFCAM GetEventCamGT(SqlConnection connection, SqlTransaction transaction, string storeProducer, object parrams)
        {
            return connection.Query<InfoRFCAM>(storeProducer, parrams, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 1);
        }
        
        public static bool IsOwnerCamera(SqlConnection connection, SqlTransaction transaction, long objId, string logId)
        {
            try
            {
                var contract = connection.Query<string>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                    {
                        ActionName = "GetContract",
                        ObjID = objId
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure)
                    .FirstOrDefault();
                
                using (var client = new HttpClient())
                {
                    var url = Utility.camera_api;
                    client.BaseAddress = new Uri(url);
                    var data = new StringContent(JsonConvert.SerializeObject(new {contract}), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/API/Info/CheckUsingCamera", data).Result.Content.ReadAsAsync<CheckUsingCameraResponse>();
                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + JsonConvert.SerializeObject(response), "CheckOwnerCameraResponse");
                    var result = response.Result;
                    return result.StatusCode == 1 && result.Data.IsUsingCamOnly;
                }
            }
            catch (Exception e)
            {
                L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + JsonConvert.SerializeObject(e.Message), "CheckOwnerCameraError");
                return false;
            }
        }
        
        public static bool AdvocacyCamera(SqlConnection connection, SqlTransaction transaction, string contractGT, int objid, string voucherCode, string orderCode, List<DeviceModel> devices, string regcode, Guid logId, StringBuilder sb)
        {
            bool res = false;
            try
            {
                #region Hop dong co CAM
                //int objidSupport = FriendSellServices.GetObjIdSupport(connection, transaction, voucherCode, contractGT);
                Tuple<int, int> objidGT = GetObjIdbyInviteCode(connection, transaction, voucherCode);
                MBSv4CommonService.WriteToLog(objidGT, " AdvocacyCamera objidGT", logId);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AdvocacyCamera objidGT", objidGT));

                if (objidGT== null) return false;
                List<DeviceModel> cameras = connection.Query<DeviceModel>(FriendSellServices.OS6_FPTVoucher_AdvocacyProgram, new
                {
                    actionName = "GetHistoryTemInfoCam",
                    xml = FriendSellServices.CreateXMLDevice(voucherCode, orderCode, devices)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AdvocacyCamera cameras", cameras));
                if (cameras.Count > 2 || cameras.Count != devices.Count) return false;

                List<InfoRFCAM> listRFcam = new List<InfoRFCAM>();
                InfoRFCAM camGT = new InfoRFCAM();
                // lấy ưu đãi cho người GT, người đăng ký hộ, chủ hợp đồng camera
                var isOwnerCamera = IsOwnerCamera(connection, transaction, objidGT.Item1, logId.ToString());
                if (isOwnerCamera)
                {//chủ hợp đồng camera
                    var parrams = new {
                        ActionName = "GetPECAM",
                        InviteCode = voucherCode,
                        PackageTypeInvited = "COMBO"
                    };
                    camGT = GetEventCamGT(connection, transaction, "PowerInside.dbo.OS6_ReferralProgram_Camera", parrams);
                }
                else
                {// lấy ưu đãi cho người GT, người đăng ký hộ
                    var parrams = new
                    {
                        ActionName = "GetCameraPE",
                        InviteCode = voucherCode,
                        Quantity = 0,
                        ServiceCode = 0,
                        ObjID = objidGT.Item1
                    };
                    camGT = GetEventCamGT(connection, transaction, ConstantAPI.OS6_ReferralProgram_DKOLV5, parrams);
                }
                

                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AdvocacyCamera objidGT", objidGT));

                int insertTemRedeem = connection.Execute(FriendSellServices.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "AddTemRedeemInvite",
                    vdan = camGT.ID1,
                    objid = objidGT.Item1,
                    voucherCode = camGT.EventCode,
                    AddBy = objid
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AdvocacyCamera insertTemRedeem", insertTemRedeem));

                if (camGT == null || camGT.QuotaGeneralCode < 1) return false;
                listRFcam.Add(new InfoRFCAM { EventCode = camGT.EventCode, ID = camGT.ID, InviteType = camGT.InviteType, QuotaGeneralCode = camGT.QuotaGeneralCode });
                EventCamera ec = new EventCamera();
                foreach (var camera in cameras)
                {
                    var camDK = connection.Query<InfoRFCAM>(ConstantAPI.OS6_FPTVoucher_SalePlatform_GetPromotionReferral, new
                    {
                        quantity = camera.qty,
                        camera.serviceCode,
                        camera.deviceId,
                        subServiceTypeId = camera.subServiceTypeID,
                        camera.serviceId
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 5);
                    if (camera.serviceCode == 322) ec.EventCodeCamIn = camDK.EventCode;
                    if (camera.serviceCode == 323 || camera.serviceCode == 443 || camera.serviceCode == 535 || camera.serviceCode == 536) ec.EventCodeCamOut = camDK.EventCode;
                    listRFcam.Add(new InfoRFCAM { EventCode = camDK.EventCode, ID = camDK.ID, InviteType = camDK.InviteType, QuotaGeneralCode = camDK.QuotaGeneralCode });
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AdvocacyCamera listRFcam", listRFcam));

                if (ec == null) return false;
                FoxGold EventCodeLoy = GetEventCodeLoyCamera(connection, transaction);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AdvocacyCamera EventCodeLoy", EventCodeLoy));

                List<ItemRequest> lstItem = new List<ItemRequest> { new ItemRequest { ObjecInvite = objidGT.Item1, Objectinvited = objid, RegCode = regcode, VoucherCode = voucherCode } };
                int InsertInviteSuccess = connection.Query<int>(FriendSellServices.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "InsertInfoRedeemRPCodeCAM",
                    XML = FriendSellServices.CreateXMLRedeemCAM(lstItem, ec, camGT.EventCode, contractGT, EventCodeLoy.EventCode)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AdvocacyCamera InsertInviteSuccess", InsertInviteSuccess));

                if (InsertInviteSuccess != 1) return false;

                MBSv4RedeemVoucherAdvocacyProgram.UpdateStatusRPCode(connection, transaction, voucherCode);
                Boolean stsAddGeneralCode = InsertGeneralCodeRPCAM(connection, transaction, lstItem, listRFcam);

                if (!stsAddGeneralCode) return false;
                res = true;
                #endregion
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AdvocacyCamera Error", ex.Message));
                res = false;
            }
            return res;
        }
        public static Boolean InsertGeneralCodeRPCAM(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRFCAM> camrf)
        {
            // camrf[0] : người đăng ký
            // camrf[1] : người giới thiệu
            int InsertGeneralCode = connection.Query<int>(FriendSellServices.OS6_FPTVoucher_GeneralCode, new
            {
                ActionName = "InsertGeneralCodeXML",
                XML = FriendSellServices.CreateXMLUpdateGeneralCodeRPCAM(input, camrf),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // check row Affected
            return (InsertGeneralCode <= 3);
        }
    }
}