using APIMBS.Models.MobileSaleV4;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;
using System.Xml.Linq;
using APIMBS.Constant;
using System.Text;

namespace APIMBS.Service
{
    public class OttReferalProgramService
    {
        public const string OS6_FPTVoucher_OTTReferralProgram = "PowerInside.dbo.OS6_FPTVoucher_OTTReferralProgram";
        public static bool checkOTTCode(string vouchercode)
        {
            try
            {
                // string url = Utility.apiOtt + "/api/v1/isc/referral/";
                // OTTReferalProgram ott = callapi_CheckOTT(string.Concat(url, vouchercode));
                // L.Mes(Level.ERROR, JsonConvert.SerializeObject(ott), "OTT - checkOTTCode");
                // if (ott.status == 1)
                // {
                //     return (ott.data.valid == 1);
                // }
                // return false;
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    var status = connection.Query<int>(ConstantAPI.OSU6_ReferralProgram_FptPlay, new
                    {
                        ActionName = "CheckValidInviteCode",
                        InviteCode = vouchercode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (status == 1)
                    {
                        return true;
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "OTT - checkOTTCode");
                return false;
            }
        }
        public static FptPlayClientModel GetInforClient(string voucher, string log, string contentType = "application/json; charset=utf-8")
        {

            try
            {
                // var url = Utility.apiOtt + "/api/v1/isc/user/" + clientID;
                // HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(url);
                // var headers = wr.Headers;
                // headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
                // wr.Method = "GET";
                // // timeout
                // wr.Timeout = 10000;
                // var httpResponse = (HttpWebResponse)wr.GetResponse();
                // using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                // {
                //     var rs = streamReader.ReadToEnd();
                //
                //     // log
                //     WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", log, DateTime.Now) + "GetInforClient OTT " + JsonConvert.SerializeObject(rs));
                //
                //     //OTTReferalProgram ott = new OTTReferalProgram { data = null, message = "Fail", status = 0 };
                //     var ott = JsonConvert.DeserializeObject<OTTUserOutput>(rs);
                //     if (ott.status == 1)
                //     {
                //         L.Mes(Level.ERROR, clientID, "GetInforClient clientOTT: " + JsonConvert.SerializeObject(ott));
                //         return ott.data.fullname;
                //     }
                //     else return "";
                // }
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    var result = connection.Query<FptPlayClientModel>(ConstantAPI.OSU6_ReferralProgram_FptPlay, new
                    {
                        ActionName = "GetClientInformation",
                        InviteCode = voucher
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    return result;
                }
            }
            catch (Exception e)
            {
                L.Mes(Level.ERROR, e.ToString(), "GetInforClient clientOTT: " + JsonConvert.SerializeObject(voucher));
                return null;
            }
        }
        public static OTTReferalProgram callapi_CheckOTT(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                OTTReferalProgram ott = new OTTReferalProgram { data = null, message = "Fail", status = 0 };
                ott = JsonConvert.DeserializeObject<OTTReferalProgram>(rs);
                return ott;
            }
        }
        private static XElement CreateXMLUpdateGeneralCodeOTT(string regCode, int objInvited, List<InfoRP> promotionEvent, int paidTimeTypeID, string ottClient)
        {
            var xmlString = new XElement("N",
                promotionEvent.Select((info, index) => new XElement("I",
                    new XElement("C", info.EventCode), // nguoi được giới thiệu
                    new XElement("P", info.ID),
                    new XElement("Or", regCode),
                    new XElement("L", 0),
                    new XElement("D", 0),
                    new XElement("S", 0),
                    new XElement("O", index == 0 ? objInvited : int.Parse(ottClient)),
                    new XElement("Ac", 111),
                    new XElement("BNET", 0),
                    new XElement("BTV", 0),
                    new XElement("IsPrepaidTV", 0)
                )));
            
            return xmlString;
        }
        public static int? InsertGeneralCodeOTT(SqlConnection connection, SqlTransaction transaction, string regCode, int objInvited, List<InfoRP> InviteInfo, int paidTimeTypeID, string ottClient)
        {
            try
            {
                var GeneralCodeIDs = connection.Query<int>(OS6_FPTVoucher_OTTReferralProgram, new
                {
                    actionName = "InsertGeneralCodeXMLV2",
                    XML = CreateXMLUpdateGeneralCodeOTT(regCode, objInvited, InviteInfo, paidTimeTypeID, ottClient)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                return GeneralCodeIDs.FirstOrDefault();
            }
            catch (Exception ex)
            {
                return 0;
            }
        }
        private static int InsertSuccessCodeOTT(SqlConnection connection, SqlTransaction transaction,
            string generalCodeInvited, string generalCodeClient, int objidinvited, string ottClient)
        {
            var result = connection.Execute(ConstantAPI.OSU6_ReferralProgram_FptPlay, new
            {
                ActionName = "InsertSuccessInvite",
                OttClient = ottClient,
                ObjIdInvited = objidinvited,
                GeneralCodeInvited = generalCodeInvited,
                GeneralCodeClient = generalCodeClient
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

            return result;
        }
        public static bool RedeemRFOTT(SqlConnection connection, SqlTransaction transaction, int objid, string orderCode, int localtype, int saleid, string vouchercode, int paidTimeTypeID, string regCode, string keylog, StringBuilder sb)
        {
            try
            {
                var inviteInfo = connection.Query<InfoRP>(ConstantAPI.OSU6_ReferralProgram_FptPlay, new
                {
                    ActionName = "GetPromotionEvent",
                    PaidTimeTypeId = paidTimeTypeID
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                if (inviteInfo.Count < 2)
                {
                    return false;
                }
                FptPlayClientModel clientModel = GetInforClient(vouchercode, keylog);

                int? generalCodeId = InsertGeneralCodeOTT(connection, transaction, regCode, objid, inviteInfo, paidTimeTypeID, clientModel.OttClient);
                int insert = InsertSuccessCodeOTT(connection, transaction, inviteInfo[0].EventCode, inviteInfo[1].EventCode,objid, clientModel.OttClient);

                if (generalCodeId == null || generalCodeId == 0) return false;

                if (generalCodeId > 0)
                {
                    var PEInfo = inviteInfo[0];
                    MBSv4CommonService.NewUpdateDiscount(
                    connection,
                    transaction,
                    objid,  // objID khách hàng
                    PEInfo.NetPromotionID, //XđồngY tháng NET + Tháng
                    PEInfo.IPTVPromotionID, //XđồngY tháng TV + Tháng
                    PEInfo.MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    PEInfo.MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    PEInfo.EventCode, // Code
                    0, false); //Object Invite -TypeVC:True  

                    sb.AppendLine("Call Voucher Bill");

                    // add voucher bill invited
                    MBSv4CommonService.NewAddVoucherBill(connection, transaction, objid, regCode, generalCodeId, paidTimeTypeID);
                    sb.AppendLine("RedeemReferalOTTCode");

                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemRFMyFPT", ex.Message));
                return false;
            }
        }
    }
}