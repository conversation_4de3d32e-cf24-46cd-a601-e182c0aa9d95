using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Dapper;
using System.Data;
using Voucher.APIHelper;
using System.Xml.Linq;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models;
using APIMBS.Constant;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using Voucher.APIHelper.Util;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Configuration;
using System.Text;

namespace APIMBS.Service
{
    public class MBSv4RedeemVoucherGC
    {
        #region redeem Voucher GC
        public const string OS6_FPTVoucher_SalePlatform_MBSv4 = "PowerInside.dbo.OS6_FPTVoucher_SalePlatform_MBSv4";
        public const string OS6_FPTVoucher_SalesManInfo = "PowerInside.dbo.OS6_FPTVoucher_SalesManInfo";
        public static bool RedeemVoucherGC(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId, SalesManInfo Salesman, 
            int localtype, string regCode,int channel,int locationId,Guid logId, StringBuilder sb,UserBranchPlatformModel userBranch =null)
        {
            int generalCodeID = 0;
            bool res = false;
            try
            {
                PEDiscountModel pdm = new PEDiscountModel();
                pdm = GetPE(connection, transaction, voucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC pdm", pdm));

                if (pdm == null) return false;

                int IDPaidTimeType = GetInfoPaidTimeType(prepaid_net_tv.Item1, prepaid_net_tv.Item2);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC IDPaidTimeType", IDPaidTimeType));

                int status = 0;
                if (channel.Equals(0))
                {
                    if (Salesman == null) return false;
                    var parrams = new
                    {
                        #region
                        ActionName = "RedeemVoucherGeneralCode",
                        EventCode = voucherCode,
                        PromotionEventID = pdm.ID,
                        LocationID = Salesman.LOCATION,
                        DepartmentID = Salesman.DEPARTMENT,
                        SaleID = saleId,
                        ObjID = objid,
                        OrderCode = regCode,
                        ActiveChannel = 1,
                        BNET = 0,
                        BTV = 0,
                        IsPrepaidTV = 0,
                        LocalTypeID = localtype,
                        PaidTimeTypeID = IDPaidTimeType,
                        RowAffected = 0,
                        #endregion
                    };
                    status = AddGCCode(connection, transaction, parrams, ConstantAPI.OS6_FPTVoucher_GeneralVoucher);
                }
                if (channel.Equals(1))
                {
                    var dataXElement = new
                    {
                        Code = voucherCode,
                        PromotionEventID = pdm.ID,
                        OrderCode = regCode,
                        Source = userBranch.sourceId,
                        SubCompany = userBranch.subcompanyId,
                        LocationID = locationId,
                        BranchCode = userBranch.branchCode,
                        DepartmentID = userBranch.departmentId,
                        SaleID = saleId,
                        ObjID = objid,
                        LocalTypeID = localtype,
                        PaidTimeTypeID = IDPaidTimeType
                    };
                    

                    var parrams = new
                    {
                        ActionName = "InsertGCeRegisterWeb",
                        XML = CreateElementGCCode(dataXElement)
                    };
                    status = AddGCCode(connection, transaction, parrams, ConstantAPI.OS6_FPTVoucher_SPF_RegisterWeb);
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC status", status));

                if (status == 0)
                    return false;
                if (status > 0)
                {
                    generalCodeID = status;
                    res = true;
                    #region voucher thuong
                    if (pdm.NetPromotionID > 0)
                    {
                        int updVc = MBSv4CommonService.UpdateDiscountNet(
                            objid,  // objID khách hàng
                            pdm.NetPromotionID, //XđồngY tháng NET + Tháng
                            pdm.IPTVPromotionID, //XđồngY tháng TV + Tháng
                            pdm.MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                            pdm.MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                            pdm.EventCode, // Code
                            saleId //Sales -TypeVC:True
                        );
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC updVc", updVc));

                        if (pdm.MoneyPromotionTVID > 0 || pdm.IPTVPromotionID >0)
                        {
                            In4VoucherTHmodel voucherTh = new In4VoucherTHmodel();
                            
                            voucherTh = MBSv4CommonService.GetIn4VoucherTH(connection, transaction, objid, pdm.MoneyPromotionTVID);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC voucherTh", voucherTh));

                            // lay thong tin clkm iptv
                            int clkmiptv = MBSv4CommonService.GetCLKMiptv(connection, transaction, regCode, objid, prepaid_net_tv.Item2);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC clkmiptv", clkmiptv));

                            int updateTH = MBSv4CommonService.UpdateDiscountTH(voucherCode, objid, voucherTh.locationId, clkmiptv, pdm.IPTVPromotionID, voucherTh.amountTv, voucherTh.addBy, logId);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC updateTH", updateTH));
                        }                        
                    }
                    else
                    {
                        // tang thang
                        int isEvenMonth = 0;
                        if (pdm.ContainerID > 0)
                        {
                            // lay clkm nen
                            var monthPromotion = GetMonthPromotion(connection,transaction, objid);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC monthPromotion", monthPromotion));

                            if (monthPromotion != null)
                            {
                                // thang chan le
                                isEvenMonth = monthPromotion.EventMonth;
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC isEvenMonth", isEvenMonth));

                                int monthAdd = monthPromotion.GetAddMonth();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC monthAdd", monthAdd));

                                // lay promotion net theo thang
                                int promotionNetID = GetPromotionNetByContainerID(connection, transaction, pdm.ContainerID, monthAdd, isEvenMonth);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC promotionNetID", promotionNetID));

                                if (promotionNetID > 0)
                                    pdm.PromotionNetID = promotionNetID;
                            }
                        }

                        var datalog = new
                        {
                            ObjID = objid,
                            PromotionNetID = pdm.PromotionNetID,
                            IPTVPromotionID = pdm.IPTVPromotionID,
                            MoneyPromotionNETID = pdm.MoneyPromotionNETID,
                            MoneyPromotionTVID = pdm.MoneyPromotionTVID,
                            EventCode = pdm.EventCode,
                            SaleID = saleId,
                            isEvenMonth = isEvenMonth,
                            generalCodeID = generalCodeID
                        };
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Call AddCustomerDiscountV2", datalog));

                        int addNet = SPFAddCustomerDiscountV2(connection, transaction,
                            objid,
                            pdm.PromotionNetID,
                            pdm.IPTVPromotionID,
                            pdm.MoneyPromotionNETID,
                            pdm.MoneyPromotionTVID,
                            pdm.EventCode,
                            saleId,
                            isEvenMonth,
                            generalCodeID
                        );
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC addNet", addNet));

                        if (pdm.MoneyPromotionTVID > 0 || pdm.IPTVPromotionID > 0)
                        {
                            In4VoucherTHmodel voucherTh = new In4VoucherTHmodel();
                            if (pdm.MoneyPromotionTVID > 0)
                            {
                                voucherTh = MBSv4CommonService.GetIn4VoucherTH(connection,transaction, objid, pdm.MoneyPromotionTVID);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC voucherTh", voucherTh));
                            }
                            int clkmiptv = MBSv4CommonService.GetCLKMiptv(connection, transaction, regCode, objid, prepaid_net_tv.Item2);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC clkmiptv", clkmiptv));

                            int updateTH = MBSv4CommonService.UpdateDiscountTH(voucherCode, objid, voucherTh.locationId, clkmiptv, pdm.IPTVPromotionID, voucherTh.amountTv, voucherTh.addBy, logId);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC updateTH", updateTH));
                        }
                    }
                    
                    int addBill = MBSv4CommonService.NewAddVoucherBill(connection,transaction, objid, regCode, generalCodeID, IDPaidTimeType);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC addBill", addBill));
                    #endregion
                }

            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC Error", ex.Message));
                return false;
            }
            return res;
        }

        private static XElement CreateElementGCCode(dynamic data)
        {
            var xmlString = new XElement("N",
                            new XElement("I",
                            new XElement("Code", data.Code),
                            new XElement("PromotionEventID", data.PromotionEventID),
                            new XElement("OrderCode", data.OrderCode),
                            new XElement("Source", data.Source),
                            new XElement("SubCompany", data.SubCompany),
                            new XElement("LocationID", data.LocationID),
                            new XElement("BranchCode", data.BranchCode),
                            new XElement("DepartmentID", data.DepartmentID),
                            new XElement("SaleID", data.SaleID),
                            new XElement("ObjID", data.ObjID),
                            new XElement("ActiveChannel", 2),
                            new XElement("BNET", 0),
                            new XElement("FreeMonthID", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("LocalTypeID", data.LocalTypeID),
                            new XElement("PaidTimeTypeID", data.PaidTimeTypeID)));
            return xmlString;            
        }
        private static int AddGCCode(SqlConnection connection, SqlTransaction transaction, object parram, string store)
        {
            return connection.QueryFirstOrDefault<int>(store, parram, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }

        private static int SPFAddCustomerDiscountV2(SqlConnection connection, SqlTransaction transaction, int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            return connection.Execute(
                "PowerInside.dbo.OS6_FPTVoucher_SPF_AddCustomerDiscountV2",
                new
                {
                    ObjID = ObjID,
                    PromotionIDNet = PromotionNetID,
                    PromotionIDTV = IPTVPromotionID,
                    MoneyPromotionIDNet = MoneyPromotionNETID,
                    MoneyPromotionIDTV = MoneyPromotionTVID,
                    VoucherCode = VoucherCode,
                    AddBy = SalesID,
                    GeneralCodeID = GeneralCodeID,
                    PrivateCodeID = PrivateCodeID,
                    IsEvenMonth = IsEvenMonth
                },transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure
            );
            
        }
        private static void AddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        private static MonthPromotionModel GetMonthPromotion(SqlConnection connection, SqlTransaction transaction,int ObjID)
        {
            return connection.QueryFirstOrDefault<MonthPromotionModel>(
                "PowerInside.dbo.OS6_FPTVoucher_GetMonthPromotion",
                new { ObjID = ObjID },
                transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure
            );
        }
        private static int GetPromotionNetByContainerID(SqlConnection connection, SqlTransaction transaction, int ContainerID, int AddMonth, int IsEvenMonth = 0)
        {
            return connection.QueryFirstOrDefault<int>(
                "PowerInside.dbo.OS6_FPTVoucher_GetPromotionNetByContainerID",
                new { ContainerID = ContainerID, AddMonth = AddMonth, IsEvenMonth = IsEvenMonth },
                transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure
            );
        }

        private static int GetInfoPaidTimeType(int IsPerpaidNET, int IsPerpaidTV)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
                {
                    ActionName = "CheckContaintPaidTimeType",
                    IsPrepaidTV = IsPerpaidTV,
                    IsPrePaidNET = IsPerpaidNET,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        private static PEDiscountModel GetPE(SqlConnection connection, SqlTransaction transaction, string VoucherCode)
        {
            PEDiscountModel PE = connection.Query<PEDiscountModel>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
            {
                ActionName = "GetPEByCode",
                EventCode = VoucherCode,
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return PE;
        }

        #endregion
        /*
        public static int GetPromotionNetIDbyContainerID(int objid, int containerid)
        {
            var monthPromotion = GetMonthPromotion(objid);
            if (monthPromotion != null)
            {
                // thang chan le
                int isEvenMonth = monthPromotion.EventMonth;
                // lay promotion net theo thang
                int promotionNetID = GetPromotionNetByContainerID(containerid, monthPromotion.GetAddMonth(), isEvenMonth);
                if (promotionNetID > 0)
                   return promotionNetID;
            }
            return 0;
        }
        */
        public static bool RedeemVoucherProduct(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, int saleId, SalesManInfo Salesman, int localtype, string regCode,
            int channel, int locationId, Guid logId, List<ProductPlatform> products, UserBranchPlatformModel userBranch, int customerType, StringBuilder sb)
        {
            try
            {
                int status = 0;
                PEDiscountModel model = GetPE(connection, transaction, voucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC pdm", model));

                if (model == null) return false;
                switch (channel)
                {
                    case 0 when Salesman == null:
                        return false;
                    case 0:
                    {
                        var pars = new
                        {
                            ActionName = "RedeemVoucherGeneralCode",
                            EventCode = voucherCode,
                            PromotionEventID = model.ID,
                            LocationID = Salesman.LOCATION,
                            DepartmentID = Salesman.DEPARTMENT,
                            SaleID = saleId,
                            ObjID = objid,
                            OrderCode = regCode,
                            ActiveChannel = 1,
                            BNET = 0,
                            BTV = 0,
                            IsPrepaidTV = 0,
                            LocalTypeID = localtype,
                            PaidTimeTypeID = 0,
                            RowAffected = 0,
                        };
                        status = AddGCCode(connection, transaction, pars, ConstantAPI.OS6_FPTVoucher_GeneralVoucher);
                        break;
                    }
                    case 1:
                    {
                        var xml = new
                        {
                            Code = voucherCode,
                            PromotionEventID = model.ID,
                            OrderCode = regCode,
                            Source = userBranch.sourceId,
                            SubCompany = userBranch.subcompanyId,
                            LocationID = locationId,
                            BranchCode = userBranch.branchCode,
                            DepartmentID = userBranch.departmentId,
                            SaleID = saleId,
                            ObjID = objid,
                            LocalTypeID = localtype,
                            PaidTimeTypeID = 0
                        };

                        var pars = new
                        {
                            ActionName = "InsertGCeRegisterWeb",
                            XML = CreateElementGCCode(xml)
                        };
                        status = AddGCCode(connection, transaction, pars, ConstantAPI.OS6_FPTVoucher_SPF_RegisterWeb);
                        break;
                    }
                }
                
                //Group sub service of product by quantity
                foreach (var subServiceType in products.SelectMany(product => product.SubServiceTypes))
                {
                    subServiceType.SubServices = subServiceType.SubServices
                        .GroupBy(x => new { x.ServiceCode, x.SubServiceID })
                        .Select(g => new SubServicesPro()
                        {
                            SubServiceID = g.Key.SubServiceID,
                            ServiceCode = g.Key.ServiceCode,
                            Qty = g.Sum(s => s.Qty)
                        }).ToList();
                }
                
                var vouchers = RegisterWebV5.GetDeviceVoucherList(products,logId)
                    .ToList()
                    .Where(x => x.VoucherCode == voucherCode && x.CustomerTypeId == customerType)
                    .ToList();
                
                if (vouchers.Count > 0 && status > 0)
                {
                    foreach (var voucher in vouchers)
                    {
                        var quantity = (from product in products
                            from subServiceType in product.SubServiceTypes
                            from subService in subServiceType.SubServices
                            where product.ServiceID == voucher.ServiceId &&
                                  subServiceType.SubServiceTypeId == voucher.SubServiceTypeId &&
                                  subService.SubServiceID == voucher.SubServiceId &&
                                  subService.ServiceCode == voucher.ServiceCode
                            select subService.Qty).FirstOrDefault();

                        if (quantity > 0)
                        {
                            var pars = new
                            {
                                ActionName = "InsertGeneralCodeDetail",
                                XML = CreateXmlGeneralCodeDetail(status, voucher, quantity, quantity * voucher.Discount)
                            };

                            connection.Execute(ConstantAPI.OS6_FPTVoucher_SalePlatform_Device, pars,
                                transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        }
                    }
                }

                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC status", status));
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC Error", e.Message));
                return false;
            }

            return true;
        }

        private static XElement CreateXmlGeneralCodeDetail(int generalCodeId, EvoucherProduct voucher, int quantity, decimal totalDiscount)
        {
            var xmlString = new XElement("N",
                new XElement("I",
                    new XElement("GeneralCodeId", generalCodeId),
                    new XElement("ServiceId", voucher.ServiceId),
                    new XElement("SubServiceId", voucher.SubServiceId),
                    new XElement("SubServiceTypeId", voucher.SubServiceTypeId),
                    new XElement("ServiceCode", voucher.ServiceCode),
                    new XElement("Quantity", quantity),
                    new XElement("TotalDiscount", totalDiscount)));
            return xmlString;         
        }
        
        public static bool UpdateStatusPrivateCode(SqlConnection connection, SqlTransaction transaction, string voucherCode, int objId)
        {
            var result = connection.Query<int>(ConstantAPI.OSU6_FPTVoucher_PrivateVoucher, new
            {
                Action = "UpdateStatusPrivateCode",
                VoucherCode = voucherCode,
                ObjId = objId
            }, transaction: transaction, commandTimeout: 0, commandType: CommandType.StoredProcedure).FirstOrDefault();

            return result > 0;
        }
    }
}