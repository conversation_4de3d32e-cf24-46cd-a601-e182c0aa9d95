using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using APIMBS.Constant;
using Dapper;
using APIMBS.Models;

namespace APIMBS.Service
{
    public static class SalePlatformPrivateVoucherService
    {
        public static string GetEventCode(string voucherCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var eventCode = connection.Query<string>(ConstantAPI.OSU6_FPTVoucher_PrivateVoucher, new
                {
                    Action = "GetEventCode",
                    VoucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                
                return eventCode;
            }
        }

        public static bool CheckLocationRule(string voucherCode, int locationId)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var configs = connection.Query<ConfigModel>(ConstantAPI.OSU6_FPTVoucher_PrivateVoucher, new
                {
                    Action = "GetEventCodeConfig"
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                var voucherConfig = configs.FirstOrDefault(c => c.Value.Contains(voucherCode));
                if (voucherConfig == null)
                {
                    return false;
                }

                var prefix = voucherConfig.Key.Replace("_event_code", "");                

                var locationRule = connection.Query<string>(ConstantAPI.OSU6_FPTVoucher_PrivateVoucher, new
                {
                    Action = "GetValueVoucherConfig",
                    Key = prefix + "_location_rule"
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                if (!locationRule.Contains(locationId.ToString()) && locationRule != "0")
                {
                    return false;
                }

                return true;
            }
        }

        public static bool CheckPrefix(string voucherCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var prefixStr = connection.Query<string>(ConstantAPI.OSU6_FPTVoucher_PrivateVoucher, new
                {
                    Action = "GetValueVoucherConfig",
                    Key = "prefix_private_code"
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                var prefixArr = prefixStr.Split(',');

                return prefixArr.Any(p => voucherCode.Contains(p));
            }
        }
    }
}