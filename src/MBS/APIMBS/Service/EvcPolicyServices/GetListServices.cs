using APIMBS.Constant;
using APIMBS.Models;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models.SalePlatform;
using Dapper;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Services.Description;
using System.Xml;
using System.Xml.Linq;
using Voucher.APIHelper;
using Voucher.APIHelper.ShareModel;

namespace APIMBS.Service.EvcPolicyServices
{
    public class GetListServices
    {
        public const string OS6_FPTVoucher_VoucherFollowPolicy = "PowerInside.dbo.OS6_FPTVoucher_VoucherFollowPolicy";
        public const string OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend = "PowerInside.dbo.OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend";
        public const string OS6_FPTVoucher_FSale = "PowerInside.dbo.OS6_FPTVoucher_FSale";
        public const string OSU6_FPTVoucher_PolicyVoucher_CheckLocation = "PowerInside.dbo.OSU6_FPTVoucher_PolicyVoucher_CheckLocation";
        public const string OS6_FPTVoucher_CheckQuotaMoney = "PowerInside.dbo.OS6_FPTVoucher_CheckQuotaMoney";
        public const string OS6_FPTVoucher_policy_evc_getlist = "PowerInside.dbo.OS6_FPTVoucher_policy_evc_getlist";
        public static List<Evoucher> GetListEvoucher(SalePolicyListEVC input, StringBuilder sb)
        {
            var lstVoucherFinal = new List<Evoucher>();
            var lstEVC_FromBD = new List<PolicyVoucher>();
            var lstVoucherToCheckLocation = new List<PolicyVoucher>();


            List<CustomerSaleOrderCollections> lstDataOrders = new List<CustomerSaleOrderCollections>();
            try
            {
                #region Chuẩn bị sơ bộ để create XML
                var listOptionService_servicesInput = formatServices(input, "Service");
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listServices", listOptionService_servicesInput));
                if (listOptionService_servicesInput.Count > 0)
                {
                    lstDataOrders.AddRange(listOptionService_servicesInput);
                }
                var listOptionService_productsInput = formatServices(input, "Product");
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProducts", listOptionService_productsInput));
                if (listOptionService_servicesInput.Count > 0)
                {
                    lstDataOrders.AddRange(listOptionService_productsInput);
                }
                var listPolicyIds = lstDataOrders.Select(x => x.policyId).Distinct().ToList();


                #endregion

                var lstEVC_policy = new List<PolicyVoucher>();
                var lstVoucher = new List<PolicyVoucher>();
                var lst = new List<PolicyVoucher>();

                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    #region Kiểm tra ngày vàng
                    var isGoldenDate = checkGoldenDate(connection, string.Join(",", listPolicyIds));
                    if (isGoldenDate.Equals(1))
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Đơn hàng là chính sách ngày vàng", ""));
                        return new List<Evoucher>();
                    }
                    #endregion

                    foreach (var policyId in listPolicyIds)
                    {
                        sb.AppendLine("START---<<<<---" + policyId + "--->>>>---");
                        var list_order_by_policy = lstDataOrders.Where(x => x.policyId == policyId).ToList();

                        var lstEVC = new List<PolicyVoucher>();
                        lstVoucher = new List<PolicyVoucher>();
                        lst = new List<PolicyVoucher>();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_order_by_policy ", list_order_by_policy));
                        #region Lấy voucher từ DB
                        if (list_order_by_policy?.Any() == true)
                        {
                            var xmlService_order = CreateXMLSubService(list_order_by_policy, input.SaleInfor.SaleChannelID);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlService_order ", xmlService_order));

                            lstVoucher = connection.Query<PolicyVoucher>(OS6_FPTVoucher_policy_evc_getlist, new //voucher lấy bao gồm cả service và product
                            {
                                xml = xmlService_order
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstVoucher_Service_FromDB", lstVoucher));


                            if (lstVoucher?.Any() == true)
                            {
                                //var lstVC = (from x in lstVoucher
                                //             select x)
                                //             .OrderByDescending(x => x.subServiceId) //nếu có PHM sẽ ưu tiên lấy subServiceId PHM ..
                                //             .GroupBy(n => new { n.VoucherCode })
                                //             .Select(g => g.FirstOrDefault())
                                //             .ToList();
                                //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstVC", lstVC));

                                var listVCservice = lstVoucher.Where(x => x.promotionTypeId != 3).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listVCservice", listVCservice));

                                if (listVCservice.Count > 0)
                                {
                                    lst.AddRange(listVCservice);
                                }

                                var _subServiceTypeId_all_input = list_order_by_policy.Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                                var _subServiceTypeId_DV = listOptionService_servicesInput.Where(x => x.policyId == policyId).Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                                var _subServiceTypeId_TB = listOptionService_productsInput.Where(x => x.policyId == policyId).Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                                Array.Sort(_subServiceTypeId_all_input);
                                Array.Sort(_subServiceTypeId_DV);
                                Array.Sort(_subServiceTypeId_TB);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_input", _subServiceTypeId_all_input));
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_DV", _subServiceTypeId_DV));
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_TB", _subServiceTypeId_TB));
                                // lấy các voucher phí hòa mạng, tiếp tục kiểm tra voucher hòa mạng thiết bị
                                // promotiontype id=3 là phí hòa mạng
                                // phí hòa mạng có kiểm tra thiết bị sẽ có applyfor =3
                                // phí hòa mạng giảm cho dịch vụ sẽ không cần kiểm tra điều kiện áp dụng thiết bị AplyFor: 1 là dịch vụ, 2 thiết bị, 3 là dịch vụ + thiết bị

                                //var listEVCPHM_product = lstVoucher.Where(x => x.promotionTypeId.Equals(3) && x.applyFor.Equals(3)).ToList(); //  PHM điều kiện dv+tb
                                //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM", listEVCPHM_product));

                                var listEVCPHM_Service = lstVoucher.Where(x => x.promotionTypeId.Equals(3) && (x.applyFor.Equals(1) || x.applyFor.Equals(3))).ToList(); // PHM (dịch vụ || dv+tb)
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM_Service", listEVCPHM_Service));

                                if (listEVCPHM_Service?.Count > 0)
                                {
                                    // lấy những service có PHM 
                                    var order_phm_net = listOptionService_servicesInput.Where(x => x.subServiceId == 999999999).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_net count", order_phm_net.Count));
                                    if (order_phm_net.Count > 0)
                                    {
                                        var data_phm_net = listEVCPHM_Service.Where(x => x.serviceId == 1).ToList();
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_net", data_phm_net));
                                        lst.AddRange(data_phm_net);
                                    }
                                    var order_phm_tv = listOptionService_servicesInput.Where(x => x.subServiceId == 999999996).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_tv count", order_phm_tv));
                                    if (order_phm_tv.Count > 0)
                                    {
                                        var data_phm_tv = listEVCPHM_Service.Where(x => x.serviceId == 7).ToList();
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_tv", data_phm_tv));
                                        lst.AddRange(data_phm_tv);
                                    }

                                }

                                if (lst.Count > 0)
                                {
                                    var data_evc_services = lst.Where(x => x.isService == 1).ToList();
                                    var data_evc_products = lst.Where(x => x.isService == 0).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_services", data_evc_services));
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_products", data_evc_products));

                                    if (data_evc_services.Count > 0)
                                    {
                                        //kiểm tra luôn cả evc thiết bị PHM
                                        var ValidVoucherByCoditionInput = new List<PolicyVoucher>();
                                        ValidVoucherByCoditionInput.AddRange(data_evc_services);
                                        ValidVoucherByCoditionInput.AddRange(data_evc_products.Where(x => x.promotionTypeId == 3).ToList());
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ValidVoucherByCoditionInput", ValidVoucherByCoditionInput));

                                        var data_evc_condition = ValidVoucherByCodition(connection, ValidVoucherByCoditionInput, _subServiceTypeId_all_input, _subServiceTypeId_DV, _subServiceTypeId_TB, list_order_by_policy, sb);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_condition", data_evc_condition));
                                        lstEVC_policy.AddRange(data_evc_condition);
                                    }
                                    if (data_evc_products.Count > 0)
                                    {
                                        foreach (var voucher in data_evc_products)
                                        {
                                            string[] serviceVoucher = voucher.voucherService.Split(',');
                                            if (voucher.applyFor != 3 && serviceVoucher.Any(x => _subServiceTypeId_all_input.Contains(x)) // nếu giảm tiền tb áp dụng cho thiết bị ===> chỉ cần thỏa 1 subService type 
                                                || (voucher.applyFor == 3 && serviceVoucher.All(x => _subServiceTypeId_all_input.Contains(x) && serviceVoucher.Length == _subServiceTypeId_all_input.Length))) //điều kiện áp dụng dv+tb ===> phải đúng hết subservice
                                            {
                                                lstEVC_policy.Add(voucher);
                                            }
                                        }
                                    }


                                }

                            }
                        }

                        #endregion

                        #region Kiểm tra voucher theo tỉnh thành
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("----------Kiểm tra theo tỉnh thành----------", ""));
                        if (lstEVC_policy.Count > 0)
                        {
                            var evc_check_location = ValidVoucherByLocation(connection, lstEVC_policy, input.CustomerInfor.LocationID, input.CustomerInfor.BranchCode, input.CustomerInfor.DistrictID, sb);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evc_check_location", evc_check_location));
                            if (evc_check_location.Count > 0)
                            {
                                lstEVC.AddRange(evc_check_location);
                            }
                        }
                        #endregion
                        #region Kiểm tra Quota hạn mức
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("----------Kiểm tra hạn mức----------", ""));
                        if (lstEVC.Count > 0)
                        {
                            var lst_check_quota = ValidVoucherByQuota(connection, lstEVC, lstVoucher, list_order_by_policy, input.CustomerInfor.LocationID, input.CustomerInfor.BranchCode, input.CustomerInfor.DistrictID, input.SaleInfor.SaleID, sb);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lst_check_quota", lst_check_quota));
                            foreach (var item in lst_check_quota)
                            {
                                lstVoucherFinal.Add(new Evoucher 
                                { 
                                    Description = item.Description, 
                                    Note = item.Note, 
                                    Todate = item.Todate, 
                                    VoucherCode = item.VoucherCode,
                                    RegisterTypeID = item.RegisterTypeID,
                                    PolicyGroupID = item.PolicyGroupID,
                                    PromotionTypeID = item.promotionTypeId,
                                    ApplyTypeID = item.ApplyTypeID
                                });
                            }
                        }
                        #endregion
                        sb.AppendLine("END--->>>>---" + policyId + "---<<<<---");
                    }
                }

            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher Error", ex.Message));
                return null;
            }
            return lstVoucherFinal.Distinct().ToList();
        }

        public static List<PolicyVoucher> ValidVoucherByQuota(SqlConnection connection, List<PolicyVoucher> lstVoucher, List<PolicyVoucher> lstVoucherAll, List<CustomerSaleOrderCollections> lstServices, int locationId, int branchCode, int districtId, int saleId, StringBuilder sb)
        {
            var lstEVC = new List<PolicyVoucher>();
            foreach (var voucher in lstVoucher)
            {
                decimal valueDiscount = 0;
                decimal quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                {
                    VoucherCode = voucher.VoucherCode,
                    SaleId = saleId,
                    LocationId = locationId,
                    BranchCode = branchCode,
                    DistrictId = districtId

                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("quotaMoneyAvailable " + voucher.VoucherCode + " ", quotaMoneyAvailable));

                if (voucher.applyTypeID.Equals(2))
                {
                    var dataVoucher = lstVoucherAll.Where(x => x.VoucherCode == voucher.VoucherCode).ToList();
                    var dataMapVoucher = (from ser in lstServices
                                          join evc in dataVoucher on new { serviceId = ser.serviceId, subServiceTypeId = ser.subServiceTypeId, subServiceId = ser.subServiceId, serviceCode = ser.serviceCode } equals
                                            new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                          select new
                                          {
                                              serviceId = evc.serviceId,
                                              subServiceTypeId = evc.subServiceTypeId,
                                              subServiceId = evc.subServiceId,
                                              serviceCode = evc.serviceCode,
                                              total_vat = ser.total_vat,
                                              monthUsed = ser.monthUsed,
                                              qty = ser.qty,
                                              valueVoucher = evc.valueVoucher
                                          }).ToList();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataMapVoucher " + voucher.VoucherCode + " ", dataMapVoucher));
                    if (voucher.promotionTypeId.Equals(4))//fix code vì id loại voucher ko thể tăng (4 là thặng tháng)
                    {
                        valueDiscount = dataMapVoucher.Count > 0 ? dataMapVoucher.Sum(a => a.total_vat / a.monthUsed) * voucher.duration : 0;
                    }
                    else
                    {
                        //valueDiscount = (Math.Max(voucher.duration, 1) * voucher.valueVoucher);
                        valueDiscount = dataMapVoucher.Sum(x => Math.Max(voucher.duration, 1) * x.valueVoucher * x.qty);
                    }
                }
                else
                {
                    var dataVoucher = GetServices_PHM(voucher.VoucherCode);

                    var dataMapVoucher = (from pro in lstServices
                                          join evc in dataVoucher on new { serviceId = pro.serviceId, subServiceTypeId = pro.subServiceTypeId, subServiceId = pro.subServiceId, serviceCode = pro.serviceCode } equals
                                            new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                          where evc.applyTypeID != 2
                                          select pro).ToList();


                    valueDiscount = dataMapVoucher == null ? 0 : dataMapVoucher.Sum(x => x.qty) * voucher.valueVoucher;
                }

                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("valueDiscount for voucher " + voucher.VoucherCode + " ", valueDiscount));
                if (quotaMoneyAvailable >= valueDiscount && quotaMoneyAvailable > 0 && valueDiscount > 0)
                {
                    lstEVC.Add(voucher);
                }
            }
            return lstEVC;
        }

        public static List<PolicyVoucher> ValidVoucherByLocation(SqlConnection connection, List<PolicyVoucher> lst, int locationId, int branchCode, int districtId, StringBuilder sb)
        {
            var lstEVC = new List<PolicyVoucher>();
            var list_policy = new List<int>();
            list_policy = connection.Query<int>(OSU6_FPTVoucher_PolicyVoucher_CheckLocation, new
            {
                XML = CreateXMLPolicy(lst, sb),
                LocationId = locationId,
                BranchCode = branchCode,
                DistrictId = districtId
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            //1+1 sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list policy id available by location", list_policy));

            if (list_policy.Count > 0)
            {
                var evclist = (from voucher in lst
                               where list_policy.Contains(voucher.programId)
                               select voucher).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list evoucher available by location", evclist));

                if (evclist.Count > 0)
                {
                    lstEVC.AddRange(evclist);
                }
            }
            return lstEVC;
        }
        public static List<PolicyVoucher> ValidVoucherByCodition(SqlConnection connection, List<PolicyVoucher> lst, string[] _serviceOrders_all, string[] _serviceOrders_dv, string[] _serviceOrders_tb, List<CustomerSaleOrderCollections> lstServices, StringBuilder sb)
        {
            var lstEVC_policy = new List<PolicyVoucher>();
            foreach (var voucher in lst)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucher", voucher));

                string[] serviceVoucher = voucher.voucherService.Split(',');
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceVoucher", serviceVoucher));

                // kiểm tra đơn hàng có đủ SubServiceType của evc không
                if ((voucher.applyFor == 3 && serviceVoucher.All(x => _serviceOrders_all.Contains(x) && serviceVoucher.Length == _serviceOrders_all.Length))
                    || (voucher.applyFor == 1 && serviceVoucher.All(x => _serviceOrders_dv.Contains(x) && serviceVoucher.Length == _serviceOrders_dv.Length))
                    || (voucher.applyFor == 2 && serviceVoucher.All(x => _serviceOrders_tb.Contains(x) && serviceVoucher.Length == _serviceOrders_tb.Length)))
                {
                    if (voucher.minmoney > 0 || voucher.minprepaid > 0 || voucher.minConnectionFee > 0)
                    {
                        //var listSubservice_by_condition = FilterSubservice(sb, connection, lstServices, voucher.subServiceCondition);\

                        var lst_phm_Services = lstServices.Where(x => x.subServiceId == 999999999 || x.subServiceId == 999999996).ToList();
                        //var list_phm_by_condition = FilterService(lst_phm_Services, voucher.voucherService); // lấy thiết bị để cộng tiền check phí hòa mạng
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lst_phm_Services", lst_phm_Services));

                        if (lstServices.Count > 0 || lst_phm_Services.Count > 0)
                        {
                            int totalMoney = lstServices.Sum(a => a.total_vat);
                            int totalMoneyConnFee = lst_phm_Services.Sum(a => a.total_vat);
                            int minPrepaid = 0;
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalMoney", totalMoney));
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalMoneyConnFee", totalMoneyConnFee));

                            if (lstServices.Count > 0)
                            {
                                minPrepaid = lstServices.FirstOrDefault(x => x.serviceId == voucher.serviceId && x.subServiceId == voucher.subServiceId).prePaid;
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("minPrepaid", minPrepaid));

                                if ((totalMoney >= voucher.minmoney && totalMoneyConnFee >= voucher.minConnectionFee && minPrepaid == voucher.minprepaid) // khớp all đk tiền tối thiểu => oke
                                    || !voucher.subServiceCondition.Contains(voucher.subServiceId.ToString())//serviceID không nằm trong đk tiền tối thiểu => oke
                                    || voucher.subServiceCondition.Contains(voucher.subServiceId.ToString()) && minPrepaid != voucher.minprepaid//khớp đk tiền tối thiểu nhưng ko khớp số tháng => oke
                                    )
                                {
                                    lstEVC_policy.Add(voucher);
                                }
                            }
                            else
                            {
                                lstEVC_policy.Add(voucher);
                            }
                        }
                    }
                    else
                    {
                        lstEVC_policy.Add(voucher);
                    }
                }
            }
            return lstEVC_policy;
        }
        public static List<CustomerSaleOrderCollections> FilterSubservice(StringBuilder sb, SqlConnection connection, List<CustomerSaleOrderCollections> input, string datafillter)
        {
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FilterSubservice input", input));

            var listres = new List<CustomerSaleOrderCollections>();
            var lstService = new List<ServiceGroup>();
            lstService = connection.Query<ServiceGroup>(OS6_FPTVoucher_VoucherFollowPolicy, new
            {
                actionName = "GetGroupService",
                subServices = datafillter
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FilterSubservice GetGroupService", lstService));

            foreach (var service in lstService)
            {
                string[] _subservices = service.SubServiceId.Split(',').Select(x => x.Trim()).ToArray();
                var data = input.Where(x => x.serviceId == service.ServiceID).ToList();
                if (data.Count > 0)
                {
                    var dataList = (from item in data
                                    where _subservices.Contains(item.subServiceId.ToString())
                                    select item).ToList();
                    if (dataList.Count > 0)
                    {
                        listres.AddRange(dataList);
                    }
                    else
                    {
                        listres = new List<CustomerSaleOrderCollections>();
                        break;
                    }
                }
                else
                {
                    listres = new List<CustomerSaleOrderCollections>();
                    break;
                }
            }

            return listres;
        }

        public static List<CustomerSaleOrderCollections> FilterService(List<CustomerSaleOrderCollections> input, string datafillter)
        {
            string[] _arrayFilter = datafillter.Split(',');

            var result = input.Where(item => _arrayFilter.Contains(item.serviceId.ToString()) && input.Count(x => _arrayFilter.Contains(x.serviceId.ToString())) == _arrayFilter.Count()).ToList();
            return result;
        }

        private static XElement CreateXMLPolicy(List<PolicyVoucher> lstEVC_policy, StringBuilder sb)
        {
            var xmlString = new XElement("N",
            from evc in lstEVC_policy
            select new XElement("I",
                           new XElement("PolicyId", evc.policyId),
                           new XElement("ProgramId", evc.programId)//
                       ));
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher CreateXMLPolicy ", xmlString));
            return xmlString;
        }
        public static XElement CreateXMLSubService(List<CustomerSaleOrderCollections> input, int saleChannel)
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("prePaid", item.prePaid),
                           new XElement("deployTypeId", item.deployTypeId),
                           new XElement("statusId", item.statusId),
                           new XElement("revokeId", item.revokeID),
                           new XElement("usesId", item.usesId),
                           new XElement("qty", item.qty),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("total", item.total),
                           new XElement("total_vat", item.total_vat),
                           new XElement("policyId", item.policyId),
                           new XElement("crossSellingLocation", item.crossSellingLocation),
                           new XElement("paymentTypeId", item.paymentTypeId),
                           //new XElement("minmoney", minmoney),
                           new XElement("customerType", item.customerType),
                           new XElement("cusTypeId", item.cusTypeId),
                           new XElement("cusTypeL2Id", item.cusTypeL2Id),
                           new XElement("objectTypeId", item.objectTypeId),
                           new XElement("customerRank", item.customerRank),
                           new XElement("saleChannel", saleChannel),
                           new XElement("arrPolicyInput", item.arrPolicyInput),
                           new XElement("totalMoneyPolicyInput", item.totalMoneyPolicyInput)
                       //new XElement("serviceRegister", s_services)
                       ));
            return xmlString;
        }

        #region get infor voucher
        public static bool CheckVoucherGC(string voucherCode)
        {
            bool trueVoucher = false;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int StatusCodeVC = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetStatusVoucherCode",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                trueVoucher = (StatusCodeVC == 1);
            }
            return trueVoucher;
        }

        public static SalePlatformVoucherValueInfor GetVoucherGCinfo(List<ServicePlatform> lstSer)
        {
            SalePlatformVoucherValueInfor voucherinfo = new SalePlatformVoucherValueInfor();
            voucherinfo.Apply = new List<Apply>();
            voucherinfo.RefInfo = new RefInfo();

            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {

                }
            }
            catch (Exception ex)
            {

                return null;
            }
            return voucherinfo;
        }

        public static int GetTypeVoucher(string voucherCode)
        {
            int type = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                type = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetTypeVoucher",
                    voucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return type;
        }

        public static XElement CreateXMLGetInfor(SalePolicyGetInforEVC input)
        {
            var listServices = (from service in input.Services
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeID,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed)
                                }).ToList();

            var xmlServices = new XElement("N",
            from item in listServices
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("statusId", -1),
                           new XElement("revokeID", -1),
                           new XElement("deployTypeId", -1)
                       ));

            var listProducts = (from service in input.Products
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    statusId = subService.StatusID,
                                    revokeID = subService.RevokeID,
                                    deployTypeId = subService.DeployTypeID
                                }).ToList();

            var xmlProducts = new XElement("N",
            from item in listProducts
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("statusId", item.statusId),
                           new XElement("revokeID", item.revokeID),
                           new XElement("deployTypeId", item.deployTypeId)
                       ));

            if (xmlProducts != null)
            {
                xmlServices.Add(xmlProducts);
            }

            return xmlServices;
        }

        public static XElement CreateXMLGetInforProduct(SalePolicyGetInforEVC input, int i)
        {
            var listProducts = (from service in input.Products
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed)
                                }).ToList();

            var xmlString = new XElement("N",
            from item in listProducts
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed)
                       //new XElement("serviceRegister", s_services)
                       ));
            return xmlString;
        }

        public static List<Apply> GetListInforService(string voucherCode, XElement xml)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetVoucherValue",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static List<Apply> GetListInforProductService(string voucherCode, XElement xml)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetInforProductService",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }


        public static decimal CheckQuota(string voucherCode, SaleInfor saleInfor, CustomerInfor customerInfor) // đơn vị: VNĐ
        {
            decimal quotaMoneyAvailable = 0;
            //hạn mức sale còn lại
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                {
                    VoucherCode = voucherCode,
                    SaleId = saleInfor.SaleID,
                    LocationId = customerInfor.LocationID,
                    BranchCode = customerInfor.BranchCode,
                    DistrictId = customerInfor.DistrictID

                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }

            return quotaMoneyAvailable;
        }

        public static List<RegisterVoucherType> GetRegisterTypeVoucher(string evc_code)
        {
            List<RegisterVoucherType> resDataa = new List<RegisterVoucherType>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                resDataa = connection.Query<RegisterVoucherType>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetRegisterTypeVoucher",
                    voucherCode = evc_code
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                return resDataa;
            }
        }

        public static int GetPromotionType(string voucherCode)
        {
            int i = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                i = connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                {
                    Action = "GetPromotionTypeId",
                    VoucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return i;
        }
        #endregion

        public static List<EvoucherService> GetListEvcService(Guid logId, List<EvoucherInput> lstEVC)
        {
            List<EvoucherService> lstVoucher = new List<EvoucherService>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();

                if (lstEVC != null)
                {
                    try
                    {
                        foreach (var evc in lstEVC)
                        {
                            List<EvoucherService> voucherServices = new List<EvoucherService>();
                            voucherServices = connection.Query<EvoucherService>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                            {
                                Action = "GetServiceVoucher",
                                VoucherCode = evc.evoucherCode
                            }, commandType: CommandType.StoredProcedure).ToList();
                            voucherServices.Select(x => x.evoucherType = evc.evoucherType).ToList();
                            if (voucherServices != null)
                                lstVoucher.AddRange(voucherServices);

                            var voucherProducts = connection.Query<EvoucherService>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                            {
                                Action = "GetServiceVoucherProduct",
                                VoucherCode = evc.evoucherCode
                            }, commandType: CommandType.StoredProcedure).ToList();
                            voucherProducts.Select(x => x.evoucherType = evc.evoucherType).ToList();
                            if (voucherProducts != null)
                                lstVoucher.AddRange(voucherProducts);
                        }
                    }
                    catch (Exception ex)
                    {
                        MBSv4CommonService.WriteToLog(ex.Message, "MBSv4 KeepVoucher getListEvcService error: ", logId);
                    }
                }
            }

            return lstVoucher;
        }

        private static XElement CreateXMLExtendHistoryTemp(SalePolicyRedeemEVC input, List<int> historyTempIds)
        {
            var xmlString = new XElement("N",
                from id in historyTempIds
                select new XElement("I",
                new XElement("HistoryTempId", id),
                new XElement("PhoneNumberContract", input.CustomerPhone?.PhoneNumberContract),
                new XElement("PhoneNumberCMR", input.CustomerPhone?.PhoneNumberCMR),
                new XElement("LocationId", input.CustomerInfor?.LocationID),
                new XElement("DistrictId", input.CustomerInfor?.DistrictID),
                new XElement("WardId", input.CustomerInfor?.WardID),
                new XElement("BuildingId", input.CustomerInfor?.BuildingID),
                new XElement("CusTypeId", input.CustomerInfor?.CusTypeID),
                new XElement("CusTypeL2Id", input.CustomerInfor?.CusTypeL2ID),
                new XElement("ObjectTypeId", input.CustomerInfor?.ObjectTypeID),
                new XElement("CustomerRank", input.CustomerInfor?.CustomerRank),
                new XElement("CrossSellingLocation", input.CrossSellingLocation),
                new XElement("ServiceAvailable", input.ServiceAvailable != null ? string.Join(",", input.ServiceAvailable) : ""),
                new XElement("SaleId", input.SaleInfor?.SaleID),
                new XElement("SaleTeamId", input.SaleInfor?.SaleTeamID),
                new XElement("SaleChannelId", input.SaleInfor?.SaleChannelID),
                new XElement("PaymentTypeId", input.PaymentTypeID),
                new XElement("GroupPoint", input.GroupPoint),
                new XElement("ActiveKplus", input.ExtendedProperties?.ActiveKplus),
                new XElement("InternetServiceId", input.ExtendedProperties?.Internet?.ServiceID),
                new XElement("InternetChargeMonthly", input.ExtendedProperties?.Internet?.ChargeMonthly)));


            return xmlString;
        }

        public static List<int> GetHistoryTempIds(SqlConnection connection, SqlTransaction transaction, string orderCode, int objId, string voucherCode)
        {
            return connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "GetHistoryTempIds",
                OrderCode = orderCode,
                ObjId = objId,
                VoucherCode = voucherCode
            }, transaction: transaction, commandTimeout: 0, commandType: CommandType.StoredProcedure).ToList();
        }

        public static bool AddExtendHistoryTemp(SqlConnection connection, SqlTransaction transaction, List<int> historyTempIds, SalePolicyRedeemEVC input)
        {
            return connection.Execute(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "AddExtendHistoryTemp",
                XML = CreateXMLExtendHistoryTemp(input, historyTempIds)
            }, transaction: transaction, commandTimeout: 0, commandType: CommandType.StoredProcedure) > 0;
        }

        public static string RecheckEvoucher(SalePolicyRedeemEVC input, StringBuilder sb)
        {
            var lstVoucherFinal = new List<Evoucher>();
            var lstEVC_FromBD = new List<PolicyVoucher>();
            var lstVoucherToCheckLocation = new List<PolicyVoucher>();


            List<CustomerSaleOrderCollections> lstDataOrders = new List<CustomerSaleOrderCollections>();
            try
            {
                #region Chuẩn bị sơ bộ để create XML
                var listOptionService_servicesInput = formatServices(input, "Service");
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listServices", listOptionService_servicesInput));
                if (listOptionService_servicesInput.Count > 0)
                {
                    lstDataOrders.AddRange(listOptionService_servicesInput);
                }
                var listOptionService_productsInput = formatServices(input, "Product");
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProducts", listOptionService_productsInput));
                if (listOptionService_servicesInput.Count > 0)
                {
                    lstDataOrders.AddRange(listOptionService_productsInput);
                }
                var listPolicyIds = lstDataOrders.Select(x => x.policyId).Distinct().ToList();


                #endregion

                var lstEVC_policy = new List<PolicyVoucher>();
                var lstEVC = new List<PolicyVoucher>();
                var lst = new List<PolicyVoucher>();

                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    lstEVC_policy = new List<PolicyVoucher>();
                    lstEVC = new List<PolicyVoucher>();
                    lst = new List<PolicyVoucher>();

                    #region Kiểm tra ngày vàng
                    var isGoldenDate = checkGoldenDate(connection, string.Join(",", listPolicyIds));
                    if (isGoldenDate.Equals(1))
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Đơn hàng là chính sách ngày vàng", ""));
                        return "Đơn hàng là chính sách ngày vàng";
                    }
                    #endregion

                    foreach (var policyId in listPolicyIds)
                    {
                        var list_order_by_policy = lstDataOrders.Where(x => x.policyId == policyId).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_order_by_policy ", list_order_by_policy));
                        #region Lấy voucher cho dịch vụ
                        if (list_order_by_policy?.Any() == true)
                        {
                            sb.AppendLine("START---<<<<---" + policyId + "--->>>>---");
                            var lstVoucher = new List<PolicyVoucher>();

                            var xmlService_order = CreateXMLSubService(list_order_by_policy, input.SaleInfor.SaleChannelID);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlService_order ", xmlService_order));

                            lstVoucher = connection.Query<PolicyVoucher>(OS6_FPTVoucher_policy_evc_getlist, new //voucher lấy bao gồm cả service và product
                            {
                                xml = xmlService_order
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstVoucher_Service_FromDB", lstVoucher));


                            if (lstVoucher?.Any() == true)
                            {
                                //var lstVC = (from x in lstVoucher
                                //             select x)
                                //             .OrderByDescending(x => x.subServiceId) //nếu có PHM sẽ ưu tiên lấy subServiceId PHM ..
                                //             .GroupBy(n => new { n.VoucherCode })
                                //             .Select(g => g.FirstOrDefault())
                                //             .ToList();
                                //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstVC", lstVC));

                                var listVCservice = lstVoucher.Where(x => x.promotionTypeId != 3).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listVCservice", listVCservice));

                                if (listVCservice.Count > 0)
                                {
                                    lst.AddRange(listVCservice);
                                }

                                var _subServiceTypeId_all_input = list_order_by_policy.Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                                var _subServiceTypeId_DV = listOptionService_servicesInput.Where(x => x.policyId == policyId).Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                                var _subServiceTypeId_TB = listOptionService_productsInput.Where(x => x.policyId == policyId).Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                                Array.Sort(_subServiceTypeId_all_input);
                                Array.Sort(_subServiceTypeId_DV);
                                Array.Sort(_subServiceTypeId_TB);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_input", _subServiceTypeId_all_input));
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_DV", _subServiceTypeId_DV));
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_TB", _subServiceTypeId_TB));
                                // lấy các voucher phí hòa mạng, tiếp tục kiểm tra voucher hòa mạng thiết bị
                                // promotiontype id=3 là phí hòa mạng
                                // phí hòa mạng có kiểm tra thiết bị sẽ có applyfor =3
                                // phí hòa mạng giảm cho dịch vụ sẽ không cần kiểm tra điều kiện áp dụng thiết bị AplyFor: 1 là dịch vụ, 2 thiết bị, 3 là dịch vụ + thiết bị

                                //var listEVCPHM_product = lstVoucher.Where(x => x.promotionTypeId.Equals(3) && x.applyFor.Equals(3)).ToList(); //  PHM điều kiện dv+tb
                                //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM", listEVCPHM_product));

                                var listEVCPHM_Service = lstVoucher.Where(x => x.promotionTypeId.Equals(3) && (x.applyFor.Equals(1) || x.applyFor.Equals(3))).ToList(); // PHM (dịch vụ || dv+tb)
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM_Service", listEVCPHM_Service));

                                if (listEVCPHM_Service?.Count > 0)
                                {
                                    // lấy những service có PHM 
                                    var order_phm_net = listOptionService_servicesInput.Where(x => x.subServiceId == 999999999).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_net count", order_phm_net.Count));
                                    if (order_phm_net.Count > 0)
                                    {
                                        var data_phm_net = listEVCPHM_Service.Where(x => x.serviceId == 1).ToList();
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_net", data_phm_net));
                                        lst.AddRange(data_phm_net);
                                    }
                                    var order_phm_tv = listOptionService_servicesInput.Where(x => x.subServiceId == 999999996).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_tv count", order_phm_tv));
                                    if (order_phm_tv.Count > 0)
                                    {
                                        var data_phm_tv = listEVCPHM_Service.Where(x => x.serviceId == 7).ToList();
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_tv", data_phm_tv));
                                        lst.AddRange(data_phm_tv);
                                    }

                                }

                                if (lst.Count > 0)
                                {
                                    var data_evc_services = lst.Where(x => x.isService == 1).ToList();
                                    var data_evc_products = lst.Where(x => x.isService == 0).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_services", data_evc_services));
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_products", data_evc_products));

                                    if (data_evc_services.Count > 0)
                                    {
                                        //kiểm tra luôn cả evc thiết bị PHM
                                        var ValidVoucherByCoditionInput = new List<PolicyVoucher>();
                                        ValidVoucherByCoditionInput.AddRange(data_evc_services);
                                        ValidVoucherByCoditionInput.AddRange(data_evc_products.Where(x => x.promotionTypeId == 3).ToList());
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ValidVoucherByCoditionInput", ValidVoucherByCoditionInput));

                                        var data_evc_condition = ValidVoucherByCodition(connection, ValidVoucherByCoditionInput, _subServiceTypeId_all_input, _subServiceTypeId_DV, _subServiceTypeId_TB, list_order_by_policy, sb);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_condition", data_evc_condition));
                                        lstEVC_policy.AddRange(data_evc_condition);
                                    }
                                    if (data_evc_products.Count > 0)
                                    {
                                        foreach (var voucher in data_evc_products)
                                        {
                                            string[] serviceVoucher = voucher.voucherService.Split(',');
                                            if (voucher.applyFor != 3 && serviceVoucher.Any(x => _subServiceTypeId_all_input.Contains(x)) // nếu giảm tiền tb áp dụng cho thiết bị ===> chỉ cần thỏa 1 subService type 
                                                || (voucher.applyFor == 3 && serviceVoucher.All(x => _subServiceTypeId_all_input.Contains(x) && serviceVoucher.Length == _subServiceTypeId_all_input.Length))) //điều kiện áp dụng dv+tb ===> phải đúng hết subservice
                                            {
                                                lstEVC_policy.Add(voucher);
                                            }
                                        }
                                    }


                                }

                            }
                            sb.AppendLine("END--->>>>---" + policyId + "---<<<<---");
                        }

                        #endregion

                    }
                    #region Kiểm tra voucher theo tỉnh thành

                    if (lstEVC_policy.Count > 0)
                    {
                        var evc_check_location = ValidVoucherByLocation(connection, lstEVC_policy, input.CustomerInfor.LocationID, input.CustomerInfor.BranchCode, input.CustomerInfor.DistrictID, sb);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evc_check_location", evc_check_location));
                        if (evc_check_location.Count > 0)
                        {
                            lstEVC.AddRange(evc_check_location);
                        }
                    }
                    #endregion

                    #region Recheck đơn hàng thỏa đk voucher hay không
                    var listEVC_General = input.evoucherList.Where(x => x.evoucherType == 1).ToList();
                    foreach (var voucher in listEVC_General)
                    {
                        var checkVoucher = lstEVC.Where(x => x.VoucherCode == voucher.evoucherCode).FirstOrDefault();
                        if (checkVoucher == null)
                        {
                            return "Voucher bạn đã chọn " + voucher.evoucherCode + " không thể áp dụng. Vui lòng kiểm tra lại.";
                        }
                    }
                    #endregion

                    #region Kiểm tra Quota hạn mức
                    if (lstEVC.Count > 0)
                    {
                        foreach (var voucher in lstEVC)
                        {
                            decimal valueDiscount = 0;
                            decimal quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                            {
                                VoucherCode = voucher.VoucherCode,
                                SaleId = input.SaleInfor.SaleID,
                                LocationId = input.CustomerInfor.LocationID,
                                BranchCode = input.CustomerInfor.BranchCode,
                                DistrictId = input.CustomerInfor.DistrictID

                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("quotaMoneyAvailable " + voucher.VoucherCode + " ", quotaMoneyAvailable));

                            if (voucher.applyTypeID.Equals(2))
                            {
                                var dataVoucher = lst.Where(x => x.VoucherCode == voucher.VoucherCode).ToList();

                                var dataMapVoucher = (from ser in lstDataOrders
                                                      join evc in dataVoucher on new { serviceId = ser.serviceId, subServiceTypeId = ser.subServiceTypeId, subServiceId = ser.subServiceId, serviceCode = ser.serviceCode } equals
                                                        new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                                      select new
                                                      {
                                                          serviceId = evc.serviceId,
                                                          subServiceTypeId = evc.subServiceTypeId,
                                                          subServiceId = evc.subServiceId,
                                                          serviceCode = evc.serviceCode,
                                                          total_vat = ser.total_vat,
                                                          monthUsed = ser.monthUsed,
                                                          qty = ser.qty,
                                                          valueVoucher = evc.valueVoucher
                                                      }).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataMapVoucher " + voucher.VoucherCode + " ", dataMapVoucher));
                                if (voucher.promotionTypeId.Equals(4))//fix code vì id loại voucher ko thể tăng (4 là thặng tháng)
                                {
                                    valueDiscount = dataMapVoucher.Count > 0 ? dataMapVoucher.Sum(a => a.total_vat / a.monthUsed) * voucher.duration : 0;
                                }
                                else
                                {
                                    valueDiscount = dataMapVoucher.Sum(x => Math.Max(voucher.duration, 1) * x.valueVoucher * x.qty);
                                }
                            }
                            else
                            {
                                var dataVoucher = GetServices_PHM(voucher.VoucherCode);

                                var dataMapVoucher = (from pro in lstDataOrders
                                                      join evc in dataVoucher on new { serviceId = pro.serviceId, subServiceTypeId = pro.subServiceTypeId, subServiceId = pro.subServiceId, serviceCode = pro.serviceCode } equals
                                                        new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                                      where evc.applyTypeID != 2
                                                      select pro).ToList();


                                valueDiscount = dataMapVoucher == null ? 0 : dataMapVoucher.Sum(x => x.qty) * voucher.valueVoucher;
                            }

                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("valueDiscount for voucher " + voucher.VoucherCode + " ", valueDiscount));
                            if (quotaMoneyAvailable >= valueDiscount && quotaMoneyAvailable > 0 && valueDiscount > 0)
                            {
                                //lstEVC.Add(voucher);
                            }
                            else if (input.evoucherList.Where(x => x.evoucherCode == voucher.VoucherCode).FirstOrDefault() != null)
                            {
                                return "Hạn mức khả dụng còn lại " + voucher.VoucherCode + " chỉ được áp dụng tối đa " + string.Format("{0:n0}", quotaMoneyAvailable) + " VNĐ. Vui lòng kiểm tra lại thông tin đơn hàng hoặc liên hệ BGĐ CN để được cấp thêm.";
                            }
                        }
                    }
                    #endregion
                }

            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher Error", ex.Message));
                return null;
            }
            return "";
        }

        public static List<CustomerSaleOrderCollections> GetServicesFromInput(SalePolicyGetInforEVC input)
        {
            var services = new List<CustomerSaleOrderCollections>();

            var listServices = (from service in input.Services
                                join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeID,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    prePaid = Convert.ToInt32(subService.PrePaid),
                                    deployTypeId = subService.DeployTypeID,
                                    qty = subService.Qty,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    total = Convert.ToInt32(subService.Total),
                                    total_vat = Convert.ToInt32(subService.Total_VAT),
                                    policyId = subService.PolicyID,
                                    customerType = customertype.CustomerType,
                                    crossSellingLocation = input.CrossSellingLocation,
                                    paymentTypeId = input.PaymentTypeID,
                                    cusTypeId = input.CustomerInfor.CusTypeID,
                                    cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                    objectTypeId = input.CustomerInfor.ObjectTypeID,
                                    customerRank = input.CustomerInfor.CustomerRank
                                }).ToList();

            var listProducts = (from product in input.Products
                                join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                from subServiceType in product.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = product.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    prePaid = subService.ApplyPrePaid,
                                    deployTypeId = subService.DeployTypeID,
                                    statusId = subService.StatusID,
                                    revokeID = subService.RevokeID,
                                    usesId = subService.UsesID,
                                    qty = subService.Qty,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    total = Convert.ToInt32(subService.Total),
                                    total_vat = Convert.ToInt32(subService.Total_VAT),
                                    policyId = subService.PolicyID,
                                    customerType = customertype.CustomerType,
                                    crossSellingLocation = input.CrossSellingLocation,
                                    paymentTypeId = input.PaymentTypeID,
                                    cusTypeId = input.CustomerInfor.CusTypeID,
                                    cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                    objectTypeId = input.CustomerInfor.ObjectTypeID,
                                    customerRank = input.CustomerInfor.CustomerRank
                                }).ToList();
            if (!(listServices is null))
            {
                services.AddRange(listServices);
            }
            if (!(listProducts is null))
            {
                services.AddRange(listProducts);
            }
            return services;
        }

        public static List<PolicyVoucher> GetServices_PHM(string voucherCode)
        {
            var data = new List<PolicyVoucher>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<PolicyVoucher>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetServices_PHM",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static List<CustomerSaleOrderCollections> formatServices(SalePolicyListEVC input, string ServiceOrProduct)
        {
            if (ServiceOrProduct == "Service")
            {
                var listServices = (from service in input.Services
                                    join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                    from subServiceType in service.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = service.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeID,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = Convert.ToInt32(subService.PrePaid),
                                        deployTypeId = subService.DeployTypeID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listServices;
            }
            if (ServiceOrProduct == "Product")
            {
                var listProducts = (from product in input.Products
                                    join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                    from subServiceType in product.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = product.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeId,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = subService.ApplyPrePaid,
                                        deployTypeId = subService.DeployTypeID,
                                        statusId = subService.StatusID,
                                        revokeID = subService.RevokeID,
                                        usesId = subService.UsesID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listProducts;
            }
            return new List<CustomerSaleOrderCollections>();
        }

        public static List<CustomerSaleOrderCollections> formatServices(SalePolicyRedeemEVC input, string ServiceOrProduct)
        {
            if (ServiceOrProduct == "Service")
            {
                var listServices = (from service in input.Services
                                    join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                    from subServiceType in service.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = service.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeID,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = Convert.ToInt32(subService.PrePaid),
                                        deployTypeId = subService.DeployTypeID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listServices;
            }
            if (ServiceOrProduct == "Product")
            {
                var listProducts = (from product in input.Products
                                    join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                    from subServiceType in product.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = product.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeId,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = subService.ApplyPrePaid,
                                        deployTypeId = subService.DeployTypeID,
                                        statusId = subService.StatusID,
                                        revokeID = subService.RevokeID,
                                        usesId = subService.UsesID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listProducts;
            }
            return new List<CustomerSaleOrderCollections>();
        }
        public static List<CustomerSaleOrderCollections> formatPolicyInput(List<CustomerSaleOrderCollections> services)
        {
            // Duyệt qua từng policyId
            foreach (var item in services)
            {
                // Lấy danh sách các serviceIds liên quan đến policyId hiện tại
                var serviceIds = services.Where(ps => ps.policyId == item.policyId)
                                               .Select(ps => ps.serviceId)
                                               .Distinct()
                                               .OrderBy(id => id)
                                               .ToList();

                // Nối các serviceIds lại
                item.arrPolicyInput = string.Join(",", serviceIds);

                item.totalMoneyPolicyInput = services.Where(x => x.policyId == item.policyId).Select(x => x.total_vat).Sum();
            }

            return services;
        }

        public static int checkGoldenDate(SqlConnection connection, string policyIds)
        {

            int goldenPolicy = connection.Query<int>(OS6_FPTVoucher_VoucherFollowPolicy, new
            {
                actionName = "CheckPolicyGoldenDate",
                policyIds = policyIds
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return goldenPolicy;
        }
        public static void GetVoucherAndCheckService(SqlConnection connection, int policyId, List<PolicyVoucher> lstEVC_policy, SalePolicyListEVC input, List<CustomerSaleOrderCollections> listOptionService_servicesInput, List<CustomerSaleOrderCollections> listOptionService_productsInput, List<int> listPolicyIds, List<PolicyVoucher> lstEVC, List<PolicyVoucher> lstVoucher, List<PolicyVoucher> lst, List<CustomerSaleOrderCollections> lstDataOrders, List<CustomerSaleOrderCollections> list_order_by_policy, StringBuilder sb)
        {
            lstEVC = new List<PolicyVoucher>();
            lstVoucher = new List<PolicyVoucher>();
            lst = new List<PolicyVoucher>();
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_order_by_policy ", list_order_by_policy));
            #region Lấy voucher từ DB
            if (list_order_by_policy?.Any() == true)
            {
                var xmlService_order = CreateXMLSubService(list_order_by_policy, input.SaleInfor.SaleChannelID);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlService_order ", xmlService_order));

                lstVoucher = connection.Query<PolicyVoucher>(OS6_FPTVoucher_policy_evc_getlist, new //voucher lấy bao gồm cả service và product
                {
                    xml = xmlService_order
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstVoucher_Service_FromDB", lstVoucher));


                if (lstVoucher?.Any() == true)
                {
                    //var lstVC = (from x in lstVoucher
                    //             select x)
                    //             .OrderByDescending(x => x.subServiceId) //nếu có PHM sẽ ưu tiên lấy subServiceId PHM ..
                    //             .GroupBy(n => new { n.VoucherCode })
                    //             .Select(g => g.FirstOrDefault())
                    //             .ToList();
                    //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstVC", lstVC));

                    var listVCservice = lstVoucher.Where(x => x.promotionTypeId != 3).ToList();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listVCservice", listVCservice));

                    if (listVCservice.Count > 0)
                    {
                        lst.AddRange(listVCservice);
                    }

                    var _subServiceTypeId_all_input = list_order_by_policy.Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                    var _subServiceTypeId_DV = listOptionService_servicesInput.Where(x => x.policyId == policyId).Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                    var _subServiceTypeId_TB = listOptionService_productsInput.Where(x => x.policyId == policyId).Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray();
                    Array.Sort(_subServiceTypeId_all_input);
                    Array.Sort(_subServiceTypeId_DV);
                    Array.Sort(_subServiceTypeId_TB);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_input", _subServiceTypeId_all_input));
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_DV", _subServiceTypeId_DV));
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_subServiceTypeId_TB", _subServiceTypeId_TB));
                    // lấy các voucher phí hòa mạng, tiếp tục kiểm tra voucher hòa mạng thiết bị
                    // promotiontype id=3 là phí hòa mạng
                    // phí hòa mạng có kiểm tra thiết bị sẽ có applyfor =3
                    // phí hòa mạng giảm cho dịch vụ sẽ không cần kiểm tra điều kiện áp dụng thiết bị AplyFor: 1 là dịch vụ, 2 thiết bị, 3 là dịch vụ + thiết bị

                    //var listEVCPHM_product = lstVoucher.Where(x => x.promotionTypeId.Equals(3) && x.applyFor.Equals(3)).ToList(); //  PHM điều kiện dv+tb
                    //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM", listEVCPHM_product));

                    var listEVCPHM_Service = lstVoucher.Where(x => x.promotionTypeId.Equals(3) && (x.applyFor.Equals(1) || x.applyFor.Equals(3))).ToList(); // PHM (dịch vụ || dv+tb)
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM_Service", listEVCPHM_Service));

                    if (listEVCPHM_Service?.Count > 0)
                    {
                        // lấy những service có PHM 
                        var order_phm_net = listOptionService_servicesInput.Where(x => x.subServiceId == 999999999).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_net count", order_phm_net.Count));
                        if (order_phm_net.Count > 0)
                        {
                            var data_phm_net = listEVCPHM_Service.Where(x => x.serviceId == 1).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_net", data_phm_net));
                            lst.AddRange(data_phm_net);
                        }
                        var order_phm_tv = listOptionService_servicesInput.Where(x => x.subServiceId == 999999996).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_tv count", order_phm_tv));
                        if (order_phm_tv.Count > 0)
                        {
                            var data_phm_tv = listEVCPHM_Service.Where(x => x.serviceId == 7).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_tv", data_phm_tv));
                            lst.AddRange(data_phm_tv);
                        }

                    }

                    if (lst.Count > 0)
                    {
                        var data_evc_services = lst.Where(x => x.isService == 1).ToList();
                        var data_evc_products = lst.Where(x => x.isService == 0).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_services", data_evc_services));
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_products", data_evc_products));

                        if (data_evc_services.Count > 0)
                        {
                            //kiểm tra luôn cả evc thiết bị PHM
                            var ValidVoucherByCoditionInput = new List<PolicyVoucher>();
                            ValidVoucherByCoditionInput.AddRange(data_evc_services);
                            ValidVoucherByCoditionInput.AddRange(data_evc_products.Where(x => x.promotionTypeId == 3).ToList());
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ValidVoucherByCoditionInput", ValidVoucherByCoditionInput));

                            var data_evc_condition = ValidVoucherByCodition(connection, ValidVoucherByCoditionInput, _subServiceTypeId_all_input, _subServiceTypeId_DV, _subServiceTypeId_TB, list_order_by_policy, sb);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_evc_condition", data_evc_condition));
                            lstEVC_policy.AddRange(data_evc_condition);
                        }
                        if (data_evc_products.Count > 0)
                        {
                            foreach (var voucher in data_evc_products)
                            {
                                string[] serviceVoucher = voucher.voucherService.Split(',');
                                if (voucher.applyFor != 3 && serviceVoucher.Any(x => _subServiceTypeId_all_input.Contains(x)) // nếu giảm tiền tb áp dụng cho thiết bị ===> chỉ cần thỏa 1 subService type 
                                    || (voucher.applyFor == 3 && serviceVoucher.All(x => _subServiceTypeId_all_input.Contains(x) && serviceVoucher.Length == _subServiceTypeId_all_input.Length))) //điều kiện áp dụng dv+tb ===> phải đúng hết subservice
                                {
                                    lstEVC_policy.Add(voucher);
                                }
                            }
                        }


                    }

                }
            }

            #endregion


        }
    }
}