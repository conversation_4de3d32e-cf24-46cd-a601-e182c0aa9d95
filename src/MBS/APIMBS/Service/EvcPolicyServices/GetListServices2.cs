using APIMBS.Constant;
using APIMBS.Models;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models.SalePlatform;
using APIMBS.Service.Policy;
using Dapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Web;
using System.Web.Services.Description;
using System.Xml;
using System.Xml.Linq;
using Voucher.APIHelper;
using Voucher.APIHelper.ShareModel;

namespace APIMBS.Service.EvcPolicyServices
{
    public class GetListServices2
    {
        public const string OS6_FPTVoucher_VoucherFollowPolicy = "PowerInside.dbo.OS6_FPTVoucher_VoucherFollowPolicy";
        public const string OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend = "PowerInside.dbo.OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend";
        public const string OS6_FPTVoucher_FSale = "PowerInside.dbo.OS6_FPTVoucher_FSale";
        public const string OSU6_FPTVoucher_PolicyVoucher_CheckLocation = "PowerInside.dbo.OSU6_FPTVoucher_PolicyVoucher_CheckLocation";
        public const string OS6_FPTVoucher_CheckQuotaMoney = "PowerInside.dbo.OS6_FPTVoucher_CheckQuotaMoney";
        public const string OS6_FPTVoucher_policy_evc_getlist_remake = "PowerInside.dbo.OS6_FPTVoucher_policy_evc_getlist_remake";
        public const string OS6_FPTVoucher_VoucherConfigCURD = "PowerInside.dbo.OS6_FPTVoucher_VoucherConfigCURD";
        public static ListVoucher GetListEvoucher(SalePolicyGetInforEVC input, StringBuilder sb)
        {
            ListVoucher listVoucher = new ListVoucher
            {
                LstVoucher = new List<PolicyVoucher2>(),
                Error = new List<string>()
            };
            var lstVoucherFinal = new List<Evoucher>();
            var lstEVC_FromBD = new List<PolicyVoucher>();
            var lstVoucherToCheckLocation = new List<PolicyVoucher>();
            var Error = new List<string>();


            var lstVoucher = new List<PolicyVoucher2>();
            var afterCondition = new List<PolicyVoucher2>();
            var afterminvalue = new List<PolicyVoucher2>();
            var afterLocation = new List<PolicyVoucher2>();
            var afterCheckQuota = new List<PolicyVoucher2>();

            List<CustomerSaleOrderCollections> lstDataOrders = new List<CustomerSaleOrderCollections>();
            try
            {
                #region to XML
                var listOptionService_servicesInput = formatServices(input, "Service");
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listServices", listOptionService_servicesInput));
                if (listOptionService_servicesInput.Count > 0)
                {
                    lstDataOrders.AddRange(listOptionService_servicesInput);
                }
                var listOptionService_productsInput = formatServices(input, "Product");
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProducts", listOptionService_productsInput));
                if (listOptionService_productsInput.Count > 0)
                {
                    lstDataOrders.AddRange(listOptionService_productsInput);
                }
                var listPolicyIds = lstDataOrders.Select(x => x.policyId).Distinct().ToList();


                #endregion

                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    var SubtypeDeploy = GetSubtypeDeploy(connection, 24);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("SubtypeDeploy", SubtypeDeploy));

                    #region Kiểm tra ngày vàng
                    var isGoldenDate = checkGoldenDate(connection, string.Join(",", listPolicyIds));
                    if (isGoldenDate.Equals(1))
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Đơn hàng là chính sách ngày vàng", ""));
                        if (input.evoucherList?.Count > 0)
                        {
                            listVoucher.Error.Add("Đơn hàng là chính sách ngày vàng");
                        }
                        return listVoucher;
                    }
                    #endregion

                    if (lstDataOrders.Count > 0)
                    {
                        var xmlService_order = CreateXMLSubService(lstDataOrders, input);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlService_order ", xmlService_order));
                        lstVoucher = connection.Query<PolicyVoucher2>(OS6_FPTVoucher_policy_evc_getlist_remake, new
                        {
                            xml = xmlService_order
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("VoucherFromDB", lstVoucher));

                        // get combo type
                        List<int> policyIds = new List<int>();
                        lstVoucher.ForEach(c => policyIds.Add(c.PolicyID));
                        lstDataOrders.ForEach(c => policyIds.Add(c.policyId));
                        policyIds = policyIds.Distinct().ToList();
                        var lst_policy = PolicyService.Policy_GetInfo(null, connection, policyIds);

                        foreach (var voucherCode in lstVoucher.Select(x => x.VoucherCode).Distinct().ToList())
                        {
                            sb.AppendLine("-------------------------------- " + voucherCode);
                            var tempVoucher = lstVoucher.Where(x => x.VoucherCode == voucherCode).ToList();
                            var voucher = tempVoucher.FirstOrDefault();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("tempVoucher", tempVoucher));

                            #region kiem tra service, qty, minmoney
                            if (voucher.ApplyFor == 1) // DICH VU
                            {
                                //  voucher combotype
                                int voucher_combotype = lst_policy.FirstOrDefault(c => c.PolicyID == voucher.PolicyID)?.ComboType ?? 0;

                                foreach (var policyId in lstDataOrders.Select(x => x.policyId).Distinct().ToList())
                                {
                                    // policy combotype
                                    int policy_combotype = lst_policy.FirstOrDefault(c => c.PolicyID == policyId)?.ComboType ?? 0;

                                    // check combotype
                                    if (voucher_combotype != policy_combotype)
                                        continue;

                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog(policyId.ToString(), ""));
                                    var orders = lstDataOrders.Where(x => x.policyId == policyId).ToList();
                                    var totalConectionFee = lstDataOrders.Where(x => x.subServiceId == 999999999 || x.subServiceId == 999999996).Sum(x => x.total_vat);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalConectionFee", totalConectionFee));

                                    var mapVoucher2 = orders
                                    .Where(ods => tempVoucher.Any(evc =>
                                        ods.serviceId == evc.ServiceID &&
                                        ods.subServiceTypeId == evc.SubServiceTypeID &&
                                        ods.subServiceId == evc.SubServiceID &&
                                        ods.serviceCode == evc.ServiceCode &&
                                        (evc.FromQuantity <= ods.qty && ods.qty <= evc.ToQuantity)
                                        ))
                                    .Select(ods => tempVoucher.FirstOrDefault(evc =>
                                        ods.serviceId == evc.ServiceID &&
                                        ods.subServiceTypeId == evc.SubServiceTypeID &&
                                        ods.subServiceId == evc.SubServiceID &&
                                        ods.serviceCode == evc.ServiceCode &&
                                        (evc.FromQuantity <= ods.qty && ods.qty <= evc.ToQuantity)
                                        ))
                                    .Where(x => x != null).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("mapVoucher2", mapVoucher2));

                                    var subServiceTypeId_input = mapVoucher2.Select(x => x.SubServiceTypeID.ToString()).Distinct().ToArray() ?? new string[0]; // da map voi input
                                    var subServiceTypeId_map = listOptionService_servicesInput.Where(x => x.policyId == policyId).Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray() ?? new string[0];
                                    var SubServiceType_EVC = voucher.SubServiceTypeID_Services?.Split(',') ?? new string[0];
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("subServiceTypeId_input", subServiceTypeId_input));
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("SubServiceType_EVC", SubServiceType_EVC));
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("subServiceTypeId_map", subServiceTypeId_map));

                                    //loai bo dich vu trien khai
                                    subServiceTypeId_map = subServiceTypeId_map.Except(SubtypeDeploy).ToArray();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("subServiceTypeId_map join SubtypeDeploy", subServiceTypeId_map));

                                    if (voucher_combotype == 1) // combo
                                    {
                                        //  policy subtype phai tuong dung voucher subtype
                                        if (SubServiceType_EVC.All(x => subServiceTypeId_input.Contains(x) && SubServiceType_EVC.Length == subServiceTypeId_input.Length)
                                            && SubServiceType_EVC.All(x => subServiceTypeId_map.Contains(x) && SubServiceType_EVC.Length == subServiceTypeId_map.Length))
                                        {
                                            if (mapVoucher2?.Count > 0)
                                            {
                                                afterCondition.AddRange(mapVoucher2);
                                            }
                                        }
                                    }
                                    else if (voucher_combotype == 2) // only
                                    {
                                        //  policy subtype chi can co trong voucher subtype
                                        if (SubServiceType_EVC.All(x => subServiceTypeId_input.Contains(x))
                                            && SubServiceType_EVC.All(x => subServiceTypeId_map.Contains(x)))
                                        {
                                            if (mapVoucher2?.Count > 0)
                                            {
                                                afterCondition.AddRange(mapVoucher2);
                                            }
                                        }
                                    }
                                }
                            }
                            if (voucher.ApplyFor == 2) // THIET BI
                            {
                                var mapVoucher = lstDataOrders
                                    .Where(ods => tempVoucher.Any(evc =>
                                        ods.serviceId == evc.ServiceID &&
                                        ods.subServiceTypeId == evc.SubServiceTypeID &&
                                        ods.subServiceId == evc.SubServiceID &&
                                        ods.serviceCode == evc.ServiceCode))
                                    .Select(ods => new
                                    {
                                        serviceId = ods.serviceId,
                                        subServiceTypeId = ods.subServiceTypeId,
                                        subServiceId = ods.subServiceId,
                                        serviceCode = ods.serviceCode,
                                        total_vat = ods.total_vat,
                                        prePaid = ods.prePaid,
                                        qty = ods.qty
                                    })
                                    .ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("mapVoucher", mapVoucher));

                                if (mapVoucher?.Count > 0)
                                {
                                    var totalQty = mapVoucher.Sum(x => x.qty);

                                    var mapVoucher2 = mapVoucher
                                        .Where(ods => tempVoucher.Any(evc =>
                                            ods.serviceId == evc.ServiceID &&
                                            ods.subServiceTypeId == evc.SubServiceTypeID &&
                                            ods.subServiceId == evc.SubServiceID &&
                                            ods.serviceCode == evc.ServiceCode &&
                                            ((evc.ApplyTypeID == 1 && evc.FromQuantity <= ods.qty && ods.qty <= evc.ToQuantity) || (evc.ApplyTypeID == 3 && evc.FromQuantity <= totalQty && totalQty <= evc.ToQuantity)) &&
                                            (evc.FromPrice == 0 || ods.total_vat >= evc.FromPrice)
                                            )
                                        )
                                        .Select(ods =>
                                            tempVoucher.FirstOrDefault(evc =>
                                                ods.serviceId == evc.ServiceID &&
                                                ods.subServiceTypeId == evc.SubServiceTypeID &&
                                                ods.subServiceId == evc.SubServiceID &&
                                                ods.serviceCode == evc.ServiceCode &&
                                                ((evc.ApplyTypeID == 1 && evc.FromQuantity <= ods.qty && ods.qty <= evc.ToQuantity) || (evc.ApplyTypeID == 3 && evc.FromQuantity <= totalQty && totalQty <= evc.ToQuantity)) &&
                                                (evc.FromPrice == 0 || ods.total_vat >= evc.FromPrice)
                                            )
                                        ).Where(x => x != null).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("mapVoucher2", mapVoucher2));

                                    if (mapVoucher2?.Count > 0)
                                    {
                                        afterCondition.AddRange(mapVoucher2);
                                    }
                                }
                            }
                            if (voucher.ApplyFor == 3) // DV + TB
                            {
                                foreach (var policyId in lstDataOrders.Select(x => x.policyId).Distinct().ToList())
                                {
                                    var orders = lstDataOrders.Where(x => x.policyId == policyId).ToList();
                                    var totalConectionFee = orders.Where(x => x.subServiceId == 999999999 || x.subServiceId == 999999996).Sum(x => x.total_vat);

                                    var mapVoucher2 = orders
                                    .Where(ods => tempVoucher.Any(evc =>
                                        ods.serviceId == evc.ServiceID &&
                                        ods.subServiceTypeId == evc.SubServiceTypeID &&
                                        ods.subServiceId == evc.SubServiceID &&
                                        ods.serviceCode == evc.ServiceCode &&
                                        (evc.FromQuantity <= ods.qty && ods.qty <= evc.ToQuantity)
                                        ))
                                    .Select(ods => tempVoucher.FirstOrDefault(evc =>
                                        ods.serviceId == evc.ServiceID &&
                                        ods.subServiceTypeId == evc.SubServiceTypeID &&
                                        ods.subServiceId == evc.SubServiceID &&
                                        ods.serviceCode == evc.ServiceCode &&
                                        (evc.FromQuantity <= ods.qty && ods.qty <= evc.ToQuantity)
                                        ))
                                    .Where(x => x != null).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("mapVoucher2", mapVoucher2));

                                    //sau khi map input
                                    var subServiceTypeId_DV_input = mapVoucher2.Where(x => !string.IsNullOrEmpty(x.SubServiceTypeID_Services)).Select(x => x.SubServiceTypeID.ToString()).Distinct().ToArray() ?? new string[0];
                                    var subServiceTypeId_TB_input = mapVoucher2.Where(x => !string.IsNullOrEmpty(x.SubServiceTypeID_Products)).Select(x => x.SubServiceTypeID.ToString()).Distinct().ToArray() ?? new string[0];

                                    // voucher
                                    var SubServiceTypeID_S = tempVoucher.Where(x => !string.IsNullOrEmpty(x.SubServiceTypeID_Services)).FirstOrDefault()?.SubServiceTypeID_Services.Split(',') ?? new string[0];
                                    var SubServiceTypeID_P = tempVoucher.Where(x => !string.IsNullOrEmpty(x.SubServiceTypeID_Products)).FirstOrDefault()?.SubServiceTypeID_Products.Split(',') ?? new string[0];

                                    // serviceType dich vu input
                                    var subServiceTypeId_mapService = listOptionService_servicesInput.Where(x => x.policyId == policyId).Select(x => x.subServiceTypeId.ToString()).Distinct().ToArray() ?? new string[0];

                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("subServiceTypeId_DV_input", subServiceTypeId_DV_input));
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("SubServiceTypeID_S", SubServiceTypeID_S));
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("subServiceTypeId_TB_input", subServiceTypeId_TB_input));
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("SubServiceTypeID_P", SubServiceTypeID_P));

                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("subServiceTypeId_mapService", subServiceTypeId_mapService));

                                    //loai bo dich vu trien khai
                                    subServiceTypeId_mapService = subServiceTypeId_mapService.Except(SubtypeDeploy).ToArray();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("subServiceTypeId_map join SubtypeDeploy", subServiceTypeId_mapService));

                                    if ((SubServiceTypeID_S.All(x => subServiceTypeId_DV_input.Contains(x)) && SubServiceTypeID_S.Length == subServiceTypeId_DV_input.Length)
                                        && (SubServiceTypeID_S.All(x => subServiceTypeId_mapService.Contains(x)) && SubServiceTypeID_S.Length == subServiceTypeId_mapService.Length)
                                        && SubServiceTypeID_P.Any(x => subServiceTypeId_TB_input.Contains(x))
                                        && SubServiceTypeID_S.Length > 0 && SubServiceTypeID_P.Length > 0)
                                    {
                                        if (mapVoucher2?.Count > 0)
                                        {
                                            afterCondition.AddRange(mapVoucher2);
                                        }
                                    }
                                }
                            }
                            #endregion

                            sb.AppendLine(voucherCode + " end");
                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("afterCondition", afterCondition));

                        foreach (var voucherid in afterCondition.Select(x => x.VoucherID).Distinct())
                        {
                            var tempCheck = true;
                            var minValueModel = GetValueMinmoney(voucherid);
                            foreach (var RQID in minValueModel.Select(x => x.RQID).Distinct())
                            {
                                var minModels = minValueModel.Where(x => x.RQID == RQID).ToList();
                                var voucherModel = afterCondition.Where(x => x.VoucherID == voucherid).ToList();
                                var joinModel = (from evc in voucherModel
                                                 join minModel in minModels
                                                 on new { SubServiceID = evc.SubServiceID, Prepaid = evc.Prepaid }
                                                 equals new { SubServiceID = minModel.SubServiceID, Prepaid = minModel.Prepaid }
                                                 select minModel).ToList();
                                if (joinModel?.Count == minModels.Count)
                                {
                                    var totalMoney = (from ods in lstDataOrders
                                                      join m in minModels
                                                      on new { SubServiceID = ods.subServiceId, Prepaid = ods.prePaid }
                                                      equals new { SubServiceID = m.SubServiceID, Prepaid = m.Prepaid }
                                                      select ods).Sum(x => x.total_vat);
                                    var totalConectionFee = lstDataOrders.Where(x => x.subServiceId == 999999999 || x.subServiceId == 999999996).Sum(x => x.total_vat);
                                    if (totalMoney < minModels.FirstOrDefault().Value || totalConectionFee < minModels.FirstOrDefault().ConnectionFee) tempCheck = false;
                                }
                            }
                            if (tempCheck)
                            {
                                afterminvalue.AddRange(afterCondition.Where(x => x.VoucherID == voucherid).ToList());
                            }
                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("afterminvalue", afterminvalue));

                        #region Kiểm tra voucher theo tỉnh thành
                        if (afterminvalue.Count > 0)
                        {
                            var evc_check_location = ValidVoucherByLocation2(connection, afterminvalue, input.CustomerInfor.LocationID, input.CustomerInfor.BranchCode, input.CustomerInfor.DistrictID, sb);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evc_check_location", evc_check_location));
                            afterLocation.AddRange(evc_check_location);
                        }
                        #endregion
                        if (input.evoucherList != null)
                        {
                            var strErorr = new List<string>();
                            foreach (var item in input.evoucherList.Where(x => x.evoucherType == 1))
                            {
                                if (!afterLocation.Any(x => x.VoucherCode == item.evoucherCode))
                                {
                                    strErorr.Add(item.evoucherCode);
                                }
                            }
                            if (strErorr.Count > 0)
                            {
                                Error.Add("Phiếu mua hàng bạn chọn " + string.Join(", ", strErorr) + " đã hết hiệu lực. Vui lòng liên hệ quản lý chi nhánh.");
                            }
                        }

                        #region Kiểm tra hạn mức
                        if (afterLocation?.Count > 0)
                        {
                            var inputCheckQuota = afterLocation.Where(x => x.CheckQuota == 1).ToList();
                            if (inputCheckQuota?.Count > 0)
                            {
                                var lst_check_quota = ValidVoucherByQuota2(connection, inputCheckQuota, lstDataOrders, input, sb, Error);
                                afterCheckQuota.AddRange(lst_check_quota);
                            }

                            inputCheckQuota = afterLocation.Where(x => x.CheckQuota == 2).ToList();
                            if (inputCheckQuota?.Count > 0)
                            {
                                var lst_check_quota = ValidVoucherByQuotaVendor(connection, inputCheckQuota, sb);
                                afterCheckQuota.AddRange(lst_check_quota);
                            }
                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("afterCheckQuota", afterCheckQuota));
                        #endregion

                        //foreach (var item in afterCheckQuota)
                        //{
                        //    lstVoucherFinal.Add(new Evoucher
                        //    {
                        //        Description = item.Description,
                        //        Note = item.Note,
                        //        Todate = item.ToDate,
                        //        VoucherCode = item.VoucherCode,
                        //        RegisterTypeID = item.RegisterTypeID,
                        //        PolicyGroupID = item.PolicyGroupID,
                        //        PromotionTypeID = item.PromotionTypeID,
                        //        ApplyTypeID = item.ApplyTypeAltID
                        //    });
                        //}
                    }
                }

            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher Error", ex.Message));
                return null;
            }

            //listVoucher.LstVoucher = lstVoucherFinal.Distinct().ToList();
            listVoucher.LstVoucher = afterCheckQuota;
            listVoucher.Error = Error;

            return listVoucher;
        }

        public static List<PolicyVoucher> ValidVoucherByQuota(SqlConnection connection, List<PolicyVoucher> lstVoucher, List<PolicyVoucher> lstVoucherAll, List<CustomerSaleOrderCollections> lstServices, int locationId, int branchCode, int districtId, int saleId, StringBuilder sb)
        {
            var lstEVC = new List<PolicyVoucher>();
            foreach (var voucher in lstVoucher)
            {
                decimal valueDiscount = 0;
                decimal quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                {
                    VoucherCode = voucher.VoucherCode,
                    SaleId = saleId,
                    LocationId = locationId,
                    BranchCode = branchCode,
                    DistrictId = districtId

                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("quotaMoneyAvailable " + voucher.VoucherCode + " ", quotaMoneyAvailable));

                if (voucher.applyTypeID.Equals(2))
                {
                    var dataVoucher = lstVoucherAll.Where(x => x.VoucherCode == voucher.VoucherCode).ToList();
                    var dataMapVoucher = (from ser in lstServices
                                          join evc in dataVoucher on new { serviceId = ser.serviceId, subServiceTypeId = ser.subServiceTypeId, subServiceId = ser.subServiceId, serviceCode = ser.serviceCode } equals
                                            new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                          select new
                                          {
                                              serviceId = evc.serviceId,
                                              subServiceTypeId = evc.subServiceTypeId,
                                              subServiceId = evc.subServiceId,
                                              serviceCode = evc.serviceCode,
                                              total_vat = ser.total_vat,
                                              monthUsed = ser.monthUsed,
                                              qty = ser.qty,
                                              valueVoucher = evc.valueVoucher
                                          }).ToList();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataMapVoucher " + voucher.VoucherCode + " ", dataMapVoucher));
                    if (voucher.promotionTypeId.Equals(4))//fix code vì id loại voucher ko thể tăng (4 là thặng tháng)
                    {
                        valueDiscount = dataMapVoucher.Count > 0 ? dataMapVoucher.Sum(a => a.total_vat / a.monthUsed) * voucher.duration : 0;
                    }
                    else
                    {
                        //valueDiscount = (Math.Max(voucher.duration, 1) * voucher.valueVoucher);
                        valueDiscount = dataMapVoucher.Sum(x => Math.Max(voucher.duration, 1) * x.valueVoucher * x.qty);
                    }
                }
                else
                {
                    var dataVoucher = GetServices_PHM(voucher.VoucherCode);

                    var dataMapVoucher = (from pro in lstServices
                                          join evc in dataVoucher on new { serviceId = pro.serviceId, subServiceTypeId = pro.subServiceTypeId, subServiceId = pro.subServiceId, serviceCode = pro.serviceCode } equals
                                            new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                          where evc.applyTypeID != 2
                                          select pro).ToList();


                    valueDiscount = dataMapVoucher == null ? 0 : dataMapVoucher.Sum(x => x.qty) * voucher.valueVoucher;
                }

                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("valueDiscount for voucher " + voucher.VoucherCode + " ", valueDiscount));
                if (quotaMoneyAvailable >= valueDiscount && quotaMoneyAvailable > 0 && valueDiscount > 0)
                {
                    lstEVC.Add(voucher);
                }
            }
            return lstEVC;
        }

        public static List<PolicyVoucher2> ValidVoucherByQuota2(SqlConnection connection, List<PolicyVoucher2> afterCondition, List<CustomerSaleOrderCollections> lstOrders, SalePolicyGetInforEVC input, StringBuilder sb, List<string> error)
        {
            var afterCheckQuota = new List<PolicyVoucher2>();
            var Erorrs = new List<string>();

            if (afterCondition == null)
            {
                return afterCheckQuota;
            }

            var unitParent = GetUnitParent(input.SaleInfor.SaleID, sb);
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("unitParent ", unitParent));

            foreach (var policyID in afterCondition.Select(x => x.PolicyID).Distinct().ToList())
            {
                var ExirstEroor = 0;
                double totalDiscount = 0;
                string code = afterCondition.Where(x => x.PolicyID == policyID).FirstOrDefault().VoucherCode;
                double quotaMoneyAvailable = connection.Query<double>(OS6_FPTVoucher_CheckQuotaMoney, new
                {
                    VoucherCode = code,
                    SaleId = input.SaleInfor.SaleID,
                    LocationId = input.CustomerInfor.LocationID,
                    BranchCode = input.CustomerInfor.BranchCode,
                    DistrictId = input.CustomerInfor.DistrictID,
                    UnitParentID = unitParent != null ? unitParent.ParentId : 0

                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                foreach (var voucherCode in afterCondition.Where(x => x.PolicyID == policyID).Select(x => x.VoucherCode).Distinct().ToList())
                {
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("QuotaMoneyAvailable " + voucherCode, quotaMoneyAvailable));
                    double valueDiscount = 0;
                    var voucher = afterCondition.Where(x => x.VoucherCode == voucherCode).FirstOrDefault();
                    var dataMapVoucher = (from ods in lstOrders
                                          join evc in afterCondition.Where(x => x.VoucherCode == voucherCode).ToList()
                                          on new { serviceId = ods.serviceId, subServiceTypeId = ods.subServiceTypeId, subServiceId = ods.subServiceId, serviceCode = ods.serviceCode, deviceStatusID = ods.statusId, deployTypeID = ods.deployTypeId }
                                          equals new { serviceId = evc.ServiceID, subServiceTypeId = evc.SubServiceTypeID, subServiceId = evc.SubServiceID, serviceCode = evc.ServiceCode, deviceStatusID = evc.DeviceStatusID, deployTypeID = evc.DeployTypeID }
                                          select new
                                          {
                                              serviceId = evc.ServiceID,
                                              subServiceTypeId = evc.SubServiceTypeID,
                                              subServiceId = evc.SubServiceID,
                                              serviceCode = evc.ServiceCode,
                                              total_vat = ods.total_vat,
                                              monthUsed = ods.monthUsed,
                                              qty = ods.qty,
                                              valueVoucher = evc.ValueVoucher,
                                              maxQuannity = evc.MaxQuantity
                                          }).ToList();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataMapVoucher " + voucher.VoucherCode + " ", dataMapVoucher));

                    if (dataMapVoucher?.Any() == true)
                    {
                        if (voucher.ApplyTypeID.Equals(2))
                        {
                            if (voucher.PromotionTypeID.Equals(4))//fix code vì id loại voucher ko thể tăng (4 là thặng tháng)
                            {
                                valueDiscount = dataMapVoucher.Sum(a => a.total_vat / a.monthUsed) * voucher.MonthValue;
                            }
                            else
                            {
                                valueDiscount = dataMapVoucher.Sum(x => Math.Max(voucher.MonthValue, 1) * x.valueVoucher * x.qty);
                            }
                        }
                        else
                        {
                            valueDiscount = dataMapVoucher.Sum(x => x.valueVoucher * CalcMaxQuantity(x.qty, x.maxQuannity));
                        }

                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Discount " + voucher.VoucherCode + " ", valueDiscount));
                        if (quotaMoneyAvailable >= valueDiscount && quotaMoneyAvailable > 0 && valueDiscount > 0)
                        {
                            afterCheckQuota.Add(voucher);
                        }
                        else if (input.evoucherList?.Count > 0 && input.evoucherList.Where(x => x.evoucherCode == voucher.VoucherCode).Count() > 0)
                        {
                            ExirstEroor++;
                            Erorrs.Add("Hạn mức khả dụng còn lại " + voucher.VoucherCode + " chỉ được áp dụng tối đa " + string.Format("{0:n0}", quotaMoneyAvailable) + " VNĐ.");
                        }

                        if (input.evoucherList?.Count > 0)
                        {
                            if (input.evoucherList.Where(x => x.evoucherCode == voucher.VoucherCode).Any())
                            {
                                totalDiscount += valueDiscount;
                            }
                        }
                    }
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalDiscount", totalDiscount));
                if (quotaMoneyAvailable < totalDiscount && totalDiscount > 0 && ExirstEroor == 0)
                {
                    Erorrs.Add("Hạn mức khả dụng còn lại " + String.Join("+", afterCondition.Where(x => x.PolicyID == policyID).Select(x => x.VoucherCode).Distinct().ToList()) + " chỉ được áp dụng tối đa " + string.Format("{0:n0}", quotaMoneyAvailable) + " VNĐ.");
                }
            }
            if (Erorrs.Count > 0)
            {
                error.Add(string.Join(" ", Erorrs.Distinct()) + " Vui lòng kiểm tra lại thông tin đơn hàng hoặc liên hệ BGĐ CN để được cấp thêm.");
            }

            return afterCheckQuota;
        }

        public static List<PolicyVoucher> ValidVoucherByLocation(SqlConnection connection, List<PolicyVoucher> lst, int locationId, int branchCode, int districtId, StringBuilder sb)
        {
            var lstEVC = new List<PolicyVoucher>();
            var list_policy = new List<int>();
            list_policy = connection.Query<int>(OSU6_FPTVoucher_PolicyVoucher_CheckLocation, new
            {
                XML = CreateXMLPolicy(lst, sb),
                LocationId = locationId,
                BranchCode = branchCode,
                DistrictId = districtId
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            //1+1 sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list policy id available by location", list_policy));

            if (list_policy.Count > 0)
            {
                var evclist = (from voucher in lst
                               where list_policy.Contains(voucher.programId)
                               select voucher).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list evoucher available by location", evclist));

                if (evclist.Count > 0)
                {
                    lstEVC.AddRange(evclist);
                }
            }
            return lstEVC;
        }

        public static List<PolicyVoucher2> ValidVoucherByLocation2(SqlConnection connection, List<PolicyVoucher2> lst, int locationId, int branchCode, int districtId, StringBuilder sb)
        {
            var lstEVC = new List<PolicyVoucher2>();
            var list_policy = new List<int>();
            list_policy = connection.Query<int>(OSU6_FPTVoucher_PolicyVoucher_CheckLocation, new
            {
                XML = CreateXMLPolicy2(lst, sb),
                LocationId = locationId,
                BranchCode = branchCode,
                DistrictId = districtId
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            //1+1 sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list policy id available by location", list_policy));

            if (list_policy.Count > 0)
            {
                var evclist = (from voucher in lst
                               where list_policy.Contains(voucher.ProgramID)
                               select voucher).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list evoucher available by location", evclist));

                if (evclist.Count > 0)
                {
                    lstEVC.AddRange(evclist);
                }
            }
            return lstEVC;
        }

        public static List<PolicyVoucher> ValidVoucherByCodition(SqlConnection connection, List<PolicyVoucher> lst, string[] _serviceOrders_all, string[] _serviceOrders_dv, string[] _serviceOrders_tb, List<CustomerSaleOrderCollections> lstServices, StringBuilder sb)
        {
            var lstEVC_policy = new List<PolicyVoucher>();
            foreach (var voucher in lst)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucher", voucher));

                string[] serviceVoucher = voucher.voucherService.Split(',');
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceVoucher", serviceVoucher));

                // kiểm tra đơn hàng có đủ SubServiceType của evc không
                if ((voucher.applyFor == 3 && serviceVoucher.All(x => _serviceOrders_all.Contains(x) && serviceVoucher.Length == _serviceOrders_all.Length))
                    || (voucher.applyFor == 1 && serviceVoucher.All(x => _serviceOrders_dv.Contains(x) && serviceVoucher.Length == _serviceOrders_dv.Length))
                    || (voucher.applyFor == 2 && serviceVoucher.All(x => _serviceOrders_tb.Contains(x) && serviceVoucher.Length == _serviceOrders_tb.Length)))
                {
                    if (voucher.minmoney > 0 || voucher.minprepaid > 0 || voucher.minConnectionFee > 0)
                    {
                        //var listSubservice_by_condition = FilterSubservice(sb, connection, lstServices, voucher.subServiceCondition);\

                        var lst_phm_Services = lstServices.Where(x => x.subServiceId == 999999999 || x.subServiceId == 999999996).ToList();
                        //var list_phm_by_condition = FilterService(lst_phm_Services, voucher.voucherService); // lấy thiết bị để cộng tiền check phí hòa mạng
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lst_phm_Services", lst_phm_Services));

                        if (lstServices.Count > 0 || lst_phm_Services.Count > 0)
                        {
                            int totalMoney = lstServices.Sum(a => a.total_vat);
                            int totalMoneyConnFee = lst_phm_Services.Sum(a => a.total_vat);
                            int minPrepaid = 0;
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalMoney", totalMoney));
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalMoneyConnFee", totalMoneyConnFee));

                            if (lstServices.Count > 0)
                            {
                                minPrepaid = lstServices.FirstOrDefault(x => x.serviceId == voucher.serviceId && x.subServiceId == voucher.subServiceId).prePaid;
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("minPrepaid", minPrepaid));

                                if ((totalMoney >= voucher.minmoney && totalMoneyConnFee >= voucher.minConnectionFee && minPrepaid == voucher.minprepaid) // khớp all đk tiền tối thiểu => oke
                                    || !voucher.subServiceCondition.Contains(voucher.subServiceId.ToString())//serviceID không nằm trong đk tiền tối thiểu => oke
                                    || voucher.subServiceCondition.Contains(voucher.subServiceId.ToString()) && minPrepaid != voucher.minprepaid//khớp đk tiền tối thiểu nhưng ko khớp số tháng => oke
                                    )
                                {
                                    lstEVC_policy.Add(voucher);
                                }
                            }
                            else
                            {
                                lstEVC_policy.Add(voucher);
                            }
                        }
                    }
                    else
                    {
                        lstEVC_policy.Add(voucher);
                    }
                }
            }
            return lstEVC_policy;
        }
        public static List<CustomerSaleOrderCollections> FilterSubservice(StringBuilder sb, SqlConnection connection, List<CustomerSaleOrderCollections> input, string datafillter)
        {
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FilterSubservice input", input));

            var listres = new List<CustomerSaleOrderCollections>();
            var lstService = new List<ServiceGroup>();
            lstService = connection.Query<ServiceGroup>(OS6_FPTVoucher_VoucherFollowPolicy, new
            {
                actionName = "GetGroupService",
                subServices = datafillter
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FilterSubservice GetGroupService", lstService));

            foreach (var service in lstService)
            {
                string[] _subservices = service.SubServiceId.Split(',').Select(x => x.Trim()).ToArray();
                var data = input.Where(x => x.serviceId == service.ServiceID).ToList();
                if (data.Count > 0)
                {
                    var dataList = (from item in data
                                    where _subservices.Contains(item.subServiceId.ToString())
                                    select item).ToList();
                    if (dataList.Count > 0)
                    {
                        listres.AddRange(dataList);
                    }
                    else
                    {
                        listres = new List<CustomerSaleOrderCollections>();
                        break;
                    }
                }
                else
                {
                    listres = new List<CustomerSaleOrderCollections>();
                    break;
                }
            }

            return listres;
        }

        public static List<CustomerSaleOrderCollections> FilterService(List<CustomerSaleOrderCollections> input, string datafillter)
        {
            string[] _arrayFilter = datafillter.Split(',');

            var result = input.Where(item => _arrayFilter.Contains(item.serviceId.ToString()) && input.Count(x => _arrayFilter.Contains(x.serviceId.ToString())) == _arrayFilter.Count()).ToList();
            return result;
        }

        private static XElement CreateXMLPolicy(List<PolicyVoucher> lstEVC_policy, StringBuilder sb)
        {
            var xmlString = new XElement("N",
            from evc in lstEVC_policy
            select new XElement("I",
                           new XElement("PolicyId", evc.policyId),
                           new XElement("ProgramId", evc.programId)//
                       ));
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher CreateXMLPolicy ", xmlString));
            return xmlString;
        }
        private static XElement CreateXMLPolicy2(List<PolicyVoucher2> lstEVC_policy, StringBuilder sb)
        {
            var xmlString = new XElement("N",
            from evc in lstEVC_policy
            select new XElement("I",
                           new XElement("PolicyId", evc.PolicyID),
                           new XElement("ProgramId", evc.ProgramID)//
                       ));
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher CreateXMLPolicy ", xmlString));
            return xmlString;
        }
        public static XElement CreateXMLSubService(List<CustomerSaleOrderCollections> Collections, SalePolicyGetInforEVC input)
        {
            int saleChanel = input.SaleInfor == null ? -1 : input.SaleInfor.SaleChannelID;
            int saleTeam = input.SaleInfor == null ? -1 : input.SaleInfor.SaleTeamID;

            var xmlString = new XElement("N",
            from item in Collections
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("prePaid", item.prePaid),
                           new XElement("deployTypeId", item.deployTypeId),
                           new XElement("statusId", item.statusId),
                           new XElement("revokeId", item.revokeID),
                           new XElement("usesId", item.usesId),
                           new XElement("qty", item.qty),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("total", item.total),
                           new XElement("total_vat", item.total_vat),
                           new XElement("policyId", item.policyId),
                           new XElement("crossSellingLocation", item.crossSellingLocation),
                           new XElement("paymentTypeId", item.paymentTypeId),
                           //new XElement("minmoney", minmoney),
                           new XElement("customerType", item.customerType),
                           new XElement("cusTypeId", item.cusTypeId),
                           new XElement("cusTypeL2Id", item.cusTypeL2Id),
                           new XElement("objectTypeId", item.objectTypeId),
                           new XElement("customerRank", item.customerRank),
                           new XElement("saleChannel", saleChanel),
                           new XElement("arrPolicyInput", item.arrPolicyInput),
                           new XElement("totalMoneyPolicyInput", item.totalMoneyPolicyInput),
                           new XElement("saleTeamId", saleTeam)
                       //new XElement("serviceRegister", s_services)
                       ));
            return xmlString;
        }

        #region get infor voucher
        public static bool CheckVoucherGC(string voucherCode)
        {
            bool trueVoucher = false;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int StatusCodeVC = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetStatusVoucherCode",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                trueVoucher = (StatusCodeVC == 1);
            }
            return trueVoucher;
        }

        public static SalePlatformVoucherValueInfor GetVoucherGCinfo(List<ServicePlatform> lstSer)
        {
            SalePlatformVoucherValueInfor voucherinfo = new SalePlatformVoucherValueInfor();
            voucherinfo.Apply = new List<Apply>();
            voucherinfo.RefInfo = new RefInfo();

            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {

                }
            }
            catch (Exception ex)
            {

                return null;
            }
            return voucherinfo;
        }

        public static int GetTypeVoucher(string voucherCode)
        {
            int type = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                type = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetTypeVoucher",
                    voucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return type;
        }

        public static XElement CreateXMLGetInfor(SalePolicyGetInforEVC input)
        {
            var listServices = (from service in input.Services
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeID,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed)
                                }).ToList();

            var xmlServices = new XElement("N",
            from item in listServices
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("statusId", -1),
                           new XElement("revokeID", -1),
                           new XElement("deployTypeId", -1)
                       ));

            var listProducts = (from service in input.Products
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    statusId = subService.StatusID,
                                    revokeID = subService.RevokeID,
                                    deployTypeId = subService.DeployTypeID
                                }).ToList();

            var xmlProducts = new XElement("N",
            from item in listProducts
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("statusId", item.statusId),
                           new XElement("revokeID", item.revokeID),
                           new XElement("deployTypeId", item.deployTypeId)
                       ));

            if (xmlProducts != null)
            {
                xmlServices.Add(xmlProducts);
            }

            return xmlServices;
        }

        public static XElement CreateXMLGetInforProduct(SalePolicyGetInforEVC input, int i)
        {
            var listProducts = (from service in input.Products
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed)
                                }).ToList();

            var xmlString = new XElement("N",
            from item in listProducts
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed)
                       //new XElement("serviceRegister", s_services)
                       ));
            return xmlString;
        }

        public static List<Apply> GetListInforService(string voucherCode, XElement xml)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetVoucherValue",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static List<Apply> GetListInforProductService(string voucherCode, XElement xml)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetInforProductService",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }


        public static decimal CheckQuota(string voucherCode, SaleInfor saleInfor, CustomerInfor customerInfor) // đơn vị: VNĐ
        {
            decimal quotaMoneyAvailable = 0;
            //hạn mức sale còn lại
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                {
                    VoucherCode = voucherCode,
                    SaleId = saleInfor.SaleID,
                    LocationId = customerInfor.LocationID,
                    BranchCode = customerInfor.BranchCode,
                    DistrictId = customerInfor.DistrictID

                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }

            return quotaMoneyAvailable;
        }

        public static List<RegisterVoucherType> GetRegisterTypeVoucher(string evc_code)
        {
            List<RegisterVoucherType> resDataa = new List<RegisterVoucherType>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                resDataa = connection.Query<RegisterVoucherType>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetRegisterTypeVoucher",
                    voucherCode = evc_code
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                return resDataa;
            }
        }

        public static int GetPromotionType(string voucherCode)
        {
            int i = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                i = connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                {
                    Action = "GetPromotionTypeId",
                    VoucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return i;
        }
        #endregion

        public static List<EvoucherService> GetListEvcService(Guid logId, List<EvoucherInput> lstEVC)
        {
            List<EvoucherService> lstVoucher = new List<EvoucherService>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();

                if (lstEVC != null)
                {
                    try
                    {
                        foreach (var evc in lstEVC)
                        {
                            List<EvoucherService> voucherServices = new List<EvoucherService>();
                            voucherServices = connection.Query<EvoucherService>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                            {
                                Action = "GetServiceVoucher",
                                VoucherCode = evc.evoucherCode
                            }, commandType: CommandType.StoredProcedure).ToList();
                            voucherServices.Select(x => x.evoucherType = evc.evoucherType).ToList();
                            if (voucherServices != null)
                                lstVoucher.AddRange(voucherServices);

                            var voucherProducts = connection.Query<EvoucherService>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                            {
                                Action = "GetServiceVoucherProduct",
                                VoucherCode = evc.evoucherCode
                            }, commandType: CommandType.StoredProcedure).ToList();
                            voucherProducts.Select(x => x.evoucherType = evc.evoucherType).ToList();
                            if (voucherProducts != null)
                                lstVoucher.AddRange(voucherProducts);
                        }
                    }
                    catch (Exception ex)
                    {
                        MBSv4CommonService.WriteToLog(ex.Message, "MBSv4 KeepVoucher getListEvcService error: ", logId);
                    }
                }
            }

            return lstVoucher;
        }

        private static XElement CreateXMLExtendHistoryTemp(SalePolicyRedeemEVC input, List<int> historyTempIds)
        {
            var xmlString = new XElement("N",
                from id in historyTempIds
                select new XElement("I",
                new XElement("HistoryTempId", id),
                new XElement("PhoneNumberContract", input.CustomerPhone?.PhoneNumberContract),
                new XElement("PhoneNumberCMR", input.CustomerPhone?.PhoneNumberCMR),
                new XElement("LocationId", input.CustomerInfor?.LocationID),
                new XElement("DistrictId", input.CustomerInfor?.DistrictID),
                new XElement("WardId", input.CustomerInfor?.WardID),
                new XElement("BuildingId", input.CustomerInfor?.BuildingID),
                new XElement("CusTypeId", input.CustomerInfor?.CusTypeID),
                new XElement("CusTypeL2Id", input.CustomerInfor?.CusTypeL2ID),
                new XElement("ObjectTypeId", input.CustomerInfor?.ObjectTypeID),
                new XElement("CustomerRank", input.CustomerInfor?.CustomerRank),
                new XElement("CrossSellingLocation", input.CrossSellingLocation),
                new XElement("ServiceAvailable", input.ServiceAvailable != null ? string.Join(",", input.ServiceAvailable) : ""),
                new XElement("SaleId", input.SaleInfor?.SaleID),
                new XElement("SaleTeamId", input.SaleInfor?.SaleTeamID),
                new XElement("SaleChannelId", input.SaleInfor?.SaleChannelID),
                new XElement("PaymentTypeId", input.PaymentTypeID),
                new XElement("GroupPoint", input.GroupPoint),
                new XElement("ActiveKplus", input.ExtendedProperties?.ActiveKplus),
                new XElement("InternetServiceId", input.ExtendedProperties?.Internet?.ServiceID),
                new XElement("InternetChargeMonthly", input.ExtendedProperties?.Internet?.ChargeMonthly)));


            return xmlString;
        }

        public static List<int> GetHistoryTempIds(SqlConnection connection, SqlTransaction transaction, string orderCode, int objId, string voucherCode)
        {
            return connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "GetHistoryTempIds",
                OrderCode = orderCode,
                ObjId = objId,
                VoucherCode = voucherCode
            }, transaction: transaction, commandTimeout: 0, commandType: CommandType.StoredProcedure).ToList();
        }

        public static bool AddExtendHistoryTemp(SqlConnection connection, SqlTransaction transaction, List<int> historyTempIds, SalePolicyRedeemEVC input)
        {
            return connection.Execute(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "AddExtendHistoryTemp",
                XML = CreateXMLExtendHistoryTemp(input, historyTempIds)
            }, transaction: transaction, commandTimeout: 0, commandType: CommandType.StoredProcedure) > 0;
        }

        public static List<CustomerSaleOrderCollections> GetServicesFromInput(SalePolicyGetInforEVC input)
        {
            var services = new List<CustomerSaleOrderCollections>();

            var listServices = (from service in input.Services
                                join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeID,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    prePaid = Convert.ToInt32(subService.PrePaid),
                                    deployTypeId = subService.DeployTypeID,
                                    qty = subService.Qty,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    total = Convert.ToInt32(subService.Total),
                                    total_vat = Convert.ToInt32(subService.Total_VAT),
                                    policyId = subService.PolicyID,
                                    customerType = customertype.CustomerType,
                                    crossSellingLocation = input.CrossSellingLocation,
                                    paymentTypeId = input.PaymentTypeID,
                                    cusTypeId = input.CustomerInfor.CusTypeID,
                                    cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                    objectTypeId = input.CustomerInfor.ObjectTypeID,
                                    customerRank = input.CustomerInfor.CustomerRank
                                }).ToList();

            var listProducts = (from product in input.Products
                                join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                from subServiceType in product.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = product.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    prePaid = subService.ApplyPrePaid,
                                    deployTypeId = subService.DeployTypeID,
                                    statusId = subService.StatusID,
                                    revokeID = subService.RevokeID,
                                    usesId = subService.UsesID,
                                    qty = subService.Qty,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    total = Convert.ToInt32(subService.Total),
                                    total_vat = Convert.ToInt32(subService.Total_VAT),
                                    policyId = subService.PolicyID,
                                    customerType = customertype.CustomerType,
                                    crossSellingLocation = input.CrossSellingLocation,
                                    paymentTypeId = input.PaymentTypeID,
                                    cusTypeId = input.CustomerInfor.CusTypeID,
                                    cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                    objectTypeId = input.CustomerInfor.ObjectTypeID,
                                    customerRank = input.CustomerInfor.CustomerRank
                                }).ToList();
            if (!(listServices is null))
            {
                services.AddRange(listServices);
            }
            if (!(listProducts is null))
            {
                services.AddRange(listProducts);
            }
            return services;
        }

        public static List<PolicyVoucher> GetServices_PHM(string voucherCode)
        {
            var data = new List<PolicyVoucher>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<PolicyVoucher>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetServices_PHM",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static List<CustomerSaleOrderCollections> formatServices(SalePolicyGetInforEVC input, string ServiceOrProduct)
        {
            if (ServiceOrProduct == "Service")
            {
                var listServices = (from service in input.Services
                                    join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                    from subServiceType in service.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = service.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeID,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = Convert.ToInt32(subService.PrePaid),
                                        deployTypeId = 0,//subService.DeployTypeID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listServices;
            }
            if (ServiceOrProduct == "Product")
            {
                var listProducts = (from product in input.Products
                                    join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                    from subServiceType in product.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = product.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeId,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = subService.ApplyPrePaid,
                                        deployTypeId = subService.DeployTypeID,
                                        statusId = subService.StatusID,
                                        revokeID = subService.RevokeID,
                                        usesId = subService.UsesID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listProducts;
            }
            return new List<CustomerSaleOrderCollections>();
        }

        public static List<CustomerSaleOrderCollections> formatServicesGTBB(SalePolicyGetInforEVC input, string ServiceOrProduct)
        {
            if (ServiceOrProduct == "Service")
            {
                var listServices = (from service in input.Services
                                    join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                    from subServiceType in service.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = service.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeID,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = Convert.ToInt32(subService.PrePaid),
                                        deployTypeId = subService.DeployTypeID,
                                        statusId = -1,
                                        revokeID = -1,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listServices;
            }
            if (ServiceOrProduct == "Product")
            {
                var listProducts = (from product in input.Products
                                    join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                    from subServiceType in product.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = product.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeId,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = subService.ApplyPrePaid,
                                        deployTypeId = subService.DeployTypeID,
                                        statusId = subService.StatusID,
                                        revokeID = subService.RevokeID,
                                        usesId = subService.UsesID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listProducts;
            }
            return new List<CustomerSaleOrderCollections>();
        }

        public static List<CustomerSaleOrderCollections> formatServices(SalePolicyRedeemEVC input, string ServiceOrProduct)
        {
            if (ServiceOrProduct == "Service")
            {
                var listServices = (from service in input.Services
                                    join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                    from subServiceType in service.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = service.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeID,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = Convert.ToInt32(subService.PrePaid),
                                        deployTypeId = subService.DeployTypeID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listServices;
            }
            if (ServiceOrProduct == "Product")
            {
                var listProducts = (from product in input.Products
                                    join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                    from subServiceType in product.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = product.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeId,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = subService.ApplyPrePaid,
                                        deployTypeId = subService.DeployTypeID,
                                        statusId = subService.StatusID,
                                        revokeID = subService.RevokeID,
                                        usesId = subService.UsesID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                return listProducts;
            }
            return new List<CustomerSaleOrderCollections>();
        }
        public static List<CustomerSaleOrderCollections> formatPolicyInput(List<CustomerSaleOrderCollections> services)
        {
            // Duyệt qua từng policyId
            foreach (var item in services)
            {
                // Lấy danh sách các serviceIds liên quan đến policyId hiện tại
                var serviceIds = services.Where(ps => ps.policyId == item.policyId)
                                               .Select(ps => ps.serviceId)
                                               .Distinct()
                                               .OrderBy(id => id)
                                               .ToList();

                // Nối các serviceIds lại
                item.arrPolicyInput = string.Join(",", serviceIds);

                item.totalMoneyPolicyInput = services.Where(x => x.policyId == item.policyId).Select(x => x.total_vat).Sum();
            }

            return services;
        }

        public static int checkGoldenDate(SqlConnection connection, string policyIds)
        {

            int goldenPolicy = connection.Query<int>(OS6_FPTVoucher_VoucherFollowPolicy, new
            {
                actionName = "CheckPolicyGoldenDate",
                policyIds = policyIds
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return goldenPolicy;
        }

        public static float GetValueByKeyConfig(string str)
        {
            using (var conn = new SqlConnection(SqlHelper.ConnRead()))
            {
                return conn.Query<float>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetVoucherConfig",
                    keyconfig = str
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static List<MinmoneyModel> GetValueMinmoney(int voucherid)
        {
            var data = new List<MinmoneyModel>();
            using (var conn = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = conn.Query<MinmoneyModel>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetValueMinmoney",
                    voucherID = voucherid
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
            return data;
        }

        public static List<PolicyVoucher2> ValidVoucherByQuotaVendor(SqlConnection connection, List<PolicyVoucher2> afterCondition, StringBuilder sb)
        {
            var afterCheckQuota = new List<PolicyVoucher2>();

            var polyciAvailable = connection.Query<int>(OS6_FPTVoucher_FSale, new
            {
                actionName = "CheckQuotaVendor",
                xml = CreateXMLPolicy2(afterCondition, sb),
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            afterCheckQuota = afterCondition.Where(x => polyciAvailable.Contains(x.PolicyID)).ToList();

            return afterCheckQuota;
        }

        public static EmployeeInfoData GetUnitParent(int saleID, StringBuilder sb)
        {
            var rs = new EmployeeInfoData();

            try
            {
                var client = new HttpClient();

                var response = client.GetAsync(Utility.bpapi_fpt_vn + "/api/ins/GetEmployeeInfo?saleID=" + saleID).Result;

                var responseContent = response.Content.ReadAsStringAsync().Result;

                var result = JsonConvert.DeserializeObject<EmployeeInfo>(responseContent);

                rs = result.Data;

            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetUnitParentID catch", e));
            }

            return rs;
        }

        public static string[] GetSubtypeDeploy(SqlConnection conn, int GroupID)
        {
            var lst = new List<DXBaseConfig>();
            lst = conn.Query<DXBaseConfig>("PowerInside..OS6_DXBusinessPolicy_BaseConfig_Get", new
            {
                GroupID = GroupID
            }, commandType: CommandType.StoredProcedure).ToList();

            if (!(lst?.Count > 0))
            {
                return new string[0];
            }

            List<string> stringList = lst.ConvertAll(i => (string)(i.FValue + ""));

            string[] stringArray = stringList.ToArray();

            return stringArray;
        }

        public static int CalcMaxQuantity(int qty, int maxQuantity)
        {
            return maxQuantity == 0 ? qty : maxQuantity;
        }
    }
}