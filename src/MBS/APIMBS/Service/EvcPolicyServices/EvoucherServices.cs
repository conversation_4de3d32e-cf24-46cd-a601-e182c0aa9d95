using APIMBS.Constant;
using APIMBS.Models;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models.SalePlatform;
using Dapper;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Services.Description;
using System.Xml;
using System.Xml.Linq;
using Voucher.APIHelper;
using Voucher.APIHelper.ShareModel;

namespace APIMBS.Service.EvcPolicyServices
{
    public class EvoucherServices
    {
        public const string OS6_FPTVoucher_VoucherConfigCURD = "PowerInside.dbo.OS6_FPTVoucher_VoucherConfigCURD";
        public static bool CheckSaleTeamGTBB(int saleTeamId)
        {
            var saleTeamIdAllow = new List<int>();
            using (var conn = new SqlConnection(Utility.ConnRead))
            {
                saleTeamIdAllow = conn.Query<int>(OS6_FPTVoucher_VoucherConfigCURD, new
                {
                    action = "GetValuesByKey",
                    key = "SaleTeamGTBB"
                }, commandType: CommandType.StoredProcedure).ToList();
            }
            return saleTeamIdAllow.Contains(saleTeamId);
        }
    }
}