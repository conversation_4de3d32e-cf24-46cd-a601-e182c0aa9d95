using APIMBS.Models.SalePlatform;
using Dapper;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using APIMBS.Models.EvcPolicy;
using System.Collections.Generic;
using System.Xml.Linq;
using Voucher.APIHelper;
using Newtonsoft.Json;
using System.Net.Http;
using System.Web;

namespace APIMBS.Service.EvcPolicyServices
{
    public static class RedeemVoucherServices
    {
        private const string OS6_FPTVoucher_PolicyRedeem = "PowerInside..OS6_FPTVoucher_PolicyRedeem";
        private const string OS6_FPTVoucher_FSale = "PowerInside.dbo.OS6_FPTVoucher_FSale";
        private const string OSU6_FPTVoucher_PolicyVoucher_VoucherLimit_UpdateUsedMoney = "PowerInside.dbo.OSU6_FPTVoucher_PolicyVoucher_VoucherLimit_UpdateUsedMoney";
        private const string OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable = "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable";
        private const string OSU6_FPTVoucher_UpdateActiveStatus = "PowerInside.dbo.OSU6_FPTVoucher_UpdateActiveStatus";


        public static bool RedeemVoucher(SqlConnection connection, SqlTransaction transaction, SalePolicyRedeemEVC input, StringBuilder sb)
        {
            var result = true;
            var privateCode = "";

            try
            {
                foreach (var evc in input.evoucherList.Where(x => x.evoucherType == 1 || VendorManager.CheckFormatVoucher(x.evoucherCode) != ""))
                {
                    if (evc.evoucherType == 4)
                    {
                        continue;
                    }
                    // Check redeem
                    if (CheckRedeem(connection, transaction, input.objId, evc.evoucherCode, input.RegCode))
                    {
                        continue;
                    }

                    //[?]
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("code::", evc.evoucherCode));
                    var addGCID = AddGeneralCode(connection, transaction, input, evc.evoucherCode, sb);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("addGCID", addGCID));

                    if (addGCID > 0)
                    {
                        foreach (var applyMoney in input.ApplyMoney.Where(x => x.VoucherCode == evc.evoucherCode))
                        {
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("ServiceCode::", applyMoney.ServiceCode));

                            if (VendorManager.CheckFormatVoucher(applyMoney.VoucherCode) != "")
                            {
                                privateCode = VendorManager.CheckFormatVoucher(applyMoney.VoucherCode);
                                applyMoney.VoucherCode = GetPolicyVoucher(connection, transaction, VendorManager.CheckFormatVoucher(applyMoney.VoucherCode));
                            }

                            // lấy data voucher[?]
                            var voucher = GetVoucherToRedeem(connection, transaction, applyMoney.VoucherCode, applyMoney.ServiceID, applyMoney.SubServiceTypeID, applyMoney.SubServiceID, applyMoney.ServiceCode);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("vouchers", voucher));
                            if (voucher == null)
                            {
                                return false;
                            }

                            //add detail[?]
                            var detailID = AddDetail(connection, transaction, addGCID, voucher, applyMoney);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("detailID", detailID));
                            if (!(detailID > 0))
                            {
                                return false;
                            }

                            //
                            if (voucher.RegisterTypeID == 2)
                            {
                                var addScheduleID = AddSchedulePolicy(connection, transaction, addGCID, input, voucher, sb);
                                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("addScheduleID", addScheduleID));
                            }
                            else
                            {
                                var updateActiveStatus = UpdateActiveStatus(connection, transaction, addGCID, sb);
                                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("updateActiveStatus", updateActiveStatus));
                            }

                            //
                            if (privateCode == "")
                            {
                                var updateQuota = UpdateQuota(connection,
                                                    transaction,
                                                    voucher,
                                                    input.SaleInfor.SaleID,
                                                    input.CustomerInfor.LocationID,
                                                    input.CustomerInfor.BranchCode,
                                                    input.CustomerInfor.DistrictID,
                                                    DateTime.Now.Year,
                                                    DateTime.Now.Month);

                                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("updateQuota", updateQuota));
                            }
                            else
                            {
                                VendorManager.RedeemPrivateCode(connection, transaction, privateCode, input.objId);
                            }

                        }
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Redeem Exception", e.Message));
                return false;
            }
            return true;

        }

        private static bool CheckRedeem(SqlConnection connection, SqlTransaction transaction, int objID, string voucherCode, string orderCode)
        {
            var rs = connection.Query<int>(OS6_FPTVoucher_PolicyRedeem, new
            {
                Action = "CheckRedeem",
                ObjID = objID,
                VoucherCode = voucherCode,
                OrderCode = orderCode
            }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return rs > 0;
        }

        private static VoucherInfor GetVoucherToRedeem(SqlConnection connection, SqlTransaction transaction, string voucherCode, int serviceId, int subServiceTypeId, int subServiceId, int serviceCode)
        {
            var rs = connection.Query<VoucherInfor>(OS6_FPTVoucher_PolicyRedeem, new
            {
                Action = "GetVoucherInforToRedeem",
                VoucherCode = voucherCode,
                ServiceID = serviceId,
                SubServiceTypeId = subServiceTypeId,
                SubServiceId = subServiceId,
                ServiceCode = serviceCode
            }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return rs;
        }

        private static double AddGeneralCode(SqlConnection conn, SqlTransaction transaction, SalePolicyRedeemEVC input, string voucherCode, StringBuilder sb)
        {
            var unitParent = GetListServices2.GetUnitParent(input.SaleInfor.SaleID, sb);

            var limitType = GetLimitType(conn, transaction, voucherCode);

            var generalCodeModel = new DiscountModel()
            {
                Code = voucherCode,
                RegCode = input.RegCode,
                SaleId = input.SaleInfor.SaleID,
                ObjId = input.objId,
                SaleChannelId = input.SaleInfor.SaleChannelID,
                LocalTypeId = 0,
                LocationID = input.CustomerInfor.LocationID,
                BranchCode = input.CustomerInfor.BranchCode,
                DistrictID = input.CustomerInfor.DistrictID,
                SaleTeamID = input.SaleInfor.SaleTeamID,
                PaymentTypeID = input.PaymentTypeID,
                CrossSellingLocation = input.CrossSellingLocation,
                CustomerRank = input.CustomerInfor.CustomerRank,
                UnitParentID = unitParent == null ? 0 : unitParent.ParentId,
                UnitParentCode = unitParent == null ? "" : unitParent.ParentCode,
                LimitType = limitType,
                UnitCode = unitParent == null ? "" : unitParent.Code

            };

            var xml = XmlToAddGeneralCode(generalCodeModel);
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("XmlToAddGeneralCode", xml));


            return conn.Query<double>(OS6_FPTVoucher_PolicyRedeem, new
            {
                Action = "AddGeneralCode",
                xml = XmlToAddGeneralCode(generalCodeModel)
            }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        private static XElement XmlToAddGeneralCode(DiscountModel model)
        {
            var xmlString = new XElement("N",
                        new XElement("I",
                            new XElement("Code", model.Code),
                            new XElement("OrderCode", model.RegCode),
                            new XElement("SaleID", model.SaleId),
                            new XElement("ObjID", model.ObjId),
                            new XElement("ActiveChannel", model.ActiveChannel),
                            new XElement("LocalTypeId", model.LocalTypeId),
                            new XElement("PolicyId", model.PolicyId),
                            new XElement("LocationID", model.LocationID),
                            new XElement("BranchCode", model.BranchCode),
                            new XElement("DistrictID", model.DistrictID),
                            new XElement("SaleChannelId", model.SaleChannelId),
                            new XElement("SaleTeamID", model.SaleTeamID),
                            new XElement("PaymentTypeID", model.PaymentTypeID),
                            new XElement("CrossSellingLocation", model.CrossSellingLocation),
                            new XElement("ObjectTypeId", model.ObjectTypeId),
                            new XElement("CusTypeId", model.CusTypeId),
                            new XElement("CusTypeL2Id", model.CusTypeL2Id),
                            new XElement("CustomerRank", model.CustomerRank),
                            new XElement("UnitParentID", model.UnitParentID),
                            new XElement("UnitParentCode", model.UnitParentCode),
                            new XElement("LimitType", model.LimitType),
                            new XElement("UnitCode", model.UnitCode)
                        )
                    );

            return xmlString;
        }

        public static int GetLimitType(SqlConnection connection, SqlTransaction transaction, string code)
        {
            int limitType = 0;
            try
            {
                limitType = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetLimitType",
                    voucherCode = code
                }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            catch (Exception)
            {

            }

            return limitType;
        }

        private static double AddDetail(SqlConnection conn, SqlTransaction trans, double addGCID, VoucherInfor voucher, ApplyMoney applyMoney)
        {
            return conn.Query<double>(OS6_FPTVoucher_PolicyRedeem, new
            {
                Action = "AddDetail",
                GeneralCodeID = addGCID,
                ServiceId = voucher.ServiceID,
                SubServiceTypeId = voucher.SubServiceTypeID,
                SubServiceId = voucher.SubServiceID,
                ServiceCode = voucher.ServiceCode,
                Qty = applyMoney.Qty,
                TotalDiscount = CalcVoucher(voucher) * applyMoney.Qty
            }, trans, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        private static decimal CalcVoucher(VoucherInfor voucher)
        {
            if (voucher.PromotionTypeID == 1)
            {
                return voucher.Value;
            }
            if (voucher.PromotionTypeID == 2)
            {
                return voucher.Value * voucher.MonthValue;
            }
            if (voucher.PromotionTypeID == 3)
            {
                return voucher.ConnectionFee > 0 ? voucher.ConnectionFee : voucher.Value;
            }

            return 0;
        }

        private static Tuple<decimal, float> CalcVoucher_Amount_MonthUse(VoucherInfor voucher)
        {
            decimal amount = voucher.Value;
            decimal connectionFee = voucher.ConnectionFee;
            float monthvalue = (float)voucher.MonthValue;

            return new Tuple<decimal, float>(connectionFee <= 0 ? amount : connectionFee, monthvalue);
        }

        private static Tuple<double, double> GetPrepaid_MonthUse(SalePolicyRedeemEVC input, VoucherInfor voucherInfor)
        {
            double prepaid = 0;
            double monthUse = 0;

            foreach (var service in input.Services)
            {
                foreach (var subservicetype in service.SubServiceTypes)
                {
                    foreach (var subservice in subservicetype.SubServices)
                    {
                        if (service.ServiceID == voucherInfor.ServiceID
                            && subservicetype.SubServiceTypeID == voucherInfor.SubServiceTypeID
                            && subservice.SubServiceID == voucherInfor.SubServiceID
                            && subservice.ServiceCode == voucherInfor.ServiceCode)
                        {
                            prepaid = subservice.PrePaid;
                            monthUse = subservice.MonthUsed;
                        }
                    }
                }
            }

            return new Tuple<double, double>(prepaid, monthUse);
        }

        private static double AddSchedulePolicy(SqlConnection conn, SqlTransaction trann, double GeneralCodeID, SalePolicyRedeemEVC input, VoucherInfor voucherInfor, StringBuilder sb)
        {
            var calcVoucher_Amount_MonthUse = CalcVoucher_Amount_MonthUse(voucherInfor);
            var getPrepaid_MonthUse = GetPrepaid_MonthUse(input, voucherInfor);
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("calcVoucher_Amount_MonthUse", calcVoucher_Amount_MonthUse));
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("getPrepaid_MonthUse", getPrepaid_MonthUse));

            var monthUsed = getPrepaid_MonthUse.Item2 < calcVoucher_Amount_MonthUse.Item2 ? getPrepaid_MonthUse.Item2 : calcVoucher_Amount_MonthUse.Item2;

            // nếu trả sau => monthused = 0
            monthUsed = getPrepaid_MonthUse.Item1 == 0 ? 0 : monthUsed;

            var checkConfig = GetBaseConfig(conn, trann, 33, 1);//.
            if (checkConfig != null && checkConfig.Count() > 0)
            {
                return conn.Query<double>(OS6_FPTVoucher_PolicyRedeem, new
                {
                    @Action = "AddSchedulePolicy",
                    @VoucherCode = voucherInfor.VoucherCode,
                    @GeneralCodeID = GeneralCodeID,
                    @ObjID = input.objId,
                    @Amount = calcVoucher_Amount_MonthUse.Item1,
                    @MonthValue = calcVoucher_Amount_MonthUse.Item2,
                    @MonthUsed = monthUsed,
                    @Used = (decimal)monthUsed * calcVoucher_Amount_MonthUse.Item1,
                    @PromotionTypeID = voucherInfor.PromotionTypeID,
                    @ServiceCode = voucherInfor.ServiceCode
                }, trann, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            else
            {
                return conn.Query<double>(OS6_FPTVoucher_PolicyRedeem, new
                {
                    @Action = "AddSchedulePolicy",
                    @VoucherCode = voucherInfor.VoucherCode,
                    @GeneralCodeID = GeneralCodeID,
                    @ObjID = input.objId,
                    @Amount = calcVoucher_Amount_MonthUse.Item1,
                    @MonthValue = calcVoucher_Amount_MonthUse.Item2,
                    @MonthUsed = 0,
                    @Used = 0,
                    @PromotionTypeID = voucherInfor.PromotionTypeID,
                    @ServiceCode = voucherInfor.ServiceCode,
                    @firstAdd = 0
                }, trann, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }

        }

        private static int UpdateQuota(SqlConnection conn, SqlTransaction trann, VoucherInfor voucherInfor, int saleID, int locationId, int branchCode, int districtId, int year, int month)
        {
            conn.Execute(OSU6_FPTVoucher_PolicyVoucher_VoucherLimit_UpdateUsedMoney, new
            {
                VoucherCode = voucherInfor.VoucherCode,
                Money = CalcVoucher(voucherInfor),
                SaleId = saleID,
                LocationId = locationId,
                BranchCode = branchCode,
                DistrictId = districtId,
                Year = year,
                Month = month
            }, transaction: trann, commandTimeout: null, commandType: CommandType.StoredProcedure);

            conn.Execute(OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable, new
            {
                Year = year,
                Month = month
            }, transaction: trann, commandTimeout: null, commandType: CommandType.StoredProcedure);

            return 1;
        }

        private static int UpdateActiveStatus(SqlConnection conn, SqlTransaction trans, double generalCodeID, StringBuilder sb)
        {
            int i = 0;
            try
            {
                i = conn.Execute(OSU6_FPTVoucher_UpdateActiveStatus, new
                {
                    generalCodeID = generalCodeID,
                    activeStatus = 3
                }, transaction: trans, commandType: CommandType.StoredProcedure);
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UpdateActiveStatus err", e));
            }
            return i;
        }

        private static string GetPolicyVoucher(SqlConnection conn, SqlTransaction trann, string code)
        {
            return conn.Query<string>(OS6_FPTVoucher_PolicyRedeem, new
            {
                @Action = "GetPolicyVoucher",
                @VoucherCode = code
            }, trann, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static List<DXBaseConfig> GetBaseConfig(SqlConnection conn, SqlTransaction trann, int GroupID, int Status)
        {
            var lst = new List<DXBaseConfig>();
            lst = conn.Query<DXBaseConfig>("PowerInside..OS6_DXBusinessPolicy_BaseConfig_Get", new////
            {
                GroupID = GroupID,
                Status = Status
            }, trann, commandType: CommandType.StoredProcedure).ToList();
            return lst;
        }

        public static List<DXBaseConfig> GetBaseConfig(int GroupID, int Status)
        {
            var lst = new List<DXBaseConfig>();

            using (var conn = new SqlConnection(SqlHelper.ConnWrite()))
            {
                lst = conn.Query<DXBaseConfig>("PowerInside..OS6_DXBusinessPolicy_BaseConfig_Get", new
                {
                    GroupID = GroupID,
                    Status = Status
                }, commandType: CommandType.StoredProcedure).ToList();
            }
            return lst;
        }

        public static string GetPolicyVoucher(SqlConnection conn, string code)
        {
            return conn.Query<string>(OS6_FPTVoucher_PolicyRedeem, new
            {
                @Action = "GetPolicyVoucher",
                @VoucherCode = code
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        #region loy_qlcs_new_customer
        public static string submit_actionCode_EVC(SalePolicyRedeemEVC input, StringBuilder sb)
        {
            try
            {
                var request = HttpContext.Current.Request;
                string domain = $"{request.Url.Scheme}://{request.Url.Authority}";

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(domain);
                    var json = JsonConvert.SerializeObject(input);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    var response = client.PostAsync("/api/loyalty/campaign/points/saleclub_sunmit_action", content).Result;

                    var statusApi = response.EnsureSuccessStatusCode();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("submit_actionCode_EVC statusApi", statusApi));

                    var result = response.Content.ReadAsStringAsync().Result;

                    return result;
                }
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }

        #endregion

    }

}