using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;
using APIMBS.Models;
using Newtonsoft.Json;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Configuration;
using APIMBS.Constant;

namespace APIMBS.Service
{
    public class MyFPTService
    {
        public static bool checkMyFPTCode(string vouchercode,string log)
        {
            int flag = 0;
            try
            {
                //kiểm tra loại
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    flag = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_Foxpro_MBS", new
                    {
                        ActionName = "CheckCodeFoxpro",
                        voucherCode = vouchercode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    <PERSON>.Mes(Level.INFO, flag.ToString(), string.Concat("checkMyFPTCode ", log));
                    return (flag == 1);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), string.Concat("checkMyFPTCode ", log));
                return false;
            }
        }

        public static bool NewCheckMyFPTCode(SqlConnection connection, SqlTransaction transaction, string vouchercode, string log)
        {
            int flag = 0;
            try
            {
                //kiểm tra loại
                flag = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_Foxpro_MBS", new
                {
                    ActionName = "CheckCodeFoxpro",
                    voucherCode = vouchercode
                }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
                L.Mes(Level.INFO, flag.ToString(), string.Concat("checkMyFPTCode ", log));
                return (flag == 1);
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), string.Concat("checkMyFPTCode ", log));
                return false;
            }
        }

        private static int GetPaidTimeTypeID(int netid, int tvid)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                {
                    ActionName = "GetPaidTimeTypeID",
                    NETID = netid,
                    IPTVID = tvid
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static string RamdoneString()
        {
            Random rd = new Random();
            return rd.Next(1, 10000).ToString();
        }
        public static ResponseModel<bool> RedeemRFMyFPT(RedeemVoucherRequest input,string keylog)
        {
            try 
	        {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        int paidTimeTypeID = GetPaidTimeTypeID(input.NETID, input.IPTVID);
                        L.Mes(Level.INFO, "paidTimeTypeID: " + paidTimeTypeID.ToString(), string.Concat("RedeemRFMyFPT ", keylog));
                        if (paidTimeTypeID.Equals(0))
                        {
                            return new ResponseModel<bool>() { data = false, error = "Đối tượng không nằm trong diện dùng ưu đãi", result = 0 };
                        }
                        FoxproUseInviteCode insertInviteCode = connection.Query<FoxproUseInviteCode>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                        {
                            actionName = "InsertInviteCode",
                            objID = input.ObjID,
                            orderCode = input.OrderCode,
                            //bnet = input.NETID,
                            //btv = input.IPTVID,
                            inviteCode = input.VoucherRP,
                            PaidTimeTypeID = paidTimeTypeID
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        if (insertInviteCode.res == 0)
                        {
                            return new ResponseModel<bool>() { data = false, error = "Dịch vụ không phải NETONLY hoặc COMBO", result = 0 };
                        }
                        else
                        {
                            int objID = insertInviteCode.ObjID;
                            int MNET = insertInviteCode.MoneyPromotionNETID;
                            int MTV = insertInviteCode.MoneyPromotionTVID;
                            int StaffID = Convert.ToInt32(insertInviteCode.StaffIDInvite);
                            UpdateDiscount(
                                 objID,  // objID khách hàng
                                 0, //XđồngY tháng NET + Tháng
                                 0, //XđồngY tháng TV + Tháng
                                 MNET, //Giảm Tiền Trực Tiếp NET
                                 MTV, //Giảm Tiền Trực Tiếp TV
                                 insertInviteCode.EventCode, // Code
                                 StaffID, false);

                            UpdateOanhVK(input.ObjID, input.OrderCode, insertInviteCode.generalCodeID);

                            transaction.Commit();                           

                            bool statusRedeem = SendNotificationReferralMyFPT(insertInviteCode,keylog);
                            L.Mes(Level.INFO, statusRedeem.ToString(), string.Concat("SendNotificationReferralMyFPT ", keylog));

                            return new ResponseModel<bool>() { data = true, error = "", result = 0 };                            
                        }
                    }
                }
	        }
	        catch (Exception ex)
	        {
                L.Mes(Level.ERROR, ex.Message.ToString(), string.Concat("RedeemRFMyFPT ", keylog));
                return new ResponseModel<bool>() { data = false, error = ex.Message, result = 0 };
	        }
        }
        private static void UpdateOanhVK(int ObjID, string OrderCode, int? generalCodeID)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, OrderCode }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@generalCodeID", generalCodeID);
                connection.Execute(ConstantAPI.OS6_FPTVoucher_AddVoucherBill2, p, commandType: CommandType.StoredProcedure);
            }
        }
        private static Boolean SendNotificationReferralMyFPT(dynamic input, string keylog)
        {
            int iStatusCode = 0;
            LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
            AuthorizationInfor aut = getAuthorInfor(keylog);
            try
            {
                if (aut == null)
                    return false;
                var request = new ModelSendNotificationReferralMyFPT()
                {
                    employeeCode = input.StaffIDInvite,
                    Referrer = input.RealMoneyAmount,
                    BookingId = input.Contract,
                    contract_owner = input.FullName,
                    BookingStatus = "pending"
                };

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    //client.DefaultRequestHeaders.Add("Authorization", aut.authorization);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/api/oauth-ms/public/auth/integration-supplier", data).Result;
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(response), string.Concat("SendNotificationReferralMyFPT ", keylog));
                    iStatusCode = (int)response.StatusCode;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, string.Concat("SendNotificationReferralMyFPT ", keylog));
                return false;
            }
            //dynamic response = new dynamic();

            if (iStatusCode == 200)
            {
                return true;
            }

            return false;
        }
        private static AuthorizationInfor getAuthorInfor(string keylog)
        {
            L.Mes(Level.INFO, "Start to get token myfpt", string.Concat("getAuthorInfor ", keylog));
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                var uri = WebAPIHelper.myFpt_fpt_vn + "/api/oauth-ms/public/auth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("client_id", Utility.my_fpt_api_client_id));
                keyValues.Add(new KeyValuePair<string, string>("client_secret", Utility.my_fpt_api_client_secret));
                keyValues.Add(new KeyValuePair<string, string>("username", Utility.my_fpt_api_username));
                keyValues.Add(new KeyValuePair<string, string>("password", Utility.my_fpt_api_password));
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "password"));

                var content = new FormUrlEncodedContent(keyValues);

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        response.EnsureSuccessStatusCode();
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, string.Concat("getAuthorInfor ", keylog));
                        res = JsonConvert.DeserializeObject<AuthorizationInfor>(r);
                    }
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, string.Concat("getAuthorInfor ", keylog));
                return null;
            }
            return res;
        }

        public static string GetStaffId(string voucherCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<string>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                {
                    actionName = "GetStaffID",
                    voucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static string getStaffInfor(string employeeCode, string logId)
        {
            try
            {
                if (string.IsNullOrEmpty(employeeCode))
                {
                    //L.Mes(Level.INFO, "Mã nhân viên trống", "getStaffInfor");
                    WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "getStaffInfor MyFPT res " + "Mã nhân viên trống");
                    return "Nhân viên FPT";
                }
                AuthorizationInfor aut = getAuthorInfor(logId);
                if (aut == null)
                    return "Nhân viên FPT";
                StaffInfor res = new StaffInfor();
                res.data = new Name();
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    //var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    HttpResponseMessage response = client.GetAsync("/api/oauth-ms/public/auth/get-info-employee?employeeCode=" + employeeCode).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    //L.Mes(Level.INFO, r, "getStaffInfor");
                    WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "getStaffInfor MyFPT res " + JsonConvert.SerializeObject(r));
                    res = JsonConvert.DeserializeObject<StaffInfor>(r);
                }

                if (res != null)
                {
                    if (!string.IsNullOrEmpty(res.data.fullname))
                    {
                        return res.data.fullname;
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getStaffInfor");
                return "Nhân viên FPT";
            }
            return "Nhân viên FPT";
        }
        private static void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, PNET, PTV, MNET, MTV, Voucher, SalesManID }));
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }
        public static ResponseModel<InfoVoucherResponse> GetReferEvent(InfoVoucherRequest input)
        {
            ResponseModel<InfoVoucherResponse> response = new ResponseModel<InfoVoucherResponse>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    string staffid = connection.Query<string>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                    {
                        actionName = "GetStaffID",
                        voucherCode = input.VoucherCode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    PromotionEventMyFPT res = connection.Query<PromotionEventMyFPT>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                    {
                        ActionName = "GetPEFoxpro",
                        PaidTimeTypeID = input.IDPrepaid
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (res != null)
                    {
                        if (input.PrepaidTimeNet == 0)
                            res.Name = "DiscountAmountPostNET";
                        else
                            res.Name = "";
                        decimal valueDiscountAmount = Math.Round(res.ValueDiscountAmount * (decimal)0.11, 0) * 10;
                        response.data.PromotionEvent = new List<ItemNode>();
                        //response.data.PromotionEvent.Add(new ItemNode() { ContentDiscount = res.Name, ServiceCode = input.ServiceCodeTTNet, Value = valueDiscountAmount.ToString(), Dismonth = 0 });
                        response.data.Type = 4;
                        response.data.FullNameRef = "Mã giới thiệu OTT";// getStaffInfor(staffid);
                        response.data.PhoneRef = null;

                        //AddHistoryTempCode(input.OrderCode, input.VoucherCode, 0, input);

                        return response;
                    }

                    return response;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "MyFPT - GetReferEvent");
                return response;
            }
        }
    }
}