using APIMBS.Models.SalePlatform;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Service
{
    public static class FPTSaleCommonServices
    {
        public static List<CustomerSaleOrderCollections> GetCustomerSaleOrderCollections(SalePolicyPlatformInputModel input)
        {
            var listServices = (from service in input.Services
                                join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeID,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    prePaid = Convert.ToInt32(subService.PrePaid),
                                    deployTypeId = subService.DeployTypeID,
                                    qty = subService.Qty,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    total = Convert.ToInt32(subService.Total),
                                    total_vat = Convert.ToInt32(subService.Total_VAT),
                                    policyId = subService.PolicyID,
                                    customerType = customertype.CustomerType,
                                    crossSellingLocation = input.CrossSellingLocation,
                                    paymentTypeId = input.PaymentTypeID,
                                    cusTypeId = input.CustomerInfor.CusTypeID,
                                    cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                    objectTypeId = input.CustomerInfor.ObjectTypeID,
                                    customerRank = input.CustomerInfor.CustomerRank
                                }).ToList();

            return listServices;
        }

        public static List<CustomerSaleOrderCollections> GetCustomerSaleOrderCollectionsFromProducts(SalePolicyPlatformInputModel input)
        {
            var listProducts = (from product in input.Products
                                join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                from subServiceType in product.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = product.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    prePaid = subService.ApplyPrePaid,
                                    deployTypeId = subService.DeployTypeID,
                                    statusId = subService.StatusID,
                                    revokeID = subService.RevokeID,
                                    usesId = subService.UsesID,
                                    qty = subService.Qty,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    total = Convert.ToInt32(subService.Total),
                                    total_vat = Convert.ToInt32(subService.Total_VAT),
                                    policyId = subService.PolicyID,
                                    customerType = customertype.CustomerType,
                                    crossSellingLocation = input.CrossSellingLocation,
                                    paymentTypeId = input.PaymentTypeID,
                                    cusTypeId = input.CustomerInfor.CusTypeID,
                                    cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                    objectTypeId = input.CustomerInfor.ObjectTypeID,
                                    customerRank = input.CustomerInfor.CustomerRank
                                }).ToList();

            return listProducts;
        }
    }
}