using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Collections.Generic;
using Newtonsoft.Json;
using APIMBS.Models.VendorModels;
using System.Text;
using APIMBS.Models.SalePlatform;
using System.Data.SqlClient;
using Dapper;
using System.Data;
using System.Linq;
using Voucher.APIHelper;
using System.Net;
using System.Xml.Linq;
using APIMBS.Models.MobileSaleV4;

namespace APIMBS.Service.EvcPolicyServices
{
    public class VendorManager
    {
        private static string _accessToken;
        private static DateTime _accessTokenExpiration;
        public static string OS6_FPTVoucher_VendorVoucher = "PowerInside..OS6_FPTVoucher_VendorVoucher";

        // API endpoint URL
        //private static string domain = "https://apis-dev.fpt.net"; //dev
        //private static string domain = "https://apis-stag.fpt.net"; //stag
        private static string domain = Utility.apis_fpt_net; //prod

        public static string GetAccessToken()
        {
            if (DateTime.Now >= _accessTokenExpiration)
            {
                // Access token đã hết hạn hoặc chưa được lấy. Lấy access token mới.
                string newAccessToken = RequestNewAccessToken();

                _accessToken = newAccessToken;
            }

            return _accessToken;
        }

        public static string RequestNewAccessToken()
        {
            // Thực hiện yêu cầu POST để lấy access token mới
            string url = Utility.iam_fpt_vn + "/auth/realms/fpt/protocol/openid-connect/token";
            string clientId = "Vendor_Cooperation";
            string clientSecret = "xBJsurmZWi7TeESU8sbe6XQRwvInZLTQ";
            string grantType = "client_credentials";

            // Tạo một HttpClientHandler để cấu hình proxy
            var handler = new HttpClientHandler
            {
                Proxy = new WebProxy(Utility.http_client_proxy),
                UseProxy = true
            };

            using (HttpClient httpClient = new HttpClient(handler))
            {
                var content = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "client_id", clientId },
                    { "client_secret", clientSecret },
                    { "grant_type", grantType }
                });

                HttpResponseMessage response = httpClient.PostAsync(url, content).Result;

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = response.Content.ReadAsStringAsync().Result;
                    dynamic jsonResponse = JsonConvert.DeserializeObject(responseContent);
                    string access_token = jsonResponse.access_token;
                    int expires_in = jsonResponse.expires_in;
                    _accessTokenExpiration = DateTime.Now.AddSeconds(expires_in);
                    return access_token;
                }
                else
                {
                    return null;
                }
            }
        }


        public static List<VendorOutput> GetPrivateVendorCode(SqlConnection connection, SqlTransaction transaction, List<VendorInput> input, StringBuilder sb)
        {
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Start GetPrivateVendorCode input", input));
            List<VendorOutput> output = new List<VendorOutput>();
            try
            {
                string endpoint = "/m-vendor-cooperation/product/api/v1/VendorProduct/get-key-code-list";

                string apiUrl = domain + endpoint;

                // Replace with your actual access token
                string accessToken = GetAccessToken();

                // Create an HttpClient instance
                using (HttpClient httpClient = new HttpClient())
                {
                    // Add the authorization header
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {accessToken}");

                    foreach (VendorInput item in input)
                    {
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("item", item));
                        // Create the request input data

                        var requestData = new GetKeyCodeListInput
                        {
                            PartnerContract = item.VendorContract,
                            OrderCode = item.OrderCode,
                            SaleChannelID = item.SaleChannelID,
                            Status = 1,
                            Quantity = 1
                        };
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("requestData", requestData));

                        // Serialize the input data to JSON
                        string jsonInputData = JsonConvert.SerializeObject(requestData);

                        // Create the HTTP request content
                        var requestContent = new StringContent(jsonInputData, Encoding.UTF8, "application/json");

                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("accessToken", accessToken));
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("apiUrl", apiUrl));

                        // Send the POST request to the API
                        HttpResponseMessage response = httpClient.PostAsync(apiUrl, requestContent).Result;
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("response", response));

                        // Check if the request was successful
                        if (response.IsSuccessStatusCode)
                        {
                            // Read the response content as a string
                            string responseContent = response.Content.ReadAsStringAsync().Result;

                            // Deserialize the response JSON into an object
                            var responseData = JsonConvert.DeserializeObject<GetKeyCodeListOutput>(responseContent);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("responseData", responseData));

                            foreach (var keyCodeData in responseData.Data)
                            {
                                var temp = new VendorOutput();
                                temp.VoucherCode = item.VoucherCode;
                                temp.Code = keyCodeData.EnCourseCode;
                                temp.Cost = keyCodeData.cost;
                                temp.ExpirationDate = keyCodeData.expirationDate;

                                int checkupdate = connection.Execute(OS6_FPTVoucher_VendorVoucher, new
                                {
                                    ActionName = "UpdateUsed",
                                    VoucherCode = item.VoucherCode,
                                    OrderCode = item.OrderCode
                                }, transaction, commandType: CommandType.StoredProcedure);
                                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("checkupdate " + item.VoucherCode, checkupdate));

                                var contractDetail = GetContractModelDetail(httpClient, 3, item.VendorContract, sb);
                                if(contractDetail != null)
                                {
                                    temp.ContractID = contractDetail.Data.ContractId;
                                    temp.KeyName = contractDetail.Data.KeyName;
                                    temp.RepName = contractDetail.Data.RepName;
                                }


                                output.Add(temp);
                            }
                        }
                    }

                }
                return output;
            }
            catch (Exception e)
            {
                return null;
            }

        }

        public static string CheckVoucher(SqlConnection connection, SqlTransaction transaction, string voucherCode, string vendorContract, string orderCode)
        {
            string res = connection.Query<string>(OS6_FPTVoucher_VendorVoucher, new
            {
                ActionName = "CheckVoucher",
                VoucherCode = voucherCode,
                OrderCode = orderCode,
                VendorContract = vendorContract
            }, transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return res;
        }

        public static List<VendorOutput> ProcessLongChau(List<VendorInput> input, out string checkEr, StringBuilder sb)
        {
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Start ProcessLongChau", input));
            var rs = new List<VendorOutput>();
            checkEr = string.Empty;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        foreach (var item in input)
                        {
                            string checkEVC = CheckVoucher(connection, transaction, item.VoucherCode, item.VendorContract, item.RegCode);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("checkEVC", checkEVC));
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("checkEVC item", item));

                            if (checkEVC.Length > 0)
                            {
                                checkEr = checkEVC;
                                return new List<VendorOutput>();
                            }
                        }
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Start GetPrivateVendorCode", ""));
                        rs = GetPrivateVendorCode(connection, transaction, input, sb);

                        transaction.Commit();
                    }
                }
            }
            catch (Exception e)
            {
                return rs;
            }

            return rs;
        }

        public static string CheckFormatVoucher(string code)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var dataPrefix = connection.Query<CheckFormatVoucher>(OS6_FPTVoucher_VendorVoucher, new
                {
                    ActionName = "CheckFormatVoucher"
                }, commandType: CommandType.StoredProcedure).ToList();

                foreach (var item in dataPrefix)
                {
                    if (code.ToUpper().StartsWith(item.Prefix.ToUpper()))
                    {
                        // Loại bỏ các ký tự Prefix ở đầu chuỗi code
                        code = code.Substring(item.Prefix.Length);
                        return code;
                    }
                }
            }

            return "";
        }

        public static XElement ToXML_CampaignID(List<PolicyVoucher2> input)
        {
            if (input == null)
            {
                return null;
            }
            var rootElement = new XElement("N",
            input.Select(item => new XElement("I",
                new XElement("CampaignID", item.CampaignID),
                new XElement("PolicyID", item.PolicyID),
                new XElement("VoucherID", item.VoucherID)
            )));
            return rootElement;
        }

        public static XElement ToXML_PrivateCode(List<EvoucherInputNew> input)
        {
            if (input == null)
            {
                return null;
            }
            var rootElement = new XElement("N",
            input.Select(item => new XElement("I",
                new XElement("PrivateCode", CheckFormatVoucher(item.evoucherCode))
            )));
            return rootElement;
        }

        public static int AddVoucherID(XElement xmlC, XElement xmlP)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Execute(OS6_FPTVoucher_VendorVoucher, new
                {
                    ActionName = "AddVoucherID",
                    xmlCampaign = xmlC,
                    xmlPrivateCode = xmlP
                }, commandType: CommandType.StoredProcedure);
            }
        }

        public static int RedeemPrivateCode(SqlConnection connection, SqlTransaction transaction, string voucherCode, int objID)
        {
            int redem = 0;
            redem += connection.Execute(OS6_FPTVoucher_VendorVoucher, new
            {
                ActionName = "RedeemPrivateCode",
                VoucherCode = voucherCode,
                ObjID = objID
            }, transaction, commandType: CommandType.StoredProcedure);

            redem += connection.Execute(OS6_FPTVoucher_VendorVoucher, new
            {
                ActionName = "UpdateQuotaUsed",
                VoucherCode = voucherCode
            }, transaction, commandType: CommandType.StoredProcedure);

            return redem;
        }

        public static int UpdateQuotaUsed(SqlConnection connection, SqlTransaction transaction, string code)
        {
            return connection.Execute(OS6_FPTVoucher_VendorVoucher, new
            {
                ActionName = "UpdateQuotaUsed",
                VoucherCode = code
            }, transaction, commandType: CommandType.StoredProcedure);
        }

        public static int UpdateQuotaVendorLongChau(SqlConnection connection, SqlTransaction transaction, string voucherCode)
        {
            return connection.Execute(OS6_FPTVoucher_VendorVoucher, new
            {
                ActionName = "UpdateQuotaVendorLongChau",
                VoucherCode = voucherCode
            }, transaction, commandType: CommandType.StoredProcedure);
        }


        public static int RollbackVoucherID(XElement xmlP)
        {
            using (var connecttion = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connecttion.Execute(OS6_FPTVoucher_VendorVoucher, new
                {
                    ActionName = "RollbackVoucherID",
                    xmlPrivateCode = xmlP
                }, commandType: CommandType.StoredProcedure);
            }
        }
    
    
        public static ContractModelDetail GetContractModelDetail(HttpClient httpClient, int type, string Contract, StringBuilder sb)
        {
            httpClient = new HttpClient();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetContractModelDetail start", Contract));
            ContractModelDetail output = new ContractModelDetail();
            try
            {
                string endpoint = "/m-vendor-cooperation/contract/api/v1/VendorContract/get-contract-detail";

                string apiUrl = domain + endpoint;
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetContractModelDetail apiUrl", apiUrl));

                // Replace with your actual access token
                string accessToken = GetAccessToken();

                // Add the authorization header
                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {accessToken}");

                var requestData = new ContractModelDetailReq
                {
                    Type = type,
                    Contract = Contract
                };
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetContractModelDetail requestData", requestData));

                // Serialize the input data to JSON
                string jsonInputData = JsonConvert.SerializeObject(requestData);

                // Create the HTTP request content
                var requestContent = new StringContent(jsonInputData, Encoding.UTF8, "application/json");

                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetContractModelDetail apiUrl", apiUrl));

                // Send the POST request to the API
                HttpResponseMessage response = httpClient.PostAsync(apiUrl, requestContent).Result;
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetContractModelDetail response", response));

                // Check if the request was successful
                if (response.IsSuccessStatusCode)
                {
                    // Read the response content as a string
                    string responseContent = response.Content.ReadAsStringAsync().Result;

                    // Deserialize the response JSON into an object
                    output = JsonConvert.DeserializeObject<ContractModelDetail>(responseContent);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetContractModelDetail responseData", output));
                }

                return output;
            }
            catch (Exception e)
            {
                return null;
            }
        }
    }
}