using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Dapper;
using System.Data;
using Voucher.APIHelper;
using System.Xml.Linq;
using System.Configuration;
using APIMBS.Models.SalePlatform;
using APIMBS.Models.MobileSaleV4;
using System.Text;
using Voucher.APIHelper.ShareModel;
using APIMBS.Models;
using APIMBS.Constant;
using System.Web.Services.Description;
using System.Xml;

namespace APIMBS.Service
{
    public class FPTSaleListVoucherServices
    {
        public const string OS6_FPTVoucher_SalePlatform_PolicyVoucher = "PowerInside.dbo.OS6_FPTVoucher_SalePlatform_PolicyVoucher";
        public const string OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend = "PowerInside.dbo.OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend";
        public const string OS6_FPTVoucher_FSale = "PowerInside.dbo.OS6_FPTVoucher_FSale";
        public const string OSU6_FPTVoucher_PolicyVoucher_CheckLocation = "PowerInside.dbo.OSU6_FPTVoucher_PolicyVoucher_CheckLocation";
        public const string OS6_FPTVoucher_CheckQuotaMoney = "PowerInside.dbo.OS6_FPTVoucher_CheckQuotaMoney";
        public static List<Evoucher> GetListEvoucher(SalePolicyListEVC input, StringBuilder sb)
        {
            var DataLstEVC = new List<Evoucher>();
            var lstEVC = new List<PolicyVoucher>();
            var lstEVC_policy = new List<PolicyVoucher>();
            var lst = new List<PolicyVoucher>();
            try
            {

                var listServices = (from service in input.Services
                                    join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                    from subServiceType in service.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = service.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeID,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = Convert.ToInt32(subService.PrePaid),
                                        deployTypeId = subService.DeployTypeID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listServices", listServices));

                var listProducts = (from product in input.Products
                                    join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                    from subServiceType in product.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = product.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeId,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = subService.ApplyPrePaid,
                                        deployTypeId = subService.DeployTypeID,
                                        statusId = subService.StatusID,
                                        revokeID = subService.RevokeID,
                                        usesId = subService.UsesID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProducts", listProducts));


                var _serviceOrders = input.CustomerTypes.Select(x => x.ServiceID.ToString()).ToArray();
                Array.Sort(_serviceOrders);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_serviceOrders", _serviceOrders));

                var listProCusNew = new List<CustomerSaleOrderCollections>();
                var listProCusOld = new List<CustomerSaleOrderCollections>();
                if (listProducts != null && listProducts.Count > 0)
                {
                    listProCusNew = listProducts.Where(x => x.customerType.Equals(2)).ToList(); // khách hàng mới là 2, cũ là 1
                    listProCusOld = listProducts.Where(x => x.customerType.Equals(1)).ToList(); // khách hàng mới là 2, cũ là 1
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProCusNew", listProCusNew));
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProCusOld", listProCusOld));

                var lstOrders = new List<CustomerSaleOrderCollections>();
                if (listServices != null && listServices.Count > 0)
                {
                    lstOrders.AddRange(listServices);
                }
                if (listProducts != null && listProducts.Count > 0)
                {
                    lstOrders.AddRange(listProducts);
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstOrders", lstOrders));

                var dataListEVC = new List<PolicyVoucher>();
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    var dataPolicyId_order = lstOrders.Select(x => x.policyId).ToList().Distinct();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataPolicyId_order", dataPolicyId_order));
                    int goldenPolicy = connection.Query<int>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                    {
                        actionName = "CheckPolicyGoldenDate",
                        policyIds = string.Join(",", dataPolicyId_order)                        
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("goldenPolicy", goldenPolicy));
                    if (goldenPolicy.Equals(1))
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Đơn hàng là chính sách ngày vàng",""));
                        return new List<Evoucher>();
                    }
                    #region lấy voucher theo service
                    if (listServices != null && listServices.Count > 0)
                    {
                        
                        dataListEVC = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                        {
                            actionName = "GetListVoucherServices",
                            xml = CreateXMLSubService(listServices, input.SaleInfor.SaleChannelID, sb)
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataListEVC", dataListEVC));

                        if (dataListEVC.Count > 0)
                        {
                            var lstVC = (from x in dataListEVC
                                         select new PolicyVoucher
                                         {
                                             policyId = x.policyId,
                                             VoucherCode = x.VoucherCode,
                                             Description = x.Description,
                                             Note = x.Note,
                                             Todate = x.Todate,
                                             voucherService = x.voucherService,
                                             promotionTypeId = x.promotionTypeId,
                                             valueVoucher = x.valueVoucher,
                                             duration = x.duration,
                                             applyTypeID = x.applyTypeID,
                                             subServiceCondition = x.subServiceCondition,
                                             minmoney = x.minmoney,
                                             minprepaid = x.minprepaid,
                                             minConnectionFee = x.minConnectionFee,
                                             applyFor = x.applyFor,
                                             serviceId = x.serviceId,
                                             subServiceTypeId = x.subServiceTypeId,
                                             subServiceId = x.subServiceId,
                                             serviceCode = x.serviceCode
                                         }).OrderByDescending(x=>x.subServiceId) //nếu có PHM sẽ ưu tiên lấy subServiceId PHM
                                           .GroupBy(n => new { n.VoucherCode, n.serviceId, n.subServiceId,n.serviceCode,n.subServiceTypeId,n.subServiceCondition })
                                           .Select(g => g.FirstOrDefault())
                                           .ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstVC", lstVC));

                            var listVCservice = lstVC.Where(x => x.promotionTypeId != 3).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listVCservice", listVCservice));

                            if (listVCservice.Count > 0)
                            {
                                lst.AddRange(listVCservice);
                            }
                            // lấy các voucher phí hòa mạng, tiếp tục kiểm tra voucher hòa mạng thiết bị
                            // promotiontype id=3 là phí hòa mạng
                            // phí hòa mạng có kiểm tra thiết bị sẽ có applyfor =3
                            // phí hòa mạng giảm cho dịch vụ sẽ không cần kiểm tra điều kiện áp dụng thiết bị: 1 là dịch vụ, 3 là dịch vụ + thiết bị
                            var listEVCPHM_product = lstVC.Where(x => x.promotionTypeId.Equals(3) && x.applyFor.Equals(3)).ToList(); //  PHM thiết bị
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM", listEVCPHM_product));

                            var listEVCPHM_Service = lstVC.Where(x => x.promotionTypeId.Equals(3) && x.applyFor.Equals(1)).ToList(); // PHM dịch vụ
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM_Service", listEVCPHM_Service));

                            if(listEVCPHM_Service?.Count > 0)
                            {
                                // lấy những service có PHM 
                                var order_phm_net = listServices.Where(x=>x.subServiceId == 999999999).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_net count", order_phm_net.Count));
                                if (order_phm_net.Count > 0)
                                {
                                    var data_phm_net = listEVCPHM_Service.Where(x => x.serviceId == 1).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_net", data_phm_net));
                                    lst.AddRange(data_phm_net);
                                }
                                var order_phm_tv = listServices.Where(x => x.subServiceId == 999999996).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_tv count", order_phm_tv.Count));
                                if (order_phm_tv.Count > 0)
                                {
                                    var data_phm_tv = listEVCPHM_Service.Where(x => x.serviceId == 7).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_tv", data_phm_tv));
                                    lst.AddRange(data_phm_tv);
                                }
                            }

                            if (listEVCPHM_product.Count > 0)
                            {

                                string evc_phm = string.Empty;
                                var evc_phm_lst = (from item in listEVCPHM_product
                                                   select item.VoucherCode).ToList();
                                evc_phm = string.Join(",", evc_phm_lst);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evc_phm", evc_phm));

                                var lst_evc_phm_true = new List<PolicyVoucher>();
                                lst_evc_phm_true = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                                {
                                    actionName = "validVoucherPHM",
                                    xml = CreateXMLSubService(listProducts, input.SaleInfor.SaleChannelID, sb),
                                    vouchers = evc_phm
                                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lst_evc_phm_true", lst_evc_phm_true));

                                if (lst_evc_phm_true.Count > 0)
                                {
                                    var evc_code_phm_lst = (from item in lst_evc_phm_true
                                                            select item.VoucherCode).ToList();
                                    var dataEVC_phm = lstVC.Where(x => evc_code_phm_lst.Contains(x.VoucherCode)).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataEVC_phm", dataEVC_phm));

                                    if (dataEVC_phm.Count > 0)
                                    {
                                        lst.AddRange(dataEVC_phm);
                                    }
                                }
                            }
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lst", lst));

                            var listEVC_b4_check_service = new List<PolicyVoucher>();
                            string voucherOrderJoin = string.Join(",", _serviceOrders);
                            var dataListVoucherService = lst.Where(x => x.voucherService.Equals(voucherOrderJoin)).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataListVoucherService", dataListVoucherService));
                            foreach (var voucher in dataListVoucherService)
                            {
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucher", voucher));

                                string[] serviceVoucher = voucher.voucherService.Split(',');
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceVoucher", serviceVoucher));

                                if (!serviceVoucher.Except(_serviceOrders).Any()) // kiểm tra đơn hàng có đủ service của evc không
                                {
                                    if (voucher.minmoney > 0 || voucher.minprepaid > 0 || voucher.minConnectionFee > 0)
                                    {
                                        var listSubservice_by_condition = FilterSubservice(sb, connection, listServices, voucher.subServiceCondition);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listSubservice_by_condition", listSubservice_by_condition));

                                        var listproduct_by_condition = FilterService(listProCusNew, voucher.voucherService); // lấy thiết bị để cộng tiền check phí hòa mạng
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listproduct_by_condition", listproduct_by_condition));

                                        if (listSubservice_by_condition.Count > 0 || listproduct_by_condition.Count > 0)
                                        {
                                            int totalMoney = listSubservice_by_condition.Sum(a => a.total_vat);
                                            int totalMoneyConnFee = listproduct_by_condition.Sum(a => a.total_vat);
                                            int minPrepaid = 0;
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalMoney", totalMoney));
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalMoneyConnFee", totalMoneyConnFee));

                                            if (listSubservice_by_condition.Count > 0)
                                            {
                                                minPrepaid = listSubservice_by_condition.Min(a => a.prePaid);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("minPrepaid", minPrepaid));

                                                if (totalMoney >= voucher.minmoney && totalMoneyConnFee >= voucher.minConnectionFee && minPrepaid >= voucher.minprepaid)
                                                {
                                                    lstEVC_policy.Add(voucher);
                                                }
                                            }
                                            else
                                            {
                                                lstEVC_policy.Add(voucher);
                                            }
                                        }
                                    }
                                    else
                                    {
                                        lstEVC_policy.Add(voucher);
                                    }
                                }
                            }
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher lstEVC", lstEVC_policy));
                        }
                    }
                    #endregion

                    #region lấy voucher theo product
                    if (listProducts != null && listProducts.Count > 0)
                    {
                        if (listProCusNew.Count > 0)
                        {
                            var list_evc_products = new List<PolicyVoucher>();
                            list_evc_products = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                            {
                                actionName = "GetListVoucherProductNew",
                                xml = CreateXMLSubService(listProCusNew, input.SaleInfor.SaleChannelID, sb)
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_evc_products 1", list_evc_products));
                            // lấy danh sách các voucher ưu đãi cho thiết bị nhưng điều kiện là thiết bi + dịch vụ
                            var VoucherlistDeviceService = list_evc_products.Where(x => x.applyFor == 3 && x.applyTypeID == 1).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("VoucherlistDeviceService ", VoucherlistDeviceService));
                            // kiểm tra danh sách voucher (thiết bi + dịch vụ) trong phần voucher dịch vụ
                            if (VoucherlistDeviceService.Count >0)
                            {
                                list_evc_products = list_evc_products.Except(VoucherlistDeviceService).ToList();
                                // kiểm tra điều kiện dịch vụ
                                var dataList = VoucherlistDeviceService.Select(x => x.VoucherCode).ToList();
                                string evouchers = string.Join(",", dataList);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evouchers ", evouchers));
                                // kiểm tra dịch vụ của evc thiết bị
                                var  list_evc_service_products = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                                {
                                    actionName = "ValidEVCdevice",
                                    xml = CreateXMLSubService(listServices, input.SaleInfor.SaleChannelID, sb),
                                    vouchers = evouchers
                                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_evc_service_products ", list_evc_service_products));

                                if(list_evc_service_products.Count > 0)
                                {
                                    list_evc_service_products = list_evc_service_products.Distinct().ToList();
                                    list_evc_products.AddRange(list_evc_service_products);
                                }
                                
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_evc_products 2", list_evc_products));
                            }
                            
                            foreach (var voucher in list_evc_products)
                            {
                                string[] serviceVoucher = voucher.voucherService.Split(',');
                                //int[] _serviceVoucher = Array.ConvertAll(serviceVoucher, s => int.Parse(s));
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceVoucher", serviceVoucher));
                                //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_serviceVoucher", _serviceVoucher));

                                if (serviceVoucher.All(x => _serviceOrders.Contains(x) && serviceVoucher.Length == _serviceOrders.Length))
                                {
                                    lstEVC_policy.Add(voucher);
                                }
                            }
                        }
                        /* có thể sẽ còn sử dụng
                        if (listProCusOld.Count > 0)
                        {
                            lst = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                            {
                                actionName = "GetListVoucherProductOld",
                                xml = CreateXMLSubService(listProCusNew, input.SaleInfor.SaleChannelID, sb)
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                            lstEVC.AddRange(lst);
                        }*/
                    }
                    #endregion

                    #region Kiểm tra voucher theo tỉnh thành
                    if (lstEVC_policy.Count > 0)
                    {
                        var list_policy = new List<int>();
                        list_policy = connection.Query<int>(OSU6_FPTVoucher_PolicyVoucher_CheckLocation, new
                        {
                            XML = CreateXMLPolicy(lstEVC_policy, sb),
                            LocationId = input.CustomerInfor.LocationID,
                            BranchCode = input.CustomerInfor.BranchCode,
                            DistrictId = input.CustomerInfor.DistrictID
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list policy id available by location", list_policy));

                        if (list_policy.Count > 0)
                        {
                            var evclist = (from voucher in lstEVC_policy
                                          where list_policy.Contains(voucher.policyId)
                                          select voucher).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list evoucher available by location", evclist));

                            if (evclist.Count > 0)
                            {
                                lstEVC.AddRange(evclist);
                            }
                        }
                    }
                    #endregion

                    #region Kiểm tra Quota hạn mức
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstEVC check quota ", lstEVC));
                    foreach (var voucher in lstEVC)
                    {
                        decimal valueDiscount = 0;
                        decimal quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                        {
                            VoucherCode = voucher.VoucherCode,
                            SaleId = input.SaleInfor.SaleID,
                            LocationId = input.CustomerInfor.LocationID,
                            BranchCode = input.CustomerInfor.BranchCode,
                            DistrictId = input.CustomerInfor.DistrictID

                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("quotaMoneyAvailable " + voucher.VoucherCode + " ", quotaMoneyAvailable));

                        if (voucher.applyTypeID.Equals(2))
                        {
                            var dataVoucher = dataListEVC.Where(x => x.VoucherCode == voucher.VoucherCode).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataVoucher for voucher " + voucher.VoucherCode + " ", dataVoucher));

                            var dataMapVoucher = (from ser in listServices
                                                  join evc in dataVoucher on new { serviceId = ser.serviceId, subServiceTypeId = ser.subServiceTypeId, subServiceId = ser.subServiceId, serviceCode = ser.serviceCode } equals
                                                    new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                                  select ser).Distinct().ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataMapVoucher for voucher " + voucher.VoucherCode + " ", dataMapVoucher));
                            if (voucher.promotionTypeId.Equals(4))//fix code vì id loại voucher ko thể tăng (4 là thặng tháng)
                            {
                                valueDiscount = (dataMapVoucher.Count > 0 ? dataMapVoucher.Sum(a => a.total_vat / a.monthUsed) * voucher.duration : 0);
                            }
                            else
                            {
                                valueDiscount = (voucher.duration > 0 ? voucher.duration * voucher.valueVoucher : voucher.valueVoucher) * dataMapVoucher.Sum(a => a.qty);
                            }
                        }else
                        {
                            var dataVoucher = GetServices_PHM(voucher.VoucherCode);
                            //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataVoucher for voucher " + voucher.VoucherCode + " ", dataVoucher));

                            var dataMapVoucher = (from pro in listProCusNew
                                                  join evc in dataVoucher on new { serviceId = pro.serviceId, subServiceTypeId = pro.subServiceTypeId, subServiceId = pro.subServiceId, serviceCode = pro.serviceCode } equals
                                                    new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                                  select pro).Distinct().ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataMapVoucher for voucher " + voucher.VoucherCode + " ", dataMapVoucher));


                            valueDiscount = dataMapVoucher == null ? 0 : dataMapVoucher.Sum(x => x.qty) * voucher.valueVoucher;
                        }
                        
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("valueDiscount for voucher " + voucher.VoucherCode + " ", valueDiscount));
                        if (quotaMoneyAvailable >= valueDiscount && quotaMoneyAvailable > 0 && valueDiscount > 0)
                        {
                            DataLstEVC.Add(new Evoucher { VoucherCode = voucher.VoucherCode, Description = voucher.Description, Note = voucher.Note, Todate = voucher.Todate });
                        }
                    }
                    #endregion
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher Error", ex.Message));
                return null;
            }
            return DataLstEVC.ToList();
        }
        public static List<CustomerSaleOrderCollections> FilterSubservice(StringBuilder sb, SqlConnection connection, List<CustomerSaleOrderCollections> input, string datafillter)
        {
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FilterSubservice input", input));
            
            var listres = new List<CustomerSaleOrderCollections>();
            var lstService = new List<ServiceGroup>();
            lstService = connection.Query<ServiceGroup>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
            {
                actionName = "GetGroupService",
                subServices = datafillter
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FilterSubservice GetGroupService", lstService));

            foreach (var service in lstService)
            {
                string[] _subservices = service.SubServiceId.Split(',').Select(x => x.Trim()).ToArray();
                var data = input.Where(x => x.serviceId == service.ServiceID).ToList();
                if (data.Count > 0)
                {
                    var dataList = (from item in data
                                    where _subservices.Contains(item.subServiceId.ToString())
                                    select item).ToList();
                    if (dataList.Count > 0)
                    {
                        listres.AddRange(dataList);
                    }
                    else
                    {
                        listres = new List<CustomerSaleOrderCollections>();
                        break;
                    }
                }
                else
                {
                    listres = new List<CustomerSaleOrderCollections>();
                    break;
                }
            }

            return listres;
        }

        public static List<CustomerSaleOrderCollections> FilterService(List<CustomerSaleOrderCollections> input, string datafillter)
        {
            string[] _arrayFilter = datafillter.Split(',');

            var result = input.Where(item => _arrayFilter.Contains(item.serviceId.ToString()) && input.Count(x => _arrayFilter.Contains(x.serviceId.ToString())) == _arrayFilter.Count()).ToList();
            return result;
        }

        private static XElement CreateXMLPolicy(List<PolicyVoucher> lstEVC_policy, StringBuilder sb)
        {
            var xmlString = new XElement("N",
            from evc in lstEVC_policy
            select new XElement("I",
                           new XElement("PolicyId", evc.policyId)
                       ));
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher CreateXMLPolicy ", xmlString));
            return xmlString;
        }
        public static XElement CreateXMLSubService(List<CustomerSaleOrderCollections> input, int saleChannel, StringBuilder sb)
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("prePaid", item.prePaid),
                           new XElement("deployTypeId", item.deployTypeId),
                           new XElement("statusId", item.statusId),
                           new XElement("revokeId", item.revokeID),
                           new XElement("usesId", item.usesId),
                           new XElement("qty", item.qty),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("total", item.total),
                           new XElement("total_vat", item.total_vat),
                           new XElement("policyId", item.policyId),
                           new XElement("crossSellingLocation", item.crossSellingLocation),
                           new XElement("paymentTypeId", item.paymentTypeId),
                           //new XElement("minmoney", minmoney),
                           new XElement("customerType", item.customerType),
                           new XElement("cusTypeId", item.cusTypeId),
                           new XElement("cusTypeL2Id", item.cusTypeL2Id),
                           new XElement("objectTypeId", item.objectTypeId),
                           new XElement("customerRank", item.customerRank),
                           new XElement("saleChannel", saleChannel)
                       //new XElement("serviceRegister", s_services)
                       ));
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher CreateXMLSubService ", xmlString));
            return xmlString;
        }

        public static SalePlatformVoucherValueInfor GetVoucherGCinfo(List<ServicePlatform> lstSer)
        {
            SalePlatformVoucherValueInfor voucherinfo = new SalePlatformVoucherValueInfor();
            voucherinfo.Apply = new List<Apply>();
            voucherinfo.RefInfo = new RefInfo();

            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {

                }
            }
            catch (Exception ex)
            {

                return null;
            }
            return voucherinfo;
        }

        public static int GetTypeVoucher(string voucherCode)
        {
            int type = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                type = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetTypeVoucher",
                    voucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return type;
        }

        public static XElement CreateXMLGetInfor(SalePolicyGetInforEVC input)
        {
            var listServices = (from service in input.Services
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeID,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed)
                                }).ToList();

            var xmlServices = new XElement("N",
            from item in listServices
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("statusId", -1),
                           new XElement("revokeID", -1),
                           new XElement("deployTypeId", -1)
                       ));

            var listProducts = (from service in input.Products
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    statusId = subService.StatusID,
                                    revokeID = subService.RevokeID,
                                    deployTypeId = subService.DeployTypeID
                                }).ToList();

            var xmlProducts = new XElement("N",
            from item in listProducts
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("statusId", item.statusId),
                           new XElement("revokeID", item.revokeID),
                           new XElement("deployTypeId", item.deployTypeId)
                       ));

            if(xmlProducts != null)
            {
                xmlServices.Add(xmlProducts);
            }

            return xmlServices;
        }

        public static XElement CreateXMLGetInforProduct(SalePolicyGetInforEVC input, int i)
        {
            var listProducts = (from service in input.Products
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed)
                                }).ToList();

            var xmlString = new XElement("N",
            from item in listProducts
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed)
                       //new XElement("serviceRegister", s_services)
                       ));
            return xmlString;
        }

        public static List<Apply> GetListInforService(string voucherCode, XElement xml)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data= connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetVoucherValue",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static List<Apply> GetListInforProductService(string voucherCode, XElement xml)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data =  connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetInforProductService",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }


        public static decimal CheckQuota(string voucherCode, SaleInfor saleInfor, CustomerInfor customerInfor) // đơn vị: VNĐ
        {
            decimal quotaMoneyAvailable  = 0;
            //hạn mức sale còn lại
            using(var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                {
                    VoucherCode = voucherCode,
                    SaleId = saleInfor.SaleID,
                    LocationId = customerInfor.LocationID,
                    BranchCode = customerInfor.BranchCode,
                    DistrictId = customerInfor.DistrictID

                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            
            return quotaMoneyAvailable;
        }

        public static List<RegisterVoucherType> GetRegisterTypeVoucher(string evc_code)
        {
            List<RegisterVoucherType> resDataa = new List<RegisterVoucherType>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                resDataa = connection.Query<RegisterVoucherType>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetRegisterTypeVoucher",
                    voucherCode = evc_code
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                return resDataa;
            }
        }

        public static int GetPromotionType(string voucherCode)
        {
            int i = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                i = connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                {
                    Action = "GetPromotionTypeId",
                    VoucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return i;
        }

        public static List<EvoucherService> GetListEvcService(Guid logId, List<EvoucherInput> lstEVC)
        {
            List<EvoucherService> lstVoucher = new List<EvoucherService>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();

                if (lstEVC != null)
                {
                    try
                    {
                        foreach (var evc in lstEVC)
                        {
                            List<EvoucherService> voucherServices = new List<EvoucherService>();
                            voucherServices = connection.Query<EvoucherService>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                            {
                                Action = "GetServiceVoucher",
                                VoucherCode = evc.evoucherCode
                            }, commandType: CommandType.StoredProcedure).ToList();
                            voucherServices.Select(x => x.evoucherType = evc.evoucherType).ToList();
                            if (voucherServices != null)
                                lstVoucher.AddRange(voucherServices);

                            var voucherProducts = connection.Query<EvoucherService>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                               {
                                   Action = "GetServiceVoucherProduct",
                                   VoucherCode = evc.evoucherCode
                               }, commandType: CommandType.StoredProcedure).ToList();
                            voucherProducts.Select(x => x.evoucherType = evc.evoucherType).ToList();
                            if (voucherProducts != null)
                                lstVoucher.AddRange(voucherProducts);
                        }
                    }
                    catch (Exception ex)
                    {
                        MBSv4CommonService.WriteToLog(ex.Message, "MBSv4 KeepVoucher getListEvcService error: ", logId);
                    }
                }
            }

            return lstVoucher;
        }

        private static XElement CreateElementGCCode(dynamic data)
        {
            var xmlString = new XElement("N",
                            new XElement("I",
                            new XElement("Code", data.Code),
                            new XElement("PromotionEventID", data.PromotionEventID),
                            new XElement("OrderCode", data.OrderCode),
                            new XElement("Source", data.Source),
                            new XElement("SubCompany", data.SubCompany),
                            new XElement("LocationID", data.LocationID),
                            new XElement("BranchCode", data.BranchCode),
                            new XElement("DepartmentID", data.DepartmentID),
                            new XElement("SaleID", data.SaleID),
                            new XElement("ObjID", data.ObjID),
                            new XElement("ActiveChannel", 2),
                            new XElement("BNET", 0),
                            new XElement("FreeMonthID", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("LocalTypeID", data.LocalTypeID),
                            new XElement("PaidTimeTypeID", data.PaidTimeTypeID)));
            return xmlString;
        }

        private static XElement CreateXMLGeneralCodeDetail(int generalCodeId, dynamic service, decimal money)
        {
            var xmlString = new XElement("N",
                    new XElement("I",
                    new XElement("GeneralCodeId", generalCodeId),
                    new XElement("ServiceId", service.ServiceID),
                    new XElement("SubServiceId", service.SubServiceID),
                    new XElement("SubServiceTypeId", service.SubServiceTypeID),
                    new XElement("ServiceCode", service.ServiceCode),
                    new XElement("Quantity", service.Qty),
                    new XElement("TotalDiscount", money)));

            return xmlString;
        }

        private static XElement CreateXMLExtendHistoryTemp(SalePolicyRedeemEVC input, List<int> historyTempIds)
        {
            var xmlString = new XElement("N",
                from id in historyTempIds
                select new XElement("I",
                new XElement("HistoryTempId", id),
                new XElement("PhoneNumberContract", input.CustomerPhone?.PhoneNumberContract),
                new XElement("PhoneNumberCMR", input.CustomerPhone?.PhoneNumberCMR),
                new XElement("LocationId", input.CustomerInfor?.LocationID),
                new XElement("DistrictId", input.CustomerInfor?.DistrictID),
                new XElement("WardId", input.CustomerInfor?.WardID),
                new XElement("BuildingId", input.CustomerInfor?.BuildingID),
                new XElement("CusTypeId", input.CustomerInfor?.CusTypeID),
                new XElement("CusTypeL2Id", input.CustomerInfor?.CusTypeL2ID),
                new XElement("ObjectTypeId", input.CustomerInfor?.ObjectTypeID),
                new XElement("CustomerRank", input.CustomerInfor?.CustomerRank),
                new XElement("CrossSellingLocation", input.CrossSellingLocation),
                new XElement("ServiceAvailable", input.ServiceAvailable != null ? string.Join(",", input.ServiceAvailable) : ""),
                new XElement("SaleId", input.SaleInfor?.SaleID),
                new XElement("SaleTeamId", input.SaleInfor?.SaleTeamID),
                new XElement("SaleChannelId", input.SaleInfor?.SaleChannelID),
                new XElement("PaymentTypeId", input.PaymentTypeID),
                new XElement("GroupPoint", input.GroupPoint),
                new XElement("ActiveKplus", input.ExtendedProperties?.ActiveKplus),
                new XElement("InternetServiceId", input.ExtendedProperties?.Internet?.ServiceID),
                new XElement("InternetChargeMonthly", input.ExtendedProperties?.Internet?.ChargeMonthly)));


            return xmlString;
        }

        public static List<int> GetHistoryTempIds(SqlConnection connection, SqlTransaction transaction, string orderCode, int objId, string voucherCode)
        {
            return connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "GetHistoryTempIds",
                OrderCode = orderCode,
                ObjId = objId,
                VoucherCode = voucherCode
            }, transaction: transaction, commandTimeout: 0, commandType: CommandType.StoredProcedure).ToList();
        }

        public static bool AddExtendHistoryTemp(SqlConnection connection, SqlTransaction transaction, List<int> historyTempIds, SalePolicyRedeemEVC input)
        {
            return connection.Execute(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "AddExtendHistoryTemp",
                XML = CreateXMLExtendHistoryTemp(input, historyTempIds)
            }, transaction: transaction, commandTimeout: 0, commandType: CommandType.StoredProcedure) > 0;
        }

        public static string RecheckEvoucher(SalePolicyRedeemEVC input, StringBuilder sb)
        {
            var DataLstEVC = new List<Evoucher>();
            var lstEVC = new List<PolicyVoucher>();
            var lstEVC_policy = new List<PolicyVoucher>();
            var lst = new List<PolicyVoucher>();
            try
            {

                var listServices = (from service in input.Services
                                    join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                    from subServiceType in service.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = service.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeID,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = Convert.ToInt32(subService.PrePaid),
                                        deployTypeId = subService.DeployTypeID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listServices", listServices));

                var listProducts = (from product in input.Products
                                    join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                    from subServiceType in product.SubServiceTypes
                                    from subService in subServiceType.SubServices
                                    select new CustomerSaleOrderCollections
                                    {
                                        serviceId = product.ServiceID,
                                        subServiceTypeId = subServiceType.SubServiceTypeId,
                                        subServiceId = subService.SubServiceID,
                                        serviceCode = subService.ServiceCode,
                                        prePaid = subService.ApplyPrePaid,
                                        deployTypeId = subService.DeployTypeID,
                                        statusId = subService.StatusID,
                                        revokeID = subService.RevokeID,
                                        usesId = subService.UsesID,
                                        qty = subService.Qty,
                                        monthUsed = Convert.ToInt32(subService.MonthUsed),
                                        total = Convert.ToInt32(subService.Total),
                                        total_vat = Convert.ToInt32(subService.Total_VAT),
                                        policyId = subService.PolicyID,
                                        customerType = customertype.CustomerType,
                                        crossSellingLocation = input.CrossSellingLocation,
                                        paymentTypeId = input.PaymentTypeID,
                                        cusTypeId = input.CustomerInfor.CusTypeID,
                                        cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                        objectTypeId = input.CustomerInfor.ObjectTypeID,
                                        customerRank = input.CustomerInfor.CustomerRank
                                    }).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProducts", listProducts));


                var _serviceOrders = input.CustomerTypes.Select(x => x.ServiceID.ToString()).ToArray();
                Array.Sort(_serviceOrders);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_serviceOrders", _serviceOrders));

                var listProCusNew = new List<CustomerSaleOrderCollections>();
                var listProCusOld = new List<CustomerSaleOrderCollections>();
                if (listProducts != null && listProducts.Count > 0)
                {
                    listProCusNew = listProducts.Where(x => x.customerType.Equals(2)).ToList(); // khách hàng mới là 2, cũ là 1
                    listProCusOld = listProducts.Where(x => x.customerType.Equals(1)).ToList(); // khách hàng mới là 2, cũ là 1
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProCusNew", listProCusNew));
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProCusOld", listProCusOld));

                var lstOrders = new List<CustomerSaleOrderCollections>();
                if (listServices != null && listServices.Count > 0)
                {
                    lstOrders.AddRange(listServices);
                }
                if (listProducts != null && listProducts.Count > 0)
                {
                    lstOrders.AddRange(listProducts);
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstOrders", lstOrders));

                var dataListEVC = new List<PolicyVoucher>();
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    var dataPolicyId_order = lstOrders.Select(x => x.policyId).ToList().Distinct();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataPolicyId_order", dataPolicyId_order));
                    int goldenPolicy = connection.Query<int>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                    {
                        actionName = "CheckPolicyGoldenDate",
                        policyIds = string.Join(",", dataPolicyId_order)
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("goldenPolicy", goldenPolicy));
                    if (goldenPolicy.Equals(1))
                    {
                        return "Đơn hàng là chính sách ngày vàng.";
                    }
                    #region lấy voucher theo service
                    if (listServices != null && listServices.Count > 0)
                    {

                        dataListEVC = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                        {
                            actionName = "GetListVoucherServices",
                            xml = CreateXMLSubService(listServices, input.SaleInfor.SaleChannelID, sb)
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataListEVC", dataListEVC));

                        if (dataListEVC.Count > 0)
                        {
                            var lstVC = (from x in dataListEVC
                                         select new PolicyVoucher
                                         {
                                             policyId = x.policyId,
                                             VoucherCode = x.VoucherCode,
                                             Description = x.Description,
                                             Note = x.Note,
                                             Todate = x.Todate,
                                             voucherService = x.voucherService,
                                             promotionTypeId = x.promotionTypeId,
                                             valueVoucher = x.valueVoucher,
                                             duration = x.duration,
                                             applyTypeID = x.applyTypeID,
                                             subServiceCondition = x.subServiceCondition,
                                             minmoney = x.minmoney,
                                             minprepaid = x.minprepaid,
                                             minConnectionFee = x.minConnectionFee,
                                             applyFor = x.applyFor,
                                             serviceId = x.serviceId,
                                             subServiceTypeId = x.subServiceTypeId,
                                             subServiceId = x.subServiceId,
                                             serviceCode = x.serviceCode
                                         }).OrderByDescending(x => x.subServiceId) //nếu có PHM sẽ ưu tiên lấy subServiceId PHM
                                           .GroupBy(n => new { n.VoucherCode, n.serviceId, n.subServiceId, n.serviceCode, n.subServiceTypeId, n.subServiceCondition })
                                           .Select(g => g.FirstOrDefault())
                                           .ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstVC", lstVC));

                            var listVCservice = lstVC.Where(x => x.promotionTypeId != 3).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listVCservice", listVCservice));

                            if (listVCservice.Count > 0)
                            {
                                lst.AddRange(listVCservice);
                            }
                            // lấy các voucher phí hòa mạng, tiếp tục kiểm tra voucher hòa mạng thiết bị
                            // promotiontype id=3 là phí hòa mạng
                            // phí hòa mạng có kiểm tra thiết bị sẽ có applyfor =3
                            // phí hòa mạng giảm cho dịch vụ sẽ không cần kiểm tra điều kiện áp dụng thiết bị: 1 là dịch vụ, 3 là dịch vụ + thiết bị
                            var listEVCPHM_product = lstVC.Where(x => x.promotionTypeId.Equals(3) && x.applyFor.Equals(3)).ToList(); //  PHM thiết bị
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM", listEVCPHM_product));

                            var listEVCPHM_Service = lstVC.Where(x => x.promotionTypeId.Equals(3) && x.applyFor.Equals(1)).ToList(); // PHM dịch vụ
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVCPHM_Service", listEVCPHM_Service));

                            if (listEVCPHM_Service?.Count > 0)
                            {
                                // lấy những service có PHM 
                                var order_phm_net = listServices.Where(x => x.subServiceId == 999999999).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_net count", order_phm_net.Count));
                                if (order_phm_net.Count > 0)
                                {
                                    var data_phm_net = listEVCPHM_Service.Where(x => x.serviceId == 1).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_net", data_phm_net));
                                    lst.AddRange(data_phm_net);
                                }
                                var order_phm_tv = listServices.Where(x => x.subServiceId == 999999996).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("order_phm_tv count", order_phm_tv.Count));
                                if (order_phm_tv.Count > 0)
                                {
                                    var data_phm_tv = listEVCPHM_Service.Where(x => x.serviceId == 7).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("data_phm_tv", data_phm_tv));
                                    lst.AddRange(data_phm_tv);
                                }
                            }

                            if (listEVCPHM_product.Count > 0)
                            {

                                string evc_phm = string.Empty;
                                var evc_phm_lst = (from item in listEVCPHM_product
                                                   select item.VoucherCode).ToList();
                                evc_phm = string.Join(",", evc_phm_lst);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evc_phm", evc_phm));

                                var lst_evc_phm_true = new List<PolicyVoucher>();
                                lst_evc_phm_true = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                                {
                                    actionName = "validVoucherPHM",
                                    xml = CreateXMLSubService(listProducts, input.SaleInfor.SaleChannelID, sb),
                                    vouchers = evc_phm
                                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lst_evc_phm_true", lst_evc_phm_true));

                                if (lst_evc_phm_true.Count > 0)
                                {
                                    var evc_code_phm_lst = (from item in lst_evc_phm_true
                                                            select item.VoucherCode).ToList();
                                    var dataEVC_phm = lstVC.Where(x => evc_code_phm_lst.Contains(x.VoucherCode)).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataEVC_phm", dataEVC_phm));

                                    if (dataEVC_phm.Count > 0)
                                    {
                                        lst.AddRange(dataEVC_phm);
                                    }
                                }
                            }
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lst", lst));

                            var listEVC_b4_check_service = new List<PolicyVoucher>();
                            string voucherOrderJoin = string.Join(",", _serviceOrders);
                            var dataListVoucherService = lst.Where(x => x.voucherService.Equals(voucherOrderJoin)).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataListVoucherService", dataListVoucherService));
                            foreach (var voucher in dataListVoucherService)
                            {
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucher", voucher));

                                string[] serviceVoucher = voucher.voucherService.Split(',');
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceVoucher", serviceVoucher));

                                if (!serviceVoucher.Except(_serviceOrders).Any()) // kiểm tra đơn hàng có đủ service của evc không
                                {
                                    if (voucher.minmoney > 0 || voucher.minprepaid > 0 || voucher.minConnectionFee > 0)
                                    {
                                        var listSubservice_by_condition = FilterSubservice(sb, connection, listServices, voucher.subServiceCondition);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listSubservice_by_condition", listSubservice_by_condition));

                                        var listproduct_by_condition = FilterService(listProCusNew, voucher.voucherService); // lấy thiết bị để cộng tiền check phí hòa mạng
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listproduct_by_condition", listproduct_by_condition));

                                        if (listSubservice_by_condition.Count > 0 || listproduct_by_condition.Count > 0)
                                        {
                                            int totalMoney = listSubservice_by_condition.Sum(a => a.total_vat);
                                            int totalMoneyConnFee = listproduct_by_condition.Sum(a => a.total_vat);
                                            int minPrepaid = 0;
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalMoney", totalMoney));
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("totalMoneyConnFee", totalMoneyConnFee));

                                            if (listSubservice_by_condition.Count > 0)
                                            {
                                                minPrepaid = listSubservice_by_condition.Min(a => a.prePaid);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("minPrepaid", minPrepaid));

                                                if (totalMoney >= voucher.minmoney && totalMoneyConnFee >= voucher.minConnectionFee && minPrepaid >= voucher.minprepaid)
                                                {
                                                    lstEVC_policy.Add(voucher);
                                                }
                                            }
                                            else
                                            {
                                                lstEVC_policy.Add(voucher);
                                            }
                                        }
                                    }
                                    else
                                    {
                                        lstEVC_policy.Add(voucher);
                                    }
                                }
                            }
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher lstEVC", lstEVC_policy));
                        }
                    }
                    #endregion

                    #region lấy voucher theo product
                    if (listProducts != null && listProducts.Count > 0)
                    {
                        if (listProCusNew.Count > 0)
                        {
                            var list_evc_products = new List<PolicyVoucher>();
                            list_evc_products = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                            {
                                actionName = "GetListVoucherProductNew",
                                xml = CreateXMLSubService(listProCusNew, input.SaleInfor.SaleChannelID, sb)
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_evc_products 1", list_evc_products));
                            // lấy danh sách các voucher ưu đãi cho thiết bị nhưng điều kiện là thiết bi + dịch vụ
                            var VoucherlistDeviceService = list_evc_products.Where(x => x.applyFor == 3 && x.applyTypeID == 1).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("VoucherlistDeviceService ", VoucherlistDeviceService));
                            // kiểm tra danh sách voucher (thiết bi + dịch vụ) trong phần voucher dịch vụ
                            if (VoucherlistDeviceService.Count > 0)
                            {
                                list_evc_products = list_evc_products.Except(VoucherlistDeviceService).ToList();
                                // kiểm tra điều kiện dịch vụ
                                var dataList = VoucherlistDeviceService.Select(x => x.VoucherCode).ToList();
                                string evouchers = string.Join(",", dataList);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evouchers ", evouchers));
                                // kiểm tra dịch vụ của evc thiết bị
                                var list_evc_service_products = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                                {
                                    actionName = "ValidEVCdevice",
                                    xml = CreateXMLSubService(listServices, input.SaleInfor.SaleChannelID, sb),
                                    vouchers = evouchers
                                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_evc_service_products ", list_evc_service_products));

                                if (list_evc_service_products.Count > 0)
                                {
                                    list_evc_service_products = list_evc_service_products.Distinct().ToList();
                                    list_evc_products.AddRange(list_evc_service_products);
                                }

                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list_evc_products 2", list_evc_products));
                            }

                            foreach (var voucher in list_evc_products)
                            {
                                string[] serviceVoucher = voucher.voucherService.Split(',');
                                //int[] _serviceVoucher = Array.ConvertAll(serviceVoucher, s => int.Parse(s));
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceVoucher", serviceVoucher));
                                //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("_serviceVoucher", _serviceVoucher));

                                if (serviceVoucher.All(x => _serviceOrders.Contains(x) && serviceVoucher.Length == _serviceOrders.Length))
                                {
                                    lstEVC_policy.Add(voucher);
                                }
                            }
                        }
                        /* có thể sẽ còn sử dụng
                        if (listProCusOld.Count > 0)
                        {
                            lst = connection.Query<PolicyVoucher>(OS6_FPTVoucher_SalePlatform_PolicyVoucher, new
                            {
                                actionName = "GetListVoucherProductOld",
                                xml = CreateXMLSubService(listProCusNew, input.SaleInfor.SaleChannelID, sb)
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                            lstEVC.AddRange(lst);
                        }*/
                    }
                    #endregion

                    #region Kiểm tra voucher theo tỉnh thành
                    if (lstEVC_policy.Count > 0)
                    {
                        var list_policy = new List<int>();
                        list_policy = connection.Query<int>(OSU6_FPTVoucher_PolicyVoucher_CheckLocation, new
                        {
                            XML = CreateXMLPolicy(lstEVC_policy, sb),
                            LocationId = input.CustomerInfor.LocationID,
                            BranchCode = input.CustomerInfor.BranchCode,
                            DistrictId = input.CustomerInfor.DistrictID
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list policy id available by location", list_policy));

                        if (list_policy.Count > 0)
                        {
                            var evclist = (from voucher in lstEVC_policy
                                           where list_policy.Contains(voucher.policyId)
                                           select voucher).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("list evoucher available by location", evclist));

                            if (evclist.Count > 0)
                            {
                                lstEVC.AddRange(evclist);
                            }
                        }
                    }
                    #endregion

                    // kiem tra xem đơn hàng có thỏa evc ko
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstEVC chua check quota", lstEVC));
                    var listEVC_General = input.evoucherList.Where(x => x.evoucherType == 1).ToList();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listEVC_General", listEVC_General));
                    foreach (var voucher in listEVC_General)
                    {
                        var checkVoucher = lstEVC.Where(x => x.VoucherCode == voucher.evoucherCode).FirstOrDefault();
                        if (checkVoucher == null)
                        {
                            return "Voucher bạn đã chọn/nhập hết hiệu lực, vui lòng liên hệ bộ phận quản lý chính sách.";
                        }
                    }

                    #region Kiểm tra Quota hạn mức
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstEVC check quota ", lstEVC));
                    foreach (var voucher in lstEVC)
                    {
                        decimal valueDiscount = 0;
                        decimal quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                        {
                            VoucherCode = voucher.VoucherCode,
                            SaleId = input.SaleInfor.SaleID,
                            LocationId = input.CustomerInfor.LocationID,
                            BranchCode = input.CustomerInfor.BranchCode,
                            DistrictId = input.CustomerInfor.DistrictID

                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("quotaMoneyAvailable " + voucher.VoucherCode + " ", quotaMoneyAvailable));

                        if (voucher.applyTypeID.Equals(2))
                        {
                            var dataVoucher = dataListEVC.Where(x => x.VoucherCode == voucher.VoucherCode).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataVoucher for voucher " + voucher.VoucherCode + " ", dataVoucher));

                            var dataMapVoucher = (from ser in listServices
                                                  join evc in dataVoucher on new { serviceId = ser.serviceId, subServiceTypeId = ser.subServiceTypeId, subServiceId = ser.subServiceId, serviceCode = ser.serviceCode } equals
                                                    new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                                  select ser).Distinct().ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataMapVoucher for voucher " + voucher.VoucherCode + " ", dataMapVoucher));
                            if (voucher.promotionTypeId.Equals(4))//fix code vì id loại voucher ko thể tăng (4 là thặng tháng)
                            {
                                valueDiscount = dataMapVoucher.Count > 0 ? dataMapVoucher.Sum(a => a.total_vat / a.monthUsed) * voucher.duration : 0;
                            }
                            else
                            {
                                valueDiscount = (voucher.duration > 0 ? voucher.duration * voucher.valueVoucher : voucher.valueVoucher) * dataMapVoucher.Sum(a => a.qty);                               
                            }
                        }
                        else
                        {
                            var dataVoucher = GetServices_PHM(voucher.VoucherCode);
                            //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataVoucher for voucher " + voucher.VoucherCode + " ", dataVoucher));

                            var dataMapVoucher = (from pro in listProCusNew
                                                  join evc in dataVoucher on new { serviceId = pro.serviceId, subServiceTypeId = pro.subServiceTypeId, subServiceId = pro.subServiceId, serviceCode = pro.serviceCode } equals
                                                    new { serviceId = evc.serviceId, subServiceTypeId = evc.subServiceTypeId, subServiceId = evc.subServiceId, serviceCode = evc.serviceCode }
                                                  select pro).Distinct().ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataMapVoucher for voucher " + voucher.VoucherCode + " ", dataMapVoucher));


                            valueDiscount = dataMapVoucher == null ? 0 : dataMapVoucher.Sum(x => x.qty) * voucher.valueVoucher;          
                        }

                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("valueDiscount for voucher " + voucher.VoucherCode + " ", valueDiscount));
                        if (quotaMoneyAvailable >= valueDiscount && quotaMoneyAvailable > 0 && valueDiscount > 0)
                        {
                            DataLstEVC.Add(new Evoucher { VoucherCode = voucher.VoucherCode, Description = voucher.Description, Note = voucher.Note, Todate = voucher.Todate });
                        }
                        else if (input.evoucherList.Where(x => x.evoucherCode == voucher.VoucherCode).FirstOrDefault() != null)
                        {
                            return "Hạn mức khả dụng còn lại chỉ được áp dụng tối đa " + string.Format("{0:n0}", quotaMoneyAvailable) + " VNĐ. Vui lòng kiểm tra lại thông tin đơn hàng hoặc liên hệ BGĐ CN để được cấp thêm.";
                        }
                    }
                    #endregion
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher Error", ex.Message));
                return null;
            }
            return "";
        }

        public static List<CustomerSaleOrderCollections> GetServicesFromInput(SalePolicyGetInforEVC input)
        {
            var services = new List<CustomerSaleOrderCollections>();

            var listServices = (from service in input.Services
                                join customertype in input.CustomerTypes on service.ServiceID equals customertype.ServiceID
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeID,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    prePaid = Convert.ToInt32(subService.PrePaid),
                                    deployTypeId = subService.DeployTypeID,
                                    qty = subService.Qty,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    total = Convert.ToInt32(subService.Total),
                                    total_vat = Convert.ToInt32(subService.Total_VAT),
                                    policyId = subService.PolicyID,
                                    customerType = customertype.CustomerType,
                                    crossSellingLocation = input.CrossSellingLocation,
                                    paymentTypeId = input.PaymentTypeID,
                                    cusTypeId = input.CustomerInfor.CusTypeID,
                                    cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                    objectTypeId = input.CustomerInfor.ObjectTypeID,
                                    customerRank = input.CustomerInfor.CustomerRank
                                }).ToList();

            var listProducts = (from product in input.Products
                                join customertype in input.CustomerTypes on product.ServiceID equals customertype.ServiceID
                                from subServiceType in product.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = product.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    prePaid = subService.ApplyPrePaid,
                                    deployTypeId = subService.DeployTypeID,
                                    statusId = subService.StatusID,
                                    revokeID = subService.RevokeID,
                                    usesId = subService.UsesID,
                                    qty = subService.Qty,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed),
                                    total = Convert.ToInt32(subService.Total),
                                    total_vat = Convert.ToInt32(subService.Total_VAT),
                                    policyId = subService.PolicyID,
                                    customerType = customertype.CustomerType,
                                    crossSellingLocation = input.CrossSellingLocation,
                                    paymentTypeId = input.PaymentTypeID,
                                    cusTypeId = input.CustomerInfor.CusTypeID,
                                    cusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                    objectTypeId = input.CustomerInfor.ObjectTypeID,
                                    customerRank = input.CustomerInfor.CustomerRank
                                }).ToList();
            if(!(listServices is null))
            {
                services.AddRange(listServices);
            }
            if (!(listProducts is null))
            {
                services.AddRange(listProducts);
            }
            return services;
        }

        public static List<PolicyVoucher> GetServices_PHM(string voucherCode)
        {
            var data = new List<PolicyVoucher>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<PolicyVoucher>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetServices_PHM",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }
    
    }
}