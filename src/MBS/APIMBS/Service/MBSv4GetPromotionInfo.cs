using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Dapper;
using System.Data;
using Voucher.APIHelper;
using System.Xml.Linq;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models;
using APIMBS.Constant;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using Voucher.APIHelper.Util;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Configuration;
using System.Text;
using System.Web.Services;

namespace APIMBS.Service
{
    public class MBSv4GetPromotionInfo
    {
        private const string OS6_FPTVoucher_SPF_GetMonthVoucher = "PowerInside.dbo.OS6_FPTVoucher_SPF_GetMonthVoucher";
        public static SalePlatformVoucherValueInfor GetVoucherGCinfo(List<ServicePlatform> lstSer,List<ProductPlatform> lstPro,string voucher,Tuple<int, int> prepaid_net_tv, int localtype, int saleid,int locationId, bool isComboService, int channel,Guid logId, StringBuilder sb, UserBranchPlatformModel userBranch= null)
        {
            sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo req", voucher));

            SalePlatformVoucherValueInfor voucherinfo = new SalePlatformVoucherValueInfor();
            voucherinfo.Apply = new List<Apply>();
            voucherinfo.RefInfo = new RefInfo();

            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    decimal discount = 0, discountVat = 0;
                    float discountMonth = 0;
                    var isDeductOrder = prepaid_net_tv.Item1 > 0 || prepaid_net_tv.Item2 > 0 ? 1 : 0;
                    const string internet = "INT", tv = "PlayN";

                    VoucherInfor PE = connection.Query<VoucherInfor>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                    {
                        ActionName = "GetPEByCode",
                        voucherCode = voucher
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo PE", PE));

                    int PrepaidID = MBSv4GetListVoucher.GetPrepaidIDForGetInfor(connection, prepaid_net_tv.Item1, prepaid_net_tv.Item2);
                    sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo PrepaidID", PrepaidID));

                    int quota = 0;
                    if (channel.Equals(0)) // MBS v4
                    {
                        int quotaAvailable = MBSv4GetListVoucher.GetQuotaV2ForGetInfor(connection, PE.ID, saleid, localtype, PrepaidID);
                        int quotaKeep = MBSv4GetListVoucher.GetQuotaKeepForGetInfor(connection, voucher, saleid);
                        quota = quotaAvailable - quotaKeep;
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo quota", quota));
                    }
                    if (channel.Equals(1)) // DKOL
                    {
                        int quotaAvailable = RegisterWebV5.GetQuotaUnuse(PE.ID, locationId, saleid, userBranch, localtype, PrepaidID);
                        int quotaKeep = 0;
                        MBSv4CommonService.WriteToLog("", $"GetVoucherGCinfo channel-1 quotaAvailable ${quotaAvailable} quotaKeep ${quotaKeep}", logId);

                        quota = quotaAvailable - quotaKeep;
                    }
                    
                    if (PE.ContainerID > 0 || PE.NetPromotionID > 0 || PE.PromotionNetID > 0 ||
                        PE.MoneyPromotionTVID > 0 || PE.MoneyPromotionNETID > 0 || PE.IPTVPromotionID > 0)
                    {
                        voucherinfo.Apply.AddRange(GetApplyForGetInfor(connection,voucher, prepaid_net_tv.Item1, lstSer, discount, discountVat,
                            discountMonth, PE.Name, isDeductOrder));
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo voucherinfo.apply", voucherinfo.Apply));
                    }

                    
                    if (PE.MoneyPromotionNETID > 0)
                    {
                        int valueVoucher = GetValueVoucher(connection, voucher, "MoneyPromotionNETID");
                        int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo valueVoucher MoneyPromotionNETID", valueVoucher));

                        discount = valueVoucher - valueVat;
                        discountVat = valueVoucher;

                        var apply = voucherinfo.Apply.FirstOrDefault(x =>
                            MBSv4GetListVoucher.GetServiceCodeForGetInfor(connection, x.ServiceID) == internet);
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo apply MoneyPromotionNETID", apply));

                        if (apply != null)
                        {
                            apply.Discount = discount;
                            apply.DiscountVAT = discountVat;
                        }
                    }

                    // phải có objid mới lấy ddc value ưu đãi
                    if (prepaid_net_tv.Item1 > -1 && PE.ContainerID >0)
                    {
                        // ưu tiên lấy số tháng tặng của net nếu hợp đồng là combo
                        discountMonth = GetDisMounth(connection, PE.EventCode);
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo voucherinfo.Dismonth NET", discountMonth));

                        var apply = voucherinfo.Apply.FirstOrDefault(x =>
                            MBSv4GetListVoucher.GetServiceCodeForGetInfor(connection, x.ServiceID) == internet);
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo apply ContainerID", apply));

                        if (apply != null)
                        {
                            apply.Dismonth = discountMonth;
                            apply.IsDeductOrder = 0;
                        }
                    }
                    
                    if (prepaid_net_tv.Item2 > -1 && PE.IPTVPromotionID >0)
                    {
                        discountMonth = GetDisMounthIPTV(PE.IPTVPromotionID);
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo voucherinfo.Dismonth IPTV", discountMonth));

                        var apply = voucherinfo.Apply.FirstOrDefault(x =>
                            MBSv4GetListVoucher.GetServiceCodeForGetInfor(connection, x.ServiceID) == tv);
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo apply IPTVPromotionID", apply));

                        if (apply != null)
                        {
                            apply.Dismonth = discountMonth;
                            apply.IsDeductOrder = 0;
                        }
                    }

                    if (PE.PromotionNetID >0)
                    {
                        int duration = GetDurationPromotionNet(connection, voucher, "PromotionNetID");
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo valueVoucher duration", duration));

                        int durationUse = (prepaid_net_tv.Item1 > 0 ? prepaid_net_tv.Item1 : 1) >= duration ? duration : (prepaid_net_tv.Item1 > 0 ? prepaid_net_tv.Item1 : 1);
                        int valueVoucher = GetValueVoucher(connection, voucher, "PromotionNetID") * durationUse;
                        int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo valueVoucher PromotionNetID", valueVoucher));

                        discount = valueVoucher - valueVat;
                        discountVat = valueVoucher;

                        var apply = voucherinfo.Apply.FirstOrDefault(x =>
                            MBSv4GetListVoucher.GetServiceCodeForGetInfor(connection, x.ServiceID) == internet);
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo apply PromotionNetID", apply));

                        if (apply != null)
                        {
                            apply.Discount = discount;
                            apply.DiscountVAT = discountVat;
                        }
                    }
                    if (PE.MoneyPromotionTVID > 0)
                    {
                        int valueVoucher = GetValueVoucher(connection, voucher, "MoneyPromotionTVID");
                        int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo valueVoucher MoneyPromotionTVID", valueVoucher));

                        discount = valueVoucher - valueVat;
                        discountVat = valueVoucher;

                        var apply = voucherinfo.Apply.FirstOrDefault(x =>
                            MBSv4GetListVoucher.GetServiceCodeForGetInfor(connection, x.ServiceID) == tv);
                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo apply MoneyPromotionTVID", apply));

                        if (apply != null)
                        {
                            apply.Discount = discount;
                            apply.DiscountVAT = discountVat;
                        }
                    }

                    if (PE.NetInterConnPromotionID > 0 || PE.IPTVInterConnPromotionID > 0)
                    {
                        List<Apply> lstApplyVoucher = getApplyPHM(connection, voucher, prepaid_net_tv.Item1);
                        
                        int valueVoucher = GetValueVoucher (connection, voucher, "NetInterConnPromotionID");
                        int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));

                        sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo valueVoucher NetInterConnPromotionID", valueVoucher));

                        var discountInter = valueVoucher - valueVat;
                        var discountInterVat = valueVoucher;

                        if (PE.NetInterConnPromotionID > 0)
                        {
                            var apply = (from service in lstSer
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                where subService.SubServiceID == 999999999
                                select new Apply
                                {
                                    ServiceID = service.ServiceID,
                                    SubServiceTypeID = subServiceType.SubServiceTypeID,
                                    SubServiceID = subService.SubServiceID,
                                    ServiceCode = subService.ServiceCode,
                                    Discount = discountInter,
                                    DiscountVAT = discountInterVat,
                                    Dismonth = 0,
                                    Value = PE.Name,
                                    IsDeductOrder = 1
                                }).FirstOrDefault();
                            sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo apply NetInterConnPromotionID", apply));

                            if (apply != null)
                            {
                                voucherinfo.Apply.Add(apply);
                            }
                        }                        

                        if (PE.IPTVInterConnPromotionID > 0 && isComboService && lstPro.Count > 0)
                        {
                            valueVoucher = GetValueVoucher(connection, voucher, "IPTVInterConnPromotionID");
                            valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                            
                            sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo valueVoucher IPTVInterConnPromotionID", valueVoucher));

                            discountInter = valueVoucher - valueVat;
                            discountInterVat = valueVoucher;

                            var filters = (from service in lstPro
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new
                                {
                                    service.ServiceID,
                                    subServiceType.SubServiceTypeId,
                                    subService.SubServiceID,
                                    subService.ApplySubServiceID,
                                    subService.ServiceCode
                                }).ToList();
                            
                            var applies = (from filter in filters
                                    from apply in lstApplyVoucher.Where(x =>
                                        x.ServiceID == filter.ServiceID &&
                                        x.SubServiceID == filter.ApplySubServiceID &&
                                        x.ServiceCode == filter.ServiceCode)
                                    select new { apply, filter })
                                .Select(x =>
                                    new Apply
                                    {
                                        Value = PE.Name,
                                        Discount = discountInter,
                                        DiscountVAT = discountInterVat,
                                        Dismonth = 0,
                                        IsDeductOrder = 1,
                                        ServiceID = x.filter.ServiceID,
                                        SubServiceID = x.filter.SubServiceID,
                                        SubServiceTypeID = x.filter.SubServiceTypeId,
                                        ServiceCode = x.filter.ServiceCode
                                    }).ToList();
                            
                            sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo applies IPTVInterConnPromotionID", applies));
                            voucherinfo.Apply.AddRange(applies);
                        }
                    }
                    
                    voucherinfo.evoucherCode = voucher;
                    
                    voucherinfo.TypeID = 0;
                    voucherinfo.Quota = quota;
                    voucherinfo.Apply.RemoveAll(x => x.Discount <= 0 && x.Dismonth <= 0);
                    sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo GetInforPromotionEvent", voucherinfo));
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(JoinStringToWriteLog("GetVoucherGCinfo Error", ex.Message));
                return null;
            }
            return voucherinfo;
        }

        public static float GetDisMounth(SqlConnection connection, string voucher)
        {
            return connection.Query<float>(OS6_FPTVoucher_SPF_GetMonthVoucher, new
            {
                voucherCode = voucher
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static float GetDisMounthIPTV(int id_iptv)
        {
            string connIptv = Utility.ConnIPTV;
            using (var connection = new SqlConnection(connIptv))
            {
                return connection.Query<float>(
                    "IPTV.dbo.OS6_FPTVoucher_GetDismonthIPTV",
                    new { id = id_iptv },
                    commandType: CommandType.StoredProcedure
                ).FirstOrDefault();
            }
        }

        public static List<Apply> GetApply(string voucher, int prepaidNet, List<ServicePlatform> services,
            decimal discount, decimal discountVat, float discountMonth, string value, int isDeductOrder)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var applies = connection.Query<Apply>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetApplyVoucher",
                    prepaidnet = prepaidNet,
                    voucherCode = voucher
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                var filters = (from service in services
                    from subServiceType in service.SubServiceTypes
                    from subService in subServiceType.SubServices
                    select new
                    {
                        service.ServiceID,
                        subService.SubServiceID,
                        subServiceType.SubServiceTypeID,
                        subService.ServiceCode,
                        subService.DeployTypeID
                    }).ToList();

                return (from filter in filters
                        from apply in applies.Where(x =>
                            x.ServiceID == filter.ServiceID &&
                            x.SubServiceID == filter.SubServiceID &&
                            x.SubServiceTypeID == filter.SubServiceTypeID &&
                            x.ServiceCode == filter.ServiceCode)
                        select new Apply()
                        {
                            ServiceID = apply.ServiceID,
                            SubServiceID = apply.SubServiceID,
                            SubServiceTypeID = apply.SubServiceTypeID,
                            ServiceCode = apply.ServiceCode,
                            StatusID = apply.StatusID,
                            RevokeID = apply.RevokeID,
                            DeployTypeID = filter.DeployTypeID,
                            Value = value,
                            Dismonth = discountMonth,
                            IsDeductOrder = isDeductOrder,
                            Discount = discount,
                            DiscountVAT = discountVat
                        }).ToList();
            }
        }

        public static List<Apply> getApply(string voucher, int PrepaidNet)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<Apply>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetApplyVoucher",
                    prepaidnet = PrepaidNet,
                    voucherCode = voucher
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
        }
        
        public static List<OldApply> oldGetApply(string voucher, int PrepaidNet)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<OldApply>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetApplyVoucher",
                    prepaidnet = PrepaidNet,
                    voucherCode = voucher
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
        }
        
        public static List<OldApply> oldGetApplyPHM(string voucher, int PrepaidNet)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<OldApply>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetApplyServiceCodeVoucher",
                    prepaidnet = PrepaidNet,
                    voucherCode = voucher
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
        }


        private static int GetValueVoucher(SqlConnection connection ,string voucherCode, string type)
        {
            return connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                            {
                                actionName = "GetValueVoucher",
                                voucherCode = voucherCode,
                                typeVoucher = type
                            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        
        public static OldSalePlatformVoucherValueInfor OldGetVoucherGCinfo(List<ServicePlatform> lstSer,List<ProductPlatform> lstPro,string voucher,Tuple<int, int> prepaid_net_tv, int localtype, int saleid,int locationId, bool isComboService, int channel,Guid logId, UserBranchPlatformModel userBranch= null)
        {
            MBSv4CommonService.WriteToLog(voucher, " GetVoucherGCinfo req ", logId);
            var voucherinfo = new OldSalePlatformVoucherValueInfor();
            voucherinfo.Apply = new List<OldApply>();
            voucherinfo.RefInfo = new RefInfo();
            voucherinfo.Discount = 0;
            voucherinfo.DiscountVAT = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    var PE = connection.Query<VoucherInfor>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                    {
                        ActionName = "GetPEByCode",
                        voucherCode = voucher
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    MBSv4CommonService.WriteToLog(PE, " GetVoucherGCinfo PE ", logId);
                    
                    if (PE.MoneyPromotionNETID > 0)
                    {
                        int valueVoucher = GetValueVoucher(connection, voucher, "MoneyPromotionNETID");
                        int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                        voucherinfo.Discount = valueVoucher - valueVat;
                        voucherinfo.DiscountVAT = valueVoucher;
                    }
                    if (PE.NetInterConnPromotionID > 0)
                    {
                        int valueVoucher = GetValueVoucher(connection, voucher, "NetInterConnPromotionID");
                        int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                        voucherinfo.Discount = valueVoucher - valueVat;
                        voucherinfo.DiscountVAT = valueVoucher;
                    }
                    
                    // phải có objid mới lấy ddc value ưu đãi
                    if (prepaid_net_tv.Item1 > -1 && PE.ContainerID >0)
                    {
                        // ưu tiên lấy số tháng tặng của net nếu hợp đồng là combo
                        voucherinfo.Dismonth = GetDisMounth(connection, PE.EventCode);
                        MBSv4CommonService.WriteToLog(voucherinfo.Dismonth, " GetVoucherGCinfo voucherinfo.Dismonth NET ", logId);
                    }
                    else if (prepaid_net_tv.Item2 > -1 && PE.IPTVPromotionID >0)
                    {
                        voucherinfo.Dismonth = GetDisMounthIPTV(PE.IPTVPromotionID);
                        MBSv4CommonService.WriteToLog(voucherinfo.Dismonth, " GetVoucherGCinfo voucherinfo.Dismonth IPTV ", logId);
                    }

                    if (PE.PromotionNetID >0)
                    {
                        int valueVoucher = GetValueVoucher(connection, voucher, "PromotionNetID") * prepaid_net_tv.Item1;
                        int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                        voucherinfo.Discount = valueVoucher - valueVat;
                        voucherinfo.DiscountVAT = valueVoucher;
                    }
                    if (PE.MoneyPromotionTVID > 0)
                    {
                        int valueVoucher = GetValueVoucher(connection, voucher, "MoneyPromotionTVID");
                        int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                        voucherinfo.Discount = valueVoucher - valueVat;
                        voucherinfo.DiscountVAT = valueVoucher;
                    }
                    if (isComboService)
                    {
                        if (lstPro.Count > 0)
                        {
                            //if (PE.MoneyPromotionTVID > 0)
                            //{
                            //    int valueVoucher = GetValueVoucher(connection, voucher, "MoneyPromotionTVID");
                            //    int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                            //    voucherinfo.Discount = valueVoucher - valueVat;
                            //    voucherinfo.DiscountVAT = valueVoucher;
                            //}
                            if (PE.IPTVInterConnPromotionID > 0)
                            {
                                int valueVoucher = GetValueVoucher(connection, voucher, "IPTVInterConnPromotionID");
                                int valueVat = valueVoucher - Convert.ToInt32(Math.Round(valueVoucher / 1.1, 0));
                                voucherinfo.Discount = valueVoucher - valueVat;
                                voucherinfo.DiscountVAT = valueVoucher;
                            }
                        }
                    }
                    int PrepaidID = MBSv4GetListVoucher.GetPrepaidID(prepaid_net_tv.Item1, prepaid_net_tv.Item2);
                    int quota = 0;
                    if (channel.Equals(0)) // MBS v4
                    {
                        int quotaAvailable = MBSv4GetListVoucher.GetQuotaV2(PE.ID, saleid, localtype, PrepaidID);
                        int quotaKeep = MBSv4GetListVoucher.GetQuotaKeep(voucher, saleid);
                        quota = quotaAvailable - quotaKeep;
                    }
                    if (channel.Equals(1)) // DKOL
                    {
                        int quotaAvailable = RegisterWebV5.GetQuotaUnuse(PE.ID, locationId, saleid, userBranch, localtype, PrepaidID);
                        quota = quotaAvailable;
                    } 

                    voucherinfo.Value = PE.Name;
                    if (PE.NetInterConnPromotionID > 0 || PE.IPTVInterConnPromotionID > 0)
                    {
                        var lstApplyVoucher = oldGetApplyPHM(voucher, prepaid_net_tv.Item1);

                        var lstServicePro = (from itemSubServiceTypes in lstPro
                            from itemSubServices in itemSubServiceTypes.SubServiceTypes
                            from item in lstApplyVoucher
                            let dataApply =
                                itemSubServices.SubServices.FirstOrDefault(x => x.ServiceCode == item.ServiceCode)
                            where dataApply != null
                            select new OldApply
                            {
                                ServiceCode = dataApply.ServiceCode,
                                SubServiceID = dataApply.SubServiceID,
                                ServiceID = itemSubServiceTypes.ServiceID,
                                SubServiceTypeID = itemSubServices.SubServiceTypeId
                            }).ToList();
                        //voucherinfo.Apply = getApplyPHM(voucher, prepaid_net_tv.Item1);
                        
                        voucherinfo.Apply = lstServicePro.GroupBy(u => new { u.ServiceID, u.SubServiceID, u.SubServiceTypeID, u.ServiceCode })
                                                        .Select(m => new OldApply()
                                                        {
                                                            ServiceCode = m.Key.ServiceCode,
                                                            ServiceID = m.Key.ServiceID,
                                                            SubServiceID = m.Key.SubServiceID,
                                                            SubServiceTypeID = m.Key.SubServiceTypeID
                                                        }
                                                        ).ToList();

                        if (PE.NetInterConnPromotionID > 0)
                        {
                            var lstAppPHMnet = (from service in lstSer
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                where subService.SubServiceID == 999999999
                                select new OldApply
                                {
                                    ServiceID = service.ServiceID, SubServiceTypeID = subServiceType.SubServiceTypeID,
                                    SubServiceID = subService.SubServiceID, ServiceCode = subService.ServiceCode
                                }).ToList();
                            
                            voucherinfo.Apply.AddRange(lstAppPHMnet);
                        }                        
                        MBSv4CommonService.WriteToLog(lstServicePro, " GetVoucherGCinfo apply PHM ", logId);
                    } else
                    {
                        voucherinfo.Apply = oldGetApply(voucher, prepaid_net_tv.Item1);
                    }                    
                    voucherinfo.evoucherCode = voucher;
                    
                    voucherinfo.TypeID = 0;
                    voucherinfo.Quota = quota;
                    MBSv4CommonService.WriteToLog(voucherinfo, " GetVoucherGCinfo GetInforPromotionEvent ", logId);
                }
            }
            catch (Exception ex)
            {
                MBSv4CommonService.WriteToLog(ex.Message, " GetVoucherGCinfo Error ", logId);
                return null;
            }
            return voucherinfo;
        }

        #region fix performace getInfor
        public static List<Apply> GetApplyForGetInfor(SqlConnection connection, string voucher, int prepaidNet, List<ServicePlatform> services,
            decimal discount, decimal discountVat, float discountMonth, string value, int isDeductOrder)
        {
            var applies = connection.Query<Apply>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "GetApplyVoucher",
                prepaidnet = prepaidNet,
                voucherCode = voucher
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            var filters = (from service in services
                           from subServiceType in service.SubServiceTypes
                           from subService in subServiceType.SubServices
                           select new
                           {
                               service.ServiceID,
                               subService.SubServiceID,
                               subServiceType.SubServiceTypeID,
                               subService.ServiceCode
                           }).ToList();

            return (from filter in filters
                    from apply in applies.Where(x =>
                        x.ServiceID == filter.ServiceID &&
                        x.SubServiceID == filter.SubServiceID &&
                        x.SubServiceTypeID == filter.SubServiceTypeID &&
                        x.ServiceCode == filter.ServiceCode)
                    select apply)
                .Select(x =>
                {
                    x.Value = value;
                    x.Discount = discount;
                    x.DiscountVAT = discountVat;
                    x.Dismonth = discountMonth;
                    x.IsDeductOrder = isDeductOrder;
                    return x;
                }).ToList();
        }
        public static List<Apply> getApplyPHM(SqlConnection connection, string voucher, int PrepaidNet)
        {
            return connection.Query<Apply>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "GetApplyServiceCodeVoucher",
                prepaidnet = PrepaidNet,
                voucherCode = voucher
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
        }
        public static string JoinStringToWriteLog(string Name, object Value)
        {
            return string.Format("{0} : {1} : {2}", DateTime.Now.ToString(), Name, JsonConvert.SerializeObject(Value));
        }
        private static int GetDurationPromotionNet(SqlConnection connection, string voucherCode, string type)
        {
            return connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "GetDurationVoucher",
                voucherCode = voucherCode
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        #endregion

    }
}