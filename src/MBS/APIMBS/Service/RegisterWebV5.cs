using APIMBS.Models.MobileSaleV4;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Text;
using System.Xml.Linq;
using APIMBS.Constant;
using Voucher.APIHelper;
using Dapper;

namespace APIMBS.Service
{
    public class RegisterWebV5
    {
        public const string OS6_FPTVoucher_SPF_RegisterWeb = "PowerInside.dbo.OS6_FPTVoucher_SPF_RegisterWeb";
        public const string OS6_FPTVoucher_OTTReferralProgram = "PowerInside.dbo.OS6_FPTVoucher_OTTReferralProgram";
        public const string OS6_FPTVoucher_FriendSellEvent = "PowerInside.dbo.OS6_FPTVoucher_FriendSellEvent";
        public static List<Evoucher> GetListEvc(SalePlatformGetListVoucher input, Tuple<int, int> prepaid_net_tv, int PrepaidID, int localtype, Guid logId, StringBuilder sb, List<EvoucherInput> lstEVCinput = null)
        {
            List<Evoucher> lstEvc = new List<Evoucher>();
            try
            {
                List<Evoucherid> lstdata = new List<Evoucherid>();
                List<EvoucherServiceCode> lstVoucherServiceCode = new List<EvoucherServiceCode>();
                foreach (var service in input.Services)
                {
                    CustomerTypeModel ctm = input.CustomerTypes.Where(x => x.ServiceID == service.ServiceID).FirstOrDefault();
                    foreach (var SubServiceType in service.SubServiceTypes)
                    {
                        List<EvoucherServiceCode> lst = new List<EvoucherServiceCode>();
                        using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                        {
                            lst = connection.Query<EvoucherServiceCode>(OS6_FPTVoucher_SPF_RegisterWeb, new
                            {
                                ActionName = "GetPromotionEvent",
                                prepaidnet = prepaid_net_tv.Item1,
                                prepaidId = PrepaidID,
                                localTypeId = localtype,
                                paymentTypeL2 = input.paymentTypeL2,
                                xml = MBSv4GetListVoucher.CreateXMLSubService(SubServiceType.SubServices, service.ServiceID,
                                                            SubServiceType.SubServiceTypeID, ctm.CustomerType, PrepaidID, logId)
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RegisterWebV5 GetListEvoucher lst", lst));

                        if (lst.Count > 0)
                            lstVoucherServiceCode.AddRange(lst);
                    }

                    var lstVoucherNonPHM = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet == 0 && x.ServiceCodePHMtv == 0).ToList();
                    if (lstVoucherNonPHM.Count > 0)
                    {
                        lstdata.AddRange(lstVoucherNonPHM);
                    }

                    int countPHMevc = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet > 0 || x.ServiceCodePHMtv > 0).Count();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RegisterWebV5 GetListEvoucher countPHMevc", countPHMevc));

                    if (countPHMevc > 0)
                    {
                        if (input.Products.Count > 0)
                        {
                            var prodbyServiceID = input.Products.Where(s => s.ServiceID == service.ServiceID).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RegisterWebV5 prodbyServiceID", prodbyServiceID));

                            if (prodbyServiceID.Count > 0)
                            {
                                foreach (var pros in prodbyServiceID)
                                {
                                    foreach (var SubServiceTypes in pros.SubServiceTypes)
                                    {
                                        foreach (var SubService in SubServiceTypes.SubServices)
                                        {
                                            var EvcPHM = lstVoucherServiceCode.Where(s => (s.ServiceCodePHMnet == SubService.ServiceCode && MBSv4GetListVoucher.GetServiceCode(pros.ServiceID).ToUpper().Equals(MBSv4GetListVoucher.INT))
                                                                                            || (s.ServiceCodePHMtv == SubService.ServiceCode && MBSv4GetListVoucher.GetServiceCode(pros.ServiceID).ToUpper().Equals(MBSv4GetListVoucher.PLAYN))).ToList();
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RegisterWebV5 GetListEvoucher EvcPHM", EvcPHM));

                                            if (EvcPHM.Count > 0) lstdata.AddRange(EvcPHM);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RegisterWebV5 GetListEvoucher lstdata theo service", lstdata));
                }

                if (lstdata.Count > 0 && lstEVCinput !=null && lstEVCinput.Count > 0)
                {
                    // tham số lstEVCinput chỉ cần để check quota khi call getinffor
                    // nếu api get infor thì sẽ thực hiện fillter lại voucher để tối ưu performance
                    var filterVoucher = new List<Evoucherid>();
                    foreach (var data in lstdata)
                    {
                        foreach (var evc in lstEVCinput)
                        {
                            if (data.VoucherCode == evc.evoucherCode && evc.evoucherType == 1)
                            {
                                filterVoucher.Add(data);
                            }
                        }
                    }
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher fillterVoucher", filterVoucher));

                    if (filterVoucher.Count > 0)
                    {
                        lstdata = filterVoucher;
                    }else
                    {
                        return new List<Evoucher>();
                    }
                }

                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RegisterWebV5 GetListEvoucher lstdata all", lstdata));

                var datagroup = lstdata.GroupBy(x => new { x.ID, x.VoucherCode, x.Description,x.Note,x.Todate }).Select(a => new Evoucherid { ID = a.Key.ID, Description = a.Key.Description, VoucherCode = a.Key.VoucherCode, Note=a.Key.Note,Todate=a.Key.Todate });
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RegisterWebV5 GetListEvoucher datagroup", datagroup));

                datagroup.AsParallel().ForAll(evc =>
                {
                    int Quota = GetQuotaUnuse(evc.ID, input.locationID, input.saleID, input.Userbranch, localtype, PrepaidID);
                    int quotarKeep = MBSv4GetListVoucher.GetQuotaKeepDKOL(input.Userbranch, input.locationID, input.saleID, evc.VoucherCode);

                    if ((Quota - quotarKeep) > 0)
                    {
                        lstEvc.Add(new Evoucher { Description = evc.Description, VoucherCode = evc.VoucherCode, Note = evc.Note, Todate = evc.Todate });
                    }
                });
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RegisterWebV5 GetListEvoucher service error", ex.Message));
                return null;
            }

            return lstEvc;
        }
        public static int GetQuotaUnuse(int promotionID,int locationId,int saleid, UserBranchPlatformModel userBranch, int localTypeID, int paidTimeTypeID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "GetQuotaUnused",
                    PromotionID = promotionID,
                    SourceID = userBranch.sourceId,
                    SubCompanyID=userBranch.subcompanyId,
                    LocationID=locationId,
                    BranchCode=userBranch.branchCode,
                    DepartmentID=userBranch.departmentId,
                    SaleID =saleid,
                    prepaidId = paidTimeTypeID,
                    LocalTypeID = localTypeID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }

        public static bool CheckVoucherRF(string voucherCode)
        {
            bool trueVoucher = false;
            int n = 0;
            if (voucherCode.StartsWith("FPL"))
            {
                return OttReferalProgramService.checkOTTCode(voucherCode);
            }
            bool isMYFPTCode = MyFPTService.checkMyFPTCode(voucherCode, "");
            if (isMYFPTCode) return isMYFPTCode;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int StatusCodeRF = connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetStatusRFCode",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                trueVoucher = (StatusCodeRF == 1);
            }
            return trueVoucher;
        }

        public static bool CheckVoucherGC(string voucherCode)
        {
            bool trueVoucher = false;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int StatusCodeVC = connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetStatusVoucherCode",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                trueVoucher = (StatusCodeVC == 1);
            }
            return trueVoucher;
        }
        public static bool CheckContractSupport(string contract, string code)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int flag = connection.Query<int>(OS6_FPTVoucher_FriendSellEvent, new
                {
                    actionName = "CheckContractSell",
                    @contractSell = contract,
                    @code = code
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return (flag == 1);
            }
        }
        
        public static OldSalePlatformVoucherValueInfor OldGetInfoVoucherOttRF(string VoucherRP, int? LocalType, int? PaidTimeTypePE)
        {
            OldSalePlatformVoucherValueInfor res = new OldSalePlatformVoucherValueInfor();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.Query<OldSalePlatformVoucherValueInfor>(OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "GetPromotionOTTCode",
                    localTypeId = LocalType,
                    prepaidId = PaidTimeTypePE
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return res;
        }

        
        public static VoucherReferralInfo GetInfoVoucherOttRF(string VoucherRP, int? LocalType, int? PaidTimeTypePE)
        {
            var res = new VoucherReferralInfo();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.Query<VoucherReferralInfo>(OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "GetPromotionOTTCode",
                    localTypeId = LocalType,
                    prepaidId = PaidTimeTypePE
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return res;
        }
        
        public static List<OldSalePlatformVoucherValueInfor> OldGetPromotionRFCAM(List<DeviceModel> devices, string voucherRF, Guid logId)
        {
            List<OldSalePlatformVoucherValueInfor> lstdataa = new List<OldSalePlatformVoucherValueInfor>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int checkVoucher = connection.Query<int>(OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "CheckStatusRF",
                    VoucherCode = voucherRF
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                MBSv4CommonService.WriteToLog(checkVoucher, "RegisterWebV5 GetPromotionRFCAM checkVoucher: ", logId);

                if (!checkVoucher.Equals(1))
                {
                    return null;
                }
                foreach (var item in devices)
                {
                    OldSalePlatformVoucherValueInfor res = new OldSalePlatformVoucherValueInfor();
                    res = connection.Query<OldSalePlatformVoucherValueInfor>(OS6_FPTVoucher_SPF_RegisterWeb, new
                    {
                        ActionName = "GetPromotionCam",
                        quantity = item.qty,
                        serviceCode = item.serviceCode,
                        EfId = item.deviceId,
                        serviceId = item.serviceId,
                        subServiceTypeId = item.subServiceTypeID
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    MBSv4CommonService.WriteToLog(res, "RegisterWebV5 GetPromotionRFCAM res: ", logId);
                    if (res != null)
                    {
                        res.evoucherCode = voucherRF;
                        res.Apply = new List<OldApply>();
                        OldApply app = new OldApply {ServiceCode=item.serviceCode, ServiceID=item.serviceId, SubServiceID=item.deviceId,SubServiceTypeID=item.subServiceTypeID };
                        res.Apply.Add(app);

                        var userInfo = ReferalProgramHiFPT.GetInforUser(voucherRF);
                        MBSv4CommonService.WriteToLog(userInfo, "RegisterWebV5 UserInfo ", logId);
                        if (userInfo != null)
                        {
                            res.RefInfo = new RefInfo()
                            {
                                fullName = userInfo.FullNameRef,
                                phone = userInfo.PhoneRef,
                                contract = userInfo.ContractRef
                            };
                        }
                        
                        lstdataa.Add(res);
                    }
                }
                MBSv4CommonService.WriteToLog(lstdataa, "RegisterWebV5 GetPromotionRFCAM lstdataa: ", logId);
            }
            return lstdataa;
        }

        public static SalePlatformVoucherValueInfor GetReferralCameraPromotion(StringBuilder sb, List<ProductPlatform> products, List<EvoucherProduct> vouchers, string voucherCode, Guid logId)
        {
            var result = new SalePlatformVoucherValueInfor();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var checkVoucher = connection.Query<int>(OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "CheckStatusRF",
                    VoucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("checkVoucher", checkVoucher));
                
                if (!checkVoucher.Equals(1))
                {
                    return null;
                }
                
                result.evoucherCode = voucherCode;
                result.Apply = GetApplyReferral(vouchers, products);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input products", products));
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result.Apply", result.Apply));

                var userInfo = ReferalProgramHiFPT.GetInforUser(voucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UserInfo", userInfo));

                if (userInfo != null)
                {
                    result.RefInfo = new RefInfo()
                    {
                        fullName = userInfo.FullNameRef,
                        phone = userInfo.PhoneRef,
                        contract = userInfo.ContractRef
                    };
                }

                result.Apply = result.Apply.Select(x =>
                {
                    x.Value = voucherCode;
                    return x;
                }).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));
            }

            return result;
        }

        public static List<SalePlatformVoucherValueInfor> GetPromotionRFCAM(List<DeviceModel> devices, string voucherRF, Guid logId)
        {
            List<SalePlatformVoucherValueInfor> lstdataa = new List<SalePlatformVoucherValueInfor>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int checkVoucher = connection.Query<int>(OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "CheckStatusRF",
                    VoucherCode = voucherRF
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                MBSv4CommonService.WriteToLog(checkVoucher, "RegisterWebV5 GetPromotionRFCAM checkVoucher: ", logId);

                if (!checkVoucher.Equals(1))
                {
                    return null;
                }
                foreach (var item in devices)
                {
                    var res = new SalePlatformVoucherValueInfor();
                    var value = connection.Query<OldSalePlatformVoucherValueInfor>(ConstantAPI.OS6_FPTVoucher_SalePlatform_GetPromotionReferral, new
                    {
                        quantity = item.qty,
                        item.serviceCode,
                        item.deviceId,
                        item.serviceId,
                        subServiceTypeId = item.subServiceTypeID
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    MBSv4CommonService.WriteToLog(value, "RegisterWebV5 GetPromotionRFCAM value: ", logId);

                    res.evoucherCode = voucherRF;
                    res.Apply = new List<Apply>();
                    if (value != null)
                    {
                        var app = new Apply
                        {
                            ServiceCode = item.serviceCode, 
                            ServiceID = item.serviceId, 
                            SubServiceID = item.deviceId,
                            SubServiceTypeID = item.subServiceTypeID,
                            Discount = value.Discount,
                            DiscountVAT = value.DiscountVAT,
                            IsDeductOrder = 0,
                            Dismonth = 0
                        };
                        
                        res.Apply.Add(app);
                    }

                    var userInfo = ReferalProgramHiFPT.GetInforUser(voucherRF);
                    MBSv4CommonService.WriteToLog(userInfo, "RegisterWebV5 UserInfo ", logId);
                    if (userInfo != null)
                    {
                        res.RefInfo = new RefInfo()
                        {
                            fullName = userInfo.FullNameRef,
                            phone = userInfo.PhoneRef,
                            contract = userInfo.ContractRef
                        };
                    }

                    if (res.Apply.Count > 0)
                    {
                        lstdataa.Add(res);
                    }
                }
                MBSv4CommonService.WriteToLog(lstdataa, "RegisterWebV5 GetPromotionRFCAM lstdataa: ", logId);
            }
            return lstdataa;
        }

        public static List<EvoucherService> getListEvcServiceProduct(Guid logId, List<ProductPlatform> products,
            int channel, List<EvoucherInput> lstEVC = null, List<EvoucherInput> lstRF = null,
            List<DeviceModel> lstDevice = null)
        {
            List<EvoucherService> lstVoucher = new List<EvoucherService>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();

                if (lstEVC.Count > 0)
                {
                    try
                    {
                        List<EvoucherProduct> voucherProducts = GetDeviceVoucherList(products,logId).ToList();

                        lstVoucher.AddRange(from evc in lstEVC
                            let voucher = voucherProducts.FirstOrDefault(x => x.VoucherCode == evc.evoucherCode)
                            where voucher != null
                            select new EvoucherService()
                            {
                                evoucherCode = evc.evoucherCode,
                                evoucherType = evc.evoucherType,
                                channel = evc.channel,
                                ServiceID = voucher.ServiceId,
                                SubServiceType = voucher.SubServiceTypeId
                            });
                    }
                    catch (Exception ex)
                    {
                        MBSv4CommonService.WriteToLog(ex.Message, "MBSv4 KeepVoucher getListEvcService error: ", logId);
                    }
                }
                if (lstRF.Count > 0)
                {

                    List<SalePlatformVoucherValueInfor> infos = GetPromotionRFCAM(lstDevice, lstRF[0].evoucherCode, logId);
                    if(infos.Count >0)
                    {
                        foreach (var info in infos)
                        {
                            foreach (var apply in info.Apply)
                            {
                                EvoucherService es = new EvoucherService { evoucherCode = lstRF[0].evoucherCode, evoucherType = lstRF[0].evoucherType, ServiceID=apply.ServiceID, SubServiceType=apply.SubServiceTypeID};
                                if (lstVoucher.Any(v => v.evoucherCode != es.evoucherCode &&
                                                        v.evoucherType != es.evoucherType &&
                                                        v.ServiceID != es.ServiceID &&
                                                        v.SubServiceType != es.SubServiceType) || lstVoucher.Count == 0)
                                    lstVoucher.Add(es);
                            }
                        }
                    }                    
                }
            }
            return lstVoucher;
        }

        public static List<EvoucherProduct> GetDeviceVoucherList(List<ProductPlatform> products,Guid logId)
        {
            List<EvoucherProduct> result = new List<EvoucherProduct>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                result = connection.Query<EvoucherProduct>(ConstantAPI.OS6_FPTVoucher_SalePlatform_Device, new
                {
                    ActionName = "GetVoucherList",
                    Xml = ConvertServiceDeviceToXml(products, logId)
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                return result;
            }
        }

        private static XElement ConvertServiceDeviceToXml(IEnumerable<ProductPlatform> products,Guid logId)
        {
            var xml = new XElement("N",
                from product in products
                from subServiceType in product.SubServiceTypes
                from subService in subServiceType.SubServices
                select new XElement("I",
                    new XElement("ServiceId", product.ServiceID),
                    new XElement("SubServiceId", subService.SubServiceID),
                    new XElement("SubServiceTypeId", subServiceType.SubServiceTypeId),
                    new XElement("ServiceCode", subService.ServiceCode),
                    new XElement("Qty", subService.Qty)));

            MBSv4CommonService.WriteToLog(xml, "MBSv4 ConvertServiceDeviceToXml", logId);
            return xml;
        }
        
        public static List<Apply> GetApplyReferral(List<EvoucherProduct> vouchers, List<ProductPlatform> products)
        {
            var result = new List<Apply>();
            
            //Group sub service of product by quantity
            foreach (var subServiceType in products.SelectMany(product => product.SubServiceTypes))
            {
                subServiceType.SubServices = subServiceType.SubServices
                    .GroupBy(x => new { x.ServiceCode, x.SubServiceID, x.StatusID, x.RevokeID, x.DeployTypeID })
                    .Select(g => new SubServicesPro()
                    {
                        SubServiceID = g.Key.SubServiceID,
                        ServiceCode = g.Key.ServiceCode,
                        StatusID = g.Key.StatusID,
                        RevokeID = g.Key.RevokeID,
                        DeployTypeID = g.Key.DeployTypeID,
                        Qty = g.Sum(s => s.Qty)
                    }).ToList();
            }

            foreach (var voucher in vouchers)
            {
                var apply = (from service in products
                    from subServiceType in service.SubServiceTypes
                    from subService in subServiceType.SubServices
                    where service.ServiceID == voucher.ServiceId &&
                          subServiceType.SubServiceTypeId == voucher.SubServiceTypeId &&
                          subService.SubServiceID == voucher.SubServiceId &&
                          subService.ServiceCode == voucher.ServiceCode &&
                          subService.Qty >= voucher.QuantityFrom &&
                          subService.Qty <= voucher.QuantityTo
                    select new Apply()
                    {
                        ServiceID = service.ServiceID,
                        SubServiceTypeID = subServiceType.SubServiceTypeId,
                        SubServiceID = subService.SubServiceID,
                        ServiceCode = subService.ServiceCode,
                        Value = voucher.Value,
                        Discount = voucher.Discount,
                        DiscountVAT = voucher.DiscountVAT,
                        Dismonth = 0,
                        IsDeductOrder = 1,
                        StatusID = subService.StatusID,
                        RevokeID = subService.RevokeID,
                        DeployTypeID = subService.DeployTypeID
                    }).ToList();
                
                result.AddRange(apply);
            }

            return result;
        }

        public static SalePlatformVoucherValueInfor GetVoucherProductInfo(List<EvoucherProduct> vouchers, List<ProductPlatform> products, int quota)
        {
            var result = new SalePlatformVoucherValueInfor
            {
                Apply = new List<Apply>(),
                RefInfo = new RefInfo()
            };

            result.Quota = quota;
            
            //Group sub service of product by quantity
            foreach (var subServiceType in products.SelectMany(product => product.SubServiceTypes))
            {
                subServiceType.SubServices = subServiceType.SubServices
                    .GroupBy(x => new { x.ServiceCode, x.SubServiceID })
                    .Select(g => new SubServicesPro()
                    {
                        SubServiceID = g.Key.SubServiceID,
                        ServiceCode = g.Key.ServiceCode,
                        Qty = g.Sum(s => s.Qty)
                    }).ToList();
            }
            
            result.evoucherCode = vouchers[0].VoucherCode;
            result.Apply = (from service in products
                from subServiceType in service.SubServiceTypes
                from subService in subServiceType.SubServices
                from voucher in vouchers
                where service.ServiceID == voucher.ServiceId &&
                      subServiceType.SubServiceTypeId == voucher.SubServiceTypeId &&
                      subService.SubServiceID == voucher.SubServiceId &&
                      subService.ServiceCode == voucher.ServiceCode &&
                      subService.Qty >= voucher.QuantityFrom &&
                      subService.Qty <= voucher.QuantityTo
                select new Apply()
                {
                    ServiceID = service.ServiceID,
                    SubServiceTypeID = subServiceType.SubServiceTypeId,
                    SubServiceID = subService.SubServiceID,
                    ServiceCode = subService.ServiceCode,
                    Value = voucher.Value,
                    Discount =  subService.Qty > 1 ? decimal.Round(voucher.DiscountVAT * subService.Qty / (decimal)1.1) : voucher.Discount,
                    DiscountVAT = voucher.DiscountVAT * subService .Qty,
                    Dismonth = 0,
                    IsDeductOrder = 1
                }).ToList();

            return result;
        }
    }
}