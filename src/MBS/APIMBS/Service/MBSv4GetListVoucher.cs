using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Dapper;
using System.Data;
using Voucher.APIHelper;
using System.Xml.Linq;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models;
using APIMBS.Constant;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using Voucher.APIHelper.Util;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Configuration;
using System.Text;
using System.Threading.Tasks;

namespace APIMBS.Service
{
    public class MBSv4GetListVoucher
    {
        public const string OS6_FPTVoucher_SalePlatform_MBSv4 = "PowerInside.dbo.OS6_FPTVoucher_SalePlatform_MBSv4";
        public const string OS6_FPTVoucher_SalesManInfo = "PowerInside.dbo.OS6_FPTVoucher_SalesManInfo";
        public const string OS6_FPTVoucher_RemoveQuotakeepForSaleman = "PowerInside.dbo.OS6_FPTVoucher_RemoveQuotakeepForSaleman";
        public const string OS6_FPTVoucher_VoucherBussiness = "PowerInside.dbo.OS6_FPTVoucher_VoucherBussiness";
        public const string INT = "INT";
        public const string PLAYN = "PLAYN";
        public const string OTT = "OTT";
        public const string CMR = "CMR";

        public static List<Evoucher> GetListEvc(SalePlatformGetListVoucher input, Tuple<int, int> prepaid_net_tv, int PrepaidID, int localtype, Guid logId, StringBuilder sb, List<EvoucherInput> lstEVCinput = null)
        {
            List<Evoucher> lstEvc = new List<Evoucher>();
            try
            {
                List<Evoucherid> lstdata = new List<Evoucherid>();
                List<EvoucherServiceCode> lstVoucherServiceCode = new List<EvoucherServiceCode>();
                foreach (var service in input.Services)
                {
                    CustomerTypeModel ctm = input.CustomerTypes.Where(x => x.ServiceID == service.ServiceID).FirstOrDefault();
                    foreach (var SubServiceType in service.SubServiceTypes)
                    {
                        List<EvoucherServiceCode> lst = new List<EvoucherServiceCode>();
                        using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                        {
                            if (input.channel.Equals(0))
                            {
                                var xmlV2 = MBSv4GetListVoucher.CreateXMLSubServiceV2(SubServiceType.SubServices, service.ServiceID,
                                                                SubServiceType.SubServiceTypeID, ctm.CustomerType, PrepaidID, input.paymentTypeL2, logId);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlV2", xmlV2));

                                lst = connection.Query<EvoucherServiceCode>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                                {
                                    actionName = "GetPromotionEvent",
                                    prepaidnet = prepaid_net_tv.Item1,
                                    PaidTimeType = PrepaidID,
                                    LocalType = localtype,
                                    xml = xmlV2
                                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher normal", lst));

                                //check and get voucher KHTN
                                //Nếu có ServiceID = 7 (của TV) thì mới sử dụng sđt để lấy voucher FPT play
                                var checkService = input.CustomerTypes.Where(x => x.ServiceID.Equals(7)).Count();
                                if (checkService > 0)
                                {
                                    List<EvoucherServiceCode> lstFPL = new List<EvoucherServiceCode>();
                                    lstFPL = connection.Query<EvoucherServiceCode>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                                    {
                                        actionName = "GetPromotionEventFPL",
                                        phone = input.fptplayCustomerPhone,
                                        prepaidnet = prepaid_net_tv.Item1,
                                        PaidTimeType = PrepaidID,
                                        LocalType = localtype,
                                        xml = xmlV2
                                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher FPL", lstFPL));

                                    if (lstFPL != null && lstFPL.Count > 0)
                                    {
                                        lst.AddRange(lstFPL);
                                    }
                                }
                            }
                        }

                        //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher lst.Count", lst.Count));
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher lst", lst));

                        if (lst.Count > 0)
                            lstVoucherServiceCode.AddRange(lst);
                    }

                    var lstVoucherNonPHM = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet == 0 && x.ServiceCodePHMtv == 0).ToList();
                    if (lstVoucherNonPHM.Count > 0)
                    {
                        lstdata.AddRange(lstVoucherNonPHM);
                    }

                    int countPHMevc = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet > 0 || x.ServiceCodePHMtv > 0).Count();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher countPHMevc", countPHMevc));

                    if (countPHMevc > 0)
                    {
                        if (input.Products.Count > 0)
                        {
                            var prodbyServiceID = input.Products.Where(s => s.ServiceID == service.ServiceID).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 prodbyServiceID prodbyServiceID.Count", prodbyServiceID.Count));
                            
                            if (prodbyServiceID.Count > 0)
                            {
                                foreach (var pros in prodbyServiceID)
                                {
                                    foreach (var SubServiceTypes in pros.SubServiceTypes)
                                    {
                                        foreach (var SubService in SubServiceTypes.SubServices)
                                        {
                                            var EvcPHM = lstVoucherServiceCode.Where(s => (s.ServiceCodePHMnet == SubService.ServiceCode && GetServiceCode(pros.ServiceID).ToUpper().Equals(INT))
                                                                                            || (s.ServiceCodePHMtv == SubService.ServiceCode && GetServiceCode(pros.ServiceID).ToUpper().Equals(PLAYN))).ToList();
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher EvcPHM.Count", EvcPHM.Count));

                                            if (EvcPHM.Count > 0) lstdata.AddRange(EvcPHM);
                                        }
                                    }
                                }
                            }
                        }
                        //else
                        //{
                        //    lstVoucherServiceCode = lstVoucherServiceCode.Where(x => x.ServiceCodePHMnet == 0 && x.ServiceCodePHMtv == 0).ToList();
                        //    if (lstVoucherServiceCode.Count > 0) lstdata.AddRange(lstVoucherServiceCode);
                        //}

                        foreach (var voucher in lstVoucherServiceCode.Where(x => !lstdata.Contains(x)))
                        {
                            lstdata.Add(voucher);
                        }
                    }
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher lstdata theo service", lstdata));
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher lstdata all", lstdata));

                if (lstdata.Count > 0 && lstEVCinput != null &&  lstEVCinput.Count > 0)
                {
                    // tham số lstEVCinput chỉ cần để check quota khi call getinffor
                    // nếu api get infor thì sẽ thực hiện fillter lại voucher để tối ưu performance
                    var filterVoucher = new List<Evoucherid>();
                    foreach (var data in lstdata)
                    {
                        foreach (var evc in lstEVCinput)
                        {
                            if(data.VoucherCode==evc.evoucherCode && evc.evoucherType == 1)
                            {
                                filterVoucher.Add(data);
                            }
                        }
                    }
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher fillterVoucher", filterVoucher));

                    if (filterVoucher.Count >0)
                    {
                        lstdata = filterVoucher;
                    } else
                    {
                        return new List<Evoucher>();
                    }
                }

                var datagroup = lstdata.GroupBy(x => new { x.ID, x.VoucherCode, x.Description }).Select(a => new Evoucherid { ID = a.Key.ID, Description = a.Key.Description, VoucherCode = a.Key.VoucherCode });
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher datagroup", datagroup));

                datagroup.AsParallel().ForAll(evc =>
                {
                    if (input.channel.Equals(0))
                    {
                        int quotaAvailable = QuotaAvailable(evc.ID, input.saleID, localtype, PrepaidID, evc.VoucherCode);

                        if (quotaAvailable > 0)
                        {
                            lstEvc.Add(new Evoucher { Description = evc.Description, VoucherCode = evc.VoucherCode, Note = evc.Note, Todate = evc.Todate });
                        }
                    }
                });
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher lstEvc", lstEvc));
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher service error", ex.Message));
                return null;
            }

            return lstEvc;
        }

        private static int QuotaAvailable(int idvoucher, int saleid, int localtype, int prepaidid, string vouchercode)
        {
            int Quota = MBSv4GetListVoucher.GetQuotaV2(idvoucher, saleid, localtype, prepaidid);
            int quotarKeep = MBSv4GetListVoucher.GetQuotaKeep(vouchercode, saleid);
            return Quota - quotarKeep;
        }
        #region
        public static string GetServiceCode(int service)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<string>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetCodeService",
                    @serviceid = service
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(); 
            }
        }

        public static List<ServicePlatform> FillerServiceTVNET(List<ServicePlatform> list)
        {
            List<ServicePlatform> lst = new List<ServicePlatform>();
            foreach (var item in list)
            {
                ServicePlatform sp = new ServicePlatform();
                string Service = GetServiceCode(item.ServiceID);
                if (Service.ToUpper() == "INT" || Service.ToUpper() == "PLAYN")
                {
                    sp = item;
                    lst.Add(sp);
                }
            }
            return lst;
        }
        public static bool CheckTrueRuleVoucher(List<EvoucherInput> listVoucher,int channel)
        {
            
            if (channel.Equals(0))
            {
                // theo rule mới, 1 voucher và 1 MGT
                if(listVoucher.Where(x=>x.evoucherType.Equals(1)).ToList().Count > 1)
                {
                    return false;
                }
                if (listVoucher.Where(x => x.evoucherType.Equals(2)).ToList().Count > 1)
                {
                    return false;
                }
                return (listVoucher.Count <=2);
            }
            bool res = true;
            if (channel.Equals(1))
            {
                if (listVoucher.Count > 3 )//|| listVoucher.Where(x => x.evoucherType.Equals(1)).ToList().Count > 2 || listVoucher.Where(x => x.evoucherType.Equals(2)).ToList().Count > 1)
                {
                    res = false;
                }
                int n;
                int iCode = listVoucher.Where(c => c.evoucherCode.ToUpper().IndexOf("FTEL") > -1 || c.evoucherCode.ToUpper().StartsWith("FPL") || c.evoucherCode.ToUpper().StartsWith("MKT") || int.TryParse(c.evoucherCode, out n) == true).Count();  
                if (iCode >= 2)
                {
                    res = false;
                }
            }
            return res;
        }
        public static Tuple<int, int> GetPrepaidtimeNetTV(List<ServicePlatform> lstService)
        {
            int prepaidNet = -1;
            int prepaidTv = -1;
            foreach (var service in lstService)
            {                
                string CodeService = GetServiceCode(service.ServiceID);
                if (service.ServiceID == 1)
                {
                    prepaidNet = Convert.ToInt32(service.SubServiceTypes[0].SubServices[0].PrePaid);
                }
                if (service.ServiceID == 7)
                {
                    prepaidTv = Convert.ToInt32(service.SubServiceTypes[0].SubServices[0].PrePaid);
                }
            }
            return new Tuple<int, int>(prepaidNet, prepaidTv);
        }
        public static SalesManInfo GetInfoSalesMan(int ObjSalesman)
        {            
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<SalesManInfo>(ConstantAPI.OS6_FPTVoucher_SalesManInfo, new
                {
                    ActionName = "SelectByID",
                    ID = ObjSalesman
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static int GetPrepaidID(int prepaidnet, int prepaidtv)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetPrepaidID",
                    prepaidnet = prepaidnet,
                    prepaidtv = prepaidtv
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
   
        public static int NewGetPrepaidID(SqlConnection connection, SqlTransaction transaction, int prepaidnet, int prepaidtv)
        {
            return connection.Query<int>(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "GetPrepaidID",
                prepaidnet = prepaidnet,
                prepaidtv = prepaidtv
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static int GetLocaltype(int subServiceID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "getLocaltype",
                    subServiceID = subServiceID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static int GetQuotaV2(int promotionID, int salesmanID, int localTypeID, int paidTimeTypeID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(OS6_FPTVoucher_SalesManInfo, new
                {
                    ActionName = "GetQuotaV2",
                    ID = salesmanID,
                    PromotionID = promotionID,
                    LocalTypeID = localTypeID,
                    PaidTimeTypeID = paidTimeTypeID

                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }

        public static int GetQuotaKeepDKOL(UserBranchPlatformModel userBranch, int locationId, int saleId,  string voucherCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(ConstantAPI.OS6_FPTVoucher_SPF_RegisterWeb, new
                {
                    ActionName = "CountKeepVoucher",
                    @SourceID = userBranch.sourceId,
                    @SubCompanyID = userBranch.subcompanyId,
                    @BranchCode = userBranch.branchCode,
                    @DepartmentID = userBranch.departmentId,
                    @LocationID = locationId,
                    @SaleID = saleId,
                    @VoucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }

        public static int GetQuotaKeep(string VoucherCode, int salesmanID )
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    ActionName = "countQuotaHistoryTem",
                    saleid = salesmanID,
                    voucherCode = VoucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }
        public static XElement CreateXMLSubService(List<SubServiceModel> input, int serviceID, int SubServiceTypeID, int customerType, int PrepaidID, Guid logId)
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("SubServiceID", item.SubServiceID),
                           new XElement("PrePaid", item.PrePaid),
                           //new XElement("DeployTypeID", item.DeployTypeID), 
                           new XElement("ServiceCode", item.ServiceCode),
                           new XElement("serviceID", serviceID),
                           new XElement("SubServiceTypeID", SubServiceTypeID),
                           new XElement("customerType", customerType),
                           new XElement("PrepaidID", PrepaidID)
                       ));
            MBSv4CommonService.WriteToLog(xmlString, " CreateXMLSubService xml serviceID " + serviceID.ToString(), logId);              
            return xmlString;
        }
        public static XElement CreateXMLSubServiceV2(List<SubServiceModel> input, int serviceID, int SubServiceTypeID, int customerType, int PrepaidID, int paymentTypeL2, Guid logId)
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("SubServiceID", item.SubServiceID),
                           new XElement("PrePaid", item.PrePaid),
                           //new XElement("DeployTypeID", item.DeployTypeID), 
                           new XElement("ServiceCode", item.ServiceCode),
                           new XElement("serviceID", serviceID),
                           new XElement("SubServiceTypeID", SubServiceTypeID),
                           new XElement("customerType", customerType),
                           new XElement("PrepaidID", PrepaidID),
                           new XElement("paymentTypeL2", paymentTypeL2)
                       ));
            return xmlString;
        }

        public static OldSalePlatformVoucherValueInfor OldGetVoucherCampaignInfo(string voucher,int localtype,int channel, Tuple<int, int> prepaid_net_tv, int typeGetVoucher,Guid logId)
        {
            MBSv4CommonService.WriteToLog(voucher, " GetVoucherCampaignInfo req ", logId);
            OldSalePlatformVoucherValueInfor voucherinfo = new OldSalePlatformVoucherValueInfor();
            voucherinfo.Apply = new List<OldApply>();
            voucherinfo.RefInfo = new RefInfo();
            int PrepaidID = GetPrepaidID(prepaid_net_tv.Item1, prepaid_net_tv.Item2);
            try
            {
                int typeRF = checkReferalProgram(voucher,channel,logId);
                if (typeRF.Equals(2)) // là mã GTBb FPt play
                {
                    voucherinfo = RegisterWebV5.OldGetInfoVoucherOttRF(voucher, localtype, PrepaidID);
                    // voucherinfo.RefInfo.fullName = OttReferalProgramService.GetInforClient(voucher, logId.ToString());
                }
                else
                {
                    bool isMyFFPT = false;
                    if (typeRF.Equals(1)) // là mã GTBb FPt play
                    {
                        isMyFFPT = true;                                       
                    }
                    voucherinfo = OldGetInfoVoucherRF(voucher, localtype, PrepaidID, isMyFFPT);
                    MBSv4CommonService.WriteToLog(voucherinfo, " GetVoucherCampaignInfo voucherinfo0 ", logId);  
                    if (isMyFFPT)
                    {
                        string staffid = MyFPTService.GetStaffId(voucher);
                        if (!string.IsNullOrEmpty(staffid))
                        {
                            voucherinfo.RefInfo.fullName = MyFPTService.getStaffInfor(staffid, logId.ToString());
                        }
                    }
                    else
                    {
                        var ui = new UserInfo();
                        ui = ReferalProgramHiFPT.GetInforUser(voucher);
                        MBSv4CommonService.WriteToLog(ui, " GetVoucherCampaignInfo ui ", logId);  
                        if (ui != null)
                        {
                            voucherinfo.RefInfo.fullName = ui.FullNameRef;
                            voucherinfo.RefInfo.phone = ui.PhoneRef;
                            voucherinfo.RefInfo.contract = ui.ContractRef;
                        }             
                    }
                }
                MBSv4CommonService.WriteToLog(voucherinfo.RefInfo, " GetVoucherCampaignInfo voucherinfo.RefInfo ", logId);  
                voucherinfo.Apply = MBSv4GetPromotionInfo.oldGetApply(voucherinfo.evoucherCode, prepaid_net_tv.Item1);
                if (typeGetVoucher == 0)
                {
                    voucherinfo.evoucherCode = voucher;
                }
                if (typeGetVoucher == 1)
                {
                    voucherinfo.evoucherCode = voucherinfo.evoucherCode;
                }
                MBSv4CommonService.WriteToLog(voucherinfo, " GetVoucherCampaignInfo GetInfoVoucherRF ", logId);                
            }
            catch (Exception ex)
            {
                MBSv4CommonService.WriteToLog(ex.Message, " GetVoucherCampaignInfo Error ", logId);
                return null;
            }
            return voucherinfo;
        }


        public static SalePlatformVoucherValueInfor GetVoucherCampaignInfo(List<ServicePlatform> services, string voucher,int localtype,int channel, Tuple<int, int> prepaid_net_tv, int typeGetVoucher,Guid logId)
        {
            MBSv4CommonService.WriteToLog(voucher, " GetVoucherCampaignInfo req ", logId);
            SalePlatformVoucherValueInfor voucherinfo = new SalePlatformVoucherValueInfor();
            voucherinfo.Apply = new List<Apply>();
            voucherinfo.RefInfo = new RefInfo();
            int PrepaidID = GetPrepaidID(prepaid_net_tv.Item1, prepaid_net_tv.Item2);
            var info = new VoucherReferralInfo();
            try
            {
                int typeRF = checkReferalProgram(voucher,channel,logId);
                if (typeRF.Equals(2)) // là mã GTBb FPt play
                {
                    info = RegisterWebV5.GetInfoVoucherOttRF(voucher, localtype, PrepaidID);
                    FptPlayClientModel clientModel = OttReferalProgramService.GetInforClient(voucher, logId.ToString());
                    voucherinfo.evoucherCode = info.evoucherCode;
                    if (clientModel != null)
                    {
                        voucherinfo.RefInfo.fullName = clientModel.FullName;
                        voucherinfo.RefInfo.phone = clientModel.Phone;
                    }
                }
                else
                {
                    bool isMyFFPT = false;
                    if (typeRF.Equals(1)) // là mã GTBb FPt play
                    {
                        isMyFFPT = true;                                       
                    }
                    info = GetInfoVoucherRF(voucher, localtype, PrepaidID, isMyFFPT);

                    voucherinfo.evoucherCode = info.evoucherCode;
                    voucherinfo.TypeID = info.TypeID;

                    MBSv4CommonService.WriteToLog(voucherinfo, " GetVoucherCampaignInfo voucherinfo0 ", logId);  
                    if (isMyFFPT)
                    {
                        string staffid = MyFPTService.GetStaffId(voucher);
                        if (!string.IsNullOrEmpty(staffid))
                        {
                            voucherinfo.RefInfo.fullName = MyFPTService.getStaffInfor(staffid, logId.ToString());
                        }
                    }
                    else
                    {
                        var ui = new UserInfo();
                        ui = ReferalProgramHiFPT.GetInforUser(voucher);
                        MBSv4CommonService.WriteToLog(ui, " GetVoucherCampaignInfo ui ", logId);  
                        if (ui != null)
                        {
                            voucherinfo.RefInfo.fullName = ui.FullNameRef;
                            voucherinfo.RefInfo.phone = ui.PhoneRef;
                            voucherinfo.RefInfo.contract = ui.ContractRef;
                        }             
                    }
                }
                MBSv4CommonService.WriteToLog(voucherinfo.RefInfo, " GetVoucherCampaignInfo voucherinfo.RefInfo ", logId);
                var isDeductOrder = prepaid_net_tv.Item1 > 0 || prepaid_net_tv.Item2 > 0 ? 1 : 0;
                voucherinfo.Apply = MBSv4GetPromotionInfo.GetApply(voucherinfo.evoucherCode, prepaid_net_tv.Item1,
                    services, info.Discount, info.DiscountVAT, 0, info.Value, isDeductOrder);

                //Xoá ưu đãi cho truyền hình
                const string tv = "PlayN";
                voucherinfo.Apply.RemoveAll(x => MBSv4GetListVoucher.GetServiceCode(x.ServiceID) == tv);

                if (typeGetVoucher == 0)
                {
                    voucherinfo.evoucherCode = voucher;
                }
                if (typeGetVoucher == 1)
                {
                    voucherinfo.evoucherCode = voucherinfo.evoucherCode;
                }
                MBSv4CommonService.WriteToLog(voucherinfo, " GetVoucherCampaignInfo GetInfoVoucherRF ", logId);                
            }
            catch (Exception ex)
            {
                MBSv4CommonService.WriteToLog(ex.Message, " GetVoucherCampaignInfo Error ", logId);
                return null;
            }
            return voucherinfo;
        }

        public static int checkReferalProgram(string voucher,int channel, Guid logId)
        {
            if (voucher.StartsWith("FPL"))
            {
                bool OTTclient = OttReferalProgramService.checkOTTCode(voucher);
                if (OTTclient)
                {
                    return 2; // return 2 nếu mã là GTBB OTT
                }
            }
            
            bool isMYFPTCode = MyFPTService.checkMyFPTCode(voucher, logId.ToString());
            
            if (isMYFPTCode)
            {
                return 1; // return 1 nếu mã là GTBB MyFPT
            }
            return 0;
        }
        public static int NewCheckReferalProgram(SqlConnection connection, SqlTransaction transaction, string voucher, int channel, Guid logId)
        {
            if (voucher.StartsWith("FPL"))
            {
                bool OTTclient = OttReferalProgramService.checkOTTCode(voucher);
                if (OTTclient)
                {
                    return 2; // return 2 nếu mã là GTBB OTT
                }
            }
            
            bool isMYFPTCode = MyFPTService.NewCheckMyFPTCode(connection, transaction, voucher, logId.ToString());

            if (isMYFPTCode)
            {
                return 1; // return 1 nếu mã là GTBB MyFPT
            }
            return 0;
        }
        public static List<EvoucherService> getListEvcService(List<ServicePlatform> services, Guid logId,int channel,List<EvoucherInput> lstEVC = null, List<EvoucherInput> lstRF = null,
            int localtype = 0, Tuple<int, int> prepaid_net_tv = null, List<DeviceModel> lstDevice = null)
        {
            List<EvoucherService> lstVoucher = new List<EvoucherService>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                
                if (lstEVC.Count > 0)
                {
                    try
                    {
                        foreach (var evc in lstEVC)
                        {
                            List<EvoucherService> lses = new List<EvoucherService>();
                            lses = connection.Query<EvoucherService>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                            {
                                actionName = "GetServiceVoucher",
                                voucherCode = evc.evoucherCode
                            }, commandType: CommandType.StoredProcedure).ToList();
                            lses.Select(x => x.evoucherType = evc.evoucherType).ToList();
                            if (lses != null)
                                lstVoucher.AddRange(lses);

                            var listServiceProducts = connection.Query<EvoucherService>(
                                ConstantAPI.OS6_FPTVoucher_SalePlatform_Device, new
                                {
                                    ActionName = "GetServiceVoucher",
                                    VoucherCode = evc.evoucherCode
                                }, commandType: CommandType.StoredProcedure).ToList();
                            if (listServiceProducts.Count > 0)
                            {
                                listServiceProducts = listServiceProducts.Select(x =>
                                {
                                    x.evoucherType = evc.evoucherType;
                                    return x;
                                }).ToList();
                                
                                lstVoucher.AddRange(listServiceProducts);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MBSv4CommonService.WriteToLog(ex.Message, "MBSv4 KeepVoucher getListEvcService error: ", logId);
                    }                    
                }
                if (lstRF.Count > 0)
                {
                    if (lstDevice == null)
                    {
                        lstDevice = new List<DeviceModel>();
                    } 
                    foreach (var rf in lstRF)
                    {
                        if (lstDevice.Count > 0)
                        {
                            List<SalePlatformVoucherValueInfor> ls = RegisterWebV5.GetPromotionRFCAM(lstDevice, rf.evoucherCode, logId);
                            if (ls.Count > 0)
                            {
                                foreach (var l in ls)
                                {
                                    if(l.Apply.Count >0)
                                    {
                                        foreach (var a in l.Apply)
	                                    {
		                                    EvoucherService es = new EvoucherService {evoucherCode=rf.evoucherCode,ServiceID=a.ServiceID,SubServiceType=a.SubServiceTypeID,evoucherType=rf.evoucherType };
                                            lstVoucher.Add(es);
	                                    } 
                                    }                                                                       
                                }

                                return lstVoucher;
                            }
                        }
                        SalePlatformVoucherValueInfor info = GetVoucherCampaignInfo(services, rf.evoucherCode, localtype, channel,prepaid_net_tv, 1, logId);
                        
                        List<EvoucherService> lses = new List<EvoucherService>();
                        lses = connection.Query<EvoucherService>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                        {
                            actionName = "GetServiceVoucher",
                            voucherCode = info.evoucherCode
                        }, commandType: CommandType.StoredProcedure).ToList();
                        lses.Select(x => x.evoucherCode = rf.evoucherCode).ToList();
                        lses.Select(x => x.evoucherType = rf.evoucherType).ToList();
                        if (lses != null)
                            lstVoucher.AddRange(lses);
                    }
                }
            }
            return lstVoucher;
        }

        public static List<EvoucherService> getListRFService(List<EvoucherInput> lst)
        {
            List<EvoucherService> lstEVC = new List<EvoucherService>();
            foreach (var evc in lst)
            {
                EvoucherService es = new EvoucherService();
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    es = connection.Query<EvoucherService>(OS6_FPTVoucher_SalePlatform_MBSv4, new
                    {
                        actionName = "GetApplyVoucher",
                        voucherCode = evc.evoucherCode
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    es.evoucherType = evc.evoucherType;
                }
                lstEVC.Add(es);
            }
            return lstEVC;
        }
        
        private static OldSalePlatformVoucherValueInfor OldGetInfoVoucherRF(string VoucherRP, int? LocalType, int? PaidTimeTypePE, bool isMyFPT)
        {
            string actionCode = "";
            OldSalePlatformVoucherValueInfor res = new OldSalePlatformVoucherValueInfor();
            
            if (isMyFPT)
            {
                actionCode = "GetVoucherRFinfo_MyFPT";
            }
            else
            {
                actionCode="GetVoucherRFinfo";
            }
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var parameters = new DynamicParameters();
                parameters.Add("@actionName", actionCode);
                parameters.Add("@voucherCode", VoucherRP);
                parameters.Add("@PaidTimeType", PaidTimeTypePE);
                parameters.Add("@LocalType", LocalType);
                res = connection.Query<OldSalePlatformVoucherValueInfor>(
                    OS6_FPTVoucher_SalePlatform_MBSv4, parameters, commandType: CommandType.StoredProcedure
                ).DistinctBy(x => x.evoucherCode).FirstOrDefault();
                res.RefInfo = new RefInfo();
                res.Apply = new List<OldApply>();
            }
            //res.evoucherCode = VoucherRP;
            return res;
        }

        
        private static VoucherReferralInfo GetInfoVoucherRF(string VoucherRP, int? LocalType, int? PaidTimeTypePE, bool isMyFPT)
        {
            string actionCode = "";
            var info = new VoucherReferralInfo();

            if (isMyFPT)
            {
                actionCode = "GetVoucherRFinfo_MyFPT";
            }
            else
            {
                actionCode="GetVoucherRFinfo";
            }
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var parameters = new DynamicParameters();
                parameters.Add("@actionName", actionCode);
                parameters.Add("@voucherCode", VoucherRP);
                parameters.Add("@PaidTimeType", PaidTimeTypePE);
                parameters.Add("@LocalType", LocalType);
                info = connection.Query<VoucherReferralInfo>(
                    OS6_FPTVoucher_SalePlatform_MBSv4, parameters, commandType: CommandType.StoredProcedure
                    ).DistinctBy(x => x.evoucherCode).FirstOrDefault();
            }
            //res.evoucherCode = VoucherRP;
            return info;
        }

        public static bool SaveHistoryTem(SqlConnection connection, SqlTransaction transaction, int objId,string orderCode,
            int SaleID, List<CustomerTypeModel> lstCusType, string PhoneCustomerFPL, int paymentTypeL2, List<ServicePlatform> lst = null, List<EvoucherService> lstevc = null, 
            List<DeviceModel> lstDevices = null, List<EvoucherService> lstEVCserviceCMR=null, UserBranchPlatformModel userbranch = null, 
            int channel =0, int locationid =0)
        {
            int isChange = connection.Execute(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "saveHistoryTem",
                objid = objId,
                OrderCode = orderCode,
                phone = PhoneCustomerFPL,
                paymentTypeL2 = paymentTypeL2,
                XML = CreateXMLSaveHistoryTem(lst, lstCusType, lstevc, orderCode, SaleID, objId, channel, locationid,userbranch),
                XML2 = CreateXMLSaveHistoryTemDevice(lstDevices, lstCusType, lstEVCserviceCMR, orderCode, channel,locationid, SaleID, objId, userbranch)
            },transaction:transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
            return (isChange > 0);
        }
        public static XElement CreateXMLSaveHistoryTemDevice(List<DeviceModel> lstDevices, List<CustomerTypeModel> lstCusType, List<EvoucherService> lstevoucher, string ordercode, int channel,int locationId,int saleid, int objid, UserBranchPlatformModel userbranch)
        {
            if (lstDevices.Count == 0 || lstevoucher.Count == 0) return null;
            if (userbranch == null)
            {
                userbranch = new UserBranchPlatformModel
                {
                    branchCode = 0, 
                    contractGT = "", 
                    departmentId = 0, 
                    sourceId = 0, 
                    subcompanyId = 0
                };
            }
            List<VoucherProductModel> ls = new List<VoucherProductModel>();
            foreach (var device in lstDevices)
            {
                int custype = 0;
                var datalistCustype = lstCusType.Where(x => x.ServiceID == device.serviceId).ToList();
                if (datalistCustype.Count > 0)
                {
                    custype = datalistCustype.FirstOrDefault().CustomerType;
                }
                
                string evoucher = string.Empty;
                var lstevc = lstevoucher.Where(v => v.ServiceID == device.serviceId && v.SubServiceType == device.subServiceTypeID).ToList();
                foreach (var vc in lstevc)
                {
                    VoucherProductModel vst = new VoucherProductModel();
                    vst.ServiceID = device.serviceId;
                    vst.SubServiceTypeID = device.subServiceTypeID;
                    vst.SubServiceID = device.deviceId;
                    vst.PrePaid = device.prePaid;
                    vst.DeployTypeID = device.deployTypeID;
                    vst.ServiceCode = device.serviceCode;
                    vst.Qty = device.qty;
                    vst.evoucher = vc.evoucherCode;
                    vst.custype = custype;
                    vst.GroupID = device.groupID;
                    vst.UsesID = device.usesID;
                    vst.RevokeID = device.revokeID;
                    vst.StatusID = device.statusID;
                    ls.Add(vst);                    
                }
            }
            var xmlString = new XElement("N",
            from item in ls
            select new XElement("I",
                           new XElement("evoucher", item.evoucher),
                           new XElement("ServiceID", item.ServiceID),
                           new XElement("SubServiceTypeID", item.SubServiceTypeID),
                           new XElement("SaleID", saleid),
                           new XElement("SubServiceID", item.SubServiceID),
                           new XElement("PrePaid", item.PrePaid),
                           new XElement("DeployTypeID", item.DeployTypeID),
                           new XElement("ServiceCode", item.ServiceCode),
                           new XElement("Qty", item.Qty),
                           new XElement("CustomerType", item.custype),
                           new XElement("objId", objid),
                           new XElement("orderCode", ordercode),
                           new XElement("channel", channel),
                           new XElement("locationId", locationId),
                           new XElement("sourceId", userbranch.sourceId),
                           new XElement("subcompanyId", userbranch.subcompanyId),
                           new XElement("branchCode", userbranch.branchCode),
                           new XElement("departmentId", userbranch.departmentId),
                           new XElement("groupID", item.GroupID),
                           new XElement("usesID", item.UsesID),
                           new XElement("revokeID", item.RevokeID),
                           new XElement("statusID", item.StatusID),
                           new XElement("statusID", item.StatusID),
                           new XElement("sourceId", userbranch.sourceId),
                           new XElement("branchCode", userbranch.branchCode),
                           new XElement("departmentId", userbranch.departmentId),
                           new XElement("subcompanyId", userbranch.subcompanyId),
                           new XElement("contractGT", userbranch.contractGT)
                       ));
            MBSv4CommonService.WriteToLog(xmlString, "MBSv4 KeepVoucher lstEVC: ", Guid.NewGuid());
            return xmlString;

        }
        public static XElement CreateXMLSaveHistoryTem(List<ServicePlatform> lst, List<CustomerTypeModel> lstCusType, List<EvoucherService> lstevoucher, string ordercode, int saleid, int objid, int channel, int locationid=0,UserBranchPlatformModel userbranch = null)
        {
            if (userbranch == null)
            {
                userbranch = new UserBranchPlatformModel {branchCode=0,contractGT="",departmentId=0, sourceId=0,subcompanyId=0 };
            }
            List<VoucherServiceTypeModel> ls = new List<VoucherServiceTypeModel>();
            foreach (var service in lst)
            {
                int custype = 0;
                var datalistCustype = lstCusType.Where(x => x.ServiceID == service.ServiceID).ToList();
                if (datalistCustype.Count > 0)
                {
                    custype = datalistCustype.FirstOrDefault().CustomerType;
                }

                foreach (var subtype in service.SubServiceTypes)
                {
                    string evoucher = string.Empty;
                    var lstevc = lstevoucher.Where(v => v.ServiceID == service.ServiceID && v.SubServiceType == subtype.SubServiceTypeID).ToList();
                    foreach (var vc in lstevc)
                    {
                        foreach (var sub in subtype.SubServices)
                        {
                            VoucherServiceTypeModel vst = new VoucherServiceTypeModel();
                            vst.ServiceID = service.ServiceID;
                            vst.SubServiceTypeID = subtype.SubServiceTypeID;
                            vst.SubServiceID = sub.SubServiceID;
                            vst.PrePaid = sub.PrePaid;
                            vst.DeployTypeID = sub.DeployTypeID;
                            vst.ServiceCode = sub.ServiceCode;
                            vst.Qty = sub.Qty;
                            vst.evoucher = vc.evoucherCode;
                            vst.custype = custype;
                            ls.Add(vst);
                        }
                    }
                }
            }
            var xmlString = new XElement("N",
            from item in ls
            select new XElement("I",
                           new XElement("evoucher", item.evoucher),
                           new XElement("ServiceID", item.ServiceID),
                           new XElement("SubServiceTypeID", item.SubServiceTypeID),
                           new XElement("SaleID", saleid),
                           new XElement("SubServiceID", item.SubServiceID),
                           new XElement("PrePaid", item.PrePaid),
                           new XElement("DeployTypeID", item.DeployTypeID),
                           new XElement("ServiceCode", item.ServiceCode),
                           new XElement("Qty", item.Qty),
                           new XElement("CustomerType", item.custype),
                           new XElement("objId", objid),
                           new XElement("orderCode", ordercode),
                           new XElement("channel", channel),
                           new XElement("locationId", locationid),
                           new XElement("subcompanyId", userbranch.subcompanyId),
                           new XElement("branchCode", userbranch.branchCode),
                           new XElement("departmentId", userbranch.departmentId),
                           new XElement("sourceId", userbranch.sourceId),
                           new XElement("contractGT", userbranch.contractGT)
                       ));
            MBSv4CommonService.WriteToLog(xmlString, "MBSv4 KeepVoucher lstEVC: ", Guid.NewGuid());
            return xmlString;

        }
        private static XElement CreateXMLHistoryTem(string evoucher,int Service, int objId, string orderCode, int CusType, int SubServiceTypeID, int SaleID, List<SubServiceModel> lst)
        {
            var xmlString = new XElement("N",
            from item in lst
            select new XElement("I",
                           new XElement("evoucher", evoucher),
                           new XElement("ServiceID", Service),
                           new XElement("SubServiceTypeID", SubServiceTypeID),
                           new XElement("SaleID", SaleID),
                           new XElement("SubServiceID", item.SubServiceID),
                           new XElement("PrePaid", item.PrePaid),
                           new XElement("DeployTypeID", item.DeployTypeID),
                           new XElement("ServiceCode", item.ServiceCode),
                           new XElement("Qty", item.Qty),
                           new XElement("CustomerType", CusType),
                           new XElement("objId", objId),
                           new XElement("orderCode", orderCode)
                       ));
            return xmlString;
        }

        public static bool CheckKeepHistoryTem(SqlConnection connection, SqlTransaction transaction, List<EvoucherService> lstevc, int objId, string orderCode,
            int SaleID, List<ServicePlatform> lst, List<CustomerTypeModel> lstCusType,int channel)
        {
            var keep = connection.Query(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "CheckKeepVoucher",
                objid = objId,
                OrderCode = orderCode,
                xml = CreateXMLSaveHistoryTem(lst, lstCusType, lstevc, orderCode, SaleID, objId, channel)
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            return ((keep.Count == 0) || (keep.Count == lstevc.Count));
        }

        public static bool CheckAddVoucherGCCode(SqlConnection connection, SqlTransaction transaction, string voucher, int objId, string regcode)
        {
            var keep = connection.Query<int>(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "CheckAddVoucher",
                objid = objId,
                OrderCode = regcode,
                voucherCode = voucher
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // >0 là đã có add trc đó
            return (keep > 0);
        }

        public static bool CheckRedeemRFCode(SqlConnection connection, SqlTransaction transaction, string voucher, int objId, string orderCode)
        {
            var Redee = connection.Query<int>(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "CheckRedeemRFCode",
                objid = objId,
                OrderCode = orderCode,
                voucherCode = voucher
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // >0 là active trc đó
            return (Redee > 0);
        }

        public static bool UpdateHistoryTem(SqlConnection connection, SqlTransaction transaction, string voucher, int objId, string orderCode, int serviceid,string regCode)
        {
            var isUpdate = connection.Execute(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "UpdateHistoryTem",
                objid = objId,
                OrderCode = orderCode,
                voucherCode = voucher,
                serviceid=serviceid,
                regCode=regCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
            
            return (isUpdate > 0);
        }

        public static bool CheckVoucherRF(string voucherCode,int channel)
        {
            bool trueVoucher = false;
            if (voucherCode.StartsWith("FPL"))
            {
                bool isRFFPTplay = OttReferalProgramService.checkOTTCode(voucherCode);
                if (isRFFPTplay)
                    return true;
            }
            
            bool isMYFPTCode = MyFPTService.checkMyFPTCode(voucherCode, "");
            if (isMYFPTCode) return isMYFPTCode;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int StatusCodeRF = connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetStatusRFCode",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();                
                trueVoucher = (StatusCodeRF == 1);                
            }
            return trueVoucher;
        }

        public static bool CheckVoucherGC(string voucherCode)
        {
            bool trueVoucher = false;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int StatusCodeVC = connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "GetStatusVoucherCode",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();                
                trueVoucher = (StatusCodeVC == 1);
            }
            return trueVoucher;
        }
        public static bool CheckDuplicateKindVoucher(List<EvoucherInput> listVoucher)
        {
            bool trueVoucher = false;
            string voucherCodes = string.Join(",", listVoucher.Where(x => x.evoucherType.Equals(1)).Select(y=>y.evoucherCode).ToList());
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int StatusCodeVC = connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                {
                    actionName = "CheckDuplicate",
                    @eventCodes = voucherCodes
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                // trả ra 1 tức là có trùng loại voucher
                trueVoucher = (StatusCodeVC == 0);
            }
            return trueVoucher;
        }

        #endregion
        public static List<DeviceModel> GetListDeviceCam(List<ProductPlatform> lstProduct)
        {
            List<DeviceModel> lstDevice = new List<DeviceModel>();
            var serviceCams = lstProduct.Where(x => x.ServiceID == 3 || x.ServiceID == 4).ToList();
            if (serviceCams.Count > 0)
            {
                foreach (var serviceCam in serviceCams)
                {
                    foreach (var SubServiceType in serviceCam.SubServiceTypes)
                    {
                        foreach (var subservice in SubServiceType.SubServices)
                        {
                            DeviceModel de = new DeviceModel();
                            de.applyPrePaid = subservice.ApplyPrePaid;
                            de.applySubServiceID = subservice.ApplySubServiceID;
                            de.deployTypeID = subservice.DeployTypeID;
                            de.deviceId = subservice.SubServiceID;
                            de.groupID = subservice.GroupID;
                            de.prePaid = Convert.ToInt32(subservice.PrePaid);
                            de.qty = subservice.Qty;
                            de.revokeID = subservice.RevokeID;
                            de.serviceCode = subservice.ServiceCode;
                            de.serviceId = serviceCam.ServiceID;
                            de.statusID = subservice.StatusID;
                            de.subServiceTypeID = SubServiceType.SubServiceTypeId;
                            de.usesID = subservice.UsesID;
                            lstDevice.Add(de);
                        }
                    }
                } 
            }               
            return lstDevice;
        }
        #region fix performace getInfor
        public static string GetServiceCodeForGetInfor(SqlConnection connection, int service)
        {
            return connection.Query<string>(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "GetCodeService",
                @serviceid = service
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static int GetPrepaidIDForGetInfor(SqlConnection connection, int prepaidnet, int prepaidtv)
        {
            return connection.Query<int>(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "GetPrepaidID",
                prepaidnet = prepaidnet,
                prepaidtv = prepaidtv
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static int GetQuotaV2ForGetInfor(SqlConnection connection, int promotionID, int salesmanID, int localTypeID, int paidTimeTypeID)
        {
            return connection.QueryFirstOrDefault<int>(OS6_FPTVoucher_SalesManInfo, new
            {
                ActionName = "GetQuotaV2",
                ID = salesmanID,
                PromotionID = promotionID,
                LocalTypeID = localTypeID,
                PaidTimeTypeID = paidTimeTypeID

            }, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }
        public static int GetQuotaKeepForGetInfor(SqlConnection connection, string VoucherCode, int salesmanID)
        {
            return connection.QueryFirstOrDefault<int>(OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                ActionName = "countQuotaHistoryTem",
                saleid = salesmanID,
                voucherCode = VoucherCode
            }, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }
        #endregion

    }
}
