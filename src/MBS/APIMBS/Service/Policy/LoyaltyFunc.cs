using APIMBS.Models.Policy;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using Voucher.APIHelper;

namespace APIMBS.Service.Policy
{
    public class LoyaltyFunc
    {
        private static loyalty_auth _auth;

        private static loyalty_auth auth_get(bool refresh = false)
        {
            try
            {
                if (!refresh && _auth != null && _auth.expire_time > DateTime.Now.AddMinutes(5))
                    return _auth;

                string uri = Utility.loyaltyapi + "/auth/oauth/token?grant_type=client_credentials";
                var authorization = Convert.ToBase64String(Encoding.UTF8.GetBytes(Utility.loyaltyapi_username + ":" + Utility.loyaltyapi_password));
                var headers = new Dictionary<string, string> { { "Authorization", "Basic " + authorization } };
                string data = "grant_type=client_credentials";
                var res = Utility.PostAsync(uri, data, headers, mediaType: "application/x-www-form-urlencoded").Result;

                if (res.result == 1)
                {
                    _auth = JsonConvert.DeserializeObject<loyalty_auth>(res.data);
                    _auth.expire_time = DateTime.Now.AddSeconds(_auth.expires_in);
                    return _auth;
                }
                else
                {
                    Utility.LogError(res.error);
                }
            }
            catch (Exception ex)
            {
                Utility.LogError(ex.ToString());
            }
            return null;
        }

        public static loyalty_contracts_customer_brief_res contracts_customer_brief(List<Log_Model> logs, int objID)
        {
            try
            {
                var auth = auth_get();
                if (auth == null)
                {
                    return null;
                }

                string uri = Utility.loyaltyapi + "/internal/api/contracts/" + objID + "/customer/brief";

                var headers = new Dictionary<string, string> { { "Authorization", "Bearer " + auth.access_token } };
                var res = Utility.PostAsync(uri, "", headers).Result;

                if (res.result == 1)
                {
                    var resData = JsonConvert.DeserializeObject<loyalty_res<List<loyalty_contracts_customer_brief_res>>>(res.data);

                    if (resData.statusCode == 200 && resData.data != null)
                        return resData.data.FirstOrDefault();
                }
                else
                {
                    Utility.LogError(res.error);
                }
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("contracts_customer_brief Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }
            return null;
        }

        public class loyalty_auth
        {
            public string access_token { get; set; }
            public int expires_in { get; set; }
            //
            public DateTime? expire_time { get; set; }
        }

        public class loyalty_res<T>
        {
            public int statusCode { get; set; }
            public T data { get; set; }
        }

        public class loyalty_contracts_customer_brief_res
        {
            public int? loyaltyStatus { get; set; }
        }
    }
}