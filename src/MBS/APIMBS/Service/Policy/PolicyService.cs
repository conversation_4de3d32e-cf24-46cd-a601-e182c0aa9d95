using APIMBS.Models.MobileSaleV4;
using APIMBS.Models.Policy;
using APIMBS.Models.SalePlatform;
using Dapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Util;

namespace APIMBS.Service.Policy
{
    public class PolicyService
    {
        #region get_list
        // voucherTypeID 1. chung, 2. le, -1. input
        public static Voucher_GetList_Model Voucher_GetList(List<Log_Model> logs, SalePolicyGetInforEVC input, int voucherTypeID = 1, bool checkLimit = true)
        {
            var res = new Voucher_GetList_Model
            {
                Vouchers = new List<Voucher_Model>(),
                LstVoucher = new List<PolicyVoucher2>(),
                Error = new List<string>()
            };

            try
            {
                bool log_time = Utility.GetConfig<dynamic>(23)?.Count > 0;

                Utility.LogFull(log_time, "Voucher_GetList: 1: " + DateTime.Now, "");

                var lst_voucher_percent = Utility.GetBaseConfig(71, "voucher-percent");

                using (var connection = new SqlConnection(Utility.ConnRead))
                {
                    List<int> policyIds = new List<int>();
                    List<Order_Model_Service> lst_service = new List<Order_Model_Service>();
                    List<Order_Model_Product> lst_product = new List<Order_Model_Product>();

                    if (input.Services != null && input.Services.Count > 0)
                    {
                        foreach (var s in input.Services)
                        {
                            foreach (var sst in s.SubServiceTypes)
                            {
                                foreach (var ss in sst.SubServices)
                                {
                                    lst_service.Add(new Order_Model_Service
                                    {
                                        PolicyID = ss.PolicyID,
                                        NotApplyVoucher = ss.NotApplyVoucher,
                                        ServiceID = s.ServiceID,
                                        SubServiceTypeID = sst.SubServiceTypeID,
                                        SubServiceID = ss.SubServiceID,
                                        PrePaid = ss.PrePaid,
                                        MonthUsed = ss.MonthUsed,
                                        DeployTypeID = ss.DeployTypeID,
                                        ServiceCode = ss.ServiceCode,
                                        Qty = ss.Qty,
                                        Total = ss.Total,
                                        Total_VAT = ss.Total_VAT
                                    });
                                }
                            }
                        }
                    }

                    if (input.Products != null && input.Products.Count > 0)
                    {
                        foreach (var s in input.Products)
                        {
                            foreach (var sst in s.SubServiceTypes)
                            {
                                foreach (var ss in sst.SubServices)
                                {
                                    lst_product.Add(new Order_Model_Product
                                    {
                                        PolicyID = ss.PolicyID,
                                        NotApplyVoucher = ss.NotApplyVoucher,
                                        ServiceID = s.ServiceID,
                                        SubServiceTypeID = sst.SubServiceTypeId,
                                        SubServiceID = ss.SubServiceID,
                                        DeployTypeID = ss.DeployTypeID,
                                        StatusID = ss.StatusID,
                                        RevokeID = ss.RevokeID,
                                        ServiceCode = ss.ServiceCode,
                                        Qty = ss.Qty,
                                        Total = ss.Total,
                                        Total_VAT = ss.Total_VAT
                                    });
                                }
                            }
                        }
                    }

                    foreach (var c in lst_service)
                    {
                        policyIds.Add(c.PolicyID);
                    }

                    foreach (var c in lst_product)
                    {
                        policyIds.Add(c.PolicyID);
                    }

                    policyIds = policyIds.Distinct().ToList();

                    var lst_p = Policy_GetInfo(logs, connection, policyIds);

                    if (lst_p.Any(c => c.IsScheduleApply > 0))
                    {
                        logs.Add(new Log_Model("Đơn hàng là chính sách ngày vàng", ""));
                        if (input.evoucherList?.Count > 0)
                        {
                            res.Error.Add("Đơn hàng là chính sách ngày vàng");
                        }
                        return res;
                    }

                    if (lst_service.Any(c => c.NotApplyVoucher == 1) || lst_product.Any(c => c.NotApplyVoucher == 1))
                    {
                        logs.Add(new Log_Model("Đơn hàng áp dụng chính sách không dùng phiếu mua hàng", ""));
                        if (input.evoucherList?.Count > 0)
                        {
                            res.Error.Add("Đơn hàng áp dụng chính sách không dùng phiếu mua hàng");
                        }
                        return res;
                    }

                    int subCompanyID = Branch_SubCompanyID(logs, connection, input.CustomerInfor.LocationID, input.CustomerInfor.BranchCode);

                    List<Voucher_GetProgram_Model_Voucher> lst_program = Voucher_GetProgram(logs, connection, new Voucher_GetProgram_Model
                    {
                        SaleTeamID = input.SaleInfor.SaleTeamID,
                        SaleChannelID = input.SaleInfor.SaleChannelID,
                        ObjID = input.objId,
                        SubCompanyID = subCompanyID,
                        LocationID = input.CustomerInfor.LocationID,
                        BranchCode = input.CustomerInfor.BranchCode,
                        DistrictID = input.CustomerInfor.DistrictID,
                        WardID = input.CustomerInfor.WardID,
                        BuildingID = input.CustomerInfor.BuildingID,
                        CusTypeID = input.CustomerInfor.CusTypeID,
                        CusTypeL2ID = input.CustomerInfor.CusTypeL2ID,
                        ObjectTypeID = input.CustomerInfor.ObjectTypeID,
                        CustomerRank = input.CustomerInfor.CustomerRank,
                        CustomerTypes = input.CustomerTypes.Select(c => new Voucher_GetProgram_Model_CustomerType
                        {
                            ServiceID = c.ServiceID,
                            CustomerTypeID = c.CustomerType
                        }).ToList(),
                        PaymentTypeID = input.PaymentTypeID,
                        CrossSellingLocation = input.CrossSellingLocation
                    });

                    logs.Add(new Log_Model("lst_program", lst_program?.Select(c => c.VoucherCode).ToList()));

                    Utility.LogFull(log_time, "Voucher_GetList: 2: " + DateTime.Now, "");

                    if (lst_program == null || lst_program.Count == 0)
                        return res;

                    var lst_subtype_deploy = BaseConfig_Get(logs, connection, 24);
                    if (lst_subtype_deploy == null)
                        lst_subtype_deploy = new List<BaseConfig_Model>();

                    var lst_voucher_output = new List<Voucher_Model>();

                    if (voucherTypeID > 0)
                    {
                        lst_program = lst_program.Where(c => c.VoucherTypeID == voucherTypeID).ToList();
                    }
                    else if (voucherTypeID == -1)
                    {
                        if (input.evoucherList?.Count > 0)
                        {
                            lst_program = lst_program.Where(c => input.evoucherList.Any(x => x.evoucherCode.Equals(c.VoucherCode, StringComparison.OrdinalIgnoreCase))).ToList();
                        }
                    }

                    // 2024-05-16 y/c ngoclt20
                    // pmh check order must have basic net
                    if (lst_program?.Count > 0)
                    {
                        var lst_config_code = BaseConfig_Get(logs, connection, 40);
                        if (lst_config_code?.Count > 0)
                        {
                            List<int> programIds = Policy_GetProgramIDByCode(logs, connection, lst_config_code.Select(c => c.FName).Distinct().ToList());
                            if (programIds?.Count > 0)
                            {
                                if (lst_program.Any(c => programIds.Contains(c.ProgramID)))
                                {
                                    var lst_config_subservice = BaseConfig_Get(logs, connection, 41);
                                    if (lst_config_subservice?.Count > 0)
                                    {
                                        lst_program = lst_program.Where(c =>
                                        !programIds.Contains(c.ProgramID)
                                        || (programIds.Contains(c.ProgramID) && lst_service.Any(x => lst_config_subservice.Any(s => s.FValue == x.SubServiceID && s.TValue <= x.PrePaid)))).ToList();
                                    }
                                }
                            }
                        }
                    }

                    using (var multi = connection.QueryMultiple(
                        "PowerInside.dbo.OS6_FPTVoucher_Policy_GetList",
                        new
                        {
                            VoucherIDs = string.Join(",", lst_program.Select(c => c.VoucherID).ToList())
                        },
                        commandType: CommandType.StoredProcedure))
                    {
                        var lst_voucher = multi.Read<Voucher_Model>().OrderBy(c => c.VoucherCode).ToList();
                        var lst_voucher_service = multi.Read<Voucher_Model_Service>().ToList();
                        var lst_voucher_service_requirement = multi.Read<Voucher_Model_Service_Requirement>().ToList();
                        var lst_voucher_product = multi.Read<Voucher_Model_Product>().ToList();

                        Utility.LogFull(log_time, "Voucher_GetList: 3: " + DateTime.Now, "");

                        var policyExtraTypes = Voucher_GetPolicyExtraType(logs, connection);

                        var subServiceConnectionFees = SubService_GetConnectionFee(logs, connection);

                        foreach (var voucher in lst_voucher)
                        {
                            // temp
                            if (voucher.ApplyFor == 3) // dv + tb
                            {
                                if (!lst_voucher_product.Any(c => c.VoucherID == voucher.VoucherID))
                                {
                                    voucher.ApplyFor = 1; // dv
                                }
                                else if (!lst_voucher_service.Any(c => c.VoucherID == voucher.VoucherID))
                                {
                                    voucher.ApplyFor = 2; // tb
                                }
                            }

                            if (voucher.ApplyFor == 1) // dv
                            {
                                foreach (var policyId in policyIds)
                                {
                                    var policy = lst_p.FirstOrDefault(c => c.PolicyID == policyId);

                                    if (policy == null)
                                        continue;

                                    if (voucher.ComboType != policy.ComboType)
                                        continue;

                                    // policy_extra_type
                                    // voucher_extra_type > 0 => check policy_extra_type
                                    if (voucher.PolicyExtraTypeID > 0 && voucher.PolicyExtraTypeID != policy.PolicyExtraTypeID)
                                        continue;

                                    // voucher_extra_type = 0 and policy_extra_type > 0 => check all_extra_type
                                    if (voucher.PolicyExtraTypeID == 0 && policy.PolicyExtraTypeID > 0 && policyExtraTypes.Contains(policy.PolicyExtraTypeID))
                                        continue;

                                    var _lst_voucher_service = lst_voucher_service.Where(c => c.VoucherID == voucher.VoucherID).ToList();
                                    var _lst_service = lst_service.Where(c => c.PolicyID == policyId).ToList();

                                    var _lst_service_connection_fee = _lst_service.Where(c => subServiceConnectionFees.Contains(c.SubServiceID)).ToList();

                                    // remove phm
                                    _lst_service = _lst_service.Where(c => !subServiceConnectionFees.Contains(c.SubServiceID)).ToList();

                                    // remove subtype deploy
                                    _lst_service = _lst_service.Where(c => !lst_subtype_deploy.Any(x => x.FValue == c.SubServiceTypeID)).ToList();

                                    bool b1 = false;
                                    bool b2 = false;

                                    if (policy.ComboType == 1)
                                    {
                                        b1 = _lst_service.All(c => _lst_voucher_service.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid && x.FromQuantity <= c.Qty && x.ToQuantity >= c.Qty));
                                        b2 = _lst_voucher_service.GroupBy(c => c.SubServiceTypeID).Select(c => c.FirstOrDefault().SubServiceTypeID).All(id => _lst_service.Any(x => x.SubServiceTypeID == id));
                                    }
                                    else if (policy.ComboType == 2)
                                    {
                                        _lst_service = _lst_service.Where(c => _lst_voucher_service.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid && x.FromQuantity <= c.Qty && x.ToQuantity >= c.Qty)).ToList();
                                        b1 = _lst_service?.Count > 0;
                                        b2 = true;
                                    }

                                    var _lst_voucher_service_requirement = lst_voucher_service_requirement.Where(c => c.VoucherID == voucher.VoucherID).ToList();
                                    var b3 = false;
                                    if (_lst_voucher_service_requirement != null && _lst_voucher_service_requirement.Count > 0)
                                    {
                                        b3 = false;
                                        bool b3_1 = false;
                                        bool b3_2 = false;
                                        // get group have all subservice
                                        var groups = _lst_voucher_service_requirement.GroupBy(c => c.VSRequirementID)
                                            .Where(g => g.All(c => _lst_service.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid)));
                                        if (groups.Count() > 0)
                                        {
                                            foreach (var g in groups)
                                            {
                                                // subservice in group
                                                var services = _lst_service.Where(c => g.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid)).ToList();

                                                // check total
                                                if (g.First().Value > 0)
                                                {
                                                    if (services.Sum(c => c.Total_VAT) >= g.First().Value)
                                                    {
                                                        b3_1 = true;
                                                    }
                                                }
                                                else
                                                {
                                                    b3_1 = true;
                                                }

                                                // check connectionfee
                                                if (g.First().ConnectionFee > 0)
                                                {
                                                    var connectionFee = _lst_service_connection_fee.Where(c => services.Any(x => x.SubServiceTypeID == c.SubServiceTypeID)).Sum(c => c.Total_VAT);
                                                    if (connectionFee >= g.First().ConnectionFee)
                                                    {
                                                        b3_2 = true;
                                                    }
                                                }
                                                else
                                                {
                                                    b3_2 = true;
                                                }

                                                if (policy.ComboType == 1)
                                                {
                                                    if (b3_1 && b3_2)
                                                    {
                                                        b3 = true;
                                                        break;
                                                    }
                                                }
                                                else if (policy.ComboType == 2)
                                                {
                                                    if (!b3_1 || !b3_2)
                                                    {
                                                        // remove service invalid in only
                                                        _lst_service = _lst_service.Where(c => !services.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid)).ToList();
                                                    }
                                                }
                                            }

                                            if (policy.ComboType == 2)
                                            {
                                                // any service ok in only then pass
                                                if (_lst_service?.Count > 0)
                                                {
                                                    b3 = true;
                                                }
                                            }
                                        }
                                        else
                                        {
                                            b3 = true;
                                        }
                                    }
                                    else
                                    {
                                        b3 = true;
                                    }

                                    if (b1 && b2 && b3)
                                    {
                                        voucher.Services = new List<Voucher_Model_Service>();
                                        foreach (var c in Utility.CloneObject(_lst_voucher_service))
                                        {
                                            var lst_s = _lst_service.Where(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid).ToList();
                                            foreach (var s in lst_s)
                                            {
                                                if (s != null)
                                                {
                                                    float month_value = c.MonthValue > 0 ? c.MonthValue : 1;
                                                    if (month_value > s.MonthUsed && s.MonthUsed >= 0)
                                                    {
                                                        month_value = (float)(s.MonthUsed > 0 ? s.MonthUsed : 1);
                                                    }
                                                    c.Qty = s.Qty;
                                                    c.ServiceCode = s.ServiceCode;
                                                    c.DeployTypeID = s.DeployTypeID;
                                                    c.MonthUsed = (float)s.MonthUsed;
                                                    c.Discount = (c.Value * (decimal)(month_value) + c.ConnectionFee);
                                                    c.Total_VAT = s.Total_VAT;

                                                    // phm
                                                    if (_lst_voucher_service.Any(x => x.ConnectionFee > 0))
                                                    {
                                                        var s_c = _lst_service_connection_fee.FirstOrDefault(x => x.SubServiceTypeID == s.SubServiceTypeID);
                                                        if (s_c != null)
                                                        {
                                                            c.SubServiceID = s_c.SubServiceID;
                                                            c.PrePaid = (float)s_c.PrePaid;
                                                            c.ServiceCode = s_c.ServiceCode;
                                                            c.DeployTypeID = s_c.DeployTypeID;
                                                            c.MonthUsed = (float)s_c.MonthUsed;
                                                            c.Discount = c.ConnectionFee;
                                                            c.Total_VAT = s.Total_VAT;
                                                        }
                                                    }

                                                    voucher.Services.Add(c);
                                                }
                                            }
                                        }

                                        // max_quantity
                                        // one each sub-type
                                        int max_quantity = 0;
                                        // fgold
                                        if (voucher.PolicyGroupID == 103 || voucher.PolicyGroupID == 109)
                                        {
                                            max_quantity = 1;
                                        }
                                        // combo one each sub-type
                                        if (voucher.ComboType == 1 || max_quantity == 1)
                                        {
                                            voucher.Services = voucher.Services.GroupBy(c => c.SubServiceTypeID).Select(c =>
                                            {
                                                var s = c.OrderByDescending(x => x.Total_VAT).ThenBy(x => x.SubServiceID).First();
                                                s.Qty = 1;
                                                return s;
                                            }).ToList();
                                        }

                                        decimal discount = 0;
                                        if (voucher.Services != null && voucher.Services.Count > 0)
                                        {
                                            discount = discount + voucher.Services.Sum(c => (c.Value * (decimal)(c.MonthValue > 0 ? c.MonthValue : 1) + c.ConnectionFee) * c.Qty);
                                        }
                                        voucher.Discount = discount;

                                        lst_voucher_output.Add(voucher);
                                    }
                                }
                            }
                            else if (voucher.ApplyFor == 2 || voucher.ApplyFor == 0) // tb
                            {
                                var _lst_voucher_product = lst_voucher_product.Where(c => c.VoucherID == voucher.VoucherID).ToList();
                                var _lst_product = lst_product.Where(c => _lst_voucher_product.Any(x => x.SubServiceTypeID == c.SubServiceTypeID && x.DeviceID == c.SubServiceID
                                    && x.DeviceStatus == c.StatusID && x.DeployTypeID == c.DeployTypeID && x.IsReturn == c.RevokeID
                                    && (x.FromPrice == 0 || x.FromPrice <= c.Total_VAT))
                                ).ToList();

                                // policy_extra_type
                                if (voucher.PolicyExtraTypeID > 0)
                                {
                                    // voucher_extra_type > 0 => check policy_extra_type
                                    List<int> _policyIds = lst_p.Where(c => c.PolicyExtraTypeID == voucher.PolicyExtraTypeID).Select(c => c.PolicyID).ToList();
                                    _lst_product = _lst_product.Where(c => _policyIds.Contains(c.PolicyID)).ToList();
                                }
                                else if (voucher.PolicyExtraTypeID == 0)
                                {
                                    // voucher_extra_type = 0 => check not in all_extra_type
                                    List<int> _policyIds = lst_p.Where(c => !policyExtraTypes.Contains(c.PolicyExtraTypeID)).Select(c => c.PolicyID).ToList();
                                    _lst_product = _lst_product.Where(c => _policyIds.Contains(c.PolicyID)).ToList();
                                }

                                bool b4 = false;

                                if (voucher.ApplyTypeID == 3)
                                {
                                    // sl theo tong tb
                                    int quantity = _lst_product.Sum(x => x.Qty);
                                    _lst_product = _lst_product.Where(c => _lst_voucher_product.Any(x => x.FromQuantity <= quantity && x.ToQuantity >= quantity)).ToList();
                                }
                                else
                                {
                                    // sl theo tung tb
                                    _lst_product = _lst_product.Where(c => _lst_voucher_product.Any(x => x.FromQuantity <= c.Qty && x.ToQuantity >= c.Qty)).ToList();
                                }

                                if (_lst_product != null && _lst_product.Count > 0)
                                {
                                    b4 = true;
                                }

                                if (b4)
                                {
                                    voucher.Products = new List<Voucher_Model_Product>();
                                    foreach (var c in Utility.CloneObject(_lst_voucher_product))
                                    {
                                        var lst_s = _lst_product.Where(x => x.SubServiceTypeID == c.SubServiceTypeID && x.SubServiceID == c.DeviceID
                                            && x.StatusID == c.DeviceStatus && x.DeployTypeID == c.DeployTypeID && x.RevokeID == c.IsReturn).ToList();
                                        foreach (var s in lst_s)
                                        {
                                            if (s != null)
                                            {
                                                // voucher percent
                                                if (lst_voucher_percent?.Count > 0)
                                                {
                                                    var voucher_percent = lst_voucher_percent.FirstOrDefault(x => x.FName.Equals(voucher.VoucherCode, StringComparison.OrdinalIgnoreCase));
                                                    if (voucher_percent?.TValue > 0)
                                                    {
                                                        c.Value = Math.Round((s.Total_VAT / s.Qty) * (decimal)(voucher_percent?.TValue ?? 0) / 100, 0);
                                                    }
                                                }

                                                c.Qty = s.Qty;
                                                c.ServiceCode = s.ServiceCode;
                                                c.Discount = c.Value;
                                                c.Total_VAT = s.Total_VAT;
                                                voucher.Products.Add(c);
                                            }
                                        }
                                    }

                                    // max_quantity
                                    // one product
                                    int max_quantity = 0;
                                    max_quantity = _lst_voucher_product.Max(c => c.MaxQuantity);
                                    // fgold
                                    if (voucher.PolicyGroupID == 103 || voucher.PolicyGroupID == 109)
                                    {
                                        max_quantity = 1;
                                    }
                                    if (max_quantity == 1)
                                    {
                                        var c = voucher.Products.OrderByDescending(x => x.Total_VAT).ThenBy(x => x.DeviceID).FirstOrDefault();
                                        c.Qty = 1;
                                        voucher.Products = new List<Voucher_Model_Product> { c };
                                    }

                                    decimal discount = 0;
                                    if (voucher.Products != null && voucher.Products.Count > 0)
                                    {
                                        discount = discount + voucher.Products.Sum(c => c.Value * c.Qty);
                                    }
                                    voucher.Discount = discount;

                                    lst_voucher_output.Add(voucher);
                                }
                            }
                            else if (voucher.ApplyFor == 3) // dv + tb
                            {
                                foreach (var policyId in policyIds)
                                {
                                    var policy = lst_p.FirstOrDefault(c => c.PolicyID == policyId);

                                    if (policy == null)
                                        continue;

                                    if (voucher.ComboType != policy.ComboType)
                                        continue;

                                    // policy_extra_type
                                    // voucher_extra_type > 0 => check policy_extra_type
                                    if (voucher.PolicyExtraTypeID > 0 && voucher.PolicyExtraTypeID != policy.PolicyExtraTypeID)
                                        continue;

                                    // voucher_extra_type = 0 and policy_extra_type > 0 => check all_extra_type
                                    if (voucher.PolicyExtraTypeID == 0 && policy.PolicyExtraTypeID > 0 && policyExtraTypes.Contains(policy.PolicyExtraTypeID))
                                        continue;

                                    var _lst_voucher_service = lst_voucher_service.Where(c => c.VoucherID == voucher.VoucherID).ToList();
                                    var _lst_service = lst_service.Where(c => c.PolicyID == policyId).ToList();

                                    var _lst_service_connection_fee = _lst_service.Where(c => subServiceConnectionFees.Contains(c.SubServiceID)).ToList();

                                    // remove phm
                                    _lst_service = _lst_service.Where(c => !subServiceConnectionFees.Contains(c.SubServiceID)).ToList();

                                    // remove subtype deploy
                                    _lst_service = _lst_service.Where(c => !lst_subtype_deploy.Any(x => x.FValue == c.SubServiceTypeID)).ToList();

                                    bool b1 = false;
                                    bool b2 = false;

                                    if (policy.ComboType == 1)
                                    {
                                        b1 = _lst_service.All(c => _lst_voucher_service.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid && x.FromQuantity <= c.Qty && x.ToQuantity >= c.Qty));
                                        b2 = _lst_voucher_service.GroupBy(c => c.SubServiceTypeID).Select(c => c.FirstOrDefault().SubServiceTypeID).All(id => _lst_service.Any(x => x.SubServiceTypeID == id));
                                    }
                                    else if (policy.ComboType == 2)
                                    {
                                        _lst_service = _lst_service.Where(c => _lst_voucher_service.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid && x.FromQuantity <= c.Qty && x.ToQuantity >= c.Qty)).ToList();
                                        b1 = _lst_service?.Count > 0;
                                        b2 = true;
                                    }

                                    var _lst_voucher_service_requirement = lst_voucher_service_requirement.Where(c => c.VoucherID == voucher.VoucherID).ToList();
                                    var b3 = false;
                                    if (_lst_voucher_service_requirement != null && _lst_voucher_service_requirement.Count > 0)
                                    {
                                        b3 = false;
                                        bool b3_1 = false;
                                        bool b3_2 = false;
                                        // get group have all subservice
                                        var groups = _lst_voucher_service_requirement.GroupBy(c => c.VSRequirementID)
                                            .Where(g => g.All(c => _lst_service.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid)));
                                        if (groups.Count() > 0)
                                        {
                                            foreach (var g in groups)
                                            {
                                                // subservice in group
                                                var services = _lst_service.Where(c => g.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid)).ToList();

                                                // check total
                                                if (g.First().Value > 0)
                                                {
                                                    if (services.Sum(c => c.Total_VAT) >= g.First().Value)
                                                    {
                                                        b3_1 = true;
                                                    }
                                                }
                                                else
                                                {
                                                    b3_1 = true;
                                                }

                                                // check connectionfee
                                                if (g.First().ConnectionFee > 0)
                                                {
                                                    var connectionFee = _lst_service_connection_fee.Where(c => services.Any(x => x.SubServiceTypeID == c.SubServiceTypeID)).Sum(c => c.Total_VAT);
                                                    if (connectionFee >= g.First().ConnectionFee)
                                                    {
                                                        b3_2 = true;
                                                    }
                                                }
                                                else
                                                {
                                                    b3_2 = true;
                                                }

                                                if (policy.ComboType == 1)
                                                {
                                                    if (b3_1 && b3_2)
                                                    {
                                                        b3 = true;
                                                        break;
                                                    }
                                                }
                                                else if (policy.ComboType == 2)
                                                {
                                                    if (!b3_1 || !b3_2)
                                                    {
                                                        // remove service invalid in only
                                                        _lst_service = _lst_service.Where(c => !services.Any(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid)).ToList();
                                                    }
                                                }
                                            }

                                            if (policy.ComboType == 2)
                                            {
                                                // any service ok in only then pass
                                                if (_lst_service?.Count > 0)
                                                {
                                                    b3 = true;
                                                }
                                            }
                                        }
                                        else
                                        {
                                            b3 = true;
                                        }
                                    }
                                    else
                                    {
                                        b3 = true;
                                    }

                                    var _lst_voucher_product = lst_voucher_product.Where(c => c.VoucherID == voucher.VoucherID).ToList();
                                    var _lst_product = lst_product.Where(c => _lst_voucher_product.Any(x => x.SubServiceTypeID == c.SubServiceTypeID && x.DeviceID == c.SubServiceID
                                        && x.DeviceStatus == c.StatusID && x.DeployTypeID == c.DeployTypeID && x.IsReturn == c.RevokeID
                                        && (x.FromPrice == 0 || x.FromPrice <= c.Total_VAT))
                                    ).ToList();

                                    bool b4 = false;

                                    if (voucher.ApplyTypeID == 3)
                                    {
                                        // sl theo tong tb
                                        int quantity = _lst_product.Sum(x => x.Qty);
                                        _lst_product = _lst_product.Where(c => _lst_voucher_product.Any(x => x.FromQuantity <= quantity && x.ToQuantity >= quantity)).ToList();
                                    }
                                    else
                                    {
                                        // sl theo tung tb
                                        _lst_product = _lst_product.Where(c => _lst_voucher_product.Any(x => x.FromQuantity <= c.Qty && x.ToQuantity >= c.Qty)).ToList();
                                    }

                                    if (_lst_product != null && _lst_product.Count > 0)
                                    {
                                        b4 = true;
                                    }

                                    if (b1 && b2 && b3 && b4)
                                    {
                                        voucher.Services = new List<Voucher_Model_Service>();
                                        foreach (var c in Utility.CloneObject(_lst_voucher_service))
                                        {
                                            var lst_s = _lst_service.Where(x => x.SubServiceID == c.SubServiceID && x.PrePaid == c.PrePaid).ToList();
                                            foreach (var s in lst_s)
                                            {
                                                if (s != null)
                                                {
                                                    float month_value = c.MonthValue > 0 ? c.MonthValue : 1;
                                                    if (month_value > s.MonthUsed && s.MonthUsed >= 0)
                                                    {
                                                        month_value = (float)(s.MonthUsed > 0 ? s.MonthUsed : 1);
                                                    }
                                                    c.Qty = s.Qty;
                                                    c.ServiceCode = s.ServiceCode;
                                                    c.DeployTypeID = s.DeployTypeID;
                                                    c.MonthUsed = (float)s.MonthUsed;
                                                    c.Discount = (c.Value * (decimal)(month_value) + c.ConnectionFee);

                                                    // phm
                                                    if (_lst_voucher_service.Any(x => x.ConnectionFee > 0))
                                                    {
                                                        var s_c = _lst_service_connection_fee.FirstOrDefault(x => x.SubServiceTypeID == s.SubServiceTypeID);
                                                        if (s_c != null)
                                                        {
                                                            c.SubServiceID = s_c.SubServiceID;
                                                            c.PrePaid = (float)s_c.PrePaid;
                                                            c.ServiceCode = s_c.ServiceCode;
                                                            c.DeployTypeID = s_c.DeployTypeID;
                                                            c.MonthUsed = (float)s_c.MonthUsed;
                                                            c.Discount = c.ConnectionFee;
                                                        }
                                                    }

                                                    voucher.Services.Add(c);
                                                }
                                            }
                                        }
                                        voucher.Products = new List<Voucher_Model_Product>();
                                        foreach (var c in Utility.CloneObject(_lst_voucher_product))
                                        {
                                            var lst_s = _lst_product.Where(x => x.SubServiceTypeID == c.SubServiceTypeID && x.SubServiceID == c.DeviceID
                                                && x.StatusID == c.DeviceStatus && x.DeployTypeID == c.DeployTypeID && x.RevokeID == c.IsReturn).ToList();
                                            foreach (var s in lst_s)
                                            {
                                                if (s != null)
                                                {
                                                    // voucher percent
                                                    if (lst_voucher_percent?.Count > 0)
                                                    {
                                                        var voucher_percent = lst_voucher_percent.FirstOrDefault(x => x.FName.Equals(voucher.VoucherCode, StringComparison.OrdinalIgnoreCase));
                                                        if (voucher_percent?.TValue > 0)
                                                        {
                                                            c.Value = Math.Round((s.Total_VAT / s.Qty) * (decimal)(voucher_percent?.TValue ?? 0) / 100, 0);
                                                        }
                                                    }

                                                    c.Qty = s.Qty;
                                                    c.ServiceCode = s.ServiceCode;
                                                    c.Discount = c.Value;
                                                    c.Total_VAT = s.Total_VAT;
                                                    voucher.Products.Add(c);
                                                }
                                            }
                                        }

                                        // phm tb remove service
                                        if (voucher.Products?.Count > 0 && voucher.Services?.Count > 0 && !voucher.Services.Any(c => c.Discount > 0))
                                        {
                                            voucher.Services = new List<Voucher_Model_Service>();
                                        }

                                        // max_quantity
                                        // one each sub-type
                                        int max_quantity = 0;
                                        // fgold
                                        if (voucher.PolicyGroupID == 103 || voucher.PolicyGroupID == 109)
                                        {
                                            max_quantity = 1;
                                        }
                                        // combo one each sub-type
                                        if (voucher.ComboType == 1 || max_quantity == 1)
                                        {
                                            voucher.Services = voucher.Services.GroupBy(c => c.SubServiceTypeID).Select(c =>
                                            {
                                                var s = c.OrderByDescending(x => x.Total_VAT).ThenBy(x => x.SubServiceID).First();
                                                s.Qty = 1;
                                                return s;
                                            }).ToList();
                                        }

                                        // max_quantity
                                        // one product
                                        max_quantity = 0;
                                        max_quantity = _lst_voucher_product.Max(c => c.MaxQuantity);
                                        // fgold
                                        if (voucher.PolicyGroupID == 103 || voucher.PolicyGroupID == 109)
                                        {
                                            max_quantity = 1;
                                        }
                                        if (max_quantity == 1)
                                        {
                                            var c = voucher.Products.OrderByDescending(x => x.Total_VAT).ThenBy(x => x.DeviceID).FirstOrDefault();
                                            c.Qty = 1;
                                            voucher.Products = new List<Voucher_Model_Product> { c };
                                        }

                                        decimal discount = 0;
                                        if (voucher.Services != null && voucher.Services.Count > 0)
                                        {
                                            discount = discount + voucher.Services.Sum(c => (c.Value * (decimal)(c.MonthValue > 0 ? c.MonthValue : 1) + c.ConnectionFee) * c.Qty);
                                        }
                                        if (voucher.Products != null && voucher.Products.Count > 0)
                                        {
                                            discount = discount + voucher.Products.Sum(c => c.Value * c.Qty);
                                        }
                                        voucher.Discount = discount;

                                        lst_voucher_output.Add(voucher);
                                    }
                                }
                            }
                        }
                    }

                    logs.Add(new Log_Model("lst_voucher_output", lst_voucher_output.Select(c => c.VoucherCode).Distinct().ToList()));

                    Utility.LogFull(log_time, "Voucher_GetList: 4: " + DateTime.Now, "");

                    if (input.evoucherList?.Count > 0)
                    {
                        List<string> lst_code_invalid = input.evoucherList
                            .Where(c => c.evoucherType == 1 && !lst_voucher_output.Any(x => x.VoucherCode.Equals(c.evoucherCode, StringComparison.OrdinalIgnoreCase)))
                            .Select(c => c.evoucherCode).ToList();

                        if (lst_code_invalid != null && lst_code_invalid.Count > 0)
                        {
                            res.Error.Add("Phiếu mua hàng bạn chọn " + string.Join(", ", lst_code_invalid) + " đã hết hiệu lực. Vui lòng liên hệ quản lý chi nhánh.");
                        }
                    }

                    var lst_voucher_ok = new List<Voucher_Model>();

                    if (checkLimit)
                    {
                        var limit_type_3_4_policyIds = lst_voucher_output.Where(c => c.LimitType == 3 || c.LimitType == 4).Select(c => c.PolicyID).ToList();
                        if (limit_type_3_4_policyIds != null && limit_type_3_4_policyIds.Count > 0)
                        {
                            lst_voucher_ok.AddRange(lst_voucher_output.Where(c => limit_type_3_4_policyIds.Contains(c.PolicyID)).ToList());
                        }

                        var limit_type_2_vouchers = lst_voucher_output.Where(c => c.LimitType == 2).ToList();
                        if (limit_type_2_vouchers != null && limit_type_2_vouchers.Count > 0)
                        {
                            var inputVoucherCodes = input.evoucherList?.Where(c => c.evoucherType == 1).Select(c => c.evoucherCode).ToList();
                            var limit_ok_vouchers = Campaign_CheckQuota(logs, connection, limit_type_2_vouchers, inputVoucherCodes, res.Error);
                            lst_voucher_ok.AddRange(limit_ok_vouchers);
                        }

                        var limit_type_1_vouchers = lst_voucher_output.Where(c => c.LimitType == 1).ToList();
                        if (limit_type_1_vouchers != null && limit_type_1_vouchers.Count > 0)
                        {
                            var inputVoucherCodes = input.evoucherList?.Where(c => c.evoucherType == 1).Select(c => c.evoucherCode).ToList();
                            var limit_ok_vouchers = Voucher_CheckLimit(logs, connection, limit_type_1_vouchers, input.SaleInfor.SaleID, input.CustomerInfor.LocationID, inputVoucherCodes, res.Error);
                            lst_voucher_ok.AddRange(limit_ok_vouchers);
                        }

                        var limit_type_5_vouchers = lst_voucher_output.Where(c => c.LimitType == 5).ToList();
                        if (limit_type_5_vouchers != null && limit_type_5_vouchers.Count > 0)
                        {
                            var inputVoucherCodes = input.evoucherList?.Where(c => c.evoucherType == 1).Select(c => c.evoucherCode).ToList();
                            var limit_phase_1_ok_vouchers = Campaign_CheckQuota(logs, connection, limit_type_5_vouchers, inputVoucherCodes, res.Error);
                            if (limit_phase_1_ok_vouchers?.Count > 0)
                            {
                                var limit_ok_vouchers = Voucher_CheckLimit(logs, connection, limit_phase_1_ok_vouchers, input.SaleInfor.SaleID, input.CustomerInfor.LocationID, inputVoucherCodes, res.Error);
                                lst_voucher_ok.AddRange(limit_ok_vouchers);
                            }

                        }

                        Utility.LogFull(log_time, "Voucher_GetList: 5: " + DateTime.Now, "");
                    }
                    else
                    {
                        lst_voucher_ok.AddRange(lst_voucher_output);
                    }

                    logs.Add(new Log_Model("lst_voucher_ok", lst_voucher_ok.Select(c => c.VoucherCode).Distinct().ToList()));

                    Utility.LogFull(log_time, "Voucher_GetList: 6: " + DateTime.Now, "");

                    foreach (var v in lst_voucher_ok)
                    {
                        res.Vouchers.Add(v);
                        if (v.Services != null && v.Services.Count > 0)
                        {
                            foreach (var s in v.Services)
                            {
                                res.LstVoucher.Add(new PolicyVoucher2
                                {
                                    PolicyID = v.PolicyID,
                                    VoucherID = v.VoucherID,
                                    VoucherCode = v.VoucherCode,
                                    Description = v.VoucherName,
                                    PolicyGroupID = v.PolicyGroupID,
                                    VoucherTypeID = v.VoucherTypeID,
                                    PromotionTypeID = v.PromotionTypeID,
                                    RegisterTypeID = v.RegisterTypeID,
                                    ApplyTypeID = v.ApplyTypeID,
                                    ApplyTypeAltID = v.ApplyTypeAltID,
                                    Note = "",
                                    ToDate = string.Format(System.Globalization.CultureInfo.InvariantCulture, "{0:dd/MM/yyyy}", v.ToDate)
                                });
                            }
                        }
                        if (v.Products != null && v.Products.Count > 0)
                        {
                            foreach (var s in v.Products)
                            {
                                res.LstVoucher.Add(new PolicyVoucher2
                                {
                                    PolicyID = v.PolicyID,
                                    VoucherID = v.VoucherID,
                                    VoucherCode = v.VoucherCode,
                                    Description = v.VoucherName,
                                    PolicyGroupID = v.PolicyGroupID,
                                    VoucherTypeID = v.VoucherTypeID,
                                    PromotionTypeID = v.PromotionTypeID,
                                    RegisterTypeID = v.RegisterTypeID,
                                    ApplyTypeID = v.ApplyTypeID,
                                    ApplyTypeAltID = v.ApplyTypeAltID,
                                    Note = "",
                                    ToDate = string.Format(System.Globalization.CultureInfo.InvariantCulture, "{0:dd/MM/yyyy}", v.ToDate)
                                });
                            }
                        }
                    }

                    Utility.LogFull(log_time, "Voucher_GetList: 7: " + DateTime.Now, "");
                }
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Voucher_GetList Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            if (logs?.Count > 0)
            {
                var totalSeconds = (logs.Max(c => c.date) - logs.Min(c => c.date)).TotalSeconds;
                if (totalSeconds > 15)
                {
                    Utility.LogError("time_out: " + totalSeconds, "");
                }
            }

            return res;
        }

        public static List<Voucher_GetProgram_Model_Voucher> Voucher_GetProgram(List<Log_Model> logs, SqlConnection connection, Voucher_GetProgram_Model input)
        {
            var param = new
            {
                SaleTeamID = input.SaleTeamID,
                SaleChannelID = input.SaleChannelID,
                ObjID = input.ObjID,
                SubCompanyID = input.SubCompanyID,
                LocationID = input.LocationID,
                BranchCode = input.BranchCode,
                DistrictID = input.DistrictID,
                WardID = input.WardID,
                BuildingID = input.BuildingID,
                CusTypeID = input.CusTypeID,
                CusTypeL2ID = input.CusTypeL2ID,
                ObjectTypeID = input.ObjectTypeID,
                CustomerRank = input.CustomerRank,
                CustomerTypes = Utility.Converter.ToXml(input.CustomerTypes),
                PaymentTypeID = input.PaymentTypeID,
                CrossSellingLocation = input.CrossSellingLocation
            };

            logs.Add(new Log_Model("GetProgram Req", param));

            var lst = Utility.Query<Voucher_GetProgram_Model_Voucher>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_GetProgram",
                param
            ).ToList();

            return lst;
        }

        public static List<Voucher_Model> Voucher_CheckLimit(List<Log_Model> logs, SqlConnection connection, List<Voucher_Model> vouchers, int saleID, int locationID, List<string> evoucherCodes, List<string> errors)
        {
            var lst = new List<Voucher_Model>();

            try
            {
                var lst_msg_err = new List<string>();
                //int unitParentID = 0;
                string unitCode = "";
                string unitParentCode = "";
                if (saleID > 0)
                {
                    locationID = 0;
                    var employee = Policy_GetEmployeeInfo(logs, saleID);
                    //unitParentID = employee?.parentId ?? 0;
                    unitCode = employee?.code ?? "";
                    unitParentCode = employee?.parentCode ?? "";

                    logs.Add(new Log_Model("Policy_GetEmployeeInfo ", new { saleID = saleID, employee = employee }));
                }

                // fix tong han muc theo policyGroupID
                //foreach (var policyID in vouchers.Select(c => c.PolicyID).Distinct().ToList())
                foreach (var policyGroupID in vouchers.Select(c => c.PolicyGroupID).Distinct().ToList())
                {
                    decimal total_available = 0;
                    decimal total_discount = 0;
                    int policy_err = 0;

                    //foreach (var voucher in vouchers.Where(c => c.PolicyID == policyID).ToList())
                    foreach (var voucher in vouchers.Where(c => c.PolicyGroupID == policyGroupID).ToList())
                    {
                        decimal available = Utility.QueryFirstOrDefault<decimal>(
                            connection,
                            "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_GetLimit_GeneralCode",
                            new
                            {
                                VoucherCode = voucher.VoucherCode,
                                //UnitID = unitParentID,
                                UnitCode = unitCode,
                                UnitParentCode = unitParentCode,
                                LocationID = locationID
                            }
                        );

                        total_available = available;

                        if (available >= voucher.Discount && available > 0 && voucher.Discount > 0)
                        {
                            lst.Add(voucher);
                        }

                        if (evoucherCodes?.Count > 0 && evoucherCodes.Any(code => voucher.VoucherCode.Equals(code, StringComparison.OrdinalIgnoreCase)))
                        {
                            total_discount += voucher.Discount;
                            if (available < voucher.Discount)
                            {
                                policy_err++;
                                lst_msg_err.Add("Hạn mức khả dụng còn lại " + voucher.VoucherCode + " chỉ được áp dụng tối đa " + string.Format("{0:n0}", available) + " VNĐ.");
                            }
                        }
                    }

                    if (total_available < total_discount && total_discount > 0 && policy_err == 0)
                    {
                        //lst_msg_err.Add("Hạn mức khả dụng còn lại " + string.Join("+", vouchers.Where(c => c.PolicyID == policyID).Select(c => c.VoucherCode).ToList()) + " chỉ được áp dụng tối đa " + string.Format("{0:n0}", total_available) + " VNĐ.");
                        lst_msg_err.Add("Hạn mức khả dụng còn lại " + string.Join("+", vouchers.Where(c => c.PolicyGroupID == policyGroupID).Select(c => c.VoucherCode).ToList()) + " chỉ được áp dụng tối đa " + string.Format("{0:n0}", total_available) + " VNĐ.");
                    }
                }

                if (lst_msg_err.Count > 0)
                {
                    errors.Add(string.Join(" ", lst_msg_err.Distinct()) + " Vui lòng kiểm tra lại thông tin đơn hàng hoặc liên hệ BGĐ CN để được cấp thêm.");
                }

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Voucher_CheckLimit Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<Policy_GetInfo_Model> Policy_GetInfo(List<Log_Model> logs, SqlConnection connection, List<int> policyIds)
        {
            var lst = new List<Policy_GetInfo_Model>();

            try
            {
                if (policyIds == null || policyIds.Count == 0)
                    return lst;

                lst = Utility.Query<Policy_GetInfo_Model>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "Policy_GetInfo",
                        policyIds = string.Join(",", policyIds)
                    }
                ).ToList();

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Policy_GetInfo Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<int> Voucher_GetPolicyExtraType(List<Log_Model> logs, SqlConnection connection)
        {
            var lst = new List<int>();

            try
            {
                lst = Utility.Query<int>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "Voucher_GetPolicyExtraType"
                    }
                ).ToList();

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Voucher_GetPolicyExtraType Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<int> Policy_GetProgramIDByCode(List<Log_Model> logs, SqlConnection connection, List<string> policyCodes)
        {
            var lst = new List<int>();

            try
            {
                if (policyCodes == null || policyCodes.Count == 0)
                    return lst;

                string sql = @"
SELECT DISTINCT g.ID AS ProgramID
FROM PowerInside.dbo.DXBusinessPolicy_Policy AS p (NOLOCK)
JOIN (SELECT value AS PolicyCode FROM STRING_SPLIT(@PolicyCodes,',')) AS t ON t.PolicyCode = p.Code
JOIN PowerInside.dbo.DXBusinessPolicy_Program AS g (NOLOCK) ON g.PolicyID = p.ID
";

                lst = Utility.Query<int>(
                    connection,
                    sql,
                    new
                    {
                        PolicyCodes = string.Join(",", policyCodes)
                    },
                    commandType: CommandType.Text
                ).ToList();

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Policy_GetInfo Policy_GetCodeByProgramID", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<Voucher_Model> Campaign_CheckQuota(List<Log_Model> logs, SqlConnection connection, List<Voucher_Model> vouchers, List<string> evoucherCodes, List<string> errors)
        {
            var lst = new List<Voucher_Model>();

            try
            {
                var lst_limit_err = new List<string>();
                var lst_qty_err = new List<string>();

                if (vouchers == null || vouchers.Count == 0)
                    return lst;

                var lst2 = Utility.Query<Campaign_CheckQuota_Model>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "Campaign_CheckQuota",
                        policyIds = string.Join(",", vouchers.Select(c => c.PolicyID).Distinct().ToList())
                    }
                ).ToList();

                foreach (var group in lst2.GroupBy(c => c.CampaignID))
                {
                    var campaign = group.FirstOrDefault();
                    if (campaign.MoneyLimit > 0)
                    {
                        decimal total_discount = 0;
                        foreach (var voucher in vouchers.Where(c => group.Any(x => x.PolicyID == c.PolicyID)))
                        {
                            if (campaign.MoneyAvailable >= voucher.Discount)
                            {
                                lst.Add(voucher);
                            }

                            // validate
                            if (evoucherCodes?.Count > 0 && evoucherCodes.Any(code => voucher.VoucherCode.Equals(code, StringComparison.OrdinalIgnoreCase)))
                            {
                                total_discount += voucher.Discount;

                                if (campaign.MoneyAvailable < voucher.Discount)
                                {
                                    var codes = evoucherCodes.Where(code => voucher.VoucherCode.Equals(code, StringComparison.OrdinalIgnoreCase)).ToList();

                                    lst_limit_err.Add("Ngân sách chiến dịch " + string.Join(",", codes) + " đã hết.");

                                    evoucherCodes = evoucherCodes.Where(code => !codes.Contains(code)).ToList();
                                }
                            }
                        }

                        // validate 
                        if (evoucherCodes?.Count > 0)
                        {
                            if (total_discount > campaign.MoneyAvailable && lst_limit_err.Count == 0)
                            {
                                var codes = evoucherCodes.Where(code => vouchers.Any(voucher => voucher.VoucherCode.Equals(code, StringComparison.OrdinalIgnoreCase))).ToList();

                                lst_limit_err.Add("Ngân sách chiến dịch " + string.Join(",", codes) + " đã hết.");

                                evoucherCodes = evoucherCodes.Where(code => !codes.Contains(code)).ToList();
                            }
                        }
                    }
                    else if (campaign.Limit > 0)
                    {
                        int total_qty = 0;
                        foreach (var voucher in vouchers.Where(c => group.Any(x => x.PolicyID == c.PolicyID)))
                        {
                            if (campaign.Available > 0)
                            {
                                lst.Add(voucher);
                            }

                            // validate
                            if (evoucherCodes?.Count > 0 && evoucherCodes.Any(code => voucher.VoucherCode.Equals(code, StringComparison.OrdinalIgnoreCase)))
                            {
                                total_qty += 1;

                                if (campaign.Available == 0)
                                {
                                    var codes = evoucherCodes.Where(code => voucher.VoucherCode.Equals(code, StringComparison.OrdinalIgnoreCase)).ToList();

                                    lst_qty_err.Add("Ngân sách chiến dịch " + string.Join(",", codes) + " đã hết.");

                                    evoucherCodes = evoucherCodes.Where(code => !codes.Contains(code)).ToList();
                                }
                            }
                        }

                        // validate
                        if (evoucherCodes?.Count > 0)
                        {
                            if (total_qty > campaign.Available && lst_qty_err.Count == 0)
                            {
                                var codes = evoucherCodes.Where(code => vouchers.Any(voucher => voucher.VoucherCode.Equals(code, StringComparison.OrdinalIgnoreCase))).ToList();

                                lst_qty_err.Add("Ngân sách chiến dịch " + string.Join(",", codes) + " đã hết.");

                                evoucherCodes = evoucherCodes.Where(code => !codes.Contains(code)).ToList();
                            }
                        }
                    }
                }

                if (lst_qty_err?.Count > 0)
                {
                    errors.AddRange(lst_qty_err.Distinct().ToList());
                }

                if (lst_limit_err?.Count > 0)
                {
                    errors.AddRange(lst_limit_err.Distinct().ToList());
                }

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Campaign_CheckQuota Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static int Branch_SubCompanyID(List<Log_Model> logs, SqlConnection connection, int locationId, int branchCode)
        {
            try
            {
                var res = Utility.QueryFirstOrDefault<int>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "Branch_SubCompanyID",
                        locationId = locationId,
                        branchCode = branchCode
                    }
                );

                return res;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Branch_SubCompanyID Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return 0;
        }

        public static List<int> SubService_GetConnectionFee(List<Log_Model> logs, SqlConnection connection)
        {
            var lst = new List<int>();

            try
            {
                lst = Utility.Query<int>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "SubService_GetConnectionFee"
                    }
                ).ToList();

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Branch_SubCompanyID Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<BaseConfig_Model> BaseConfig_Get(List<Log_Model> logs, SqlConnection connection, int groupID)
        {
            var lst = new List<BaseConfig_Model>();

            try
            {
                if (connection != null)
                {
                    lst = Utility.Query<BaseConfig_Model>(
                        connection,
                        "PowerInside.dbo.OS6_DXBusinessPolicy_BaseConfig_Get",
                        new
                        {
                            GroupID = groupID
                        }
                    ).ToList();
                }
                else
                {
                    lst = Utility.Query<BaseConfig_Model>(
                        Utility.ConnWrite,
                        "PowerInside.dbo.OS6_DXBusinessPolicy_BaseConfig_Get",
                        new
                        {
                            GroupID = groupID
                        }
                    ).ToList();
                }

                return lst;
            }
            catch (Exception ex)
            {
                if (logs != null)
                {
                    logs.Add(new Log_Model("BaseConfig_Get Exception", ex.ToString()));
                }

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static Policy_GetEmployeeInfo_Model Policy_GetEmployeeInfo(List<Log_Model> logs, int saleID = 0, int objID = 0)
        {
            try
            {
                if (saleID == 0 && objID == 0)
                    return null;

                var res = Utility.GetAsync(Utility.bpapi_fpt_vn + "/api/ins/GetEmployeeInfo?saleID=" + saleID + "&objID=" + objID).Result;
                if (res.result == 1)
                {
                    return JsonConvert.DeserializeObject<ResponseModel<Policy_GetEmployeeInfo_Model>>(res.data)?.data;
                }
                else
                {
                    logs.Add(new Log_Model("Policy_GetEmployeeInfo Exception", res.error));
                }
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Policy_GetEmployeeInfo Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return null;
        }
        #endregion

        #region get_info
        public static Voucher_GetInfo_Model Voucher_GetInfo(List<Log_Model> logs, SalePolicyGetInforEVC input)
        {
            var res = new Voucher_GetInfo_Model
            {
                LstVoucher = new List<SalePlatformVoucherValueInfor>(),
                LstInput = new List<Order_Model_Voucher>()
            };
            try
            {
                bool full_value_x_y = !(BaseConfig_Get(logs, null, 44)?.Count > 0);

                bool log_time = Utility.GetConfig<dynamic>(23)?.Count > 0;

                Utility.LogFull(log_time, "Voucher_GetInfo: 1: " + DateTime.Now, "");

                using (var connection = new SqlConnection(Utility.ConnWrite))
                {
                    var lst_campaign = Campaign_GetPrefix(logs);
                    var lst_input_voucher = input.evoucherList.Where(c => c.evoucherType == 1 || c.evoucherType == 2)
                        .Select(c =>
                        {
                            var campaign = lst_campaign.FirstOrDefault(x => c.evoucherType == 2 && c.evoucherCode.ToUpper().StartsWith(x.Prefix.ToUpper()));
                            string privateCode = null;
                            if (campaign != null)
                            {
                                if (campaign.ApplyTypeID == 1)
                                    privateCode = c.evoucherCode;
                                else
                                    privateCode = c.evoucherCode.Substring(campaign?.Prefix?.Length ?? 0);
                            }
                            return new Order_Model_Voucher
                            {
                                VoucherCode = c.evoucherCode,
                                VoucherType = c.evoucherType,
                                CampaignID = campaign?.CampaignID ?? 0,
                                Prefix = campaign?.Prefix ?? "",
                                PrivateCode = privateCode
                            };
                        }).ToList();

                    logs.Add(new Log_Model("lst_input_voucher", lst_input_voucher));

                    Utility.LogFull(log_time, "Voucher_GetInfo: 2: " + DateTime.Now, "");

                    if (lst_input_voucher.Any(c => c.VoucherType == 1 || c.CampaignID > 0))
                    {
                        // for recheck
                        res.LstInput = lst_input_voucher.Where(c => c.VoucherType == 1 || c.CampaignID > 0).ToList();

                        var lst_voucher = new List<Voucher_Model>();
                        if (lst_input_voucher.Any(c => c.CampaignID > 0))
                        {
                            var res_get_list = Voucher_GetList(logs, input, 2);
                            if (res_get_list?.Vouchers?.Count > 0)
                            {
                                // cant update value
                                //var lst_voucher_private = lst_input_voucher.Where(c => c.CampaignID > 0).ToList();
                                foreach (var v in lst_input_voucher.Where(c => c.CampaignID > 0).ToList())
                                {
                                    var p = res_get_list.Vouchers.Where(c => c.CampaignID == v.CampaignID).OrderByDescending(c => c.Priority).FirstOrDefault();
                                    v.PolicyID = p?.PolicyID ?? 0;
                                    v.VoucherID = p?.VoucherID ?? 0;
                                }

                                /*
                                if (lst_input_voucher.Any(c => c.PolicyID > 0))
                                {
                                    var lst_private_code = PrivateCode_UpdateVoucherID(logs, connection, lst_input_voucher.Where(c => c.PolicyID > 0).ToList());
                                    if (lst_private_code?.Count > 0)
                                    {
                                        logs.Add(new Log_Model("lst_private_code", lst_private_code));
                                        lst_voucher.AddRange(res_get_list.Vouchers.Where(c => lst_private_code.Any(x => x.VoucherID == c.VoucherID)).ToList());
                                    }
                                }
                                */
                            }

                            logs.Add(new Log_Model("lst_input_voucher", lst_input_voucher));

                            Utility.LogFull(log_time, "Voucher_GetInfo: 3: " + DateTime.Now, "");

                            // update and clear private code
                            var lst_private_code = PrivateCode_UpdateVoucherID(logs, connection, lst_input_voucher.Where(c => c.CampaignID > 0).ToList());
                            if (lst_private_code?.Count > 0)
                            {
                                logs.Add(new Log_Model("lst_private_code", lst_private_code));
                                foreach (var private_code in lst_private_code)
                                {
                                    var lst_voucher_private = res_get_list.Vouchers.Where(c => c.VoucherID == private_code.VoucherID).ToList();
                                    lst_voucher_private.ForEach(c => c.ExpiredDate = private_code.ExpiredDate);
                                    lst_voucher.AddRange(lst_voucher_private);
                                }
                            }

                            Utility.LogFull(log_time, "Voucher_GetInfo: 4: " + DateTime.Now, "");
                        }

                        if (lst_input_voucher.Any(c => c.VoucherType == 1))
                        {
                            var lst_voucher_general = lst_input_voucher.Where(c => c.VoucherType == 1).ToList();
                            // khong check han muc
                            var res_get_list = Voucher_GetList(logs, input, -1, false);
                            if (res_get_list?.Vouchers?.Count > 0)
                            {
                                lst_voucher.AddRange(res_get_list.Vouchers.Where(c => lst_voucher_general.Any(x => x.VoucherCode.Equals(c.VoucherCode, StringComparison.OrdinalIgnoreCase))).ToList());
                            }

                            Utility.LogFull(log_time, "Voucher_GetInfo: 5: " + DateTime.Now, "");
                        }

                        if (lst_voucher?.Count > 0)
                        {
                            logs.Add(new Log_Model("lst_voucher", lst_voucher.Select(c => c.VoucherCode).Distinct().ToList()));

                            Utility.LogFull(log_time, "Voucher_GetInfo: 6: " + DateTime.Now, "");

                            var serviceCodes = new List<int>();
                            lst_campaign = Campaign_GetVendorContract(logs, connection);
                            foreach (var voucher in lst_voucher)
                            {
                                var lst_apply = new List<Apply>();
                                var input_voucher = lst_input_voucher.FirstOrDefault(c => c.VoucherCode.Equals(voucher.VoucherCode, StringComparison.OrdinalIgnoreCase));
                                int voucher_type = input_voucher?.VoucherType ?? 0;
                                string voucher_code = voucher.VoucherCode;
                                string map_voucher_code = null;
                                if (voucher.VoucherTypeID == 2)
                                {
                                    input_voucher = lst_input_voucher.FirstOrDefault(c => 
                                                                            c.VoucherID == voucher.VoucherID
                                                                            && !res.LstVoucher.Exists(x=>x.evoucherCode == c.VoucherCode));
                                    voucher_type = input_voucher?.VoucherType ?? 0;
                                    voucher_code = input_voucher?.VoucherCode ?? "";
                                    map_voucher_code = voucher.VoucherCode;
                                }
                                var vendorContract = lst_campaign.FirstOrDefault(c => c.CampaignID == voucher.CampaignID)?.VendorContract;

                                if (voucher.Services?.Count > 0)
                                {
                                    var lst_billing_type = SubService_GetBillingType(logs, connection, voucher.Services.Select(c => c.SubServiceID).Distinct().ToList());
                                    foreach (var s in voucher.Services)
                                    {
                                        serviceCodes.Add(s.ServiceCode);
                                        var billing_type = lst_billing_type.FirstOrDefault(c => c.SubServiceID == s.SubServiceID);
                                        var is_deduct_order = (s.PrePaid > 0 || s.MonthUsed == 0 || billing_type?.BillingTypeID > 0) ? 1 : 0;
                                        for (int i = 0; i < s.Qty; i++)
                                        {
                                            lst_apply.Add(new Apply
                                            {
                                                ServiceID = s.ServiceID,
                                                SubServiceTypeID = s.SubServiceTypeID,
                                                SubServiceID = s.SubServiceID,
                                                ServiceCode = s.ServiceCode,
                                                StatusID = -1,
                                                RevokeID = -1,
                                                DeployTypeID = s.DeployTypeID,
                                                Value = voucher.VoucherName,
                                                DiscountVAT = s.Discount,
                                                FullDiscountVAT = ((full_value_x_y && s.MonthValue > 0) ? s.Value * (decimal)s.MonthValue : 0),
                                                IsDeductOrder = is_deduct_order
                                            });
                                        }
                                    }
                                }
                                if (voucher.Products?.Count > 0)
                                {
                                    foreach (var s in voucher.Products)
                                    {
                                        serviceCodes.Add(s.ServiceCode);
                                        for (int i = 0; i < s.Qty; i++)
                                        {
                                            lst_apply.Add(new Apply
                                            {
                                                ServiceID = s.ServiceID,
                                                SubServiceTypeID = s.SubServiceTypeID,
                                                SubServiceID = s.DeviceID,
                                                ServiceCode = s.ServiceCode,
                                                StatusID = s.DeviceStatus,
                                                RevokeID = s.IsReturn,
                                                DeployTypeID = s.DeployTypeID,
                                                Value = voucher.VoucherName,
                                                DiscountVAT = s.Discount,
                                                IsDeductOrder = 1
                                            });
                                        }
                                    }

                                }

                                var info = new SalePlatformVoucherValueInfor
                                {
                                    evoucherCode = voucher_code,
                                    VoucherType = voucher_type,
                                    VoucherTypeL2 = 1,
                                    RegisterTypeID = voucher.RegisterTypeID,
                                    PolicyGroupID = voucher.PolicyGroupID,
                                    ApplyTypeID = voucher.ApplyTypeAltID,
                                    PromotionTypeID = voucher.PromotionTypeID,
                                    mapEvoucherCode = map_voucher_code,
                                    ExpiredDate = voucher.ExpiredDate,
                                    VendorContract = vendorContract,
                                    Apply = lst_apply
                                };
                                res.LstVoucher.Add(info);
                            }

                            Utility.LogFull(log_time, "Voucher_GetInfo: 7: " + DateTime.Now, "");

                            // vat
                            var lst_service_code = ServiceCode_GetVATRate(logs, connection, serviceCodes.Distinct().ToList());
                            foreach (var info in res.LstVoucher)
                            {
                                foreach (var a in info.Apply)
                                {
                                    var s = lst_service_code.FirstOrDefault(c => c.ServiceCode == a.ServiceCode);
                                    a.Discount = Utility.PriceNoVAT(a.DiscountVAT, s?.VATRate);
                                }
                            }

                            if (res.LstVoucher?.Count > 0)
                            {
                                res.LstVoucher = res.LstVoucher.Where(c => c.Apply?.Count > 0).ToList();
                            }

                            Utility.LogFull(log_time, "Voucher_GetInfo: 8: " + DateTime.Now, "");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Voucher_GetInfo Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            if (logs?.Count > 0)
            {
                var totalSeconds = (logs.Max(c => c.date) - logs.Min(c => c.date)).TotalSeconds;
                if (totalSeconds > 15)
                {
                    Utility.LogError("time_out: " + totalSeconds, "");
                }
            }

            return res;
        }

        public static List<Campaign_GetPrefix_Model> Campaign_GetPrefix(List<Log_Model> logs)
        {
            var lst = new List<Campaign_GetPrefix_Model>();

            try
            {
                lst = Utility.Query<Campaign_GetPrefix_Model>(
                    Utility.ConnWrite,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "Campaign_GetPrefix"
                    }
                ).ToList();

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Campaign_GetPrefix Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<Campaign_GetPrefix_Model> Campaign_GetVendorContract(List<Log_Model> logs, SqlConnection connection)
        {
            var lst = new List<Campaign_GetPrefix_Model>();

            try
            {
                lst = Utility.Query<Campaign_GetPrefix_Model>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "Campaign_GetVendorContract",
                    }
                ).ToList();

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Campaign_GetVendorContract Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<SubService_GetBillingType_Model> SubService_GetBillingType(List<Log_Model> logs, SqlConnection connection, List<int> subServiceIds)
        {
            var lst = new List<SubService_GetBillingType_Model>();

            try
            {
                if (subServiceIds == null || subServiceIds.Count == 0)
                    return lst;

                lst = Utility.Query<SubService_GetBillingType_Model>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "SubService_GetBillingType",
                        subServiceIds = string.Join(",", subServiceIds)
                    }
                ).ToList();

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("SubService_GetBillingType Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<ServiceCode_GetVATRate_Model> ServiceCode_GetVATRate(List<Log_Model> logs, SqlConnection connection, List<int> serviceCodes)
        {
            var lst = new List<ServiceCode_GetVATRate_Model>();

            try
            {
                if (serviceCodes == null || serviceCodes.Count == 0)
                    return lst;

                lst = Utility.Query<ServiceCode_GetVATRate_Model>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy",
                    new
                    {
                        actionName = "ServiceCode_GetVATRate",
                        serviceCodes = string.Join(",", serviceCodes)
                    }
                ).ToList();

                return lst;
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("ServiceCode_GetVATRate Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static List<Order_Model_Voucher> PrivateCode_UpdateVoucherID(List<Log_Model> logs, SqlConnection connection, List<Order_Model_Voucher> vouchers)
        {
            return Utility.Query<Order_Model_Voucher>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy",
                new
                {
                    actionName = "PrivateCode_UpdateVoucherID",
                    xmlVouchers = Utility.Converter.ToXml(vouchers)
                }
            ).ToList();
        }

        public static List<SalePlatformVoucherValueInfor> Policy_GetVoucherRequestOrder(List<Log_Model> logs, string orderCode)
        {
            var lst = new List<SalePlatformVoucherValueInfor>();

            try
            {
                string data = JsonConvert.SerializeObject(new { OrderCode = orderCode });
                var res = Utility.PostAsync(Utility.bpapi_fpt_vn + "/api/ins/policy/GetVoucherRequestOrder", data).Result;
                if (res.result == 1)
                {
                    lst = JsonConvert.DeserializeObject<ResponseModels<SalePlatformVoucherValueInfor>>(res.data)?.data;

                    logs.Add(new Log_Model("GetVoucherRequestOrder", lst.Select(c => c.evoucherCode).ToList()));

                    return lst;
                }
                else
                {
                    logs.Add(new Log_Model("Policy_GetVoucherRequestOrder Exception", res.error));
                }
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Policy_GetVoucherRequestOrder Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return lst;
        }

        public static string FGold_Validate(List<Log_Model> logs, int objID, List<int> subServiceTypeIDs, List<DateTime?> expiredDates, string strListPrivateCode, int saleID = 0)
        {
            try
            {
                logs.Add(new Log_Model("FGold_Validate", new { objID = objID, subServiceTypeIDs = subServiceTypeIDs, expiredDates = expiredDates, strListPrivateCode = strListPrivateCode }));

                if (expiredDates?.Count > 0 && expiredDates.Any(c => c < DateTime.Now))
                {
                    return "Phiếu mua hàng bạn nhập đã hết hiệu lực.";
                }

                var loy = LoyaltyFunc.contracts_customer_brief(logs, objID);

                    int result = Utility.QueryFirstOrDefault<int>(
                        Utility.ConnWrite,
                        "PowerInside.dbo.OS6_FPTVoucher_Policy",
                        new
                        {
                            actionName = "SchedulePromotion_Check",
                            objID = objID,
                            subServiceTypeIds = subServiceTypeIDs?.Count > 0 ? string.Join(",", subServiceTypeIDs) : "",
                            strListPrivateCode = strListPrivateCode ?? ""
                        }
                    );

                logs.Add(new Log_Model("FGold_Validate result", result));

                    if (result > 0)
                    {
                        return "Khách hàng đang có phiếu mua hàng khác chưa sử dụng hoàn tất.";
                    }

                if ((loy?.loyaltyStatus ?? -1) != 1)
                {
                    if (Utility.environment == "stag")
                    {
                        // bypass for test
                        if (PolicyService.BaseConfig_Get(logs, null, 56)?.Any(c => c.FValue == saleID) == true)
                        {
                            return "";
                        }
                    }

                    // config sub type not check khtt
                    if (PolicyService.BaseConfig_Get(logs, null, 78)?.Any(c => subServiceTypeIDs?.Contains(c.FValue) == true) == true)
                    {
                        return "";
                    }

                    return "Hợp đồng chưa tham gia KHTT.";
                }
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("FGold_Validate Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }

            return "";
        }
        #endregion

        #region redeem
        public static bool Voucher_Redeem(List<Log_Model> logs, SqlConnection connection, SqlTransaction transaction, SalePolicyRedeemEVC input)
        {
            try
            {
                var lst_campaign = Campaign_GetPrefix(logs);
                var lst_input_voucher = input.evoucherList.Where(c => c.evoucherType == 1 || c.evoucherType == 2)
                    .Select(c =>
                    {
                        var campaign = lst_campaign.FirstOrDefault(x => c.evoucherType == 2 && c.evoucherCode.ToUpper().StartsWith(x.Prefix.ToUpper()));
                        string privateCode = null;
                        if (campaign != null)
                        {
                            if (campaign.ApplyTypeID == 1)
                                privateCode = c.evoucherCode;
                            else
                                privateCode = c.evoucherCode.Substring(campaign?.Prefix?.Length ?? 0);
                        }
                        return new Order_Model_Voucher
                        {
                            VoucherCode = c.evoucherCode,
                            VoucherType = c.evoucherType,
                            CampaignID = campaign?.CampaignID ?? 0,
                            Prefix = campaign?.Prefix ?? "",
                            PrivateCode = privateCode
                        };
                    }).ToList();

                lst_input_voucher = lst_input_voucher.Where(c => c.VoucherType == 1 || c.CampaignID > 0).ToList();

                logs.Add(new Log_Model("lst_input_voucher", lst_input_voucher));

                if (lst_input_voucher?.Count > 0)
                {
                    List<int> limitTypes = new List<int>();

                    foreach (var input_voucher in lst_input_voucher)
                    {
                        if (GeneralCode_CheckRedeem(connection, transaction, input.objId, input.RegCode, input_voucher.VoucherCode))
                        {
                            if (input.KeepStatus == 2)
                            {
                                GeneralCode_UpdateKeepStatus(connection, transaction, input.objId, input.RegCode, input_voucher.VoucherCode);
                            }
                            continue;//
                        }
                        else
                        {
                            // redeem 2 truoc keep thi redeem luon
                            if (input.KeepStatus == 2)
                            {
                                input.KeepStatus = 0;
                            }
                        }

                        // get policyid, limit type
                        var map_voucher = VoucherRedeem_GetInfo(connection, transaction, input_voucher.VoucherCode, input_voucher.PrivateCode);
                        if (map_voucher == null)
                        {
                            return false;
                        }

                        input_voucher.PolicyID = map_voucher.PolicyID;
                        input_voucher.LimitType = map_voucher.LimitType;
                        if (!string.IsNullOrEmpty(input_voucher.PrivateCode) && !string.IsNullOrEmpty(map_voucher.VoucherCode))
                        {
                            // map voucher_code for private_code
                            input_voucher.MapVoucherCode = map_voucher.VoucherCode;
                        }

                        logs.Add(new Log_Model("input_voucher", input_voucher));

                        long generalCodeID = GeneralCode_Add(logs, connection, transaction, input, input_voucher);

                        logs.Add(new Log_Model("generalCodeID", generalCodeID));

                        if (generalCodeID > 0)
                        {

                            foreach (var applyMoney in input.ApplyMoney.Where(x => x.VoucherCode == input_voucher.VoucherCode))
                            {

                                var voucher = VoucherRedeem_GetValue(connection, transaction, !string.IsNullOrEmpty(input_voucher.MapVoucherCode) ? input_voucher.MapVoucherCode : input_voucher.VoucherCode, applyMoney.SubServiceTypeID, applyMoney.SubServiceID);

                                logs.Add(new Log_Model("voucher", voucher));

                                if (voucher == null)
                                {
                                    return false;
                                }

                                // tang thang wip

                                decimal totalDiscount = (voucher.Value + voucher.ConnectionFee) * (decimal)(voucher.MonthValue > 0 ? voucher.MonthValue : 1) * applyMoney.Qty;

                                // giam doanh thu
                                // lay gia tri theo spf
                                if (voucher.RegisterTypeID == 1)
                                {
                                    totalDiscount = applyMoney.Money;
                                }

                                int detailID = GeneralCodeDetail_Add(connection, transaction, generalCodeID, applyMoney.ServiceID, applyMoney.SubServiceTypeID, applyMoney.SubServiceID, applyMoney.ServiceCode, applyMoney.Qty, totalDiscount);

                                logs.Add(new Log_Model("detailID", detailID));

                                if (!(detailID > 0))
                                {
                                    return false;
                                }

                                if (voucher.RegisterTypeID == 1)
                                {
                                    // giam doanh thu

                                    GeneralCode_UpdateActiveStatus(logs, connection, transaction, generalCodeID, 3);

                                    long privateCodeID = 0;
                                    if (!string.IsNullOrEmpty(input_voucher.PrivateCode))
                                    {
                                        privateCodeID = PrivateCode_Redeem(connection, transaction, generalCodeID, input.objId, input_voucher.PrivateCode);
                                        logs.Add(new Log_Model("privateCodeID", privateCodeID));
                                    }
                                }
                                else if (voucher.RegisterTypeID == 2)
                                {
                                    // ho tro thanh toan

                                    var subServices = input.Services
                                        .SelectMany(s => s.SubServiceTypes
                                        .SelectMany(t => t.SubServices
                                        .Select(c => new
                                        {
                                            ServiceID = s.ServiceID,
                                            SubServiceTypeID = t.SubServiceTypeID,
                                            SubServiceID = c.SubServiceID,
                                            ServiceCode = c.ServiceCode,
                                            MonthUsed = c.MonthUsed
                                        })));

                                    /*
                                    double monthUsed = subServices.FirstOrDefault(c =>
                                            c.ServiceID == applyMoney.ServiceID && c.SubServiceTypeID == applyMoney.SubServiceTypeID
                                            && c.SubServiceID == applyMoney.SubServiceID && c.ServiceCode == applyMoney.ServiceCode
                                        )?.MonthUsed ?? 0;
                                    */

                                    decimal amount = (voucher.Value + voucher.ConnectionFee) * applyMoney.Qty;
                                    // ???
                                    if (amount > applyMoney.Money && applyMoney.Money > 0)
                                        amount = applyMoney.Money;
                                    //float monthValue = voucher.MonthValue > monthUsed ? (float)monthUsed : voucher.MonthValue;
                                    float monthValue = voucher.MonthValue;

                                    long privateCodeID = 0;
                                    if (!string.IsNullOrEmpty(input_voucher.PrivateCode))
                                    {
                                        privateCodeID = PrivateCode_Redeem(connection, transaction, generalCodeID, input.objId, input_voucher.PrivateCode);
                                        logs.Add(new Log_Model("privateCodeID", privateCodeID));
                                    }

                                    // fgold
                                    if ((voucher.PolicyGroupID == 103 || voucher.PolicyGroupID == 109) && privateCodeID > 0)
                                    {
                                        int schedulePromotionID = SchedulePromotion_Add(connection, transaction, privateCodeID, input.objId, amount, applyMoney.ServiceCode);
                                        logs.Add(new Log_Model("schedulePromotionID", schedulePromotionID));
                                    }
                                    else
                                    {
                                        int schedulePolicyID = SchedulePolicy_Add(connection, transaction, generalCodeID, input.objId, amount, monthValue, voucher.PromotionTypeID, applyMoney.ServiceCode);
                                        logs.Add(new Log_Model("schedulePolicyID", schedulePolicyID));
                                    }
                                }



                                limitTypes.Add(input_voucher.LimitType);
                            }
                        }
                    }

                    limitTypes = limitTypes.Distinct().ToList();
                    foreach (int limitType in limitTypes)
                    {
                        var updateQuota = Voucher_CalcAvailable(connection, transaction, DateTime.Now.Year, DateTime.Now.Month, limitType, input.SaleInfor.SaleID, input.CustomerInfor.LocationID);
                    }
                }
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("Voucher_Redeem Exception", ex.ToString()));

                Utility.LogError(ex.ToString());

                return false;
            }
            return true;
        }


        public static Order_Model_Voucher VoucherRedeem_GetInfo(SqlConnection connection, SqlTransaction transaction, string voucherCode, string privateCode)
        {
            if (!string.IsNullOrEmpty(privateCode))
            {
                return Utility.QueryFirstOrDefault<Order_Model_Voucher>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                    new
                    {
                        actionName = "VoucherRedeem_Private_GetInfo",
                        voucherCode = privateCode
                    },
                    transaction
                );
            }
            else
            {
                return Utility.QueryFirstOrDefault<Order_Model_Voucher>(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                    new
                    {
                        actionName = "VoucherRedeem_GetInfo",
                        voucherCode = voucherCode
                    },
                    transaction
                );
            }
        }

        public static GeneralCode_Model_Voucher VoucherRedeem_GetValue(SqlConnection connection, SqlTransaction transaction, string voucherCode, int subServiceTypeID, int subServiceID)
        {
            return Utility.QueryFirstOrDefault<GeneralCode_Model_Voucher>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "VoucherRedeem_GetValue",
                    voucherCode = voucherCode,
                    subServiceTypeID = subServiceTypeID,
                    subServiceID = subServiceID
                },
                transaction
            );
        }

        public static bool GeneralCode_CheckRedeem(SqlConnection connection, SqlTransaction transaction, int objID, string orderCode, string voucherCode)
        {
            int result = Utility.QueryFirstOrDefault<int>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "GeneralCode_CheckRedeem",
                    objID = objID,
                    orderCode = orderCode,
                    voucherCode = voucherCode
                },
                transaction
            );
            return result > 0;
        }

        public static bool GeneralCode_UpdateKeepStatus(SqlConnection connection, SqlTransaction transaction, int objID, string orderCode, string voucherCode)
        {
            int result = Utility.Execute(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "GeneralCode_UpdateKeepStatus",
                    objID = objID,
                    orderCode = orderCode,
                    voucherCode = voucherCode
                },
                transaction
            );
            return result > 0;
        }

        public static void GeneralCode_UpdateActiveStatus(List<Log_Model> logs, SqlConnection connection, SqlTransaction transaction, long generalCodeID, int activeStatus)
        {
            try
            {
                Utility.Execute(
                    connection,
                    "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                    new
                    {
                        actionName = "GeneralCode_UpdateActiveStatus",
                        generalCodeID = generalCodeID,
                        activeStatus = activeStatus
                    },
                    transaction
                );
            }
            catch (Exception ex)
            {
                logs.Add(new Log_Model("GeneralCode_UpdateActiveStatus Exception", ex.ToString()));

                Utility.LogError(ex.ToString());
            }
        }

        public static long GeneralCode_Add(List<Log_Model> logs, SqlConnection connection, SqlTransaction transaction, SalePolicyRedeemEVC input, Order_Model_Voucher voucher)
        {
            var item = new GeneralCode_Model
            {
                VoucherCode = voucher.VoucherCode,
                OrderCode = input.RegCode,
                SaleID = input.SaleInfor.SaleID,
                ObjID = input.objId,
                PolicyID = voucher.PolicyID,
                LocationID = input.CustomerInfor.LocationID,
                BranchCode = input.CustomerInfor.BranchCode,
                DistrictID = input.CustomerInfor.DistrictID,
                SaleChannelID = input.SaleInfor.SaleChannelID,
                SaleTeamID = input.SaleInfor.SaleTeamID,
                PaymentTypeID = input.PaymentTypeID,
                CrossSellingLocation = input.CrossSellingLocation,
                ObjectTypeID = input.CustomerInfor.ObjectTypeID,
                CusTypeID = input.CustomerInfor.CusTypeID,
                CusTypeL2ID = input.CustomerInfor.CusTypeL2ID,
                CustomerRank = input.CustomerInfor.CustomerRank,
                LimitType = voucher.LimitType,
                KeepStatus = input.KeepStatus
            };

            if (voucher.LimitType == 3)
            {
                // au
                var employee = PolicyService.Policy_GetEmployeeInfo(logs, 0, input.objId);
                if (employee != null)
                {
                    item.UnitCode = employee.code;
                    item.UnitParentID = employee.parentId ?? 0;
                    item.UnitParentCode = employee.parentCode;
                }
            }
            else
            {
                var employee = PolicyService.Policy_GetEmployeeInfo(logs, input.SaleInfor.SaleID);
                if (employee != null)
                {
                    item.UnitCode = employee.code;
                    item.UnitParentID = employee.parentId ?? 0;
                    item.UnitParentCode = employee.parentCode;
                }
            }

            var items = new List<GeneralCode_Model> { item };
            var xml = Utility.Converter.ToXml(items);

            logs.Add(new Log_Model("GeneralCode_Add Xml", xml));

            long result = Utility.QueryFirstOrDefault<long>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "GeneralCode_Add",
                    xmlVouchers = xml
                },
                transaction
            );

            logs.Add(new Log_Model("GeneralCode_Add GeneralCodeID", result));

            return result;
        }

        public static int GeneralCodeDetail_Add(SqlConnection connection, SqlTransaction transaction, long generalCodeID, int serviceID, int subServiceTypeID, int subServiceID, int serviceCode, int qty, decimal totalDiscount)
        {
            int result = Utility.QueryFirstOrDefault<int>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "GeneralCodeDetail_Add",
                    generalCodeID = generalCodeID,
                    serviceID = serviceID,
                    subServiceTypeID = subServiceTypeID,
                    subServiceID = subServiceID,
                    serviceCode = serviceCode,
                    qty = qty,
                    totalDiscount = totalDiscount
                },
                transaction
            );

            return result;
        }

        public static int SchedulePolicy_Add(SqlConnection connection, SqlTransaction transaction, long generalCodeID, int objID, decimal amount, float monthValue, int promotionTypeID, int serviceCode)
        {
            int result = Utility.QueryFirstOrDefault<int>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "SchedulePolicy_Add",
                    generalCodeID = generalCodeID,
                    objID = objID,
                    amount = amount,
                    monthValue = monthValue,
                    promotionTypeID = promotionTypeID,
                    serviceCode = serviceCode
                },
                transaction
            );

            return result;
        }

        public static long PrivateCode_Redeem(SqlConnection connection, SqlTransaction transaction, long generalCodeID, int objID, string voucherCode)
        {
            long result = Utility.QueryFirstOrDefault<long>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "PrivateCode_Redeem",
                    generalCodeID = generalCodeID,
                    objID = objID,
                    voucherCode = voucherCode
                },
                transaction
            );

            return result;
        }

        public static int SchedulePromotion_Add(SqlConnection connection, SqlTransaction transaction, long privateCodeID, int objID, decimal amount, int serviceCode)
        {
            int result = Utility.QueryFirstOrDefault<int>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "SchedulePromotion_Add",
                    privateCodeID = privateCodeID,
                    objID = objID,
                    amount = amount,
                    serviceCode = serviceCode
                },
                transaction
            );

            return result;
        }

        public static GeneralCode_ReturnLimit_Model GeneralCode_ReturnLimit(SqlConnection connection, SqlTransaction transaction, long generalCodeID)
        {
            var result = Utility.QueryFirstOrDefault<GeneralCode_ReturnLimit_Model>(
                connection,
                "PowerInside.dbo.OS6_FPTVoucher_Policy_Redeem",
                new
                {
                    actionName = "GeneralCode_ReturnLimit",
                    generalCodeID = generalCodeID
                },
                transaction
            );

            return result;
        }

        public static int Voucher_CalcAvailable(SqlConnection connection, SqlTransaction transaction, int year, int month, int limitType, int saleID, int locationID)
        {
            if (limitType == 1 || limitType == 5)
            {
                if (limitType == 5)
                {
                    Utility.Execute(
                        connection,
                        "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable_Campaign",
                        new { Year = year, Month = month },
                        transaction
                    );
                }

                if (saleID > 0)
                {
                    Utility.Execute(
                        connection,
                        "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable_GeneralCode",
                        new { Year = year, Month = month },
                        transaction
                    );

                    return 1;
                }
                else if (locationID > 0)
                {
                    Utility.Execute(
                        connection,
                        "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable_GeneralCode_Location",
                        new { Year = year, Month = month },
                        transaction
                    );

                    return 1;
                }
            }
            else if (limitType == 2 || limitType == 4)
            {
                Utility.Execute(
                    connection,
                    "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable_Campaign",
                    new { Year = year, Month = month },
                    transaction
                );

                return 1;
            }
            else if (limitType == 3)
            {
                Utility.Execute(
                    connection,
                    "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable_Campaign",
                    new { Year = year, Month = month },
                    transaction
                );

                Utility.Execute(
                    connection,
                    "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable_Apply",
                    new { Year = year, Month = month },
                    transaction
                );

                return 1;
            }

            return 0;
        }
        #endregion

        #region chong_pmh
        public static string Voucher_ApplyConfig_Check(List<Log_Model> logs, List<ServicePlatform> services, List<ProductPlatform> products, List<SalePlatformVoucherValueInfor> vouchers)
        {
            string error = "";

            // flat data
            var _services = new List<VoucherApplyConfig_Limit_Check_Req_Service>();
            var _products = new List<VoucherApplyConfig_Limit_Check_Req_Product>();

            if (services != null && services.Count > 0)
            {
                foreach (var s in services)
                {
                    foreach (var sst in s.SubServiceTypes)
                    {
                        foreach (var ss in sst.SubServices)
                        {
                            _services.Add(new VoucherApplyConfig_Limit_Check_Req_Service
                            {
                                PolicyID = ss.PolicyID,
                                ServiceID = s.ServiceID,
                                SubServiceTypeID = sst.SubServiceTypeID,
                                SubServiceID = ss.SubServiceID,
                                ServiceCode = ss.ServiceCode,
                                Total_VAT = ss.Total_VAT,

                                PrePaid = Convert.ToInt32(ss.PrePaid),
                                MonthUsed = ss.MonthUsed
                            });
                        }
                    }
                }
            }

            if (products != null && products.Count > 0)
            {
                foreach (var s in products)
                {
                    foreach (var sst in s.SubServiceTypes)
                    {
                        foreach (var ss in sst.SubServices)
                        {
                            _products.Add(new VoucherApplyConfig_Limit_Check_Req_Product
                            {
                                PolicyID = ss.PolicyID,
                                ServiceID = s.ServiceID,
                                SubServiceTypeID = sst.SubServiceTypeId,
                                SubServiceID = ss.SubServiceID,
                                ServiceCode = ss.ServiceCode,
                                Total_VAT = ss.Total_VAT,

                                StatusID = ss.StatusID,
                                DeployTypeID = ss.DeployTypeID,
                                RevokeID = ss.RevokeID
                            });
                        }
                    }
                }
            }

            // config 39 khong ap dung pmh cho dich vu config voi pmh dich vu khac
            var lst_config = PolicyService.BaseConfig_Get(null, null, 39);
            if (lst_config?.Count > 0 && vouchers?.Count > 1)
            {
                // not gtbb
                var lst_voucher_service = vouchers.Where(v => !(v.VoucherType == 2 && v.VoucherTypeL2 == 2))
                    .Select(v => new { VoucherCode = v.evoucherCode, ServiceIDs = v.Apply.Select(a => a.ServiceID).ToList() }).ToList();

                foreach (var config in lst_config)
                {
                    if (lst_voucher_service.Count > 1)
                    {
                        if (lst_voucher_service.Any(c => c.ServiceIDs.Contains(config.FValue)) && lst_voucher_service.Any(c => !c.ServiceIDs.Contains(config.FValue)))
                        {
                            error = "Bạn không thể áp dụng phiếu mua hàng " + config.FName + " cùng với dịch vụ khác. Vui lòng kiểm tra lại.";
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(error))
                {
                    return error;
                }
            }

            List<string> voucherCodes = vouchers.GroupBy(c => c.evoucherCode)
                .Select(c => !string.IsNullOrEmpty(c.First().mapEvoucherCode) ? c.First().mapEvoucherCode : c.First().evoucherCode).ToList();
            var lst1 = VoucherApplyConfig_Quantity_Check(voucherCodes);
            if (lst1 != null && lst1.Count > 0)
            {
                var err1 = lst1.FirstOrDefault(c => c.CheckQuantity == -1);
                if (err1 != null)
                {
                    error = "Bạn đã nhập/chọn phiếu mua hàng vượt quá số lượng " + err1.Quantity + " cho phép. Vui lòng liên hệ quản lý chi nhánh.";
                }

                if (!string.IsNullOrEmpty(error))
                {
                    return error;
                }
            }

            var subServiceTypes = SubServiceType_Get();
            var _vouchers = new List<VoucherApplyConfig_Limit_Check_Req_Voucher>();
            var _orderItems = new List<VoucherApplyConfig_Limit_Check_Req_OrderItem>();

            if (vouchers != null && vouchers.Count > 0)
            {
                foreach (var v in vouchers)
                {
                    // add tien voucher
                    foreach (var a in v.Apply)
                    {
                        // lay loai dich vu san pham
                        int type = subServiceTypes.FirstOrDefault(x => x.ID == a.SubServiceTypeID)?.Type ?? 1;
                        int policyGroupL2ID = v.PolicyGroupID;
                        // 198: GTBB
                        if (v.VoucherType == 2 && v.VoucherTypeL2 == 2)
                        {
                            policyGroupL2ID = 102;
                        }
                        if (type == 2)
                        {
                            var orderItem = _products.FirstOrDefault(c => c.ServiceID == a.ServiceID && c.SubServiceTypeID == a.SubServiceTypeID
                                && c.SubServiceID == a.SubServiceID && c.ServiceCode == a.ServiceCode
                                && c.StatusID == a.StatusID && c.DeployTypeID == a.DeployTypeID && c.RevokeID == a.RevokeID);
                            _vouchers.Add(new VoucherApplyConfig_Limit_Check_Req_Voucher
                            {
                                RegisterTypeID = v.RegisterTypeID,
                                PolicyID = orderItem?.PolicyID ?? 0,
                                SubServiceTypeID = orderItem?.SubServiceTypeID ?? 0,
                                VoucherCode = v.evoucherCode,
                                PolicyGroupL2ID = policyGroupL2ID,
                                Type = type,
                                Type2 = v.PromotionTypeID == 3 ? 1 : 2, // phm => not tb
                                Discount = a.DiscountVAT,
                                FullDiscount = (a.FullDiscountVAT > a.DiscountVAT ? a.FullDiscountVAT : a.DiscountVAT)
                            });
                        }
                        else
                        {
                            var orderItem = _services.FirstOrDefault(c => c.ServiceID == a.ServiceID && c.SubServiceTypeID == a.SubServiceTypeID
                                && c.SubServiceID == a.SubServiceID && c.ServiceCode == a.ServiceCode);
                            _vouchers.Add(new VoucherApplyConfig_Limit_Check_Req_Voucher
                            {
                                RegisterTypeID = v.RegisterTypeID,
                                PolicyID = orderItem?.PolicyID ?? 0,
                                SubServiceTypeID = orderItem?.SubServiceTypeID ?? 0,
                                VoucherCode = v.evoucherCode,
                                PolicyGroupL2ID = policyGroupL2ID,
                                Type = type,
                                Type2 = 1,
                                Discount = a.DiscountVAT,
                                FullDiscount = (a.FullDiscountVAT > a.DiscountVAT ? a.FullDiscountVAT : a.DiscountVAT)
                            });
                        }
                    }
                }
            }

            var policyGroupIDs = new List<VoucherApplyConfig_Multi_Req>();

            // chuyen group cho ap dung dong thoi
            var lst_config_group = BaseConfig_Get(logs, null, 74);
            foreach (var v in _vouchers)
            {
                int policyGroupID = v.PolicyGroupL2ID;
                if (lst_config_group?.Count > 0)
                {
                    var c = lst_config_group.FirstOrDefault(x => x.FValue == policyGroupID && x.FName == v.SubServiceTypeID.ToString() && _vouchers.Any(t => t.PolicyGroupL2ID == x.TValue));
                    if (c != null)
                    {
                        policyGroupID = c.TValue;
                    }
                }
                policyGroupIDs.Add(new VoucherApplyConfig_Multi_Req { PolicyGroupID = policyGroupID, Type2 = v.Type2 });
            }

            // ap dung dong thoi
            //var policyGroupIDs = _vouchers.Select(c => c.PolicyGroupL2ID).ToList();
            if (!VoucherApplyConfig_Multi_Check(logs, policyGroupIDs))
            {
                return "Bạn đã chọn/nhập các loại phiếu mua hàng không thể áp dụng đồng thời. Vui lòng liên hệ quản lý chi nhánh."; ;
            }

            // add tien don hang
            foreach (var ss in _services)
            {
                decimal total_VAT = 0;
                // khong check gia tri cuoc goi tra sau
                if (ss.PrePaid > 0 || ss.MonthUsed == 0)
                {
                    total_VAT = ss.Total_VAT;
                }
                _orderItems.Add(new VoucherApplyConfig_Limit_Check_Req_OrderItem
                {
                    PolicyID = ss.PolicyID,
                    ServiceID = ss.ServiceID,
                    SubServiceID = ss.SubServiceID,
                    Prepaid = Convert.ToInt32(ss.PrePaid),
                    Type = 1,
                    Price = total_VAT
                });
            }

            // add tien don hang
            foreach (var ss in _products)
            {
                _orderItems.Add(new VoucherApplyConfig_Limit_Check_Req_OrderItem
                {
                    PolicyID = ss.PolicyID,
                    ServiceID = ss.ServiceID,
                    SubServiceID = 0,
                    Prepaid = 0,
                    Type = 2,
                    Price = ss.Total_VAT
                });
            }

            var lst = VoucherApplyConfig_Limit_Check(_orderItems, _vouchers);

            logs.Add(new Log_Model(
                "Voucher_ApplyConfig_Check",
                new
                {
                    services = _services,
                    products = _products,
                    orderItems = _orderItems,
                    vouchers = _vouchers,
                    lst = lst
                }));

            if (lst != null && lst.Count > 0)
            {
                // vuoc qua tien dich vu
                var err1 = lst.FirstOrDefault(c => c.LimitConfigID > 0 && c.CheckValue == -1);
                if (err1 != null)
                {
                    //error = "Bạn đã chọn/nhập phiếu mua hàng vượt quá " + string.Format("{0:n0}", err1.LimitValue) + " VNĐ tiền ưu đãi " + (err1.Type == 2 ? "thiết bị" : "dịch vụ") + " " + err1.ServiceNames + ". Không thể áp dụng, vui lòng kiểm tra lại.";
                    error = "Bạn đã chọn/nhập phiếu mua hàng vượt quá " + string.Format("{0:n0}", err1.LimitValue) + " VNĐ giá trị tối đa cho phép. Vui lòng liên hệ Quản lý chi nhánh.";
                }
                if (string.IsNullOrEmpty(error))
                {
                    // vuoc qua phan tram dich vu
                    var err2 = lst.FirstOrDefault(c => c.LimitConfigID > 0 && c.CheckRate == -1);
                    if (err2 != null)
                    {
                        //error = "Phiếu mua hàng áp dụng vượt quá " + err2.LimitRate + "% tiền bán " + (err2.Type == 2 ? "thiết bị" : "dịch vụ") + " " + err2.ServiceNames + ". Không thể áp dụng, vui lòng kiểm tra lại.";
                        error = "Bạn đã chọn/nhập phiếu mua hàng vượt quá " + err2.LimitRate + "% giá trị tối đa cho phép. Vui lòng liên hệ Quản lý chi nhánh.";
                    }
                }
                if (string.IsNullOrEmpty(error))
                {
                    // vuoc qua phan tram don han
                    var err3 = lst.FirstOrDefault(c => c.LimitConfigID == 0 && c.CheckRate == -1);
                    if (err3 != null)
                    {
                        error = "Bạn đã chọn/nhập phiếu mua hàng vượt quá " + err3.LimitRate + "% giá trị đơn hàng. Vui lòng liên hệ Quản lý chi nhánh.";
                    }
                }
            }

            return error;
        }

        public static bool VoucherApplyConfig_Multi_Check(List<Log_Model> logs, List<VoucherApplyConfig_Multi_Req> policyGroupIDs)
        {
            policyGroupIDs = policyGroupIDs.GroupBy(c => new { c.PolicyGroupID, c.Type2 }).Select(g => g.FirstOrDefault()).ToList();

            if (policyGroupIDs.GroupBy(c => c.PolicyGroupID).Count() == 1)
                return true;

            var lst = Utility.Query<VoucherApplyConfig_Multi_Res>(
                Utility.ConnRead,
                "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherApplyConfig_Multi_Get"
                ).ToList();


            var groups = lst.GroupBy(c => c.GroupID).OrderBy(g => g.Max(c => c.Type)).ToList();

            logs.Add(new Log_Model(
                "Voucher_Multi_Check",
                new
                {
                    policyGroupIDs = policyGroupIDs,
                    groups = groups
                }));

            /*
            if (groups.Any(g => policyGroupIDs.All(c => g.Any(x => x.PolicyGroupID == c.PolicyGroupID))))
                return true;
            */
            var group = groups.FirstOrDefault(g => policyGroupIDs.All(c => g.Any(x => x.PolicyGroupID == c.PolicyGroupID)));
            if (group != null)
            {
                // r1. sl ap dung dich vu khong lon hon 1
                var r1 = group.Where(c => c.Type == 1).ToList();
                if (r1?.Count > 0)
                {
                    if (policyGroupIDs.Where(c => r1.Any(x => x.PolicyGroupID == c.PolicyGroupID) && c.Type2 == 1).Count() > 1)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                {
                    return true;
                }

            }

            return false;
        }

        public static List<VoucherApplyConfig_Quantity_Check_Model> VoucherApplyConfig_Quantity_Check(List<string> voucherCodes)
        {
            return Utility.Query<VoucherApplyConfig_Quantity_Check_Model>(
                Utility.ConnRead,
                "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherApplyLimitQuantityConfig_Check",
                new { VoucherCodes = voucherCodes != null ? string.Join(",", voucherCodes) : "" }
                ).ToList();
        }

        public static List<VoucherApplyConfig_Limit_Check_Res> VoucherApplyConfig_Limit_Check(List<VoucherApplyConfig_Limit_Check_Req_OrderItem> orderItems, List<VoucherApplyConfig_Limit_Check_Req_Voucher> vouchers)
        {
            return Utility.Query<VoucherApplyConfig_Limit_Check_Res>(
                Utility.ConnRead,
                "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherApplyLimitConfig_Check",
                new { OrderItems = Utility.Converter.ToXml(orderItems), Vouchers = Utility.Converter.ToXml(vouchers) }
                ).ToList();
        }

        public static List<SubServiceType_Model> SubServiceType_Get()
        {
            return Utility.Query<SubServiceType_Model>(
                Utility.ConnRead,
                "PowerInside.dbo.OS6_DXBusinessPolicy_SubServiceType_Get"
                ).ToList();
        }
        #endregion

        #region limit_info
        public static List<Voucher_GetSaleQuota_Res> Voucher_GetSaleQuota(Voucher_GetSaleQuota_Req req)
        {
            string unitCode = "";
            string unitParentCode = "";
            var lst = new List<Voucher_GetSaleQuota_Res>();
            if (req.SaleID > 0)
            {
                var employee = Policy_GetEmployeeInfo(new List<Log_Model>(), req.SaleID);
                unitCode = employee?.code ?? "";
                unitParentCode = employee?.parentCode ?? "";
                var t101 = Utility.QueryFirstOrDefault<Voucher_GetSaleQuota_Res>(
                    Utility.ConnWrite,
                    "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_GetLimit_View",
                    new
                    {
                        PolicyGroupID = 101,
                        UnitCode = unitCode,
                        UnitParentCode = unitParentCode
                    }
                );
                if (t101 != null)
                {
                    t101.PolicyGroupID = 101;
                    lst.Add(t101);
                }
                var t113 = Utility.QueryFirstOrDefault<Voucher_GetSaleQuota_Res>(
                    Utility.ConnWrite,
                    "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_GetLimit_View",
                    new
                    {
                        PolicyGroupID = 113,
                        UnitCode = unitCode,
                        UnitParentCode = unitParentCode
                    }
                );
                if (t113 != null)
                {
                    t113.PolicyGroupID = 113;
                    lst.Add(t113);
                }
            }
            return lst;
        }
        #endregion
    }
}