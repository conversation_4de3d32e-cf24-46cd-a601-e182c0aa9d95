using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using Dapper;
using System.Data;
using Voucher.APIHelper;
using System.Xml.Linq;
using APIMBS.Models.SalePlatform;
using APIMBS.Models.MobileSaleV4;
using System.Text;
using APIMBS.Service.EvcPolicyServices;
using System.Net.Http;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using Voucher.APIHelper.Util;
using Voucher.APIHelper.ShareModel;

namespace APIMBS.Service
{
    public class FSaleGetinforVoucherServices
    {
        public const string OS6_FPTVoucher_SalePlatform_PolicyVoucher = "PowerInside.dbo.OS6_FPTVoucher_SalePlatform_PolicyVoucher";
        public const string OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend = "PowerInside.dbo.OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend";
        public const string OS6_FPTVoucher_FSale = "PowerInside.dbo.OS6_FPTVoucher_FSale";
        public const string OSU6_FPTVoucher_PolicyVoucher_CheckLocation = "PowerInside.dbo.OSU6_FPTVoucher_PolicyVoucher_CheckLocation";
        public const string OS6_FPTVoucher_CheckQuotaMoney = "PowerInside.dbo.OS6_FPTVoucher_CheckQuotaMoney";
        public const string OSU6_FPTVoucher_GetDiscountVoucherPolicy = "PowerInside.dbo.OSU6_FPTVoucher_GetDiscountVoucherPolicy";
        public const string OS6_FPTVoucher_Campaign_FoxGold_NewCustomer = "PowerInside.dbo.OS6_FPTVoucher_Campaign_FoxGold_NewCustomer";

        #region get infor voucher

        public static List<SalePlatformVoucherValueInfor> GetVoucherinfo(SalePolicyGetInforEVC input, StringBuilder sb)
        {
            var dataAll_evc = new List<SalePlatformVoucherValueInfor>();
            try
            {
                var voucher_general = input.evoucherList.Where(x => x.evoucherType == 1).ToList();
                if (voucher_general?.Count > 0)
                {
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucher_general", voucher_general));

                    var listServices = FPTSaleCommonServices.GetCustomerSaleOrderCollections(input);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listServices", listServices));

                    var listProducts = FPTSaleCommonServices.GetCustomerSaleOrderCollectionsFromProducts(input);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProducts", listProducts));

                    var service_order_all = new List<CustomerSaleOrderCollections>();
                    if (listServices?.Any() == true)
                    {
                        service_order_all.AddRange(listServices);
                    }
                    if (listProducts?.Any() == true)
                    {
                        service_order_all.AddRange(listProducts);
                    }
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("service_order_all", service_order_all));

                    #region Voucher chọn
                    if (voucher_general?.Any() == true)
                    {
                        foreach (var item in voucher_general)
                        {
                            var listDataIn4 = new SalePlatformVoucherValueInfor();
                            listDataIn4.Apply = new List<Apply>();
                            var serviceApply = new List<Apply>();
                            // biến tạm dùng để xử lý tiền theo số lượng  
                            // 1. Lấy services cả phần services và products
                            var xmlServiceAll = CreateXMLGetInfor(service_order_all);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlServiceAll " + item.evoucherCode, xmlServiceAll));

                            // 2. Lấy services + giá trị evc từ db
                            var servicesEVC = GetListInforService(item.evoucherCode, xmlServiceAll);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("servicesEVC " + item.evoucherCode, servicesEVC));

                            var servicesProductsEVC = GetListInforProductService(item.evoucherCode, xmlServiceAll, input.Products);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("servicesProductsEVC " + item.evoucherCode, servicesProductsEVC));

                            if (servicesEVC?.Any() == true)
                            {
                                serviceApply.AddRange(servicesEVC);
                            }
                            if (servicesProductsEVC?.Any() == true)
                            {
                                serviceApply.AddRange(servicesProductsEVC);
                            }
                            serviceApply = serviceApply.Distinct().ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceApply " + item.evoucherCode, serviceApply));

                            // 3. Lấy số tiền còn lại có thể sử dụng
                            var quota = CheckQuota(item.evoucherCode, input.SaleInfor, input.CustomerInfor);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("quota " + item.evoucherCode, quota));

                            var _quota = quota;

                            var registerModel = GetRegisterTypeModel(item.evoucherCode);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("registerModel " + item.evoucherCode, registerModel));

                            // 4. Tính tiền cho từng nhóm service
                            listDataIn4 = new SalePlatformVoucherValueInfor();
                            listDataIn4.Apply = new List<Apply>();
                            listDataIn4.evoucherCode = item.evoucherCode;
                            listDataIn4.Quota = (int)quota;

                            listDataIn4.RegisterTypeID = registerModel.RegisterTypeID;
                            listDataIn4.ApplyTypeID = registerModel.ApplyTypeID;
                            listDataIn4.PolicyGroupID = registerModel.PolicyGroupID;
                            listDataIn4.PromotionTypeID = registerModel.PromotionTypeID;
                            listDataIn4.VoucherTypeL2 = 1;
                            listDataIn4.VendorContract = registerModel.VendorContract;

                            listDataIn4.Policy_Type = registerModel.Policy_Type;

                            var maxQty = registerModel.MaxQuantity;

                            foreach (var apply in serviceApply)
                            {
                                var DataQty = service_order_all.Where(x => x.serviceId == apply.ServiceID
                                                              && x.subServiceTypeId == apply.SubServiceTypeID
                                                              && x.subServiceId == apply.SubServiceID //&& x.deployTypeId == apply.DeployTypeID
                                                              && x.serviceCode == apply.ServiceCode).FirstOrDefault();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("DataQty " + item.evoucherCode, DataQty));

                                int qty = DataQty.qty;
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("qty " + item.evoucherCode, qty));

                                for (int i = 0; i < qty; i++)
                                {
                                    if (registerModel.MaxQuantity > 0 && maxQty == 0)
                                        break;

                                    Apply apply1 = new Apply()
                                    {
                                        ServiceCode = apply.ServiceCode,
                                        DeployTypeID = apply.DeployTypeID,
                                        SubServiceID = apply.SubServiceID,
                                        ServiceID = apply.ServiceID,
                                        StatusID = apply.StatusID,
                                        SubServiceTypeID = apply.SubServiceTypeID,
                                        Discount = apply.Discount,
                                        DiscountVAT = apply.DiscountVAT,
                                        Dismonth = apply.Dismonth,
                                        IsDeductOrder = apply.IsDeductOrder,
                                        RevokeID = apply.RevokeID,
                                        Value = apply.Value
                                    };

                                    listDataIn4.Apply.Add(apply1);
                                    quota -= apply1.DiscountVAT;
                                    maxQty = maxQty - 1;
                                }
                            }
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog(" listDataIn4 " + item.evoucherCode, listDataIn4));
                            // 
                            if (/*listDataIn4.Apply?.Any() == true*/true)
                            {
                                dataAll_evc.Add(listDataIn4);
                            }

                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataAll_evc", dataAll_evc));
                    }
                    #endregion
                }

                var voucher_vendor = input.evoucherList.Where(x => VendorManager.CheckFormatVoucher(x.evoucherCode) != "").ToList();
                if (voucher_vendor?.Count > 0)
                {
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucher_vendor", voucher_vendor));

                    var listServices = FPTSaleCommonServices.GetCustomerSaleOrderCollections(input);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listServices", listServices));

                    var listProducts = FPTSaleCommonServices.GetCustomerSaleOrderCollectionsFromProducts(input);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProducts", listProducts));

                    var service_order_all = new List<CustomerSaleOrderCollections>();
                    if (listServices?.Any() == true)
                    {
                        service_order_all.AddRange(listServices);
                    }
                    if (listProducts?.Any() == true)
                    {
                        service_order_all.AddRange(listProducts);
                    }
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("service_order_all", service_order_all));

                    foreach (var item in voucher_vendor)
                    {
                        var listDataIn4 = new SalePlatformVoucherValueInfor();
                        listDataIn4.Apply = new List<Apply>();
                        var serviceApply = new List<Apply>();
                        // biến tạm dùng để xử lý tiền theo số lượng  
                        // 1. Lấy services cả phần services và products
                        var xmlServiceAll = CreateXMLGetInfor(service_order_all);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlServiceAll " + item.evoucherCode, xmlServiceAll));

                        // 2. Lấy services + giá trị evc từ db
                        var servicesEVC = GetListInforServiceVendor(VendorManager.CheckFormatVoucher(item.evoucherCode), xmlServiceAll);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("servicesEVC " + item.evoucherCode, servicesEVC));

                        var servicesProductsEVC = GetListInforProductServiceVendor(VendorManager.CheckFormatVoucher(item.evoucherCode), xmlServiceAll, input.Products);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("servicesProductsEVC " + item.evoucherCode, servicesProductsEVC));

                        if (servicesEVC?.Any() == true)
                        {
                            serviceApply.AddRange(servicesEVC);
                        }
                        if (servicesProductsEVC?.Any() == true)
                        {
                            serviceApply.AddRange(servicesProductsEVC);
                        }
                        serviceApply = serviceApply.Distinct().ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceApply " + item.evoucherCode, serviceApply));

                        // 3. Lấy số tiền còn lại có thể sử dụng
                        var quota = CheckQuota(item.evoucherCode, input.SaleInfor, input.CustomerInfor);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("quota " + item.evoucherCode, quota));

                        var _quota = quota;

                        var registerModel = GetRegisterTypeModelVendor(VendorManager.CheckFormatVoucher(item.evoucherCode));
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("registerModel " + item.evoucherCode, registerModel));

                        if (registerModel == null) continue;

                        // 4. Tính tiền cho từng nhóm service
                        listDataIn4 = new SalePlatformVoucherValueInfor();
                        listDataIn4.Apply = new List<Apply>();
                        listDataIn4.evoucherCode = item.evoucherCode;
                        listDataIn4.Quota = (int)quota;

                        listDataIn4.RegisterTypeID = registerModel.RegisterTypeID;
                        listDataIn4.ApplyTypeID = registerModel.ApplyTypeID;
                        listDataIn4.PolicyGroupID = registerModel.PolicyGroupID;
                        listDataIn4.PromotionTypeID = registerModel.PromotionTypeID;
                        listDataIn4.VoucherTypeL2 = 1;
                        listDataIn4.mapEvoucherCode = registerModel.mapEvoucherCode;

                        listDataIn4.Policy_Type = registerModel.Policy_Type;

                        var maxQty = registerModel.MaxQuantity;

                        foreach (var apply in serviceApply)
                        {
                            var DataQty = service_order_all.Where(x => x.serviceId == apply.ServiceID
                                                          && x.subServiceTypeId == apply.SubServiceTypeID
                                                          && x.subServiceId == apply.SubServiceID //&& x.deployTypeId == apply.DeployTypeID
                                                          && x.serviceCode == apply.ServiceCode).FirstOrDefault();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("DataQty " + item.evoucherCode, DataQty));

                            int qty = DataQty.qty;
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("qty " + item.evoucherCode, qty));

                            for (int i = 0; i < qty; i++)
                            {
                                if (registerModel.MaxQuantity > 0 && maxQty == 0)
                                    break;

                                Apply apply1 = new Apply()
                                {
                                    ServiceCode = apply.ServiceCode,
                                    DeployTypeID = apply.DeployTypeID,
                                    SubServiceID = apply.SubServiceID,
                                    ServiceID = apply.ServiceID,
                                    StatusID = apply.StatusID,
                                    SubServiceTypeID = apply.SubServiceTypeID,
                                    Discount = apply.Discount,
                                    DiscountVAT = apply.DiscountVAT,
                                    Dismonth = apply.Dismonth,
                                    IsDeductOrder = apply.IsDeductOrder,
                                    RevokeID = apply.RevokeID,
                                    Value = apply.Value
                                };

                                listDataIn4.Apply.Add(apply1);
                                quota -= apply1.DiscountVAT;
                                maxQty = maxQty - 1;

                            }
                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog(" listDataIn4 " + item.evoucherCode, listDataIn4));
                        // 
                        if (/*listDataIn4.Apply?.Any() == true*/true)
                        {
                            dataAll_evc.Add(listDataIn4);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("error ", e.ToString()));
                return new List<SalePlatformVoucherValueInfor>();
            }

            return dataAll_evc;
        }

        public static int GetTypeVoucher(string voucherCode)
        {
            int type = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                type = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetTypeVoucher",
                    voucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return type;
        }

        public static XElement CreateXMLGetInfor(List<CustomerSaleOrderCollections> lst)
        {
            var xmlServices = new XElement("N",
            from item in lst
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed),
                           new XElement("statusId", item.statusId),
                           new XElement("revokeID", item.revokeID),
                           new XElement("deployTypeId", item.deployTypeId),
                           new XElement("prePaid", item.prePaid),
                           new XElement("qty", item.qty)
                       ));

            return xmlServices;
        }

        public static XElement CreateXMLGetInforProduct(SalePolicyGetInforEVC input, int i)
        {
            var listProducts = (from service in input.Products
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                select new CustomerSaleOrderCollections
                                {
                                    serviceId = service.ServiceID,
                                    subServiceTypeId = subServiceType.SubServiceTypeId,
                                    subServiceId = subService.SubServiceID,
                                    serviceCode = subService.ServiceCode,
                                    monthUsed = Convert.ToInt32(subService.MonthUsed)
                                }).ToList();

            var xmlString = new XElement("N",
            from item in listProducts
            select new XElement("I",
                           new XElement("serviceId", item.serviceId),
                           new XElement("subServiceTypeId", item.subServiceTypeId),
                           new XElement("subServiceId", item.subServiceId),
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("monthUsed", item.monthUsed)
                       //new XElement("serviceRegister", s_services)
                       ));
            return xmlString;
        }

        public static List<Apply> GetListInforService(string voucherCode, XElement xml)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetVoucherValue",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static List<Apply> GetListInforProductService(string voucherCode, XElement xml, List<ProductPlatform> products)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                data = connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetInforProductService",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static decimal CheckQuota(string voucherCode, SaleInfor saleInfor, CustomerInfor customerInfor) // đơn vị: VNĐ
        {
            decimal quotaMoneyAvailable = 0;
            //hạn mức sale còn lại
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                quotaMoneyAvailable = connection.Query<decimal>(OS6_FPTVoucher_CheckQuotaMoney, new
                {
                    VoucherCode = voucherCode,
                    SaleId = saleInfor.SaleID,
                    LocationId = customerInfor.LocationID,
                    BranchCode = customerInfor.BranchCode,
                    DistrictId = customerInfor.DistrictID

                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }

            return quotaMoneyAvailable;
        }

        public static int GetPromotionType(string voucherCode)
        {
            int i = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                i = connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                {
                    Action = "GetPromotionTypeId",
                    VoucherCode = voucherCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return i;
        }

        public static void SplitReferralApplyCamera(List<SalePlatformVoucherValueInfor> infors, List<ProductPlatform> products)
        {

            foreach (var infor in infors)
            {
                var result = new List<Apply>();

                var list = (from service in products
                            from subServiceType in service.SubServiceTypes
                            from subService in subServiceType.SubServices
                            from apply in infor.Apply
                            where service.ServiceID == apply.ServiceID && subServiceType.SubServiceTypeId == apply.SubServiceTypeID &&
                                  subService.SubServiceID == apply.SubServiceID && subService.ServiceCode == apply.ServiceCode
                            select new { service.ServiceID, subServiceType.SubServiceTypeId, subService.SubServiceID, subService.ServiceCode, subService.Qty }).ToList();

                var applies = (from service in products
                               from subServiceType in service.SubServiceTypes
                               from subService in subServiceType.SubServices
                               from apply in infor.Apply
                               where service.ServiceID == apply.ServiceID && subServiceType.SubServiceTypeId == apply.SubServiceTypeID &&
                                     subService.SubServiceID == apply.SubServiceID && subService.ServiceCode == apply.ServiceCode
                               select new Apply()
                               {
                                   ServiceID = apply.ServiceID,
                                   SubServiceID = apply.SubServiceID,
                                   SubServiceTypeID = apply.SubServiceTypeID,
                                   ServiceCode = apply.ServiceCode,
                                   StatusID = apply.StatusID,
                                   RevokeID = apply.RevokeID,
                                   DeployTypeID = apply.DeployTypeID,
                                   Value = apply.Value,
                                   Dismonth = apply.Dismonth,
                                   IsDeductOrder = apply.IsDeductOrder,
                                   Discount = apply.Discount / subService.Qty,
                                   DiscountVAT = apply.DiscountVAT / subService.Qty
                               }).ToList();

                foreach (var apply in applies)
                {
                    var item = list.FirstOrDefault(x => x.ServiceID == apply.ServiceID && x.SubServiceTypeId == apply.SubServiceTypeID &&
                                                        x.SubServiceID == apply.SubServiceID && x.ServiceCode == apply.ServiceCode);

                    result.AddRange(Enumerable.Range(0, item.Qty).Select(x => apply.ShallowCopy()));
                }

                if (result.Count > 0)
                {
                    infor.Apply = result;
                }
            }
        }

        public static List<DiscountVoucher> GetDiscountVoucher(SqlConnection connection, StringBuilder sb, string contract, int serviceid)
        {
            var listData = new List<DiscountVoucher>();
            try
            {
                listData = connection.Query<DiscountVoucher>(OSU6_FPTVoucher_GetDiscountVoucherPolicy, new
                {
                    contract = contract,
                    serviceID = serviceid
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                return listData;
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetDiscountVoucher error", e));
                return new List<DiscountVoucher>();
            }
        }
        #endregion

        public static RegisterTypeModel GetRegisterTypeModel(string voucherCode)
        {
            RegisterTypeModel model = new RegisterTypeModel();

            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                model = connection.Query<RegisterTypeModel>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetRegisterTypeModel",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }

            return model;
        }

        public static List<Apply> GetListInforServiceVendor(string voucherCode, XElement xml)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                data = connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetVoucherValueVendor",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static List<Apply> GetListInforProductServiceVendor(string voucherCode, XElement xml, List<ProductPlatform> products)
        {
            var data = new List<Apply>();
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                data = connection.Query<Apply>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetInforProductServiceVendor",
                    xml = xml,
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            }
            return data;
        }

        public static RegisterTypeModel GetRegisterTypeModelVendor(string voucherCode)
        {
            RegisterTypeModel model = new RegisterTypeModel();

            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                model = connection.Query<RegisterTypeModel>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetRegisterTypeModelVendor",
                    voucherCode = voucherCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }

            return model;
        }

        public static bool CheckGTBBInContract(string code, int objdk)
        {
            int ck = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                ck = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_CheckGTBBInContract", new
                {
                    code = code,
                    objdk = objdk
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return ck == 0;
        }

        public static List<SalePlatformVoucherValueInfor> GetValueEVCRequest(string orderCode, StringBuilder sb)
        {
            try
            {
                var url = Utility.bpapi_fpt_vn + "/api/ins/policy/GetVoucherRequestOrder";

                var jsonData = "{\"OrderCode\": \"" + orderCode + "\"}";

                using (var httpClient = new HttpClient())
                {
                    var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                    var response = httpClient.PostAsync(url, content).Result;

                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = response.Content.ReadAsStringAsync().Result;
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetValueEVCRequest responseContent", responseContent));

                        var apiResponse = JsonConvert.DeserializeObject<ResponseModels<SalePlatformVoucherValueInfor>>(responseContent);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetValueEVCRequest apiResponse", apiResponse));

                        var data = apiResponse.data;

                        return data;
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetValueEVCRequest error", e.Message));
            }
            return new List<SalePlatformVoucherValueInfor>();
        }

        public static List<SalePlatformVoucherValueInfor> GetVoucherinfoV2(SalePolicyGetInforEVC input, StringBuilder sb)
        {
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetVoucherinfoV2 Start", ""));

            var dataAll_evc = new List<SalePlatformVoucherValueInfor>();

            string privateCode = "";

            try
            {
                var listServices = FPTSaleCommonServices.GetCustomerSaleOrderCollections(input);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listServices", listServices));

                var listProducts = FPTSaleCommonServices.GetCustomerSaleOrderCollectionsFromProducts(input);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listProducts", listProducts));

                var service_order_all = new List<CustomerSaleOrderCollections>();
                if (listServices?.Any() == true)
                {
                    service_order_all.AddRange(listServices);
                }
                if (listProducts?.Any() == true)
                {
                    service_order_all.AddRange(listProducts);
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("service_order_all", service_order_all));

                var xmlServiceAll = CreateXMLGetInfor(service_order_all);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("service_order_all", xmlServiceAll));

                using (var conn = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    foreach (var voucher in input.evoucherList.Where(x => x.evoucherType == 1 || VendorManager.CheckFormatVoucher(x.evoucherCode) != ""))
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("VOUCHER", voucher));
                        if (VendorManager.CheckFormatVoucher(voucher.evoucherCode) != "")
                        {
                            privateCode = VendorManager.CheckFormatVoucher(voucher.evoucherCode);
                            voucher.evoucherCode = RedeemVoucherServices.GetPolicyVoucher(conn, VendorManager.CheckFormatVoucher(voucher.evoucherCode));
                        }

                        // Get ưu đãi voucher dịch vụ
                        var servicesEVC = GetListInforService(voucher.evoucherCode, xmlServiceAll);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("servicesEVC ", servicesEVC));

                        // Get ưu đãi voucher thiết bị
                        var servicesProductsEVC = GetListInforProductService(voucher.evoucherCode, xmlServiceAll, input.Products);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("servicesProductsEVC ", servicesProductsEVC));

                        var serviceApply = new List<Apply>();
                        if (servicesEVC?.Any() == true)
                        {
                            serviceApply.AddRange(servicesEVC);
                        }
                        if (servicesProductsEVC?.Any() == true)
                        {
                            serviceApply.AddRange(servicesProductsEVC);
                        }
                        serviceApply = serviceApply.Distinct().ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("serviceApply ", serviceApply));

                        var registerModel = GetRegisterTypeModel(voucher.evoucherCode);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("registerModel ", registerModel));

                        // 4. Tính tiền cho từng nhóm service
                        var listDataIn4 = new SalePlatformVoucherValueInfor();
                        listDataIn4.Apply = new List<Apply>();
                        listDataIn4.evoucherCode = privateCode == "" ? voucher.evoucherCode : privateCode;
                        listDataIn4.Quota = 0;

                        listDataIn4.RegisterTypeID = registerModel.RegisterTypeID;
                        listDataIn4.ApplyTypeID = registerModel.ApplyTypeID;
                        listDataIn4.PolicyGroupID = registerModel.PolicyGroupID;
                        listDataIn4.PromotionTypeID = registerModel.PromotionTypeID;
                        listDataIn4.VoucherTypeL2 = 1;
                        listDataIn4.VendorContract = registerModel.VendorContract;

                        listDataIn4.Policy_Type = registerModel.Policy_Type;

                        foreach (var apply in serviceApply)
                        {
                            var DataQty = service_order_all.Where(x => x.serviceId == apply.ServiceID
                                                          && x.subServiceTypeId == apply.SubServiceTypeID
                                                          && x.subServiceId == apply.SubServiceID //&& x.deployTypeId == apply.DeployTypeID
                                                          && x.serviceCode == apply.ServiceCode).FirstOrDefault();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("DataQty", DataQty));

                            int qty = DataQty.qty;
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("qty", qty));

                            for (int i = 0; i < qty; i++)
                            {
                                Apply apply1 = new Apply()
                                {
                                    ServiceCode = apply.ServiceCode,
                                    DeployTypeID = apply.DeployTypeID,
                                    SubServiceID = apply.SubServiceID,
                                    ServiceID = apply.ServiceID,
                                    StatusID = apply.StatusID,
                                    SubServiceTypeID = apply.SubServiceTypeID,
                                    Discount = apply.Discount,
                                    DiscountVAT = apply.DiscountVAT,
                                    Dismonth = apply.Dismonth,
                                    IsDeductOrder = apply.IsDeductOrder,
                                    RevokeID = apply.RevokeID,
                                    Value = apply.Value
                                };

                                listDataIn4.Apply.Add(apply1);
                            }
                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("listDataIn4 ", listDataIn4));
                        // 
                        if (/*listDataIn4.Apply?.Any() == true*/true)
                        {
                            dataAll_evc.Add(listDataIn4);
                        }
                    }
                }



            }
            catch (Exception e)
            {


            }
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetVoucherinfoV2 End", ""));
            return dataAll_evc;
        }
        #region loy_qlcs_new_customer
        public static List<event_prepaid_config> get_promotion_loy(string regcode)
        {
            using(var conn = new SqlConnection(Utility.ConnRead))
            {
                return conn.Query<event_prepaid_config>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                {
                    action_name = "get_promotion_event",
                    regCode = regcode
                }, commandType: CommandType.StoredProcedure).ToList();
            }
        }

        public static List<event_prepaid_config_valid> get_promotion_loy_valid(string fullCodes)
        {
            using (var conn = new SqlConnection(Utility.ConnRead))
            {
                var result = conn.Query<event_prepaid_config_valid>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                {
                    action_name = "get_promotion_event_valid",
                    fullCodes = fullCodes
                }, commandType: CommandType.StoredProcedure).ToList();
                
                return result ?? new List<event_prepaid_config_valid>();
            }
        }

        public static logitem get_promotion_loy_map_prepaid(SalePolicyRedeemEVC input)
        {
            var logitem = new logitem();
            StringBuilder sb = new StringBuilder();
            var event_prepaid = get_promotion_loy(input.RegCode);
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("get_promotion_loy", event_prepaid));

            var event_prepaid_valid = get_promotion_loy_valid(string.Join(",", input.evoucherList.Select(x => x.evoucherCode)));
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("get_promotion_loy_valid", event_prepaid_valid));

            var event_prepaid_check = new List<event_prepaid_config>();

            foreach (var service in input.Services.Where(x => x.ServiceID == 1))
            {
                foreach (var subtype in service.SubServiceTypes)
                {
                    foreach (var subservice in subtype.SubServices)
                    {
                        var i = event_prepaid.Where(x => x.prepaid == subservice.PrePaid 
                                                        && x.month_used == subservice.MonthUsed 
                                                        && (event_prepaid_valid?.Where(a=>a.Item2 == x.amount)?.Any() ?? false)).ToList();
                        if (i != null && i.Count > 0)
                        {
                            event_prepaid_check.AddRange(i);
                        }
                    }
                }
            }

            if (event_prepaid_check.Count == 0) return null;

            var result = event_prepaid_check.OrderByDescending(x => x.prepaid).FirstOrDefault();

            result.gold = GetGoldFromDSC(result.action_code, input.CustomerInfor.LocationID, input.CustomerInfor.BranchCode, result.voucherType, result.event_code, sb);

            logitem.config = result;
            logitem.log = sb.ToString();

            return logitem;
        }


        public static int GetGoldFromDSC(string actionCode, int locationId, int branchCode, string voucherType, string eventCode, StringBuilder sb)
        {
            try
            {
                LoginInforShared login = new LoginInforShared() { username = Utility.loyaltyapi_username, password = Utility.loyaltyapi_password };
                AuthorizationInforShared aut = FuncShared.GetTokenv2(login, "");
                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);

                    var url = $"/internal/api/request-action/coins?actionCode={actionCode}&locationId={locationId}&branchCode={branchCode}&voucherType={voucherType}";
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC url", url));

                    var response = client.GetAsync(url).Result;
                    response.EnsureSuccessStatusCode();

                    // Log the response after receiving
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC response", response));

                    string result = response.Content.ReadAsStringAsync().Result;
                    var respose = JsonConvert.DeserializeObject<GetGold>(result);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC respose", respose));

                    if (respose == null || !respose.Data.EventCodes.Contains(eventCode))
                    {
                        return 0;
                    }
                    return respose.Data.Coins;
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC ex", e.Message));
                return 0;
            }
        }
        #endregion

        public static int GetGoldFromDSC_test(string actionCode, int locationId, int branchCode, StringBuilder sb)
        {
            try
            {
                LoginInforShared login = new LoginInforShared() { username = Utility.loyaltyapi_username, password = Utility.loyaltyapi_password };
                AuthorizationInforShared aut = FuncShared.GetTokenv2(login, "");
                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);

                    var url = $"/internal/api/request-action/coins?actionCode={actionCode}&locationId={locationId}&branchCode={branchCode}";
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC url", url));

                    var response = client.GetAsync(url).Result;
                    response.EnsureSuccessStatusCode();

                    // Log the response after receiving
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC response", response));

                    string result = response.Content.ReadAsStringAsync().Result;
                    var respose = JsonConvert.DeserializeObject<dynamic>(result);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC respose", respose));

                    return respose.data.coins;
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC ex", e.Message));
                return 0;
            }
        }

    }
}