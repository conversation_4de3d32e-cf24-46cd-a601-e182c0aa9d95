using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Dapper;
using System.Data;
using Voucher.APIHelper;
using System.Xml.Linq;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models;
using APIMBS.Constant;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using Voucher.APIHelper.Util;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Configuration;
using System.Text;


namespace APIMBS.Service
{
    public class MBSv4RedeemVoucherAdvocacyProgram
    {
        #region Redeem Voucher campaign
        public static bool RedeemVoucherCampaign(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId,SalesManInfo Salesman,int channel, int localtype, string regCode,string contractSuport,List<DeviceModel> devices,Guid logId, StringBuilder sb)
        {
            bool res = false;
            List<PEDiscountModel> rpDiscountUpdate = new List<PEDiscountModel>();
            try
            {
                var idPrepaidTime = MBSv4GetListVoucher.NewGetPrepaidID(connection, transaction, prepaid_net_tv.Item1, prepaid_net_tv.Item2);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign idPrepaidTime", idPrepaidTime));
                
                int typeRF = MBSv4GetListVoucher.NewCheckReferalProgram(connection, transaction, voucherCode, channel, logId);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign typeRF", typeRF));

                if (typeRF.Equals(1)) // type=1 là GTBB MyFPT
                {
                    return RedeemRFMyFPT(connection, transaction, objid, orderCode, localtype, saleId, voucherCode, idPrepaidTime, regCode, logId.ToString(), sb);
                }
                if (typeRF.Equals(2)) // type=2 là GTBB FPT play
                {
                    return OttReferalProgramService.RedeemRFOTT(connection, transaction, objid, orderCode, localtype, saleId, voucherCode, idPrepaidTime, regCode, logId.ToString(), sb);
                }

                Tuple<int, int> objidGT = ReferalProgramHiFPT.GetObjIdbyInviteCode(connection, transaction, voucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign objidGT", objidGT));

                var isOwnerCamera = ReferalProgramHiFPT.IsOwnerCamera(connection, transaction, objidGT.Item1, logId.ToString());
                if (channel.Equals(1) && !string.IsNullOrEmpty(contractSuport)) // đăng ký hộ
                {
                    // NET và truyền hình
                    if (idPrepaidTime >0)
                    {
                        if (channel.Equals(1) && isOwnerCamera)
                        {
                            return FriendSellServices.RedeemInviteCodeOwnerCamera(connection, transaction, voucherCode, objid, orderCode, prepaid_net_tv, saleId, Salesman, idPrepaidTime, channel, localtype, regCode, objidGT.Item1, logId, sb);
                        }
                        return FriendSellServices.RedeemFriendSellNetTV(connection, transaction, contractSuport, objid, voucherCode, idPrepaidTime, prepaid_net_tv, regCode, objidGT.Item1, logId, sb);
                    }
                    // CMR
                    if (devices.Count > 0)
                    {
                        return ReferalProgramHiFPT.AdvocacyCamera(connection, transaction, contractSuport, objid, voucherCode, orderCode, devices, regCode, logId, sb);
                    }
                    
                }
                
                /*if (channel.Equals(0))
                {
                    if (idPrepaidTime == 0) return false;
                }*/
                #region GTBB comment
                /*
                // get ObjOfUserInvite
                int objGT = GetOBJid(connection, transaction,voucherCode);
                if (objGT == 0) return false;
                //GetObjInvite(connection, transaction, ref input);

                // convert Rp to voucher code
                // query with index = 0 is discount for invited user
                // query with index = 1 is discount for invite user
                rpDiscountUpdate = GetPEREF(voucherCode, idPrepaidTime, BuildPackageType(prepaid_net_tv));
                MBSv4CommonService.WriteToLog(rpDiscountUpdate, "RedeemVoucherCampaign rpDiscountUpdate", logId);
                if (rpDiscountUpdate.Count != 3) return false;                                
                
                FoxGold EventCodeLoy = ReferalProgramHiFPT.GetEventCodeLoy(objid);
                var request = new List<RPCodeRedeemModel>{
                                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[1].ID, 
                                    VoucherCode = rpDiscountUpdate[1].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objid,
                                    ActiveChannel = 222,
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                },
                                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[2].ID, 
                                    VoucherCode = rpDiscountUpdate[2].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objGT,
                                    ActiveChannel = 200, 
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                }
                            };
                // add GC code cho người đăng ký
                var inforVoucherInvited = new List<RPCodeRedeemModel> { 
                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[1].ID, 
                                    VoucherCode = rpDiscountUpdate[1].EventCode, 
                                    OrderCode =regCode, 
                                    Salesman = saleId, 
                                    ObjID = objid,
                                    ActiveChannel = 222,
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                },
                };

                var inforVoucherInvite = new List<RPCodeRedeemModel> { 
                                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[2].ID, 
                                    VoucherCode = rpDiscountUpdate[2].EventCode, 
                                    OrderCode = regCode, 
                                    Salesman = saleId, 
                                    ObjID = objGT,
                                    ActiveChannel = 200, 
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = localtype,
                                    PaidTimeTypeID = idPrepaidTime
                                }
                };
                // add success invite
                bool isAddSuccess = InsertInviteCodeSuccess(connection, transaction, request, EventCodeLoy.EventCode, voucherCode);
                MBSv4CommonService.WriteToLog(isAddSuccess, "RedeemVoucherCampaign isAddSuccess", logId);
                if (!isAddSuccess) return false;                    

                generalCodeID = RedeemRPCodeMBS(connection, transaction, inforVoucherInvited, voucherCode);
                MBSv4CommonService.WriteToLog(generalCodeID, "RedeemVoucherCampaign generalCodeID", logId);

                var addGCcodeInitve = RedeemRPCodeMBS(connection, transaction, inforVoucherInvite, voucherCode, EventCodeLoy);
                MBSv4CommonService.WriteToLog(addGCcodeInitve, "RedeemVoucherCampaign addGCcodeInitve", logId);

                if (generalCodeID <= 0) return false;
                else res = (generalCodeID > 0);
                
                // update status
                UpdateStatusRPCode(connection, transaction, voucherCode);

                
                // add voucher TemRedem cho objid đăng ký
                MBSv4CommonService.UpdateDiscount(
                    objid,  // objID user invited
                    rpDiscountUpdate[1].NetPromotionID, //XđồngY tháng NET + Tháng
                    rpDiscountUpdate[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    rpDiscountUpdate[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    rpDiscountUpdate[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    rpDiscountUpdate[1].EventCode, // Code
                    saleId, false //Sales -TypeVC:True.
                );


                // add voucher TemRedem cho objid Giới thiệu
                MBSv4CommonService.UpdateDiscount(
                    objGT,  // objID user invite
                    rpDiscountUpdate[2].NetPromotionID, //XđồngY tháng NET + Tháng
                    rpDiscountUpdate[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    rpDiscountUpdate[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    rpDiscountUpdate[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    rpDiscountUpdate[2].EventCode, // Code
                    saleId, false //Sales -TypeVC:True
                );

                MBSv4CommonService.AddVoucherBill(objid, regCode, generalCodeID, idPrepaidTime);
                 */
                #endregion
                // GTBB net và truyền hình
                if (idPrepaidTime > 0)
                {
                    if (channel.Equals(1) && isOwnerCamera)
                    {
                       return ReferalProgramHiFPT.RedeemInviteCodeOwnerCamera(connection, transaction, voucherCode, objid, orderCode, prepaid_net_tv, saleId, Salesman, idPrepaidTime, channel, localtype, regCode,objidGT.Item1, logId, sb);
                    }
                    return ReferalProgramHiFPT.RedeemInviteCodeNetTV(connection, transaction, voucherCode, objid, orderCode, prepaid_net_tv, saleId, Salesman, idPrepaidTime, channel, localtype, regCode, contractSuport,  objidGT.Item1, logId, sb);
                }
                if (devices.Count > 0)
                {
                    return ReferalProgramHiFPT.AdvocacyCamera(connection, transaction, contractSuport, objid, voucherCode, orderCode, devices, regCode, logId, sb);
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherCampaign Error", ex.Message));
                return false;
            }
            return res;
        }

        public static int GetOBJid(SqlConnection connection, SqlTransaction transaction, string InviteCode)
        {
            return connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                voucherCode = InviteCode,
                actionName = "GetOBJid",
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static void UpdateStatusRPCode(SqlConnection connection, SqlTransaction transaction, string InviteCode)
        {
            connection.Query<int>(ConstantAPI.OS6_ReferralProgram_InviteCode, new
            {
                InviteCode = InviteCode,
                ActionName = "UpdateInvite",
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static bool InsertInviteCodeSuccess(SqlConnection connection, SqlTransaction transaction, List<RPCodeRedeemModel> input, string eventLoyalty, string voucherRP)
        {
            int InsertInviteSuccess = connection.Query<int>(ConstantAPI.OS6_ReferralProgram_SucceedInvite, new
            {
                ActionName = "InsertInfoRedeemRPCode",
                XML = CreateXMLRedeemIV(input, eventLoyalty, voucherRP),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StatusUpdateInviteSuccess = (InsertInviteSuccess == 1) }));

            return InsertInviteSuccess == 1;
        }
        private static XElement CreateXMLRedeemIV(List<RPCodeRedeemModel> input, string eventLoyalty, string voucherRP)
        {
            var xmlString = new XElement("N",
                new XElement("I",
                    new XElement("RPinvited", voucherRP),  // nguoi giới thiệu
                    new XElement("RPLoyalty", eventLoyalty), // Mã ưu đãi add điểm của Loyalty
                    new XElement("Vinvite", input[1].VoucherCode),
                    new XElement("Vinvited", input[0].VoucherCode),
                    new XElement("Or", input[0].OrderCode),
                    new XElement("Oinvited", input[0].ObjID),
                    new XElement("Oinvite", input[1].ObjID),
                    new XElement("Oinvite", input[1].ObjID)
                )
            );
            return xmlString;
        }
        public static int RedeemRPCodeMBS(SqlConnection connection, SqlTransaction transaction, List<RPCodeRedeemModel> input, string voucherRP, FoxGold eventLoyalty =null)
        {
            XElement xml = null;
            string store = string.Empty;
            if (eventLoyalty == null)
            {
                xml = CreateXMLRedeemRPcode(input);
                store = MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4;

            }
            else { 
                xml = CreateXMLRedeemRPcode(input, eventLoyalty);
                store = ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher;
            }
            int idGC = connection.Query<int>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
            {
                actionName = "InsertGeneralCodeXML",
                xml = xml,
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            return idGC;
        }
        private static XElement CreateXMLRedeemRPcode(List<RPCodeRedeemModel> input, FoxGold eventLoy = null)
        {
            int PrepaidTimeTV;
            if (input[0].IsPrepaidTV > 0)
                PrepaidTimeTV = 1;
            else if (input[0].IsPrepaidTV < 0)
                PrepaidTimeTV = -1;
            else
                PrepaidTimeTV = 0;

            var xmlString = new XElement("N",
                new XElement("I",
                    new XElement("L", input[0].Location),
                    new XElement("B", input[0].BranchCode),
                    new XElement("D", input[0].Department),
                    new XElement("S", input[0].Salesman),

                    new XElement("C", input[0].VoucherCode),
                    new XElement("P", input[0].PromotionEventID),
                    new XElement("Or", input[0].OrderCode),

                    new XElement("Obj", input[0].ObjID),

                    new XElement("BNET", input[0].BNET),
                    new XElement("BTV", input[0].BTV),
                    new XElement("IsPrepaidTV", PrepaidTimeTV),

                    new XElement("Ac", input[0].ActiveChannel),

                    new XElement("Su", input[0].SubCompanyID),
                    new XElement("Lo", input[0].LocalTypeID),
                    new XElement("Pa", input[0].PaidTimeTypeID)
                )
            );
            /*
            xmlString.Add(
                new XElement("I",
                    new XElement("L", input[1].Location),
                    new XElement("B", input[1].BranchCode),
                    new XElement("D", input[1].Department),
                    new XElement("S", input[1].Salesman),

                    new XElement("C", input[1].VoucherCode),
                    new XElement("P", input[1].PromotionEventID),
                    new XElement("Or", input[1].OrderCode),

                    new XElement("BNET", input[1].BNET),
                    new XElement("BTV", input[1].BTV),
                    new XElement("IsPrepaidTV", input[1].IsPrepaidTV),

                    new XElement("Obj", input[1].ObjID),
                    new XElement("Ac", input[1].ActiveChannel),

                    new XElement("Su", input[1].SubCompanyID),
                    new XElement("Lo", input[1].LocalTypeID),
                    new XElement("Pa", input[1].PaidTimeTypeID)
                )
            );*/

            if (eventLoy != null)
            {
                xmlString.Add(
                    new XElement("I",
                        new XElement("L", input[0].Location),
                        new XElement("B", input[0].BranchCode),
                        new XElement("D", input[0].Department),
                        new XElement("S", input[0].Salesman),

                        new XElement("C", eventLoy.EventCode),
                        new XElement("P", eventLoy.ID),
                        new XElement("Or", input[0].OrderCode),

                        new XElement("BNET", input[0].BNET),
                        new XElement("BTV", input[0].BTV),
                        new XElement("IsPrepaidTV", input[0].IsPrepaidTV),

                        new XElement("Obj", input[0].ObjID),
                        new XElement("Ac", input[0].ActiveChannel),

                        new XElement("Su", input[0].SubCompanyID),
                        new XElement("Lo", input[0].LocalTypeID),
                        new XElement("Pa", input[0].PaidTimeTypeID)
                    )
                );
            }          

            return xmlString;
        }

        private static int GetQuotaGeneralCodeUsed(int PromotionEventID)
        {
            int quota = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                quota = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
                {
                    ActionName = "GetQuotaGeneralCodeUsed",
                    PromotionEvent = PromotionEventID,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return quota;

        }
        private static int GetTotalQuotaValiable(SalesManInfo SalesInfo, int PromotionEventID, int IDSalesMan, int TotalQuotaUsed)
        {
            try
            {
                Dictionary<string, int> QuotaAssign = new Dictionary<string, int>
                {
                    { ConstantAction.SALESMAN, 0 },
                    { ConstantAction.DEPARTMENT, 0 },
                    { ConstantAction.BRANCHCODE, 0 },
                    { ConstantAction.LOCATION, 0 },
                    { ConstantAction.REGION, 0 }
                };

                for (int i = 0; i < QuotaAssign.Count(); i++)
                {
                    SalesInfo.Quota = GetQuotaOfNodeIsAssign(QuotaAssign.ElementAt(i).Key, SalesInfo, PromotionEventID, ConstantAction.GetQuota);
                    L.Mes(Level.QUOTAINNODE, string.Format("{0} - {1}", QuotaAssign.ElementAt(i).Key, SalesInfo.Quota));

                    if (i == 0 && SalesInfo.Quota > 0)
                    {
                        SalesInfo.Quota = SalesInfo.Quota - TotalQuotaUsed;
                        break;
                    }

                    if (SalesInfo.Quota > 0 && (QuotaAssign.ElementAt(i - 1).Value == 0))
                    {
                        int SumQuota = GetQuotaOfNodeIsAssign(QuotaAssign.ElementAt(i - 1).Key, SalesInfo, PromotionEventID, ConstantAction.GetQuotaParallelNode);

                        SalesInfo.Quota = SalesInfo.Quota - SumQuota;
                        SalesInfo.Quota = SalesInfo.Quota - TotalQuotaUsed;
                        break;
                    }
                    if ((SalesInfo.Quota > 0) && (QuotaAssign.ElementAt(i - 1).Value == -1))
                    {
                        int CountRow = GetQuotaOfNodeIsAssign(QuotaAssign.ElementAt(i - 1).Key, SalesInfo, PromotionEventID, ConstantAction.CheckContainChildNode);
                        if (CountRow > 0)
                        {
                            return -1;
                        }
                        else
                        {
                            SalesInfo.Quota = SalesInfo.Quota - TotalQuotaUsed;
                            break;
                        }
                        break;
                    }

                    if (i == 4 && SalesInfo.Quota == -1)
                        return -1;
                    QuotaAssign[QuotaAssign.ElementAt(i).Key] = SalesInfo.Quota;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, "GetTotalQuotaValiable: " + ex.Message);
            }
            return SalesInfo.Quota;
        }
        private static int GetQuotaOfNodeIsAssign(string AssignPosition, SalesManInfo user, int PromotionID, string ActionName)
        {
            int Quota = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int Result = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_SalesManInfo, new
                {
                    ActionName = ActionName,
                    QuotaAction = AssignPosition,
                    AccountName = user.Name,
                    REGION = user.REGION,
                    LOCATION = user.LOCATION,
                    BRANCH = user.BRANCHCODE,
                    DEPARTMENT = user.DEPARTMENT,
                    SALESMAN = user.SALESMAN,
                    PromotionID = PromotionID
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                Quota = Result;
            }
            return Quota;
        }
        public static string BuildPackageType(Tuple<int, int> input)
        {
            if (input.Item1 == -1 && input.Item2 > -1) return "TVONLY";
            else if (input.Item1 > -1 && input.Item2 > -1) return "COMBO";
            else if (input.Item1 > -1 && input.Item2 == -1) return "NETONLY";
            else return "";
        }
        public static List<PEDiscountModel> GetPEREF(SqlConnection connection, SqlTransaction transaction, string VoucherRP, int PaidTimeTypePE, string PackageTypeInvited, int objidGT)
        {
            List<PEDiscountModel> res = new List<PEDiscountModel>();
            var parameters = new DynamicParameters();
            parameters.Add("@ActionName", "GetReferralPE");
            parameters.Add("@InviteCode", VoucherRP);
            parameters.Add("@PackageTypeInvited", PackageTypeInvited);
            parameters.Add("@PaidTimeType", PaidTimeTypePE);
            parameters.Add("@ObjID", objidGT);
            res = connection.Query<PEDiscountModel>(ConstantAPI.OS6_ReferralProgram_DKOLV5, parameters, transaction: transaction, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
            return res;
        }

        public static bool RedeemRFMyFPT(SqlConnection connection, SqlTransaction transaction, int objid, string orderCode, int localtype, int saleid, string vouchercode, int paidTimeTypeID, string regCode,string keylog, StringBuilder sb)
        {
            Guid Logid = Guid.Parse(keylog);
            try
            {
                FoxproUseInviteCode insertInviteCode = connection.Query<FoxproUseInviteCode>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                {
                    actionName = "InsertInviteCode",
                    objID = objid,
                    orderCode = regCode,
                    //bnet = 0,
                    //btv = 0,
                    inviteCode = vouchercode,
                    PaidTimeTypeID = paidTimeTypeID
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                if (insertInviteCode.res == 0)
                {
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemRFMyFPT Dịch vụ không phải NETONLY hoặc COMBO", ""));
                    return false;
                }
                else
                {
                    int objID = insertInviteCode.ObjID;
                    int MNET = insertInviteCode.MoneyPromotionNETID;
                    int MTV = insertInviteCode.MoneyPromotionTVID;
                    int StaffID = Convert.ToInt32(insertInviteCode.StaffIDInvite);
                    MBSv4CommonService.NewUpdateDiscount(
                         connection,
                         transaction,
                         objID,  // objID khách hàng
                         0, //XđồngY tháng NET + Tháng
                         0, //XđồngY tháng TV + Tháng
                         MNET, //Giảm Tiền Trực Tiếp NET
                         MTV, //Giảm Tiền Trực Tiếp TV
                         insertInviteCode.EventCode, // Code
                         StaffID, false);

                    //MBSv4CommonService.NewAddVoucherBill(connection, transaction, objid, regCode, insertInviteCode.generalCodeID, paidTimeTypeID);

                    bool statusRedeem = SendNotificationReferralMyFPT(insertInviteCode, keylog);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("SendNotificationReferralMyFPT", statusRedeem));
                    return true;
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemRFMyFPT", ex.Message));
                return false;
            }
        }
        

        private static Boolean SendNotificationReferralMyFPT(dynamic input, string keylog)
        {
            int iStatusCode = 0;
            LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
            AuthorizationInfor aut = getAuthorInfor(keylog);
            try
            {
                if (aut == null)
                    return false;
                var request = new ModelSendNotificationReferralMyFPT()
                {
                    employeeCode = input.StaffIDInvite,
                    Referrer = input.RealMoneyAmount,
                    BookingId = input.Contract,
                    contract_owner = input.FullName,
                    BookingStatus = "pending"
                };

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    //client.DefaultRequestHeaders.Add("Authorization", aut.authorization);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/api/oauth-ms/public/auth/integration-supplier", data).Result;
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(response), string.Concat("SendNotificationReferralMyFPT ", keylog));
                    iStatusCode = (int)response.StatusCode;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, string.Concat("SendNotificationReferralMyFPT ", keylog));
                return false;
            }
            //dynamic response = new dynamic();

            if (iStatusCode == 200)
            {
                return true;
            }

            return false;
        }
        private static AuthorizationInfor getAuthorInfor(string keylog)
        {
            L.Mes(Level.INFO, "Start to get token myfpt", string.Concat("getAuthorInfor ", keylog));
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                var uri = WebAPIHelper.myFpt_fpt_vn + "/api/oauth-ms/public/auth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("client_id", Utility.my_fpt_api_client_id));
                keyValues.Add(new KeyValuePair<string, string>("client_secret", Utility.my_fpt_api_client_secret));
                keyValues.Add(new KeyValuePair<string, string>("username", Utility.my_fpt_api_username));
                keyValues.Add(new KeyValuePair<string, string>("password", Utility.my_fpt_api_password));
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "password"));

                var content = new FormUrlEncodedContent(keyValues);

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        response.EnsureSuccessStatusCode();
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, string.Concat("getAuthorInfor ", keylog));
                        res = JsonConvert.DeserializeObject<AuthorizationInfor>(r);
                    }
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, string.Concat("getAuthorInfor ", keylog));
                return null;
            }
            return res;
        }
        #endregion
    }
}