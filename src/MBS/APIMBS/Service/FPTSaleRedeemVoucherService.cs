using APIMBS.Constant;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models.SalePlatform;
using APIMBS.Service.EvcPolicyServices;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using Voucher.APIHelper;

namespace APIMBS.Service
{
    public static class FPTSaleRedeemVoucherService
    {
        public const string OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend = "PowerInside.dbo.OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend";
        public const string OS6_FPTVoucher_FSale = "PowerInside.dbo.OS6_FPTVoucher_FSale";
        public const string OSU6_FPTVoucher_PolicyVoucher_VoucherLimit_UpdateUsedMoney = "PowerInside.dbo.OSU6_FPTVoucher_PolicyVoucher_VoucherLimit_UpdateUsedMoney";
        public const string OS6_FPTVoucher_SalePlatform_MBSv4 = "PowerInside.dbo.OS6_FPTVoucher_SalePlatform_MBSv4";
        public const string OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable = "PowerInside.dbo.OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable";
        public const string OSU6_FPTVoucher_AddDiscount = "PowerInside.dbo.OSU6_FPTVoucher_AddDiscount";
        public const string OSU6_FPTVoucher_UpdateActiveStatus = "PowerInside.dbo.OSU6_FPTVoucher_UpdateActiveStatus";

        public static bool RedeemVoucher(SqlConnection connection, SqlTransaction transaction, SalePolicyRedeemEVC input, StringBuilder sb)
        {
            var result = false;
            try
            {
                List<ServicePlatform> lsp = input.Services.Where(x => x.ServiceID == 1).ToList();
                int localType = 0;

                if (lsp.Count > 0)
                {
                    localType = MBSv4GetListVoucher.GetLocaltype(lsp[0].SubServiceTypes[0].SubServices[0].SubServiceID);
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher localType", localType));

                foreach (var voucher in input.evoucherList)
                {
                    #region voucher chon
                    if (voucher.evoucherType == 1)
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog(voucher.evoucherCode, ""));
                        decimal money = 0;

                        var unitParent = GetListServices2.GetUnitParent(input.SaleInfor.SaleID, sb);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("unitParent ", unitParent));

                        var limitType = GetLimitType(connection, transaction, voucher.evoucherCode);

                        var PromotionTypeID = 0;
                        PromotionTypeID = GetPromotionTypeID(connection, transaction, voucher.evoucherCode);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Fsale_RedeemVoucher PromotionTypeID", PromotionTypeID));

                        var valueServices = connection.Query<ServiceValueModel>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                        {
                            Action = "GetValueServices",
                            VoucherCode = voucher.evoucherCode
                        }, transaction: transaction, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC valueServices", valueServices));

                        var valueProducts = connection.Query<ServiceValueModel>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
                        {
                            Action = "GetValueProductServices",
                            VoucherCode = voucher.evoucherCode
                        }, transaction: transaction, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC valueProducts", valueProducts));

                        var discountServices = (from service in input.Services
                                                from subServiceType in service.SubServiceTypes
                                                from subService in subServiceType.SubServices
                                                from valueService in valueServices
                                                where service.ServiceID == valueService.ServiceID && subServiceType.SubServiceTypeID == valueService.SubServiceTypeID
                                                && (valueService.PolicyGroupID != 101 || (valueService.MonthValue > 0 || valueService.Value > 0 || valueService.ConnectionFee > 0))
                                                select new DiscountModel()
                                                {
                                                    Code = voucher.evoucherCode,
                                                    OrderCode = input.OrderCode,
                                                    RegCode = input.RegCode,
                                                    SaleId = input.SaleInfor.SaleID,
                                                    ActiveChannel = input.SaleInfor.SaleChannelID,
                                                    ObjId = input.objId,
                                                    LocationID = input.CustomerInfor.LocationID,
                                                    LocalTypeId = localType,
                                                    PolicyId = valueService.PolicyID,
                                                    ServiceId = service.ServiceID,
                                                    SubServiceTypeId = subServiceType.SubServiceTypeID,
                                                    SubServiceId = subService.SubServiceID,
                                                    ServiceCode = subService.ServiceCode,
                                                    Qty = subService.Qty,
                                                    monthUse = subService.MonthUsed,
                                                    Total = subService.Total,
                                                    BranchCode = input.CustomerInfor.BranchCode,
                                                    DistrictID = input.CustomerInfor.DistrictID,
                                                    SaleChannelId = input.SaleInfor.SaleChannelID,
                                                    SaleTeamID = input.SaleInfor.SaleTeamID,
                                                    PaymentTypeID = input.PaymentTypeID,
                                                    CrossSellingLocation = input.CrossSellingLocation,
                                                    ObjectTypeId = input.CustomerInfor.ObjectTypeID,
                                                    CusTypeId = input.CustomerInfor.CusTypeID,
                                                    CusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                                    CustomerRank = input.CustomerInfor.CustomerRank,
                                                    UnitParentID = unitParent == null ? 0 : unitParent.ParentId,
                                                    UnitParentCode = unitParent == null ? "" : unitParent.ParentCode,
                                                    LimitType = limitType,
                                                    UnitCode = unitParent == null ? "" : unitParent.Code
                                                }).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServices", discountServices));

                        var discountProducts = (from product in input.Products
                                                from subServiceType in product.SubServiceTypes
                                                from subService in subServiceType.SubServices
                                                from valueService in valueProducts
                                                where product.ServiceID == valueService.ServiceID && subServiceType.SubServiceTypeId == valueService.SubServiceTypeID
                                                && (valueService.PolicyGroupID != 101 || (valueService.MonthValue > 0 || valueService.Value > 0 || valueService.ConnectionFee > 0))
                                                select new DiscountModel()
                                                {
                                                    Code = voucher.evoucherCode,
                                                    OrderCode = input.OrderCode,
                                                    RegCode = input.RegCode,
                                                    SaleId = input.SaleInfor.SaleID,
                                                    ActiveChannel = input.SaleInfor.SaleChannelID,
                                                    ObjId = input.objId,
                                                    LocationID = input.CustomerInfor.LocationID,
                                                    LocalTypeId = localType,
                                                    PolicyId = valueService.PolicyID,
                                                    ServiceId = product.ServiceID,
                                                    SubServiceTypeId = subServiceType.SubServiceTypeId,
                                                    SubServiceId = subService.SubServiceID,
                                                    ServiceCode = subService.ServiceCode,
                                                    Qty = subService.Qty,
                                                    Total = valueService.Value,
                                                    BranchCode = input.CustomerInfor.BranchCode,
                                                    DistrictID = input.CustomerInfor.DistrictID,
                                                    SaleChannelId = input.SaleInfor.SaleChannelID,
                                                    SaleTeamID = input.SaleInfor.SaleTeamID,
                                                    PaymentTypeID = input.PaymentTypeID,
                                                    CrossSellingLocation = input.CrossSellingLocation,
                                                    ObjectTypeId = input.CustomerInfor.ObjectTypeID,
                                                    CusTypeId = input.CustomerInfor.CusTypeID,
                                                    CusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                                    CustomerRank = input.CustomerInfor.CustomerRank,
                                                    UnitParentID = unitParent == null ? 0 : unitParent.ParentId,
                                                    UnitParentCode = unitParent == null ? "" : unitParent.ParentCode,
                                                    LimitType = limitType,
                                                    UnitCode = unitParent == null ? "" : unitParent.Code
                                                }).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountProducts", discountProducts));

                        var AddGeneralCodeModel = new DiscountModel();
                        if (discountServices?.Count > 0)
                        {
                            AddGeneralCodeModel = discountServices.FirstOrDefault();
                        }
                        if (discountProducts?.Count > 0)
                        {
                            AddGeneralCodeModel = discountProducts.FirstOrDefault();
                        }

                        int generalCodeId = 0;

                        if (AddGeneralCodeModel != null)
                        {
                            generalCodeId = AddGeneralCode(connection, transaction, AddGeneralCodeModel);
                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ma chan generalCodeId", generalCodeId));

                        if (discountServices?.Count > 0)
                        {
                            if (generalCodeId > 0) // insert generalCode thanh cong
                            {
                                // NET
                                ServiceValueModel promotionNet = valueServices.FirstOrDefault(x => x.ServiceID == 1);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("promotionNet", promotionNet));

                                // TV
                                ServiceValueModel promotionTv = valueServices.FirstOrDefault(x => x.ServiceID == 7);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("promotionTv", promotionTv));

                                //fSaleGo, ultrafast
                                ServiceValueModel promotionFsaleGo = valueServices.FirstOrDefault(x => x.ServiceID == 12 || x.ServiceID == 10);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("promotionFsaleGo", promotionFsaleGo));

                                if (PromotionTypeID == 1)// Tặng tiền trực tiếp
                                {
                                    result = ProcessPromotionServiceType1(connection, transaction, discountServices, input, promotionNet, promotionTv, promotionFsaleGo, generalCodeId, sb);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                                if (PromotionTypeID == 2)// Tặng x tiền y tháng
                                {
                                    result = ProcessPromotionServiceType2(connection, transaction, discountServices, input, promotionNet, promotionTv, promotionFsaleGo, generalCodeId, sb);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    if (promotionNet != null)
                                    {
                                        var discountServiceNet = discountServices.FirstOrDefault(s => s.ServiceId == promotionNet.ServiceID);
                                        MonthValueModel evcMonthValueNet = GetMonthValue(discountServiceNet != null ? discountServiceNet.Code : "", promotionNet.ServiceID);

                                        if (evcMonthValueNet != null)
                                        {
                                            decimal moneyNET = evcMonthValueNet.monthDiscount * evcMonthValueNet.valueDiscount * discountServiceNet.Qty;
                                            money += moneyNET;
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money evcMonthValueNet", moneyNET));
                                            InsertGeneralCodeDetail(connection, transaction, discountServiceNet, generalCodeId, moneyNET);
                                        }
                                    }

                                    if (promotionTv != null)
                                    {
                                        var discountServiceTV = discountServices.FirstOrDefault(s => s.ServiceId == promotionTv.ServiceID);
                                        MonthValueModel evcMonthValueTV = GetMonthValue(discountServiceTV != null ? discountServiceTV.Code : "", promotionTv.ServiceID);

                                        if (evcMonthValueTV != null)
                                        {
                                            decimal moneyTV = evcMonthValueTV.monthDiscount * evcMonthValueTV.valueDiscount * discountServiceTV.Qty;
                                            money += moneyTV;
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money evcMonthValueTV", moneyTV));
                                            InsertGeneralCodeDetail(connection, transaction, discountServiceTV, generalCodeId, moneyTV);
                                        }

                                    }

                                    if (promotionFsaleGo != null)
                                    {
                                        var discountServiceGo = discountServices.FirstOrDefault(s => s.ServiceId == promotionFsaleGo.ServiceID);
                                        MonthValueModel evcMonthValueTV = GetMonthValue(discountServiceGo != null ? discountServiceGo.Code : "", promotionFsaleGo.ServiceID);

                                        if (evcMonthValueTV != null)
                                        {
                                            decimal moneyTV = evcMonthValueTV.monthDiscount * evcMonthValueTV.valueDiscount * discountServiceGo.Qty;
                                            money += moneyTV;
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money evcMonthValueTV", moneyTV));
                                            InsertGeneralCodeDetail(connection, transaction, discountServiceGo, generalCodeId, moneyTV);
                                        }

                                    }

                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                                if (PromotionTypeID == 4)// tang thang
                                {
                                    result = ProcessPromotionServiceType4(connection, transaction, discountServices, input, promotionNet, promotionTv, generalCodeId, sb);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                                if (PromotionTypeID == 3)// Phí hòa mạng
                                {
                                    result = ProcessPromotionServiceType3(connection, transaction, discountServices, input, promotionNet, promotionTv, generalCodeId, sb);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                            }
                        }
                        if (discountProducts?.Count() > 0 && generalCodeId > 0)
                        {
                            if(generalCodeId > 0)
                        {
                            if (PromotionTypeID == 1)
                            {
                                result = ProcessPromotionProdutcType1(connection, transaction, discountProducts, input, generalCodeId, sb); // tang tien tt

                                if (result == false)
                                {
                                    return false;
                                }

                                money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                            }

                            if (PromotionTypeID == 3)
                            {
                                    result = ProcessPromotionProdutcType3(connection, transaction, discountProducts, input, generalCodeId, sb); // tang tien PHM

                                if (result == false)
                                {
                                    return false;
                                }

                                money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                            }
                        }
                        }

                        if (money > 0)
                        {
                            CalcQuota(connection, transaction, voucher.evoucherCode, money, input.SaleInfor.SaleID, input.CustomerInfor.LocationID, input.CustomerInfor.BranchCode, input.CustomerInfor.DistrictID, DateTime.Now.Year, DateTime.Now.Month);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("CalcQuota", "thành công"));
                        }

                        var UpdateHistory = UpdateHistoryTem(connection, transaction, voucher.evoucherCode, input.objId, input.OrderCode, input.RegCode);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UpdateHistory", UpdateHistory));

                        int updatelongchauquota = VendorManager.UpdateQuotaVendorLongChau(connection, transaction, voucher.evoucherCode);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UpdateHistory", UpdateHistory));

                        //if (!UpdateHistory)
                        //{
                        //    return false;
                        //}
                    }
                    #endregion

                    #region voucher le
                    if (voucher.evoucherType == 2 && VendorManager.CheckFormatVoucher(voucher.evoucherCode) != "")
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog(voucher.evoucherCode, ""));
                        decimal money = 0;

                        var PromotionTypeID = GetPromotionTypeIDVendor(connection, transaction, VendorManager.CheckFormatVoucher(voucher.evoucherCode));
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Fsale_RedeemVoucher PromotionTypeID", PromotionTypeID));

                        if (!(PromotionTypeID > 0))
                        {
                            return false;
                        }

                        var valueServices = connection.Query<ServiceValueModel>(OS6_FPTVoucher_FSale, new
                        {
                            ActionName = "GetValueServicesVendor",
                            VoucherCode = VendorManager.CheckFormatVoucher(voucher.evoucherCode)
                        }, transaction: transaction, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC valueServices", valueServices));

                        var valueProducts = connection.Query<ServiceValueModel>(OS6_FPTVoucher_FSale, new
                        {
                            ActionName = "GetValueProductServicesVendor",
                            VoucherCode = VendorManager.CheckFormatVoucher(voucher.evoucherCode)
                        }, transaction: transaction, commandType: CommandType.StoredProcedure).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC valueProducts", valueProducts));

                        var discountServices = (from service in input.Services
                                                from subServiceType in service.SubServiceTypes
                                                from subService in subServiceType.SubServices
                                                from valueService in valueServices
                                                where service.ServiceID == valueService.ServiceID && subServiceType.SubServiceTypeID == valueService.SubServiceTypeID
                                                //&& (valueService.MonthValue > 0 || valueService.Value > 0 || valueService.ConnectionFee > 0)
                                                select new DiscountModel()
                                                {
                                                    Code = voucher.evoucherCode,
                                                    OrderCode = input.OrderCode,
                                                    RegCode = input.RegCode,
                                                    SaleId = input.SaleInfor.SaleID,
                                                    ActiveChannel = input.SaleInfor.SaleChannelID,
                                                    ObjId = input.objId,
                                                    LocationID = input.CustomerInfor.LocationID,
                                                    LocalTypeId = localType,
                                                    PolicyId = valueService.PolicyID,
                                                    ServiceId = service.ServiceID,
                                                    SubServiceTypeId = subServiceType.SubServiceTypeID,
                                                    SubServiceId = subService.SubServiceID,
                                                    ServiceCode = subService.ServiceCode,
                                                    Qty = subService.Qty,
                                                    monthUse = subService.MonthUsed,
                                                    Total = subService.Total,
                                                    BranchCode = input.CustomerInfor.BranchCode,
                                                    DistrictID = input.CustomerInfor.DistrictID,
                                                    SaleChannelId = input.SaleInfor.SaleChannelID,
                                                    SaleTeamID = input.SaleInfor.SaleTeamID,
                                                    PaymentTypeID = input.PaymentTypeID,
                                                    CrossSellingLocation = input.CrossSellingLocation,
                                                    ObjectTypeId = input.CustomerInfor.ObjectTypeID,
                                                    CusTypeId = input.CustomerInfor.CusTypeID,
                                                    CusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                                    CustomerRank = input.CustomerInfor.CustomerRank
                                                }).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServices", discountServices));

                        var discountProducts = (from product in input.Products
                                                from subServiceType in product.SubServiceTypes
                                                from subService in subServiceType.SubServices
                                                from valueService in valueProducts
                                                where product.ServiceID == valueService.ServiceID && subServiceType.SubServiceTypeId == valueService.SubServiceTypeID
                                                //&& (valueService.MonthValue > 0 || valueService.Value > 0 || valueService.ConnectionFee > 0)
                                                select new DiscountModel()
                                                {
                                                    Code = voucher.evoucherCode,
                                                    OrderCode = input.OrderCode,
                                                    RegCode = input.RegCode,
                                                    SaleId = input.SaleInfor.SaleID,
                                                    ActiveChannel = input.SaleInfor.SaleChannelID,
                                                    ObjId = input.objId,
                                                    LocationID = input.CustomerInfor.LocationID,
                                                    LocalTypeId = localType,
                                                    PolicyId = valueService.PolicyID,
                                                    ServiceId = product.ServiceID,
                                                    SubServiceTypeId = subServiceType.SubServiceTypeId,
                                                    SubServiceId = subService.SubServiceID,
                                                    ServiceCode = subService.ServiceCode,
                                                    Qty = subService.Qty,
                                                    Total = valueService.Value,
                                                    BranchCode = input.CustomerInfor.BranchCode,
                                                    DistrictID = input.CustomerInfor.DistrictID,
                                                    SaleChannelId = input.SaleInfor.SaleChannelID,
                                                    SaleTeamID = input.SaleInfor.SaleTeamID,
                                                    PaymentTypeID = input.PaymentTypeID,
                                                    CrossSellingLocation = input.CrossSellingLocation,
                                                    ObjectTypeId = input.CustomerInfor.ObjectTypeID,
                                                    CusTypeId = input.CustomerInfor.CusTypeID,
                                                    CusTypeL2Id = input.CustomerInfor.CusTypeL2ID,
                                                    CustomerRank = input.CustomerInfor.CustomerRank
                                                }).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountProducts", discountProducts));

                        var AddGeneralCodeModel = new DiscountModel();
                        if (discountServices?.Count > 0)
                        {
                            AddGeneralCodeModel = discountServices.FirstOrDefault();
                        }
                        if (discountProducts?.Count > 0)
                        {
                            AddGeneralCodeModel = discountProducts.FirstOrDefault();
                        }

                        int generalCodeId = 0;

                        if (AddGeneralCodeModel != null)
                        {
                            generalCodeId = AddGeneralCode(connection, transaction, AddGeneralCodeModel);
                        }
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ma le generalCodeId", generalCodeId));

                        if (discountServices?.Count > 0)
                        {
                            if (generalCodeId > 0) // insert generalCode thanh cong
                            {
                                // NET
                                ServiceValueModel promotionNet = valueServices.FirstOrDefault(x => x.ServiceID == 1);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("promotionNet", promotionNet));

                                // TV
                                ServiceValueModel promotionTv = valueServices.FirstOrDefault(x => x.ServiceID == 7);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("promotionTv", promotionTv));

                                //fSaleGo
                                ServiceValueModel promotionFsaleGo = valueServices.FirstOrDefault(x => x.ServiceID == 12 || x.ServiceID == 10);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("promotionFsaleGo", promotionFsaleGo));

                                if (PromotionTypeID == 1)// Tặng tiền trực tiếp
                                {
                                    result = ProcessPromotionServiceType1(connection, transaction, discountServices, input, promotionNet, promotionTv, promotionFsaleGo, generalCodeId, sb);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));

                                    if (result == false)
                                    {
                                        return false;
                                    }
                                    int RedeemPrivateCode = VendorManager.RedeemPrivateCode(connection, transaction, VendorManager.CheckFormatVoucher(voucher.evoucherCode), input.objId);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemPrivateCode", RedeemPrivateCode));

                                    money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                                if (PromotionTypeID == 2)// Tặng x tiền y tháng
                                {
                                    result = ProcessPromotionServiceType2(connection, transaction, discountServices, input, promotionNet, promotionTv, promotionFsaleGo, generalCodeId, sb);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    int RedeemPrivateCode = VendorManager.RedeemPrivateCode(connection, transaction, VendorManager.CheckFormatVoucher(voucher.evoucherCode), input.objId);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemPrivateCode", RedeemPrivateCode));

                                    if (promotionNet != null)
                                    {
                                        var discountServiceNet = discountServices.FirstOrDefault(s => s.ServiceId == promotionNet.ServiceID);
                                        MonthValueModel evcMonthValueNet = GetMonthValue(discountServiceNet != null ? discountServiceNet.Code : "", promotionNet.ServiceID);

                                        if (evcMonthValueNet != null)
                                        {
                                            decimal moneyNET = evcMonthValueNet.monthDiscount * evcMonthValueNet.valueDiscount * discountServiceNet.Qty;
                                            money += moneyNET;
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money evcMonthValueNet", moneyNET));
                                            InsertGeneralCodeDetail(connection, transaction, discountServiceNet, generalCodeId, moneyNET);
                                        }
                                    }

                                    if (promotionTv != null)
                                    {
                                        var discountServiceTV = discountServices.FirstOrDefault(s => s.ServiceId == promotionTv.ServiceID);
                                        MonthValueModel evcMonthValueTV = GetMonthValue(discountServiceTV != null ? discountServiceTV.Code : "", promotionTv.ServiceID);

                                        if (evcMonthValueTV != null)
                                        {
                                            decimal moneyTV = evcMonthValueTV.monthDiscount * evcMonthValueTV.valueDiscount * discountServiceTV.Qty;
                                            money += moneyTV;
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money evcMonthValueTV", moneyTV));
                                            InsertGeneralCodeDetail(connection, transaction, discountServiceTV, generalCodeId, moneyTV);
                                        }

                                    }

                                    if (promotionFsaleGo != null)
                                    {
                                        var discountServiceGo = discountServices.FirstOrDefault(s => s.ServiceId == promotionFsaleGo.ServiceID);
                                        MonthValueModel evcMonthValueTV = GetMonthValue(discountServiceGo != null ? discountServiceGo.Code : "", promotionFsaleGo.ServiceID);

                                        if (evcMonthValueTV != null)
                                        {
                                            decimal moneyTV = evcMonthValueTV.monthDiscount * evcMonthValueTV.valueDiscount * discountServiceGo.Qty;
                                            money += moneyTV;
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money evcMonthValueTV", moneyTV));
                                            InsertGeneralCodeDetail(connection, transaction, discountServiceGo, generalCodeId, moneyTV);
                                        }

                                    }

                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                                if (PromotionTypeID == 4)// tang thang
                                {
                                    result = ProcessPromotionServiceType4(connection, transaction, discountServices, input, promotionNet, promotionTv, generalCodeId, sb);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    int RedeemPrivateCode = VendorManager.RedeemPrivateCode(connection, transaction, VendorManager.CheckFormatVoucher(voucher.evoucherCode), input.objId);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemPrivateCode", RedeemPrivateCode));

                                    money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                                if (PromotionTypeID == 3)// Phí hòa mạng
                                {
                                    result = ProcessPromotionServiceType3(connection, transaction, discountServices, input, promotionNet, promotionTv, generalCodeId, sb);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("result", result));

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    int RedeemPrivateCode = VendorManager.RedeemPrivateCode(connection, transaction, VendorManager.CheckFormatVoucher(voucher.evoucherCode), input.objId);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemPrivateCode", RedeemPrivateCode));

                                    money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                            }
                            else
                            {
                                return false;
                            }
                        }
                        if (discountProducts?.Count() > 0 && generalCodeId > 0)
                        {
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Fsale_RedeemVoucher generalCodeId", generalCodeId));
                            if (generalCodeId > 0)
                            {
                                if(PromotionTypeID == 1)
                                {
                                    result = ProcessPromotionProdutcType1(connection, transaction, discountProducts, input, generalCodeId, sb); // tang tien tt

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    int RedeemPrivateCode = VendorManager.RedeemPrivateCode(connection, transaction, VendorManager.CheckFormatVoucher(voucher.evoucherCode), input.objId);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemPrivateCode", RedeemPrivateCode));

                                    money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                                if (PromotionTypeID == 3)
                                {
                                    result = ProcessPromotionProdutcType3(connection, transaction, discountProducts, input, generalCodeId, sb); // tang tien tt

                                    if (result == false)
                                    {
                                        return false;
                                    }

                                    int RedeemPrivateCode = VendorManager.RedeemPrivateCode(connection, transaction, VendorManager.CheckFormatVoucher(voucher.evoucherCode), input.objId);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemPrivateCode", RedeemPrivateCode));

                                    money = input.ApplyMoney.Where(x => x.VoucherCode == voucher.evoucherCode).Sum(x => x.Money);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money", money));
                                }
                            }
                            else
                            {
                                return false;
                            }
                        }

                        if (money > 0)
                        {
                            CalcQuota(connection, transaction, voucher.evoucherCode, money, input.SaleInfor.SaleID, input.CustomerInfor.LocationID, input.CustomerInfor.BranchCode, input.CustomerInfor.DistrictID, DateTime.Now.Year, DateTime.Now.Month);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("CalcQuota", "thành công"));
                        }

                        var UpdateHistory = UpdateHistoryTem(connection, transaction, voucher.evoucherCode, input.objId, input.OrderCode, input.RegCode);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UpdateHistory", UpdateHistory));

                        //if (!UpdateHistory)
                        //{
                        //    return false;
                        //}
                    }
                    #endregion
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucher Error", ex.ToString()));
                return false;
            }

            return true;
        }

        private static void AddGeneralCodeDetail(SqlConnection connection, SqlTransaction transaction, DiscountModel discountService, int generalCodeId, decimal money)
        {
            connection.Execute(ConstantAPI.OS6_FPTVoucher_SalePlatform_Device, new
            {
                ActionName = "InsertGeneralCodeDetail",
                XML = CreateXMLGeneralCodeDetail(generalCodeId, discountService, money)
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }

        private static int AddGeneralCode(SqlConnection connection, SqlTransaction transaction, DiscountModel discountService)
        {
            return connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "AddGeneralCode",
                XML = CreateXMLGeneralCode(discountService)
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        private static int AddGeneralCodeVendor(SqlConnection connection, SqlTransaction transaction, DiscountModel discountService)
        {
            return connection.Query<int>(OS6_FPTVoucher_FSale, new
            {
                ActionName = "AddGeneralCodeVendor",
                XML = CreateXMLGeneralCodeVendor(discountService)
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        private static XElement CreateXMLGeneralCodeDetail(int generalCodeId, DiscountModel service, decimal money)
        {
            var xmlString = new XElement("N",
                    new XElement("I",
                    new XElement("GeneralCodeId", generalCodeId),
                    new XElement("ServiceId", service.ServiceId),
                    new XElement("SubServiceId", service.SubServiceId),
                    new XElement("SubServiceTypeId", service.SubServiceTypeId),
                    new XElement("ServiceCode", service.ServiceCode),
                    new XElement("Quantity", service.Qty),
                    new XElement("TotalDiscount", money),
                    new XElement("LocationID", service.LocationID),
                    new XElement("BranchCode", service.BranchCode),
                    new XElement("DistrictID", service.DistrictID)));

            return xmlString;
        }
        private static XElement CreateXMLGeneralCode(DiscountModel model)
        {
            var xmlString = new XElement("N",
                        new XElement("I",
                            new XElement("Code", model.Code),
                            new XElement("OrderCode", model.RegCode),
                            new XElement("SaleID", model.SaleId),
                            new XElement("ObjID", model.ObjId),
                            new XElement("ActiveChannel", model.ActiveChannel),
                            new XElement("LocalTypeId", model.LocalTypeId),
                            new XElement("PolicyId", model.PolicyId),
                            new XElement("LocationID", model.LocationID),
                            new XElement("BranchCode", model.BranchCode),
                            new XElement("DistrictID", model.DistrictID),
                            new XElement("SaleChannelId", model.SaleChannelId),
                            new XElement("SaleTeamID", model.SaleTeamID),
                            new XElement("PaymentTypeID", model.PaymentTypeID),
                            new XElement("CrossSellingLocation", model.CrossSellingLocation),
                            new XElement("ObjectTypeId", model.ObjectTypeId),
                            new XElement("CusTypeId", model.CusTypeId),
                            new XElement("CusTypeL2Id", model.CusTypeL2Id),
                            new XElement("CustomerRank", model.CustomerRank),
                            new XElement("UnitParentID", model.UnitParentID),
                            new XElement("UnitParentCode", model.UnitParentCode),
                            new XElement("LimitType", model.LimitType),
                            new XElement("UnitCode", model.UnitCode)
                        )
                    );

            return xmlString;
        }

        private static XElement CreateXMLGeneralCodeVendor(DiscountModel model)
        {
            var xmlString = new XElement("N",
                        new XElement("I",
                            new XElement("Code", VendorManager.CheckFormatVoucher(model.Code)),
                            new XElement("OrderCode", model.RegCode),
                            new XElement("SaleID", model.SaleId),
                            new XElement("ObjID", model.ObjId),
                            new XElement("ActiveChannel", model.ActiveChannel),
                            new XElement("LocalTypeId", model.LocalTypeId),
                            new XElement("PolicyId", model.PolicyId),
                            new XElement("LocationID", model.LocationID),
                            new XElement("BranchCode", model.BranchCode),
                            new XElement("DistrictID", model.DistrictID),
                            new XElement("SaleChannelId", model.SaleChannelId),
                            new XElement("SaleTeamID", model.SaleTeamID),
                            new XElement("PaymentTypeID", model.PaymentTypeID),
                            new XElement("CrossSellingLocation", model.CrossSellingLocation),
                            new XElement("ObjectTypeId", model.ObjectTypeId),
                            new XElement("CusTypeId", model.CusTypeId),
                            new XElement("CusTypeL2Id", model.CusTypeL2Id),
                            new XElement("CustomerRank", model.CustomerRank)
                        )
                    );

            return xmlString;
        }

        #region redeem vouches cs
        public static int AddVoucherBill(SqlConnection connection, SqlTransaction transaction, int generalCodeId, int objId, string orderCode, int serviceCode, decimal money)
        {
            return connection.Execute(OSU6_FPTVoucher_AddDiscount, new
            {
                Action = "AddVoucherBill",
                GeneralCodeID = generalCodeId,
                ObjID = objId,
                OrderCode = orderCode,
                ServiceCode = serviceCode,
                Money = Math.Round(money, 0)
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }

        public static int GetPromotionTypeID(SqlConnection connection, SqlTransaction transaction, string voucherCode)
        {
            return connection.Query<int>(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "GetPromotionTypeId",
                VoucherCode = voucherCode
            }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static int GetPromotionTypeIDVendor(SqlConnection connection, SqlTransaction transaction, string voucherCode)
        {
            return connection.Query<int>(OS6_FPTVoucher_FSale, new
            {
                ActionName = "GetPromotionTypeIdVendor",
                VoucherCode = voucherCode
            }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static int UpdateDiscountTH(string voucherCode, int objId, int locationId, decimal money, decimal duration, float? discountRate, int addMonth, StringBuilder sb)
        {
            int i = 0;
            try
            {
                var pars = new
                {
                    VoucherCode = voucherCode,
                    ObjID = objId,
                    LocationId = locationId,
                    Amount = Math.Round(money, 0),
                    Duration = duration,
                    DiscountRate = discountRate,
                    AddMonth = addMonth
                };
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UpdateDiscountTH", pars));

                string connIptv = Utility.ConnIPTV;
                using (var connection = new SqlConnection(connIptv))
                {
                    i = connection.Query<int>(
                        "IPTV.dbo.OSU6_FPTVoucher_SalePlatform_PolicyVoucher_AddCustomerDiscount",
                        pars,
                        commandType: CommandType.StoredProcedure
                    ).FirstOrDefault();
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UpdateDiscountTH i ", pars));
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UpdateDiscountTH error", ex.Message));

            }
            return i;
        }

        public static bool ProcessPromotionServiceType1(SqlConnection connection, SqlTransaction transaction, List<DiscountModel> discountServices, SalePolicyRedeemEVC input, ServiceValueModel promotionNet, ServiceValueModel promotionTv, ServiceValueModel promotionFsaleGo, int generalCodeID, StringBuilder sb)
        {
            var statusNET = true;
            var statusPAY = true;
            var statusGo = true;

            try
            {
                if (discountServices.Count > 0)
                {
                    if (promotionNet != null)
                    {
                        DiscountModel discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionNet.ServiceID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServiceNET GTTT", discountService));
                        if (discountService != null)
                        {
                            var money = input.ApplyMoney.Where(x => x.ServiceID == discountService.ServiceId && x.SubServiceTypeID == discountService.SubServiceTypeId &&
                                                                x.SubServiceID == discountService.SubServiceId && x.ServiceCode == discountService.ServiceCode && x.VoucherCode == discountService.Code)
                                                    .Sum(x => x.Money);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money NET GTTT", money));

                            int add_GC_detail = InsertGeneralCodeDetail(connection, transaction, discountService, generalCodeID, money);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("add_GC_detail", add_GC_detail));

                            statusNET = ScheduleBillPayment(connection, transaction, input.objId, money, 0, 1, discountService.ServiceCode, generalCodeID);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule net", statusNET));

                        }
                    }

                    if (promotionTv != null)
                    {
                        var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionTv.ServiceID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServiceTV GTTT", discountService));
                        if (discountService != null)
                        {
                            var money = input.ApplyMoney.Where(x => x.ServiceID == discountService.ServiceId && x.SubServiceTypeID == discountService.SubServiceTypeId &&
                                                                x.SubServiceID == discountService.SubServiceId && x.ServiceCode == discountService.ServiceCode && x.VoucherCode == discountService.Code)
                                                    .Sum(x => x.Money);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money TV GTTT", money));

                            int add_GC_detail = InsertGeneralCodeDetail(connection, transaction, discountService, generalCodeID, money);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("add_GC_detail", add_GC_detail));

                            statusPAY = ScheduleBillPayment(connection, transaction, input.objId, money, 0, 1, discountService.ServiceCode, generalCodeID);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule pay", statusPAY));
                        }
                    }

                    if (promotionFsaleGo != null)
                    {
                        var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionFsaleGo.ServiceID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServiceTV GTTT", discountService));
                        if (discountService != null)
                        {
                            var money = input.ApplyMoney.Where(x => x.ServiceID == discountService.ServiceId && x.SubServiceTypeID == discountService.SubServiceTypeId &&
                                                                x.SubServiceID == discountService.SubServiceId && x.ServiceCode == discountService.ServiceCode && x.VoucherCode == discountService.Code)
                                                    .Sum(x => x.Money);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money TV GTTT", money));

                            int add_GC_detail = InsertGeneralCodeDetail(connection, transaction, discountService, generalCodeID, money);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("add_GC_detail", add_GC_detail));

                            statusGo = ScheduleBillPayment(connection, transaction, input.objId, money, 0, 1, discountService.ServiceCode, generalCodeID);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule statusGo", statusGo));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC ProcessPromotionType1", e.ToString()));
                return false;
            }

            return statusNET == statusPAY == statusGo == true;
        }

        public static int InsertGeneralCodeDetail(SqlConnection connection, SqlTransaction transaction, DiscountModel discountService, int generalCodeId, decimal money)
        {
            return connection.Execute(ConstantAPI.OS6_FPTVoucher_SalePlatform_Device, new
            {
                ActionName = "InsertGeneralCodeDetail",
                XML = CreateXMLGeneralCodeDetail(generalCodeId, discountService, money)
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }

        public static bool ProcessPromotionServiceType2(SqlConnection connection, SqlTransaction transaction, List<DiscountModel> discountServices, SalePolicyRedeemEVC input, ServiceValueModel promotionNet, ServiceValueModel promotionTv, ServiceValueModel promotionFsaleGo, int generalCodeID, StringBuilder sb)
        {
            var statusNET = true;
            var statusPAY = true;
            var statusGo = true;

            try
            {
                if (promotionNet != null)
                {
                    var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionNet.ServiceID);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServiceNET", discountService));

                    if (discountService != null)
                    {
                        // lấy số tháng trên evc

                        MonthValueModel evcMonthValue = GetMonthValue(discountService.Code, promotionNet.ServiceID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evcMonthValue", evcMonthValue));

                        // lấy số tháng trên input
                        int inputMonthValue = (from service in input.Services
                                               from subServiceType in service.SubServiceTypes
                                               from subService in subServiceType.SubServices
                                               where service.ServiceID == promotionNet.ServiceID
                                               && subServiceType.SubServiceTypeID == promotionNet.SubServiceTypeID
                                               && subService.Total > 0
                                               select (int)(subService.PrePaid == 0 ? 0 : subService.MonthUsed)).FirstOrDefault();

                        // fix
                        if (inputMonthValue > evcMonthValue.monthDiscount)
                            inputMonthValue = evcMonthValue.monthDiscount;

                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("inputMonthValue", inputMonthValue));

                        statusNET = ScheduleBillPaymentXdongYthang(connection, transaction, input.objId, evcMonthValue.valueDiscount, evcMonthValue.monthDiscount, 2, discountService.ServiceCode, generalCodeID, inputMonthValue, inputMonthValue * evcMonthValue.valueDiscount);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule net", statusNET));
                    }
                }

                if (promotionTv != null)
                {

                    var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionTv.ServiceID);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServiceTV", discountService));

                    // lấy số tháng trên evc
                    MonthValueModel evcMonthValue = new MonthValueModel();
                    evcMonthValue = GetMonthValue(discountService.Code, promotionTv.ServiceID);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evcMonthValue", evcMonthValue));

                    // lấy số tháng trên input
                    int inputMonthValue = (from service in input.Services
                                           from subServiceType in service.SubServiceTypes
                                           from subService in subServiceType.SubServices
                                           where service.ServiceID == promotionTv.ServiceID
                                           && subServiceType.SubServiceTypeID == promotionTv.SubServiceTypeID
                                           && subService.Total > 0
                                           select (int)(subService.PrePaid == 0 ? 0 : subService.MonthUsed)).FirstOrDefault();

                    // fix
                    if (inputMonthValue > evcMonthValue.monthDiscount)
                        inputMonthValue = evcMonthValue.monthDiscount;

                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("inputMonthValue", inputMonthValue));

                    statusPAY = ScheduleBillPaymentXdongYthang(connection, transaction, input.objId, evcMonthValue.valueDiscount, evcMonthValue.monthDiscount, 2, discountService.ServiceCode, generalCodeID, inputMonthValue, inputMonthValue * evcMonthValue.valueDiscount);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule pay", statusPAY));
                }

                if (promotionFsaleGo != null)
                {

                    var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionFsaleGo.ServiceID);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServiceTV", discountService));

                    // lấy số tháng trên evc
                    MonthValueModel evcMonthValue = new MonthValueModel();
                    evcMonthValue = GetMonthValue(discountService.Code, promotionFsaleGo.ServiceID);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("evcMonthValue", evcMonthValue));

                    // lấy số tháng trên input
                    int inputMonthValue = (from service in input.Services
                                           from subServiceType in service.SubServiceTypes
                                           from subService in subServiceType.SubServices
                                           where service.ServiceID == promotionFsaleGo.ServiceID
                                           && subServiceType.SubServiceTypeID == promotionFsaleGo.SubServiceTypeID
                                           && subService.Total > 0
                                           select (int)(subService.PrePaid == 0 ? 0 : subService.MonthUsed)).FirstOrDefault();

                    // fix
                    if (inputMonthValue > evcMonthValue.monthDiscount)
                        inputMonthValue = evcMonthValue.monthDiscount;

                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("inputMonthValue", inputMonthValue));

                    statusGo = ScheduleBillPaymentXdongYthang(connection, transaction, input.objId, evcMonthValue.valueDiscount, evcMonthValue.monthDiscount, 2, discountService.ServiceCode, generalCodeID, inputMonthValue, inputMonthValue * evcMonthValue.valueDiscount);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule FsaleGo", statusGo));
                }

                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ProcessPromotionType2", "done"));
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ProcessPromotionType2 Exception:", e.ToString()));
                return false;
            }

            return statusNET == statusPAY == statusGo == true;
        }

        public static bool ProcessPromotionServiceType4(SqlConnection connection, SqlTransaction transaction, List<DiscountModel> discountServices, SalePolicyRedeemEVC input, ServiceValueModel promotionNet, ServiceValueModel promotionTv, int generalCodeID, StringBuilder sb)
        {
            var statusNET = true;
            var statusPAY = true;

            try
            {
                if (promotionNet != null)
                {
                    var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionNet.ServiceID);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC discountServiceNET", discountService));
                    if (discountService != null)
                    {
                        var money = (from service in input.Services
                                     from subServiceType in service.SubServiceTypes
                                     from subService in subServiceType.SubServices
                                     where service.ServiceID == promotionNet.ServiceID
                                     && subServiceType.SubServiceTypeID == promotionNet.SubServiceTypeID
                                     && subService.Total_VAT > 0
                                     select (decimal)(subService.Total_VAT / (decimal)subService.MonthUsed)).FirstOrDefault();

                        int add_GC_detail = InsertGeneralCodeDetail(connection, transaction, discountService, generalCodeID, Math.Round(money * promotionNet.MonthValue, 0));
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("add_GC_detail", add_GC_detail));

                        money = promotionNet.MonthValue == (decimal)0.5 ? money * promotionNet.MonthValue : money;

                        statusNET = ScheduleBillPayment(connection, transaction, input.objId, money, (float)promotionNet.MonthValue, 4, discountService.ServiceCode, generalCodeID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule net", statusNET));
                    }
                }

                if (promotionTv != null)
                {
                    var data = (from service in input.Services
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                where service.ServiceID == promotionTv.ServiceID
                                && subServiceType.SubServiceTypeID == promotionTv.SubServiceTypeID
                                && subService.MonthUsed > 0
                                select new { AddMonth = (decimal)subService.MonthUsed, Money = (decimal)(subService.Total_VAT / (decimal)subService.MonthUsed) }).FirstOrDefault();

                    var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionTv.ServiceID);
                    //var money = promotionTv.MonthValue == (decimal)0.5 ? data.Money * promotionTv.MonthValue : data.Money;
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC discountServiceTV", discountService));
                    if (discountService != null)
                    {
                        int add_GC_detail = InsertGeneralCodeDetail(connection, transaction, discountService, generalCodeID, Math.Round(data.Money * promotionTv.MonthValue, 0));
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("add_GC_detail", add_GC_detail));

                        statusPAY = ScheduleBillPayment(connection, transaction, input.objId, data.Money, (float)promotionTv.MonthValue, 4, discountService.ServiceCode, generalCodeID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule pay", statusPAY));
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC ProcessPromotionType4", e.ToString()));
                return false;
            }

            return statusNET == statusPAY == true;
        }

        public static bool ProcessPromotionServiceType3(SqlConnection connection, SqlTransaction transaction, List<DiscountModel> discountServices, SalePolicyRedeemEVC input, ServiceValueModel promotionNet, ServiceValueModel promotionTv, int generalCodeID, StringBuilder sb)
        {
            var statusNET = true;
            var statusPAY = true;

            try
            {
                if (promotionNet != null)
                {
                    var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionNet.ServiceID && s.SubServiceId == 999999999);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServiceNET PHM", discountService));
                    if (discountService != null)
                    {
                        var money = input.ApplyMoney.Where(x => x.ServiceID == discountService.ServiceId && x.SubServiceTypeID == discountService.SubServiceTypeId &&
                                                                x.SubServiceID == discountService.SubServiceId && x.ServiceCode == discountService.ServiceCode && x.VoucherCode == discountService.Code)
                                                    .Sum(x => x.Money);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money NET PHM", money));

                        int add_GC_detail = InsertGeneralCodeDetail(connection, transaction, discountService, generalCodeID, money);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("add_GC_detail", add_GC_detail));

                        statusNET = ScheduleBillPayment(connection, transaction, input.objId, money, 0, 3, discountService.ServiceCode, generalCodeID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule net", statusNET));
                    }
                }

                if (promotionTv != null)
                {
                    var discountService = discountServices.FirstOrDefault(s => s.ServiceId == promotionTv.ServiceID && s.SubServiceId == 999999996);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("discountServiceTV PHM", discountService));
                    if (discountService != null)
                    {
                        var money = input.ApplyMoney.Where(x => x.ServiceID == discountService.ServiceId && x.SubServiceTypeID == discountService.SubServiceTypeId &&
                                                                x.SubServiceID == discountService.SubServiceId && x.ServiceCode == discountService.ServiceCode && x.VoucherCode == discountService.Code)
                                                    .Sum(x => x.Money);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money TV PHM", money));

                        int add_GC_detail = InsertGeneralCodeDetail(connection, transaction, discountService, generalCodeID, money);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("add_GC_detail", add_GC_detail));

                        statusPAY = ScheduleBillPayment(connection, transaction, input.objId, money, 0, 3, discountService.ServiceCode, generalCodeID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Schedule net", statusNET));
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC ProcessPromotionType3", e.Message));
                return false;
            }

            return statusNET == statusPAY == true;
        }

        public static bool ProcessPromotionProdutcType1(SqlConnection connection, SqlTransaction transaction, List<DiscountModel> discountProducts, SalePolicyRedeemEVC input, int generalCodeID, StringBuilder sb)
        {
            try
            {
                if (discountProducts?.Count > 0)
                {
                    foreach (var discountProduct in discountProducts)
                    {
                        var money = input.ApplyMoney.Where(x => x.ServiceID == discountProduct.ServiceId && x.SubServiceTypeID == discountProduct.SubServiceTypeId &&
                                                                x.SubServiceID == discountProduct.SubServiceId && x.ServiceCode == discountProduct.ServiceCode && x.VoucherCode == discountProduct.Code)
                                                    .Sum(x => x.Money);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money product", money));

                        var i = connection.Execute(ConstantAPI.OS6_FPTVoucher_SalePlatform_Device, new
                        {
                            ActionName = "InsertGeneralCodeDetail",
                            XML = CreateXMLGeneralCodeDetail(generalCodeID, discountProduct, money)
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ProcessPromotionProdutcType1 insertDetail", i));
                        if (!(i > 0))
                        {
                            return false;
                        }
                        //var Schedule = ScheduleBillPayment(connection, transaction, input.objId, money, 0, 1, discountProduct.ServiceCode, generalCodeID);
                        //sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ProcessPromotionProdutcType1 Schedule", Schedule));
                        //if (!Schedule)
                        //{
                        //    return false;
                        //}

                        var updateActiveStatus = UpdateActiveStatus(connection, transaction, generalCodeID, sb);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("updateActiveStatus", updateActiveStatus));
                    }
                }

            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC ProcessPromotionProdutcType1", e.Message));
                return false;
            }

            return true;
        }

        public static bool UpdateHistoryTem(SqlConnection connection, SqlTransaction transaction, string voucher, int objId, string orderCode, string regCode)
        {
            var isUpdate = connection.Execute(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "UpdateHistoryTem",
                ObjID = objId,
                OrderCode = orderCode,
                VoucherCode = voucher,
                RegCode = regCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

            return (isUpdate > 0);
        }
        #endregion

        #region Trừ hạn mức
        public static void CalcQuota(SqlConnection connection, SqlTransaction transaction, string voucherCode, decimal money, int saleID, int locationId, int branchCode, int districtId, int year, int month)
        {
            connection.Execute(OSU6_FPTVoucher_PolicyVoucher_VoucherLimit_UpdateUsedMoney, new
            {
                VoucherCode = voucherCode,
                Money = money,
                SaleId = saleID,
                LocationId = locationId,
                BranchCode = branchCode,
                DistrictId = districtId,
                Year = year,
                Month = month
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

            connection.Execute(OS6_DXBusinessPolicy_VoucherLimit_CalcAvailable, new
            {
                Year = year,
                Month = month
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }
        public static int CheckMoney(SqlConnection connection, SqlTransaction transaction, string voucherCode)
        {
            return connection.Query<int>(OS6_FPTVoucher_FSale, new
            {
                actionName = "CheckMoney",
                voucherCode = voucherCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        #endregion

        public static MonthValueModel GetMonthValue(string voucherCode, int serviceId)
        {
            var datares = new MonthValueModel();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                datares = connection.Query<MonthValueModel>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetMonthValue",
                    voucherCode = voucherCode,
                    serviceId = serviceId
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return datares;
            }
        }

        public static bool ScheduleBillPayment(SqlConnection connection, SqlTransaction transaction, int objid, decimal amount, float monthValue, int promotionTypeID, int serviceCode, int generalCodeID)
        {
            var result = 0;

            result = connection.Execute(OS6_FPTVoucher_FSale, new
            {
                actionName = "ScheduleBillPayment",
                objID = objid,
                generalCodeID = generalCodeID,
                amount = amount,
                promotionTypeID = promotionTypeID,
                serviceCode = serviceCode,
                monthValue = monthValue
            }, transaction, commandType: CommandType.StoredProcedure);

            return result > 0;
        }
        public static bool ScheduleBillPaymentXdongYthang(SqlConnection connection, SqlTransaction transaction, int objid, decimal amount, float monthValue, int promotionTypeID, int serviceCode, int generalCodeID, float monthUsed, float used)
        {
            var result = 0;

            var checkCF = GetBaseConfig(connection, 33, 1);
            if(checkCF == null || checkCF.Count() == 0)
            {
                result = connection.Execute(OS6_FPTVoucher_FSale, new
                {
                    actionName = "ScheduleBillPaymentXdongYthang",
                    objID = objid,
                    generalCodeID = generalCodeID,
                    amount = amount,
                    promotionTypeID = promotionTypeID,
                    serviceCode = serviceCode,
                    monthValue = monthValue,
                    monthUsed = 0,
                    used = 0,
                    firstAdd = 0
                }, transaction, commandType: CommandType.StoredProcedure);
            }
            else
            {
            result = connection.Execute(OS6_FPTVoucher_FSale, new
            {
                actionName = "ScheduleBillPaymentXdongYthang",
                objID = objid,
                generalCodeID = generalCodeID,
                amount = amount,
                promotionTypeID = promotionTypeID,
                serviceCode = serviceCode,
                monthValue = monthValue,
                monthUsed = monthUsed,
                used = used
            }, transaction, commandType: CommandType.StoredProcedure);
            }
            return result > 0;
        }

        public static bool ProcessPromotionProdutcType3(SqlConnection connection, SqlTransaction transaction, List<DiscountModel> discountProducts, SalePolicyRedeemEVC input, int generalCodeID, StringBuilder sb)
        {
            try
            {
                if (discountProducts?.Count > 0)
                {
                    foreach (var discountProduct in discountProducts)
                    {
                        var money = input.ApplyMoney.Where(x => x.ServiceID == discountProduct.ServiceId && x.SubServiceTypeID == discountProduct.SubServiceTypeId &&
                                                                x.SubServiceID == discountProduct.SubServiceId && x.ServiceCode == discountProduct.ServiceCode && x.VoucherCode == discountProduct.Code)
                                                    .Sum(x => x.Money);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("money product", money));

                        var i = connection.Execute(ConstantAPI.OS6_FPTVoucher_SalePlatform_Device, new
                        {
                            ActionName = "InsertGeneralCodeDetail",
                            XML = CreateXMLGeneralCodeDetail(generalCodeID, discountProduct, money)
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ProcessPromotionProdutcType1 insertDetail", i));
                        if (!(i > 0))
                        {
                            return false;
                        }
                        var Schedule = ScheduleBillPayment(connection, transaction, input.objId, money, 0, 3, discountProduct.ServiceCode, generalCodeID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ProcessPromotionProdutcType1 Schedule", Schedule));
                        //if (!Schedule)
                        //{
                        //    return false;
                        //}
                    }
                }

            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherGC ProcessPromotionProdutcType1", e.Message));
                return false;
            }

            return true;
        }

        public static int GetLimitType(SqlConnection connection, SqlTransaction transaction, string code)
        {
            int limitType = 0;
            try
            {
                limitType = connection.Query<int>(OS6_FPTVoucher_FSale, new
                {
                    actionName = "GetLimitType",
                    voucherCode = code
                }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            catch (Exception)
            {

            }

            return limitType;
        }

        public static int UpdateActiveStatus(SqlConnection conn, SqlTransaction trans, int generalCodeID, StringBuilder sb)
        {
            int i = 0;
            try
            {
                i = conn.Execute(OSU6_FPTVoucher_UpdateActiveStatus, new
                {
                    generalCodeID = generalCodeID,
                    activeStatus = 3
                }, transaction: trans, commandType: CommandType.StoredProcedure);
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("UpdateActiveStatus err", e));
            }
            return i;
        }

        public static List<DXBaseConfig> GetBaseConfig(SqlConnection conn, int GroupID, int Status)
        {
            var lst = new List<DXBaseConfig>();
            lst = conn.Query<DXBaseConfig>("PowerInside..OS6_DXBusinessPolicy_BaseConfig_Get", new
            {
                GroupID = GroupID,
                Status = Status
            }, commandType: CommandType.StoredProcedure).ToList();
            return lst;
        }
    }
}