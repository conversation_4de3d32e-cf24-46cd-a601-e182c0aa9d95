using APIMBS.Models.MobileSaleV4;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Voucher.APIHelper.Util;
using System.Linq;
using System.Web;
using Dapper;
using APIMBS.Models;
using System.Xml.Linq;
using APIMBS.Constant;
using System.Text;

namespace APIMBS.Service
{
    public class FriendSellServices
    {
        public const string OS6_FPTVoucher_FriendSellEvent = "PowerInside.dbo.OS6_FPTVoucher_FriendSellEvent";
        public const string OS6_FPTVoucher_GeneralCode = "PowerInside.dbo.OS6_FPTVoucher_GeneralCode";
        public const string OS6_ReferralProgram_InviteCode = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";
        public const string OS6_FPTVoucher_AdvocacyProgram = "PowerInside.dbo.OS6_FPTVoucher_AdvocacyProgram";
        public const string OS6_ReferalProgram_CAM = "PowerInside.dbo.OS6_ReferalProgram_CAM";
        public static string BuildPackageType(int IsPrepaidNET, int IsPrepaidTV)
        {
            if (IsPrepaidNET == -1 && IsPrepaidTV != -1) return "TVONLY";
            else if (IsPrepaidNET != -1 && IsPrepaidTV != -1) return "COMBO";
            else if (IsPrepaidNET != -1 && IsPrepaidTV == -1) return "NETONLY";
            else return "";
        }
        private static XElement CreateXMLRedeem(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.RegCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite)// object của người giới thiệu
                       ));
            return xmlString;
        }
        public static XElement CreateXMLUpdateGeneralCodeRPbyGC(List<ItemRequest> input, InfoRP promotionEvent, int i)
        {
            if (i.Equals(0))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // Mã loy cho người giới thiệu status = 100
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));
                return xmlString;
            }
            if (i.Equals(1))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 111)));
                return xmlString;
            }
            if (i.Equals(2))
            {
                var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent.EventCode),  // nguoi giới thiệu
                                        new XElement("P", promotionEvent.ID),
                                        new XElement("Or", input[0].RegCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
                return xmlString;
            }
            return null;
        }

        public static List<GeneralCodeInsert> InsertGeneralCodeRPgetGCID(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo)
        {
            List<GeneralCodeInsert> lst = new List<GeneralCodeInsert>();
            for (int i = 0; i < InviteInfo.Count; i++)
            {
                GeneralCodeInsert gc = new GeneralCodeInsert();
                gc.eventCode = InviteInfo[i].EventCode;
                gc.id = connection.Query<int>(OS6_FPTVoucher_GeneralCode, new
                {
                    ActionName = "InsertGeneralCodeXMLGCID",
                    XML = CreateXMLUpdateGeneralCodeRPbyGC(input, InviteInfo[i], i),
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                lst.Add(gc);
            }
            return lst;
        }
        public static XElement CreateXMLDevice(string voucherCode, string orderCode, List<DeviceModel> devices)
        {
            var xmlString = new XElement("N",
            from device in devices
            select new XElement("I",
                           new XElement("voucherCode", voucherCode),
                           new XElement("orderCode", orderCode), 
                           new XElement("serviceId", device.serviceId), 
                           new XElement("subServiceTypeID", device.subServiceTypeID),
                           new XElement("subServiceId", device.deviceId),
                           new XElement("serviceCode", device.serviceCode),
                           new XElement("qty", device.qty)
                       ));
            return xmlString;
        }

        public static XElement CreateXMLRedeemCAM(List<ItemRequest> input, EventCamera ec, string GeneralCodeInvite = "", string contractGT="", string eventCodeLoy = "")
        {
            string typeRF = "HiFPT";
            if (!string.IsNullOrEmpty(contractGT)) typeRF = "DKH";
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited-in", ec.EventCodeCamIn),
                           new XElement("Vinvited-out", ec.EventCodeCamOut),
                           new XElement("Or", item.RegCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite),// object của người giới thiệu
                           new XElement("RF", typeRF)
                       ));
            return xmlString;
        }

        public static XElement CreateXMLUpdateGeneralCodeRPCAM(List<ItemRequest> input, List<InfoRFCAM> camrf)
        {
            int voucherRF = camrf.Count() - 1;
            var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", camrf[0].EventCode),  // nguoi giới thiệu
                                        new XElement("P", camrf[0].ID),
                                        new XElement("Or", input[0].RegCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
            for (int i = 1; i <= voucherRF; i++)
            {
                xmlString.Add(new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", camrf[i].EventCode),
                            new XElement("P", camrf[i].ID),
                            new XElement("Or", input[0].RegCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 111)));
            }

            return xmlString;
        }
        public static int GetObjIdSupport(SqlConnection connection, SqlTransaction transaction, string voucherCode, string contractGT)
        {
            return connection.Query<int>(OS6_FPTVoucher_FriendSellEvent, new
            {
                actionName = "GetObjContractSell",
                code = voucherCode,
                contractSell = contractGT
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static bool RedeemFriendSellNetTV(SqlConnection connection, SqlTransaction transaction, string contractGT,int objid, string voucherCode, int prepaidId, Tuple<int, int> prepaid_net_tv,string regcode, int objidGT, Guid logId, StringBuilder sb)
        {

            try
            {
                #region Active NET hoac TV
                int objidSupport = GetObjIdSupport(connection, transaction, voucherCode, contractGT);
                if (objidSupport.Equals(0))
                {
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemFriendSellNetTV objidSupport", objidSupport));
                    return false;
                }
                List<InfoRP> InviteInfo = new List<InfoRP>();
                InviteInfo = connection.Query<InfoRP>(ConstantAPI.OS6_ReferralProgram_DKOLV5, new
                {
                    InviteCode = voucherCode,
                    ActionName = "GetFriendSellPE",
                    PackageTypeInvited = BuildPackageType(prepaid_net_tv.Item1, prepaid_net_tv.Item2),
                    PaidTimeType = prepaidId,
                    ObjID = objidGT 
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemFriendSellNetTV InviteInfo", InviteInfo));

                if (InviteInfo.Count() != 3)
                {
                    return false;
                }
                else if (InviteInfo.Count() == 3)
                {
                   
                }

                // insert data to table InviteSuccess
                // [1] : người được giới thiệu
                // [2] : người giới thiệu
                FoxGold EventCodeLoy = new FoxGold() { ID = InviteInfo[0].ID, EventCode = InviteInfo[0].EventCode };
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemFriendSellNetTV EventCodeLoy", EventCodeLoy));

                List<ItemRequest> lstItem = new List<ItemRequest> { new ItemRequest { ObjecInvite = objidSupport, Objectinvited = objid, RegCode = regcode, VoucherCode = voucherCode } };
                int InsertInviteSuccess = connection.Query<int>(OS6_FPTVoucher_FriendSellEvent, new
                {
                    ActionName = "InsertInfoRedeemRPCode",
                    XML = CreateXMLRedeem(lstItem, InviteInfo[2].EventCode, InviteInfo[1].EventCode, EventCodeLoy.EventCode)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemFriendSellNetTV InsertInviteSuccess", InsertInviteSuccess));

                if (InsertInviteSuccess != lstItem.Count()) return false;

                MBSv4RedeemVoucherAdvocacyProgram.UpdateStatusRPCode(connection, transaction, voucherCode);

                // insert data to table generalcode
                List<GeneralCodeInsert> stsAddGeneralCode = InsertGeneralCodeRPgetGCID(connection, transaction, lstItem, InviteInfo);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemFriendSellNetTV stsAddGeneralCode", stsAddGeneralCode));

                // update Discount RpCode User Invited
                MBSv4CommonService.NewUpdateDiscount(
                        connection,
                        transaction,
                        objid,  // objID khách hàng
                        InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                        InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                        InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                        InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                        InviteInfo[1].EventCode, // Code
                        objid, false); //Object Invite -TypeVC:True    

                // Update Discount RpCode User Invite
                MBSv4CommonService.NewUpdateDiscount(
                        connection,
                        transaction,
                        objidSupport,  // objID khách hàng
                        InviteInfo[2].NetPromotionID, //XđồngY tháng NET + Tháng
                        InviteInfo[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                        InviteInfo[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                        InviteInfo[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                        InviteInfo[2].EventCode, // Code
                        objid, false); //Object Invite -TypeVC:True    

                //L.Mes(Level.INFO, keylog + " " + "Call Voucher Bill");
                // add voucher bill invited
                //MBSv4CommonService.NewAddVoucherBill(connection, transaction, objid, regcode,  stsAddGeneralCode[1].id,prepaidId);
                // add voucher bill invite
                //MBSv4CommonService.NewAddVoucherBill(connection, transaction, objidSupport, regcode, stsAddGeneralCode[2].id, prepaidId);

                //SenNotifyDKH(ContractGT, input[0].Objectinvited, "successful_service_registration", keylog);                
                
                #endregion
                return true;
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemFriendSellNetTV error", ex.Message));
                return false;
            }
        }

        public static bool RedeemInviteCodeOwnerCamera(SqlConnection connection, SqlTransaction transaction,
            string voucherCode, int objid, string orderCode, Tuple<int, int> prepaid_net_tv, int saleId, SalesManInfo Salesman, int idPrepaidTime, int channel, int localtype, string regCode, int objidGT, Guid logId, StringBuilder sb)
        {
            try
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera DKH for reg code", regCode));
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera DKH objidGT", objidGT));

                List<ItemRequest> lstItem = new List<ItemRequest> { new ItemRequest { ObjecInvite = objidGT, Objectinvited = objid, RegCode = regCode, VoucherCode = voucherCode } };
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera DKH lstItem", lstItem));

                List<InfoRP> InviteInfo = new List<InfoRP>();

                InviteInfo = connection.Query<InfoRP>(ConstantAPI.OS6_ReferralProgram_Camera, new
                {
                    ActionName = "GetPEREF",
                    PackageTypeInvited = MBSv4RedeemVoucherAdvocacyProgram.BuildPackageType(prepaid_net_tv),
                    PaidTimeType = idPrepaidTime
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera DKH InviteInfo", InviteInfo));

                if (InviteInfo.Count() != 3)
                {
                    return false;
                }

                int InsertInviteSuccess = connection.Query<int>(OS6_FPTVoucher_FriendSellEvent, new
                {
                    ActionName = "InsertInfoRedeemRPCode",
                    XML = CreateXMLRedeem(lstItem, InviteInfo[2].EventCode, InviteInfo[1].EventCode, InviteInfo[0].EventCode)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera DKH InsertInviteSuccess", InsertInviteSuccess));

                if (InsertInviteSuccess != lstItem.Count()) return false;
                MBSv4RedeemVoucherAdvocacyProgram.UpdateStatusRPCode(connection, transaction, voucherCode);

                List<GeneralCodeInsert> stsAddGeneralCode = ReferalProgramHiFPT.InsertGeneralCodeRPgetGCID(connection, transaction, lstItem, InviteInfo, prepaid_net_tv);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera DKH stsAddGeneralCode", stsAddGeneralCode));

                if (stsAddGeneralCode.Count() != 2) return false;

                // update Discount RpCode User Invited
                MBSv4CommonService.NewUpdateDiscount(
                     connection,
                     transaction,
                     lstItem[0].Objectinvited,  // objID khách hàng
                     InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                     InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                     InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                     InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                     InviteInfo[1].EventCode, // Code
                     lstItem[0].Objectinvited, false); //Object Invite -TypeVC:True   

                //MBSv4CommonService.NewAddVoucherBill(connection, transaction, objid, regCode, stsAddGeneralCode[1].id, idPrepaidTime);
                return true;
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemInviteCodeOwnerCamera DKH Error", ex.Message));
                return false;
            }
        }
    }
}