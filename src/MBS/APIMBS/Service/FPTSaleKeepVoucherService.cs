using APIMBS.Constant;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Models.SalePlatform;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web;
using System.Xml.Linq;

namespace APIMBS.Service
{
    public static class FPTSaleKeepVoucherService
    {
        public const string OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend = "PowerInside.dbo.OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend";

        public static bool SaveHistoryTemp(SqlConnection connection, SqlTransaction transaction, List<EvoucherService> evoucherServices, SalePolicyRedeemEVC input, StringBuilder sb)
        {
            var tempServices = (from service in input.Services
                                from subServiceType in service.SubServiceTypes
                                from subService in subServiceType.SubServices
                                from evoucherService in evoucherServices
                                from customerType in input.CustomerTypes
                                where evoucherService.ServiceID == service.ServiceID && evoucherService.SubServiceType == subServiceType.SubServiceTypeID && customerType.ServiceID == evoucherService.ServiceID
                                select new HistoryTempModel()
                                {
                                    CustomerType = customerType.CustomerType,
                                    VoucherCode = evoucherService.evoucherCode,
                                    ServiceId = service.ServiceID,
                                    SubServiceTypeId = subServiceType.SubServiceTypeID,
                                    SubServiceId = subService.SubServiceID,
                                    ServiceCode = subService.ServiceCode,
                                    Prepaid = (int)subService.PrePaid,
                                    DeployTypeId = subService.DeployTypeID,
                                    Qty = subService.Qty,
                                    GroupId = 0,
                                    StatusDeviceId = -1,
                                    RevokeId = -1,
                                    UsesId = -1,
                                    MonthUsed = subService.MonthUsed
                                }).ToList();
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher tempServices", tempServices));

            var tempProducts = (from product in input.Products
                                from subServiceType in product.SubServiceTypes
                                from subService in subServiceType.SubServices
                                from evoucherService in evoucherServices
                                from customerType in input.CustomerTypes
                                where evoucherService.ServiceID == product.ServiceID && evoucherService.SubServiceType == subServiceType.SubServiceTypeId && customerType.ServiceID == evoucherService.ServiceID
                                select new HistoryTempModel()
                                {
                                    CustomerType = customerType.CustomerType,
                                    VoucherCode = evoucherService.evoucherCode,
                                    ServiceId = product.ServiceID,
                                    SubServiceTypeId = subServiceType.SubServiceTypeId,
                                    SubServiceId = subService.SubServiceID,
                                    ServiceCode = subService.ServiceCode,
                                    Prepaid = (int)subService.PrePaid,
                                    DeployTypeId = subService.DeployTypeID,
                                    Qty = subService.Qty,
                                    GroupId = subService.GroupID,
                                    StatusDeviceId = subService.StatusID,
                                    RevokeId = subService.RevokeID,
                                    UsesId = subService.UsesID,
                                    MonthUsed = subService.MonthUsed
                                }).ToList();
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher tempProducts", tempProducts));

            var temps = new List<HistoryTempModel>();
            temps.AddRange(tempServices);
            temps.AddRange(tempProducts);

            var xmlString = new XElement("N",
            from item in temps
            select new XElement("I",
                           new XElement("OrderCode", input.OrderCode),
                           new XElement("VoucherCode", item.VoucherCode),
                           new XElement("ObjId", input.objId),
                           new XElement("CustomerType", item.CustomerType),
                           new XElement("ServiceId", item.ServiceId),
                           new XElement("SubServiceTypeId", item.SubServiceTypeId),
                           new XElement("SubServiceId", item.SubServiceId),
                           new XElement("ServiceCode", item.ServiceCode),
                           new XElement("Prepaid", item.Prepaid),
                           new XElement("DeployTypeId", item.DeployTypeId),
                           new XElement("Qty", item.Qty),
                           new XElement("GroupId", item.GroupId),
                           new XElement("StatusDeviceId", item.StatusDeviceId),
                           new XElement("RevokeId", item.RevokeId),
                           new XElement("UsesId", item.UsesId),
                           new XElement("SaleId", input.SaleInfor.SaleID),
                           new XElement("Channel", input.SaleInfor.SaleChannelID),
                           new XElement("PhoneCustomerFPL", input.CustomerPhone.FptplayCustomerPhone),
                           new XElement("PaymentTypeL2", input.PaymentTypeID),
                           new XElement("MonthUsed", item.MonthUsed)
                       ));

            var isChange = connection.Execute(OSU6_FPTVoucher_SalePlatform_PolicyVoucher_Extend, new
            {
                Action = "SaveHistoryTemp",
                XML = xmlString,
                ObjID = input.objId,
                OrderCode = input.OrderCode
            }, transaction: transaction, commandTimeout: 0, commandType: System.Data.CommandType.StoredProcedure);
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher isChange", isChange));

            return isChange >= 0;
        }
    }
}