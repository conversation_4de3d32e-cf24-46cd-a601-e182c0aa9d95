using System.Collections.Generic;

namespace APIMBS.Models.VendorModels
{
    public class GetKeyCodeListInput
    {
        public string PartnerContract { get; set; }
        public string OrderCode { get; set; }
        public int SaleChannelID { get; set; }
        public int Status { get; set; }
        public int Quantity { get; set; }
    }

    public class GetKeyCodeListOutput
    {
        public bool Succeeded { get; set; }
        public string Message { get; set; }
        public string Errors { get; set; }
        public List<KeyCodeData> Data { get; set; }
    }

    public class KeyCodeData
    {
        public string EnCourseCode { get; set; }
        public string SkuCode { get; set; }
        public string Actived { get; set; }
        public int cost { get; set; }
        public string expirationDate { get; set; }
    }
    public class CheckFormatVoucher
    {
        public int ID { get; set; }
        public string Prefix { get; set; }
    }
}