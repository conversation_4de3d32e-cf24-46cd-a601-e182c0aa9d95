using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using APIMBS.Models.SalePlatform;

namespace APIMBS.Models.MobileSaleV4
{
    public class SalePlatformVoucherValueInfor
    {
        public string evoucherCode { set; get; }
        public int VoucherType { set; get; }
        public int VoucherTypeL2 { set; get; }
        // public string Value { set; get; }
        // public decimal Discount { set; get; }
        // public float Dismonth { set; get; }
        public int RegisterTypeID { set; get; }
        public int PolicyGroupID { set; get; }
        public int ApplyTypeID { set; get; }
        public int PromotionTypeID { set; get; }
        // ma voucher tren chinh sach theo ma le
        public string mapEvoucherCode { get; set; }
        // combo / only
        [JsonIgnore]
        public int Policy_Type { set; get; }
        // for fgold
        [JsonIgnore]
        public DateTime? ExpiredDate { get; set; }
        public int TypeID { set; get; }
        public int Quota { set; get; }
        public string VendorContract { set; get; }
        public List<Apply> Apply { set; get; }
        public RefInfo RefInfo { set; get; }
        public int Gold { set; get; }
    }

    public class RegisterTypeModel
    {
        public int RegisterTypeID { set; get; }
        public int PolicyGroupID { set; get; }
        public int ApplyTypeID { set; get; }
        public int PromotionTypeID { set; get; }

        // combo / only
        public int Policy_Type { set; get; }
        public string VendorContract { set; get; }
        // ma voucher tren chinh sach theo ma le
        public string mapEvoucherCode { get; set; }
        public int MaxQuantity { get; set; } = 0;
    }

    public class OldSalePlatformVoucherValueInfor
    {
        public string evoucherCode { set; get; }
        public string Value { set; get; }
        public decimal Discount { set; get; }
        public float Dismonth { set; get; }
        public decimal DiscountVAT { set; get; }
        public int TypeID { set; get; }
        public int Quota { set; get; }
        public List<OldApply> Apply { set; get; }
        public RefInfo RefInfo { set; get; }
    }

    public class VoucherReferralInfo
    {
        public string evoucherCode { set; get; }
        public string Value { set; get; }
        public decimal Discount { set; get; }
        public decimal DiscountVAT { set; get; }
        public int TypeID { set; get; }
    }

    public class RefInfo
    {
        public string fullName { set; get; }
        public string phone { set; get; }
        public string contract { set; get; }
    }
    public class DeviceModel : GroupProduct
    {
        public int qty { get; set; }
        public int serviceId { get; set; }
        public int subServiceTypeID { get; set; }
        public int deviceId { get; set; }
        public int serviceCode { get; set; }
    }
    public class GroupProduct
    {
        public int groupID { set; get; }
        public int applyPrePaid { set; get; }
        public int applySubServiceID { set; get; }
        public int statusID { set; get; }
        public int revokeID { set; get; }
        public int usesID { set; get; }
        public int prePaid { set; get; }
        public int deployTypeID { set; get; }
    }

    public class OldApply
    {
        public int ServiceID { set; get; }
        public int SubServiceTypeID { set; get; }
        public int SubServiceID { set; get; }
        public int ServiceCode { set; get; }
    }

    public class Apply
    {
        public int ServiceID { set; get; }
        public int SubServiceTypeID { set; get; }
        public int SubServiceID { set; get; }
        public int ServiceCode { set; get; }

        public int StatusID { set; get; } = -1;
        public int RevokeID { set; get; } = -1;
        public int DeployTypeID { set; get; } = -1;

        public string Value { get; set; }
        public decimal Discount { get; set; }
        public decimal DiscountVAT { get; set; }
        public float Dismonth { get; set; }
        public int IsDeductOrder { get; set; }
        public int Qty { get; set; } = 0;

        // tinh gia tri chong x dong y thang
        [JsonIgnore]
        public decimal FullDiscountVAT { get; set; }

        public Apply ShallowCopy()
        {
            return (Apply)this.MemberwiseClone();
        }
        public override bool Equals(object obj)
        {
            if (obj == null || GetType() != obj.GetType())
            {
                return false;
            }

            Apply other = (Apply)obj;

            return ServiceID == other.ServiceID
                && SubServiceTypeID == other.SubServiceTypeID
                && SubServiceID == other.SubServiceID
                && ServiceCode == other.ServiceCode

                && StatusID == other.StatusID
                && RevokeID == other.RevokeID
                && DeployTypeID == other.DeployTypeID

                && Value == other.Value
                && Discount == other.Discount
                && DiscountVAT == other.DiscountVAT
                && Dismonth == other.Dismonth
                && IsDeductOrder == other.IsDeductOrder;
        }
        public override int GetHashCode()
        {
            return (ServiceID, SubServiceTypeID, SubServiceID, ServiceCode, StatusID, RevokeID, DeployTypeID, Value, Discount, DiscountVAT, Dismonth, IsDeductOrder).GetHashCode();
        }
    }

    public class Evoucher : IEquatable<Evoucher>
    {
        public string VoucherCode { get; set; }
        public string Description { get; set; }
        public string Note { get; set; }
        public string Todate { get; set; }
        public int RegisterTypeID { get; set; }
        public int PolicyGroupID { get; set; }
        public int ApplyTypeID { get; set; }
        public int PromotionTypeID { get; set; }

        public bool Equals(Evoucher other)
        {
            if (other == null)
                return false;

            return this.VoucherCode == other.VoucherCode &&
                   this.Description == other.Description &&
                   this.Note == other.Note &&
                   this.Todate == other.Todate &&
                   this.RegisterTypeID == other.RegisterTypeID &&
                   this.PolicyGroupID == other.PolicyGroupID &&
                   this.ApplyTypeID == other.ApplyTypeID &&
                   this.PromotionTypeID == other.PromotionTypeID;
        }

        public override bool Equals(object obj)
        {
            if (obj == null)
                return false;

            Evoucher evoucherObj = obj as Evoucher;
            if (evoucherObj == null)
                return false;
            else
                return Equals(evoucherObj);
        }

        public override int GetHashCode()
        {
            return (VoucherCode != null ? VoucherCode.GetHashCode() : 0) ^
                   (Description != null ? Description.GetHashCode() : 0) ^
                   (Note != null ? Note.GetHashCode() : 0) ^
                   (Todate != null ? Todate.GetHashCode() : 0) ^
                   RegisterTypeID.GetHashCode() ^
                   PolicyGroupID.GetHashCode() ^
                   ApplyTypeID.GetHashCode() ^
                   PromotionTypeID.GetHashCode();
        }
    }

    public class ListVoucher
    {
        public List<PolicyVoucher2> LstVoucher { get; set; }
        public List<string> Error { get; set; }
    }
    public class EvoucherProduct : Evoucher
    {
        public int Id { get; set; }
        public string Value { get; set; }
        public decimal Discount { get; set; }
        public decimal DiscountVAT { get; set; }
        public int QuantityFrom { get; set; }
        public int QuantityTo { get; set; }
        public int InviteType { get; set; }
        public int ServiceId { get; set; }
        public int SubServiceId { get; set; }
        public int SubServiceTypeId { get; set; }
        public int ServiceCode { get; set; }
        public int CustomerTypeId { get; set; }
    }

    public class Evoucherid : Evoucher
    {
        public int ID { set; get; }
        public int LocaltypeID { set; get; }
    }

    public class EvoucherServiceCode : Evoucherid
    {
        public int ServiceCodeGTTTnet { set; get; }
        public int ServiceCodeGTTTtv { set; get; }
        public int ServiceCodePHMnet { set; get; }
        public int ServiceCodePHMtv { set; get; }
    }

    public class VoucherInfor : PEDiscountModel
    {
        public string Name { set; get; }
    }

    #region loy_qlcs_new_customer
    public class event_prepaid_config
    {
        public string action_code { get; set; }
        public int prepaid { get; set; }
        public int month_used { get; set; }
        public string event_code { get; set; }
        public int event_id { get; set; }
        public int gold { get; set; }
        public int amount { get; set; }
        public string voucherType { get; set; }
    }

    public class event_prepaid_config_valid
    {
        public string Item1 { get; set; }
        public int Item2 { get; set; }
    }

    public class  logitem
    {
        public event_prepaid_config config { get; set; }
        public string log {get; set; }
    }

    #endregion
}