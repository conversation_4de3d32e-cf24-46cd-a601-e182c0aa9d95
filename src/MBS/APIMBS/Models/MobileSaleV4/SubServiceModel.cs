using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.MobileSaleV4
{
    public class SubServiceModel 
    {
        public int GroupID { set; get; }
        public int SubServiceID { set; get; }
        public double PrePaid { set; get; }
        public int DeployTypeID { set; get; }
        public int ServiceCode { set; get; }
        public int Qty { set; get; }
        public double MonthUsed { set; get; }
        public decimal Total { set; get; }
        public decimal Total_VAT { set; get; }
        public int PolicyID { set; get; }
        public int PolicyExtraTypeID { set; get; }
        public int NotApplyVoucher { set; get; }
    }

    public class VoucherServiceModel : SubServiceModel
    {
        public string evoucher { set; get; }
        public int custype { set; get; }
    }

    public class VoucherProductModel
    {
        public int SubServiceTypeID { set; get; }
        public int ServiceID { set; get; }
        public string evoucher { set; get; }
        public int custype { set; get; }
        public int SubServiceID { set; get; }
        public int PrePaid { set; get; }
        public int DeployTypeID { set; get; }
        public int ServiceCode { set; get; }
        public int Qty { set; get; }
        public int GroupID { set; get; }
        public int ApplySubServiceID { set; get; }
        public int ApplyPrePaid { set; get; }
        public int StatusID { set; get; }
        public int RevokeID { set; get; }
        public int UsesID { set; get; }
    }
    public class VoucherServiceTypeModel : VoucherServiceModel
    {
        public int SubServiceTypeID { set; get; }
        public int ServiceID { set; get; }
    }
    public class SubServiceTypesModel 
    {
        public int SubServiceTypeID { set; get; }
        public List<SubServiceModel> SubServices { set; get; }
    }

    public class ServicePlatform:Services
    {
        public List<SubServiceTypesModel> SubServiceTypes { set; get; }
    }
    public class ProductPlatform : Services
    {
        public List<SubServiceTypesProModel> SubServiceTypes { set; get; }
    }

    public class Services
    {
        public int ServiceID { set; get; }
        
    }
    public class SubServiceTypesProModel
    {
        public int SubServiceTypeId { set; get; }
        public List<SubServicesPro> SubServices { set; get; } 
    }
    public class SubServicesPro : SubServiceModel
    {        
        public int ApplySubServiceID { set; get; }
        public int ApplyPrePaid { set; get; }
        public int StatusID { set; get; }
        public int RevokeID { set; get; }
        public int UsesID { set; get; }
    }

    
}