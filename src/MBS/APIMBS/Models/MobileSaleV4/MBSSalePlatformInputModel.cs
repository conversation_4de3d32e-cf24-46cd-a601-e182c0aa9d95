using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.MobileSaleV4
{
    public class MBSSalePlatformInputModel
    {
        public int locationID { set; get; }
        public int saleID { set; get; }
        public int saleTeamId { set; get; }

        public List<CustomerTypeModel> CustomerTypes { set; get; }

        public List<ServicePlatform> Services { set; get; }
        public List<ProductPlatform> Products { set; get; }
        public UserBranchPlatformModel Userbranch { set; get; }
        public int channel { set; get; }
    }

    public class SalePlatformGetListVoucher : MBSSalePlatformInputModel
    {
        public string fptplayCustomerPhone { set; get; }
        public int paymentTypeL2 { set; get; }
    }

    public class SalePlatformGetVoucherInfo : MBSSalePlatformInputModel
    {
        public List<EvoucherInput> evoucherList { set; get; }
    }

    public class SalePlatformKeepVoucher : MBSSalePlatformInputModel
    {
        public List<TypeActiveVoucher> evoucherList { set; get; }
        public string OrderCode { set; get; }
    }

    public class SalePlatformRedeemVoucher : MBSSalePlatformInputModel
    {
        public List<EvoucherInput> evoucherList { set; get; }
        public string OrderCode { set; get; }
        public string RegCode { set; get; }
        public int objId { set; get; }
        public string fptplayCustomerPhone { set; get; }
        public int paymentTypeL2 { set; get; }
    }
    public class UserBranchPlatformModel
    {
        public int sourceId { set; get; }
        public int subcompanyId { set; get; }
        public int branchCode { set; get; }
        public int departmentId { set; get; }
        public string contractGT { set; get; }
    }
}