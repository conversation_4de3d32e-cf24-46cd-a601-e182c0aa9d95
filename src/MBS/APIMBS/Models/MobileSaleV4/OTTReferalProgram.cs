using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.MobileSaleV4
{
    public class OTTReferalProgram
    {
        public validCode data { set; get; }
        public string message { set; get; }
        public int status { set; get; }
    }
    public class InfoRP
    {
        public int ID { get; set; }
        public string EventCode { get; set; }
        public int MoneyPromotionNETID { get; set; }
        public int MoneyPromotionTVID { get; set; }
        public int NetPromotionID { get; set; }
        public int IPTVPromotionID { get; set; }
        public int NetInterConnPromotionID { get; set; }
        public int IPTVInterConnPromotionID { get; set; }
        public int GiftPromotionID { get; set; }

        public int PromotionNetID { get; set; }
        public int ContainerID { get; set; }
    }
    public class ItemRequest
    {
        public int PromotionEventID { get; set; }
        public int ObjecInvite { get; set; }
        public int Objectinvited { get; set; }
        public string RegCode { get; set; }
        public string VoucherCode { get; set; }
        public int FreeMonthNet { get; set; }
    }

    public class OTTUserOutput
    {
        public OTTUserInfo data { set; get; }
        public string message { set; get; }
        public int status { set; get; }
    }

    public class FptPlayClientModel
    {
        public string FullName { get; set; }
        public string Phone { get; set; }
        public string OttClient { get; set; }
    }
    
    public class OTTUserInfo
    {
        public string fullname { set; get; }
    }
    public class validCode
    {
        public string referral_code { set; get; }
        public int valid { set; get; }
    }

    public class SendRequestOTTOutput
    {
        public string message { set; get; }
        public int status { set; get; }
    }

    public class DataActiveOTT
    {
        public int IDSuccessInvice { set; get; }
        public int GeneralCodeID { set; get; }
        public DateTime RedeemDate { set; get; }
        public int ID { set; get; }
        public int ObjIDDK { set; get; }
        public string ContractDK { set; get; }
        public string Fullname { set; get; }
        public string OttClient { set; get; }
        public string FirstAccess { set; get; }
        public string Date { set; get; }
        public string Internet { set; get; }
        public string IPTV { set; get; }
        public string Location_Phone { set; get; }
        public decimal Reward { set; get; }
    }
    public class CallbackReferralOTT
    {
        public string user_phone { set; get; }
        public string campaign_id { set; get; }
        public string transaction_id { set; get; }
        public string additional_info { set; get; }
        public string lucky_number { set; get; }
        public string user_fullname { set; get; }
    }
}