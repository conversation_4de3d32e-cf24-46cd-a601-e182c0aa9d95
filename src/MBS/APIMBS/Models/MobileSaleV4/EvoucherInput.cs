using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.MobileSaleV4
{
    public class EvoucherInput
    {
        public string evoucherCode { set; get; }
        public int evoucherType { set; get; }
        public int channel { set; get; }
        public int RefId { set; get; }
    }

    public class EvoucherInputNew
    {
        public string evoucherCode { set; get; }
        public int evoucherType { set; get; }
        public int RefId { set; get; }
    }

    public class EvoucherService : EvoucherInput
    {
        public int ServiceID { set; get; }
        public int SubServiceType { set; get; }
    }

    public class TypeActiveVoucher : EvoucherInput
    {
        public int inputType { set; get; }
    }
    
    public class MonthVoucherOutput
    {
        public string evoucherCode { set; get; }
        public float countMonth { set; get; }
    }
    public class MonthVoucherInput
    {
        public List<string> evoucherCodes { set; get; }
    }

    public class RemoveQuotakeepForSalemanInput
    {
        public int SaleID { set; get; }
        public string OrderCode { set; get; }
    }
    #region clear voucher
    public class ClearVoucherInput
    {
        public string regCode { set; get; }
        public int objID { set; get; }
        public string voucherGc { set; get; }
        public string user { set; get; }
        public string OrderCode { set; get; }

    }
    public class DataEventID
    {
        public int generalCodeID { set; get; }
        public int voucherBillID { set; get; }
        public long prePaidID { set; get; }
        public int objPromotionID { set; get; }

    }
    #endregion
}