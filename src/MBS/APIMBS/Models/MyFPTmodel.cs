using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{
    public class MyFPTmodel
    {
    }
    public class InfoVoucherResponse
    {
        public int Type { get; set; }
        public List<ItemNode> PromotionEvent { get; set; }
        public string ContractRef { get; set; }
        public string FullNameRef { get; set; }
        public string PhoneRef { get; set; }
    }
    public class PromotionEventMyFPT
    {
        public string Name { set; get; }
        public decimal ValueDiscountAmount { set; get; }
    }
    public class InfoVoucherRequest
    {
        public string VoucherCode { get; set; }
        public string OrderCode { get; set; }

        public int NETID { get; set; }
        public int IPTVID { get; set; }

        public int LocalType { get; set; }
        public int PrepaidTimeNet { get; set; }
        public int IDPrepaid { get; set; }

        public int PrepaidTimeTV { get; set; }


        public int ServiceCodeTTNet { get; set; }

        public int ServiceCodeTTTV { get; set; }

        public int ServiceCodeHMNet { get; set; }
        public int ServiceCodeHMTV { get; set; }
    }
}