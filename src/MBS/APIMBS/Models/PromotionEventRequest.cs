using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{
    public class PromotionEventRequest
    {
        public int NETID { get; set; }
        public int IPTVID { get; set; }
        public int CAM { get; set; }
        public int QuantityCam { get; set; }
        public int IsPrepaidTV { get; set; }
        public int LocalType { get; set; }
        public string VoucherRP { get; set; }
        public string VoucherGeneral { get; set; }
        public int LocationID { get; set; }
        public int SalesID { get; set; }

        //////////////////////////
        public int ServiceCodeTTNet { get; set; }

        public int ServiceCodeTTTV { get; set; }

        public int ServiceCodeHMNet { get; set; }
        public int ServiceCodeHMTV { get; set; }
        public List<int> ServiceCodeCamera { get; set; }

        //////////////////////////

        [JsonIgnore]
        public int IsPrepaidNET { get; set; }

        [JsonIgnore]
        public int NETServiceCode { get; set; }


        [JsonIgnore]
        public int DSLType { get; set; }       
    }
}