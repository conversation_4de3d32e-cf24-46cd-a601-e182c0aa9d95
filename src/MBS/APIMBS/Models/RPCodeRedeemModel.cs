using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{
    public class RPCodeRedeemModel
    {
        public int PromotionEventID { get; set; }
        public int ObjID{ get; set; }
        public string OrderCode { get; set; }
        public string VoucherCode { get; set; }
        public int Location { get; set; }
        public int BranchCode { get; set; }
        public int Department { get; set; }
        public int Salesman { get; set; }
        public int BNET { get; set; }
        public int BTV { get; set; }
        public int IsPrepaidTV { get; set; }
        // status in active in MBS chanel will be have two type:
        // 200 is user invite - 222 is user invited
        // for websilte online chanel : 100 is user invite - 111 is user invited
        public int ActiveChannel { get; set; }
        public int SubCompanyID { get; set; }
        public int LocalTypeID { get; set; }
        public int PaidTimeTypeID { get; set; }
    }
}