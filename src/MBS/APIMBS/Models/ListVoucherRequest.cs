using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{
    public class ListVoucherRequest
    {

        public int NETID { get; set; }
        public int IPTVID { get; set; }

        public int SalesID { get; set; }

        public int LocationID { get; set; }
        public int LocalType { get; set; }


        //////////////////////////
        public int ServiceCodeTTNet { get; set; }

        public int ServiceCodeTTTV { get; set; }

        public int ServiceCodeHMNet { get; set; }
        public int ServiceCodeHMTV { get; set; }

        //////////////////////////


        [JsonIgnore]
        public int IsPrepaidNet { get; set; }
        [JsonIgnore]
        public int NETServiceCode { get; set; }
        public int IsPrepaidTV { get; set; }
    }
}