using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{
    public class PEDiscountModel
    {
        public int ID { get; set; }
        public string EventCode { get; set; }
        public int MoneyPromotionNETID { get; set; }
        public int MoneyPromotionTVID { get; set; }
        // SubsProm
        public int NetPromotionID { get; set; }
        public int IPTVPromotionID { get; set; }
        public int NetInterConnPromotionID { get; set; }
        public int IPTVInterConnPromotionID { get; set; }
        public int GiftPromotionID { get; set; }
        // PromotionNet
        public int PromotionNetID { get; set; }
        public int ContainerID { get; set; }
    }

    public class In4VoucherTHmodel
    {
        public int amountTv { get; set; }
        public int locationId { get; set; }
        public int addBy { get; set; }
    }
    public class DataBackMoney
    {
        public int SaleID { get; set; }
        public int LocationID { get; set; }
        public int BranchCode { get; set; }
        public int DistrictID { get; set; }
        public int TotalDiscount { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
    }
}