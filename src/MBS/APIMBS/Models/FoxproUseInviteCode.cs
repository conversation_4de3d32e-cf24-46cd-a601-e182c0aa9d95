using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{
    public class FoxproUseInviteCode
    {
        public int ID { set; get; }
        public int generalCodeID { set; get; }
        public int res { set; get; }
        public string StaffIDInvite { set; get; }
        public string InviteCode { set; get; }
        public string Description { set; get; }
        public int ObjID { set; get; }
        public string FullName { set; get; }
        public string Contract { set; get; }
        public DateTime? FirstAccess { set; get; }
        public string RealMoneyAmount { set; get; }
        public string EventCode { set; get; }
        public int MoneyPromotionNETID { set; get; }
        public string NET { set; get; }
        public int MoneyPromotionTVID { set; get; }
        public string TV { set; get; }
    }
    public class LoginInfor
    {
        public string username { set; get; }
        public string password { set; get; }
    }
    public class Login
    {
        public const string userName = "<EMAIL>";
        public const string passWord = "!@#Referral123";
    }
    public class AuthorizationInfor
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string refresh_token { set; get; }
        public string accessTokenExpiresOn { set; get; }
        public string refreshTokenExpiresOn { set; get; }
    }
    public class StaffInfor
    {
        public Name data { set; get; }
    }
    public class Name
    {
        public string fullname { set; get; }
        public string email { set; get; }
    }
    public class ModelSendNotificationReferralMyFPT
    {
        public string employeeCode { get; set; }
        public decimal Referrer { get; set; }
        public string BookingId { get; set; }
        public string contract_owner { get; set; }
        public string BookingStatus { get; set; }
    }    
}