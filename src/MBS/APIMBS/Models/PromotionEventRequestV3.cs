using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{    
    public class PromotionEventRequestV3
    {
        public int NETID { get; set; }
        public int IPTVID { get; set; }
        public int IsPrepaidTV { get; set; }
        public int LocalType { get; set; }
        public int LocationID { get; set; }
        public int SalesID { get; set; }

        //////////////////////////
        //public int ServiceCodeTTNet { get; set; }

        //public int ServiceCodeTTTV { get; set; }

        //public int ServiceCodeHMNet { get; set; }
        //public int ServiceCodeHMTV { get; set; }

        //////////////////////////

        [JsonIgnore]
        public int IsPrepaidNET { get; set; }

        [JsonIgnore]
        public int NETServiceCode { get; set; }


        [JsonIgnore]
        public int DSLType { get; set; }
        public List<Voucher> Voucher { set; get; }
    }

    public class Voucher
    {
        public string voucherCode { set; get; }
        public int type { set; get; }
    }

    public class PromotionEventOutput
    {
        public Promotion Data { set; get; }
    }
    public class Promotion
    {
        public string Name { set; get; }
        public string VoucherCode { set; get; }
        public decimal Amount { set; get; }
        public List<ServicePromotion> Services { set; get; }

    }
    public class ServicePromotion
    {
        public string PolicyType { set; get; }
        public string Service { set; get; }
        public int ServiceType { set; get; }
        public int SubService { set; get; }
        public string DeviceType { set; get; }
        public string DeviceID { set; get; }
        public int ServiceCode { set; get; }
        public string Package { set; get; }
        public decimal PriceID { set; get; }
    }
}