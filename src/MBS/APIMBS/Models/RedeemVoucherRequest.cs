using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{
    public class RedeemVoucherRequest
    {
        public int NETID { get; set; }
        public int IPTVID { get; set; }
        public string OrderCode { get; set; }
        public int SaleID { get; set; }
        // properties of user invited
        public int ObjID { get; set; }
        // properties of user invite
        [JsonIgnore]
        public int ObjIDInvite { get; set; }
        public string VoucherRP { get; set; }
        public string VoucherGeneral { get; set; }
        [JsonIgnore]
        public int IsPrepaidNET { get; set; }
        public int IsPrepaidTV { get; set; }
        public int LocalType { get; set; }
    }
}