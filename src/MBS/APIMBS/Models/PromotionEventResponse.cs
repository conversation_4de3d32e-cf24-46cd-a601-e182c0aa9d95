using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models
{
    public class PromotionEventResponse
    {
       public PromotionEventResponse ()
	    {
            VoucherRP = new List<ItemNode>();
            VoucherGeneral = new List<ItemNode>();
	    }

        public List<ItemNode> VoucherRP { get; set; }
        public List<ItemNode> VoucherGeneral { get; set; }
    }


    public class ItemNode
    {
        public string PromotionEvent { get; set; }
        public string  Value { get; set; }

       
        public int ServiceCode { get; set; }

        [JsonIgnore]
        public int Dismonth { get; set; }
    }
}