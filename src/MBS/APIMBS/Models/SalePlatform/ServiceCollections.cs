namespace APIMBS.Models.SalePlatform
{
    public class SaleOrderCollections
    {
        public int serviceId { set; get; }
        public int subServiceTypeId { set; get; }
        public int subServiceId { set; get; }
        public int serviceCode { set; get; }
        public int prePaid { set; get; }
        public int deployTypeId { set; get; } = 0;
        public int statusId { set; get; } = 0;
        public int revokeID { set; get; } = 0;
        public int usesId { set; get; }
        public int qty { set; get; }
        public int monthUsed { set; get; }
        public int total { set; get; }
        public int total_vat { set; get; }
        public int policyId { set; get; }
        public int crossSellingLocation { set; get; }
        public int paymentTypeId { set; get; }
        public int cusTypeId { set; get; }
        public int cusTypeL2Id { set; get; }
        public int objectTypeId { set; get; }
        public string customerRank { set; get; }
        public string arrPolicyInput { set; get; }
        public decimal totalMoneyPolicyInput { set; get; }
        public int policyExtraTypeID { set; get; }
    }
    public class CustomerSaleOrderCollections : SaleOrderCollections
    {
        public int customerType { set; get; }
        public override bool Equals(object obj)
        {
            if (obj == null || GetType() != obj.GetType())
            {
                return false;
            }

            CustomerSaleOrderCollections other = (CustomerSaleOrderCollections)obj;

            return customerType == other.customerType
                && customerType == other.customerType
                && serviceId == other.serviceId
                && subServiceTypeId == other.subServiceTypeId
                && subServiceId == other.subServiceId
                && serviceCode == other.serviceCode
                && prePaid == other.prePaid
                && deployTypeId == other.deployTypeId
                && statusId == other.statusId
                && revokeID == other.revokeID
                && usesId == other.usesId
                && qty == other.qty
                && monthUsed == other.monthUsed
                && total == other.total
                && total_vat == other.total_vat
                && policyId == other.policyId
                && crossSellingLocation == other.crossSellingLocation
                && paymentTypeId == other.paymentTypeId
                && cusTypeId == other.cusTypeId
                && cusTypeL2Id == other.cusTypeL2Id
                && objectTypeId == other.objectTypeId
                && customerRank == other.customerRank
                && arrPolicyInput == other.arrPolicyInput
                && totalMoneyPolicyInput == other.totalMoneyPolicyInput
                && policyExtraTypeID == other.policyExtraTypeID;
        }
        public override int GetHashCode()
        {
            return (customerType,
                serviceId, 
                subServiceTypeId, 
                subServiceId, 
                serviceCode, 
                prePaid, 
                deployTypeId, 
                statusId, 
                revokeID, 
                usesId, 
                qty, 
                monthUsed, 
                total, 
                total_vat, 
                policyId, 
                crossSellingLocation, 
                paymentTypeId, 
                cusTypeId, 
                cusTypeL2Id, 
                objectTypeId, 
                customerRank, 
                arrPolicyInput, 
                totalMoneyPolicyInput,
                policyExtraTypeID).GetHashCode();
        }
    }
}