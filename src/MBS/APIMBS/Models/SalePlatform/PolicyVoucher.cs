using APIMBS.Models.MobileSaleV4;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.SalePlatform
{
    public class PolicyVoucher : Evoucher
    {
        public int policyId { set; get; }
        public int programId { set; get; }//
        public string voucherService { set; get; }
        public int valueVoucher { set; get; }
        public int applyTypeID { set; get; }
        public string subServiceCondition { set; get; }
        public int minprepaid { set; get; }
        public int minConnectionFee { set; get; }
        public int minmoney { set; get; }
        public int promotionTypeId { set; get; }
        public decimal duration { set; get; }
        public int serviceId { set; get; }
        public int subServiceTypeId { set; get; }
        public int subServiceId { set; get; }
        public int serviceCode { set; get; }
        public int applyFor { set; get; }
        public int isService { set; get; }
        public int ApplyTypeAltID { set; get; }
    }
    public class PolicyVoucher2
    {
        public int PolicyID { get; set; }
        public int VoucherID { get; set; }
        public string VoucherCode { get; set; }
        public int ProgramID { get; set; }
        public string Description { get; set; }
        public string Note { get; set; }
        public int PromotionTypeID { get; set; }
        public int ApplyTypeID { get; set; }
        public int ApplyFor { get; set; }
        public int ServiceID { get; set; }
        public int SubServiceTypeID { get; set; }
        public int SubServiceID { get; set; }
        public int ServiceCode { get; set; }
        public int Prepaid { get; set; }
        public int FromQuantity { get; set; }
        public int ToQuantity { get; set; }
        public double ValueVoucher { get; set; }
        public double MonthValue { get; set; }
        public double MinPrepaid { get; set; }
        public double MinValue { get; set; }
        public double MinConnectionFee { get; set; }
        public int RegisterTypeID { get; set; }
        public int PolicyGroupID { get; set; }
        public int ApplyTypeAltID { get; set; }
        public string ToDate { get; set; }
        public string SubServiceTypeID_Services { get; set; }
        public string SubServiceTypeID_Products { get; set; }
        public int DeviceStatusID { get; set; }
        public int RevokeID { get; set; }
        public int DeployTypeID { get; set; }
        public int VoucherTypeID { get; set; }
        public int CampaignID { get; set; }
        public int CheckQuota { get; set; }
        public int FromPrice { get; set; }
        public int PolicyExtraTypeID { get; set; } = 0;
        public int MaxQuantity { get; set; } = 0;
    }

    public class ServiceGroup
    {
        public int ServiceID { set; get; }
        public string SubServiceId { set; get; }
    }

    public class ServiceValueModel
    {
        public string VoucherCode { get; set; }
        public int ServiceID { get; set; }
        public int SubServiceTypeID { get; set; }
        public int PolicyID { get; set; }
        public decimal Value { get; set; }
        public decimal MonthValue { get; set; }
        public decimal ConnectionFee { get; set; }
        public int PolicyGroupID { get; set; }
    }

    public class HistoryTempModel
    {
        public int CustomerType { get; set; }
        public string VoucherCode { get; set; }
        public int ServiceId { get; set; }
        public int SubServiceTypeId { get; set; }
        public int SubServiceId { get; set; }
        public int ServiceCode { get; set; }
        public int Prepaid { get; set; }
        public int DeployTypeId { get; set; }
        public int Qty { get; set; }
        public int GroupId { get; set; }
        public int StatusDeviceId { get; set; }
        public int RevokeId { get; set; }
        public int UsesId { get; set; }
        public double MonthUsed { get; set; }
    }

    public class DiscountModel
    {
        public string Code { get; set; }
        public string OrderCode { get; set; }
        public string RegCode { get; set; }
        public int SaleId { get; set; }
        public int ObjId { get; set; }
        public int LocationID { get; set; }
        public int BranchCode { get; set; }
        public int DistrictID { get; set; }
        public int ActiveChannel { get; set; }
        public int LocalTypeId { get; set; }
        public int PolicyId { get; set; }
        public int ServiceId { get; set; }
        public int SubServiceTypeId { get; set; }
        public int SubServiceId { get; set; }
        public int ServiceCode { get; set; }
        public int Qty { get; set; }
        public double monthUse { get; set; }
        public decimal Total { get; set; }
        public int SaleChannelId { set; get; }
        public int SaleTeamID { set; get; }
        public int PaymentTypeID { set; get; }
        public int CrossSellingLocation { set; get; }
        public int ObjectTypeId { set; get; }
        public int CusTypeId { set; get; }
        public int CusTypeL2Id { set; get; }
        public string CustomerRank { set; get; }
        public int UnitParentID { set; get; }
        public string UnitParentCode { set; get; }
        public int LimitType { set; get; }
        public string UnitCode { set; get; }
    }

    public class GeneralCodeDetailModel
    {
        public int GeneralCodeId { get; set; }
        public int ServiceId { get; set; }
        public int SubServiceTypeId { get; set; }
        public int SubServiceId { get; set; }
        public int ServiceCode { get; set; }
        public int Quantity { get; set; }
        public int TotalDiscount { get; set; }
    }

    public class DiscountVoucher : ServiceValueModel
    {
        public int PromotionTypeID { get; set; }
        public string VoucherCode { set; get; }
        public DateTime RedeemDate { set; get; }
    }

    public class DiscountVoucherInput
    {
        public string Contract { set; get; }
        public int ServiceID { set; get; }
    }
    public class MinmoneyModel
    {
        public int RQID { set; get; }
        public int SubServiceID { set; get; }
        public int Prepaid { set; get; }
        public int Value { set; get; }
        public int ConnectionFee { set; get; }
    }

    public class VendorInput
    {
        public string OrderCode { set; get; }
        public string RegCode { set; get; }
        public string VoucherCode { set; get; }
        public string VendorContract { set; get; }
        public int SaleChannelID { set; get; }
    }
    public class VendorOutput
    {
        public string VoucherCode { set; get; }
        public string Code { set; get; }
        public int Cost { set; get; }
        public string ExpirationDate { set; get; }
        public int ContractID { set; get; }
        public string KeyName { set; get; }
        public string RepName { set; get; }
    }

    public class EmployeeInfo
    {
        public EmployeeInfoData Data { get; set; }
        public int Status { get; set; }
        public string Message { get; set; }
    }

    public class EmployeeInfoData
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public int ParentId { get; set; }
        public string ParentCode { get; set; }
        public string Path { get; set; }
        public int SaleTeamId { get; set; }
        public object NamePath { get; set; }
    }

    public class DXBaseConfig
    {
        public int ID { get; set; }
        public int FValue { get; set; }
        public string FName { get; set; }
        public int TValue { get; set; }
        public string TName { get; set; }
        public int GroupID { get; set; }
        public string GroupName { get; set; }
        public int Status { get; set; }
    }

    public class ContractModelDetail
    {
        public bool Succeeded { get; set; }
        public string Message { get; set; }
        public object Errors { get; set; }
        public ContractData Data { get; set; }
    }

    public class ContractData
    {
        public int ContractId { get; set; }
        public string CoopContract { get; set; }
        public string VendorName { get; set; }
        public string TaxCode { get; set; }
        public string AddressBusiness { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string RepresentativeName { get; set; }
        public string Position { get; set; }
        public string Note { get; set; }
        public int SupplierType { get; set; }
        public string BusinessModel { get; set; }
        public string SupplierForm { get; set; }
        public int ContractStatus { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string Agency { get; set; }
        public string POwner { get; set; }
        public string CreateBy { get; set; }
        public string CreateDate { get; set; }
        public string UpdateBy { get; set; }
        public string UpdateDate { get; set; }
        public string UnitCreate { get; set; }
        public string SaleAccount { get; set; }
        public string RepresentationContract { get; set; }
        public string KeyName { get; set; }
        public string RepName { get; set; }
        public string BillingAccount { get; set; }
        public int IsRepresentationContract { get; set; }
        public List<Cost> Costs { get; set; }
        public List<Bill> Bills { get; set; }
        public List<Review> Reviews { get; set; }
        public List<File> Files { get; set; }
        public List<Contact> Contacts { get; set; }
    }

    public class Cost
    {
        public int CostId { get; set; }
        public int ContractId { get; set; }
        public string CodeApendix { get; set; }
        public string CommissionCost { get; set; }
        public string EffectiveDate { get; set; }
        public string ExpirationDate { get; set; }
        public int Status { get; set; }
        public string NameFile { get; set; }
        public int IdFile { get; set; }
        public int Enabled { get; set; }
    }

    public class Bill
    {
        public int BillId { get; set; }
        public int ContractId { get; set; }
        public int Province { get; set; }
        public string UnitRelease { get; set; }
        public string TaxRelease { get; set; }
        public string AddressRelease { get; set; }
        public string EmailRelease { get; set; }
        public string Note { get; set; }
        public string AccountName { get; set; }
        public string AccountNumber { get; set; }
        public string BankBranch { get; set; }
        public string PaymentTerms { get; set; }
        public int Enabled { get; set; }
    }

    public class Review
    {
        public int ReviewId { get; set; }
        public int ContractId { get; set; }
        public int Times { get; set; }
        public int TimeType { get; set; }
        public string FixedDay { get; set; }
        public string FixedDate { get; set; }
        public int Enabled { get; set; }
    }

    public class File
    {
        public int Id { get; set; }
        public int ContractId { get; set; }
        public int Type { get; set; }
        public int FileId { get; set; }
        public string Note { get; set; }
        public int Enabled { get; set; }
    }

    public class Contact
    {
        public int ContactId { get; set; }
        public int ContractId { get; set; }
        public string FullName { get; set; }
        public string Department { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public int VendorType { get; set; }
        public int TypeContact { get; set; }
        public int Enabled { get; set; }
        public int LocationID { get; set; }
    }
    public class ContractModelDetailReq
    {
        public int Type { get; set; }
        public string Contract { get; set; }
    }
}