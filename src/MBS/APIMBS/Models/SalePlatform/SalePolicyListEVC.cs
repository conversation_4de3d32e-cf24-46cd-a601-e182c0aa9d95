using APIMBS.Models.MobileSaleV4;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.SalePlatform
{
    public class SalePolicyListEVC : SalePolicyPlatformInputModel
    {
    }

    public class SalePolicyGetInforEVC : SalePolicyPlatformInputModel
    {
        public List<EvoucherInputNew> evoucherList { set; get; }

        public int objId { set; get; }

        //public int debug { get; set; }
    }

    public class SalePolicyRedeemEVC : SalePolicyGetInforEVC
    {
        public string OrderCode { set; get; }
        public string RegCode { set; get; }
        public int KeepStatus { get; set; }

        // move to SalePolicyGetInforEVC
        //public int objId { set; get; }
    }

    public class SalePolicyGetInforEVCoutput
    {
        public string evoucherCode { set; get; }
        // public string Value { set; get; }
        // public decimal Discount { set; get; }
        // public float Dismonth { set; get; }
        // public decimal DiscountVAT { set; get; }
        public int TypeID { set; get; }
        public int Quota { set; get; }
        public List<Apply> Apply { set; get; }
        public RefInfo RefInfo { set; get; }
    }
    public class SaleInfor
    {
        public int SaleID { set; get; }
        public int SaleTeamID { set; get; }
        public int SaleChannelID { set; get; }
    }

    public class CustomerInfor
    {
        public int BranchCode { set; get; }
        public int LocationID { set; get; }
        public int DistrictID { set; get; }
        public int WardID { set; get; }
        public int BuildingID { set; get; }
        public int CusTypeID { set; get; }
        public int CusTypeL2ID { set; get; }
        public int ObjectTypeID { set; get; }
        public string CustomerRank { set; get; }
        public int? LoyaltyStatus { set; get; }
        public string ContractGT { set; get; }
    }
    public class CustomerPhone
    {
        public string PhoneNumberContract { set; get; }
        public string PhoneNumberCMR { set; get; }
        public string FptplayCustomerPhone { set; get; }
    }

    public class SalePolicyPlatformInputModel
    {
        public List<CustomerTypeModel> CustomerTypes { set; get; }
        public List<ServicePlatform> Services { set; get; }
        public List<ProductPlatform> Products { set; get; }
        public CustomerPhone CustomerPhone { set; get; }
        public CustomerInfor CustomerInfor { set; get; }
        public int CrossSellingLocation { set; get; }
        public List<int> ServiceAvailable { set; get; }
        public SaleInfor SaleInfor { set; get; }
        public int PaymentTypeID { set; get; }
        public string GroupPoint { set; get; }
        public ExtendedProperties ExtendedProperties { set; get; }
        public List<ApplyMoney> ApplyMoney { set; get; }
    }
    public class ApplyMoney
    {
        public string VoucherCode { set; get; }
        public int ServiceID { set; get; }
        public int SubServiceTypeID { set; get; }
        public int SubServiceID { set; get; }
        public int ServiceCode { set; get; }
        public decimal Money { set; get; }
        public int Qty { set; get; }
    }
    public class SPServices
    {
        public int ServiceID { set; get; }

    }
    public class SPServicePlatform : SPServices
    {
        public List<SPSubServiceTypesModel> SubServiceTypes { set; get; }
    }
    public class SPProductPlatform : SPServices
    {
        public List<SPSubServiceTypesProModel> SubServiceTypes { set; get; }
    }
    public class SPSubServiceTypesProModel
    {
        public int SubServiceTypeID { set; get; }
        public List<SPSubServicesPro> SubServices { set; get; }
    }
    public class SPSubServicesPro : SPSubServiceModel
    {
        public int GroupID { set; get; }
        public int ApplySubServiceID { set; get; }
        public int ApplyPrePaid { set; get; }
        public int StatusID { set; get; }
        public int RevokeID { set; get; }
        public int UsesID { set; get; }
    }
    public class SPSubServiceTypesModel
    {
        public int SubServiceTypeID { set; get; }
        public List<SPSubServiceModel> SubServices { set; get; }
    }
    public class SPSubServiceModel
    {
        public int SubServiceID { set; get; }
        public int PrePaid { set; get; }
        public int DeployTypeID { set; get; }
        public int ServiceCode { set; get; }
        public int Qty { set; get; }
        public int MonthUsed { set; get; }
        public int Total { set; get; }
        public int Total_VAT { set; get; }
        public int PolicyID { set; get; }
    }

    public class ExtendedProperties
    {
        public int ActiveKplus { set; get; }
        public InternetProperties Internet { set; get; }
    }

    public class InternetProperties
    {
        public int ServiceID { set; get; }
        public int ChargeMonthly { set; get; }
    }
}