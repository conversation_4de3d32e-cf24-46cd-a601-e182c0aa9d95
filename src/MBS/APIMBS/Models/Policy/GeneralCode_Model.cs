using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.Policy
{
    public class GeneralCode_Model
    {
        public string VoucherCode { get; set; }
        public string OrderCode { get; set; }
        public int SaleID { get; set; }
        public int ObjID { get; set; }
        public int PolicyID { get; set; }
        public int LocationID { get; set; }
        public int BranchCode { get; set; }
        public int DistrictID { get; set; }
        public int SaleChannelID { get; set; }
        public int SaleTeamID { get; set; }
        public int PaymentTypeID { get; set; }
        public int CrossSellingLocation { get; set; }
        public int ObjectTypeID { get; set; }
        public int CusTypeID { get; set; }
        public int CusTypeL2ID { get; set; }
        public string CustomerRank { get; set; }
        public string UnitCode { get; set; }
        public int UnitParentID { get; set; }
        public string UnitParentCode { get; set; }
        public int LimitType { get; set; }
        public int KeepStatus { get; set; }
    }

    public class GeneralCode_Model_Voucher
    {
        public int PolicyGroupID { get; set; }
        public int PromotionTypeID { get; set; }
        public int RegisterTypeID { get; set; }
        public decimal Value { get; set; }
        public float MonthValue { get; set; }
        public decimal ConnectionFee { get; set; }
    }
}