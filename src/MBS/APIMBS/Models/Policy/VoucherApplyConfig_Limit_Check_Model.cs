using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.Policy
{
    public class VoucherApplyConfig_Limit_Check_Res
    {
        //public int ConfigID { get; set; }
        public int LimitConfigID { get; set; }
        public int ConfigDetailID { get; set; }
        public decimal LimitValue { get; set; }
        public int LimitRate { get; set; }
        public int CheckValue { get; set; }
        public int CheckRate { get; set; }
        public int? Type { get; set; }
        //public string ServiceNames { get; set; }
    }

    public class VoucherApplyConfig_Limit_Check_Req_Service
    {
        public int PolicyID { get; set; }
        public int ServiceID { set; get; }
        public int SubServiceTypeID { set; get; }
        public int SubServiceID { get; set; }
        public int ServiceCode { set; get; }
        public decimal Total_VAT { get; set; }

        public int PrePaid { get; set; }
        public double MonthUsed { get; set; }
    }

    public class VoucherApplyConfig_Limit_Check_Req_Product
    {
        public int PolicyID { get; set; }
        public int ServiceID { set; get; }
        public int SubServiceTypeID { set; get; }
        public int SubServiceID { get; set; }
        public int ServiceCode { set; get; }
        public decimal Total_VAT { get; set; }

        public int StatusID { set; get; }
        public int RevokeID { set; get; }
        public int DeployTypeID { set; get; }
    }

    public class VoucherApplyConfig_Limit_Check_Req_OrderItem
    {
        public int PolicyID { get; set; }
        //public int Policy_Type { get; set; }
        public int ServiceID { get; set; }
        public int SubServiceID { get; set; }
        public int Prepaid { get; set; }
        public int Type { get; set; }
        public decimal Price { get; set; }
    }

    public class VoucherApplyConfig_Limit_Check_Req_Voucher
    {
        public int RegisterTypeID { get; set; }
        public int PolicyID { get; set; }
        public int SubServiceTypeID { get; set; }
        public string VoucherCode { get; set; }
        public int PolicyGroupL2ID { get; set; }
        public int Type { get; set; }
        public decimal Discount { get; set; }
        public decimal FullDiscount { get; set; }

        // for apply multi
        public int Type2 { get; set; }
    }
}