using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.Policy
{
    public class Voucher_GetProgram_Model
    {
        public int SaleTeamID { get; set; }
        public int SaleChannelID { get; set; }
        public int SubCompanyID { get; set; }
        public int ObjID { get; set; }
        public int LocationID { get; set; }
        public int BranchCode { get; set; }
        public int DistrictID { get; set; }
        public int WardID { get; set; }
        public int BuildingID { get; set; }
        public int CusTypeID { get; set; }
        public int CusTypeL2ID { get; set; }
        public int ObjectTypeID { get; set; }
        public string CustomerRank { get; set; }
        public List<Voucher_GetProgram_Model_CustomerType> CustomerTypes { get; set; }
        public int PaymentTypeID { get; set; }
        public int CrossSellingLocation { get; set; }
    }

    public class Voucher_GetProgram_Model_CustomerType
    {
        public int ServiceID { get; set; }
        public int CustomerTypeID { get; set; }
    }

    public class Voucher_GetProgram_Model_Voucher
    {
        public int VoucherID { get; set; }
        public string VoucherCode { get; set; }
        public int ProgramID { get; set; }
        public int VoucherTypeID { get; set; }
    }
}