using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.Policy
{
    public class Voucher_GetSaleQuota_Req
    {
        public int SaleID { get; set; }
    }

    public class Voucher_GetSaleQuota_Res
    {
        [JsonProperty("Type")]
        public int PolicyGroupID { get; set; }
        [JsonIgnore]
        public decimal UnitQuota { get; set; }
        [JsonIgnore]
        public decimal UnitUsed { get; set; }
        [JsonIgnore]
        public decimal UnitAvailable { get; set; }
        [JsonIgnore]
        public decimal SaleQuota { get; set; }
        [JsonIgnore]
        public decimal SaleUsed { get; set; }
        [JsonProperty("Available")]
        public decimal SaleAvailable { get; set; }
        [JsonIgnore]
        public int IsShare { get; set; }
    }
}