using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.Policy
{
    public class Voucher_Model
    {
        public int PolicyID { get; set; }
        public int CampaignID { get; set; }
        public int VoucherID { get; set; }
        public string VoucherCode { get; set; }
        public string VoucherName { get; set; }
        public int PolicyGroupID { get; set; }
        public int VoucherTypeID { get; set; }
        public int PromotionTypeID { get; set; }
        public int RegisterTypeID { get; set; }
        public int ComboType { get; set; }
        public int PolicyExtraTypeID { get; set; }
        public int ApplyTypeID { get; set; }
        public int ApplyTypeAltID { get; set; }
        public int ApplyFor { get; set; }
        public int LimitType { get; set; }
        public DateTime? ToDate { get; set; }
        // for fgold
        public DateTime? ExpiredDate { get; set; }
        // for map
        public int Priority { get; set; }
        public decimal Discount { get; set; }
        public List<Voucher_Model_Service> Services { get; set; }
        public List<Voucher_Model_Product> Products { get; set; }
    }

    public class Voucher_Model_Service
    {
        public int VoucherID { get; set; }

        public int VoucherServiceID { get; set; }
        public int ServiceID { get; set; }
        public int SubServiceTypeID { get; set; }
        public int SubServiceID { get; set; }
        public float PrePaid { get; set; }
        public int FromQuantity { get; set; }
        public int ToQuantity { get; set; }

        public decimal Value { get; set; }
        public float MonthValue { get; set; }
        public decimal ConnectionFee { get; set; }

        // for map
        public int Qty { get; set; }
        // for info
        public int ServiceCode { get; set; }
        public int DeployTypeID { get; set; }
        public float MonthUsed { set; get; }
        public decimal Discount { get; set; }
        public decimal Total_VAT { set; get; }
    }

    public class Voucher_Model_Service_Requirement
    {
        public int VoucherID { get; set; }

        public int VSRequirementID { get; set; }
        public int SubServiceID { get; set; }
        public float PrePaid { get; set; }
        public decimal Value { get; set; }
        public decimal ConnectionFee { get; set; }
    }

    public class Voucher_Model_Product
    {
        public int VoucherID { get; set; }

        public int VoucherDeviceID { get; set; }
        public int ServiceID { get; set; }
        public int SubServiceTypeID { get; set; }
        public int DeviceID { get; set; }
        public int DeviceStatus { get; set; }
        public int DeployTypeID { get; set; }
        public int IsReturn { get; set; }
        public int FromQuantity { get; set; }
        public int ToQuantity { get; set; }
        public decimal FromPrice { get; set; }
        public int MaxQuantity { get; set; }

        public decimal Value { get; set; }

        // for map
        public int Qty { get; set; }
        // for info
        public int ServiceCode { get; set; }
        public decimal Discount { get; set; }
        public decimal Total_VAT { set; get; }
    }
}