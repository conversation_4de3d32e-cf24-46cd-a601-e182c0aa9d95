using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.Policy
{
    public class Order_Model_Model
    {
    }

    public class Order_Model_Voucher
    {
        public string VoucherCode { get; set; }
        public int VoucherType { get; set; }
        public int CampaignID { get; set; }
        public string Prefix { get; set; }
        public string PrivateCode { get; set; }
        public int PolicyID { get; set; }
        public int VoucherID { get; set; }
        // for fgold
        public DateTime? ExpiredDate { get; set; }
        // for redeem
        public int LimitType { get; set; }
        public string MapVoucherCode { get; set; }
    }

    public class Order_Model_Service
    {
        public int PolicyID { set; get; }
        public int NotApplyVoucher { get; set; }
        public int ServiceID { set; get; }
        public int SubServiceTypeID { set; get; }
        public int SubServiceID { set; get; }
        public double PrePaid { set; get; }
        public double MonthUsed { set; get; }
        public int DeployTypeID { set; get; }
        public int ServiceCode { set; get; }
        public int Qty { set; get; }
        public decimal Total { set; get; }
        public decimal Total_VAT { set; get; }
    }

    public class Order_Model_Product
    {
        public int PolicyID { set; get; }
        public int NotApplyVoucher { get; set; }
        public int ServiceID { set; get; }
        public int SubServiceTypeID { set; get; }
        public int SubServiceID { set; get; }
        public int DeployTypeID { set; get; }
        public int StatusID { set; get; }
        public int RevokeID { set; get; }
        public int ServiceCode { set; get; }
        public int Qty { set; get; }
        public decimal Total { set; get; }
        public decimal Total_VAT { set; get; }
    }
}