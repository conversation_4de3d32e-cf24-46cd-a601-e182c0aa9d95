using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace APIMBS.Models.Policy
{
    public class Log_Model
    {
        public DateTime date { get; set; }
        public string msg { get; set; }
        public string obj { get; set; }

        public Log_Model(string m, object o)
        {
            date = DateTime.Now;
            msg = m;
            try
            {
                obj = JsonConvert.SerializeObject(o);
            }
            catch { }
        }
    }
}