using APIMBS.Models;
using APIMBS.Models.MobileSaleV4;
using APIMBS.Service;
using MobisaleV4.Controllers;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Http;
using Voucher.APIHelper;

namespace APIMBS.Controllers
{
    public class MobisaleV4CloneController : ApiController
    {
        public ResponseModel<bool> RedeemVoucher(SalePlatformRedeemVoucher input, Guid? parLogId = null, SqlConnection connection = null, SqlTransaction transaction = null)
        {
            Guid logId = parLogId == null ? Guid.NewGuid() : (Guid)parLogId;

            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher req", input));

            bool status = false;
            int result = 0;
            string error = "";
            var res = new ResponseModel<bool> { data = status, error = error, result = result };

            try
            {
                MobisaleV4Controller mobisaleV4 = new MobisaleV4Controller();
                var resGTBB = mobisaleV4.KeepVoucher(input).data;
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher resGTBB", resGTBB));
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher keep ex", e));
            }

            try
            {
                var privateVoucherCode = input.evoucherList.FirstOrDefault(e => e.evoucherType == 2 && SalePlatformPrivateVoucherService.CheckPrefix(e.evoucherCode))?.evoucherCode;
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("privateVoucherCode", privateVoucherCode));

                var eventCode = SalePlatformPrivateVoucherService.GetEventCode(privateVoucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("eventCode", eventCode));

                if (privateVoucherCode != null)
                {
                    if (!string.IsNullOrEmpty(eventCode))
                    {
                        input.evoucherList = input.evoucherList
                            .Select(e => { if (e.evoucherCode == privateVoucherCode) { e.evoucherType = 1; e.evoucherCode = eventCode; } return e; })
                            .ToList();
                    }
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input.evoucherList", input.evoucherList));

                // lấy thông tin saleman
                SalesManInfo Salesman = MBSv4GetListVoucher.GetInfoSalesMan(input.saleID);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher Salesman", Salesman));

                if (Salesman == null && input.channel.Equals(0))
                {
                    return new ResponseModel<bool> { data = false, error = "Thông tin SaleMan không tồn tại", result = 0 };
                }
                //lọc voucher mã chung và voucher RF
                List<EvoucherInput> lstEVCinput = input.evoucherList.Where(a => a.evoucherType == 1).ToList();
                List<EvoucherInput> lstRFinput = input.evoucherList.Where(a => a.evoucherType == 2).ToList();

                //xác định loại gói thanh toán
                Tuple<int, int> prepaid_net_tv = MBSv4GetListVoucher.GetPrepaidtimeNetTV(input.Services);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher prepaid_net_tv", prepaid_net_tv));

                // xác đinh localtype
                List<ServicePlatform> lsp = input.Services.Where(x => MBSv4GetListVoucher.GetServiceCode(x.ServiceID).ToUpper() == "INT").ToList();

                int localtype = 0;

                if (lsp.Count > 0)
                {
                    localtype = MBSv4GetListVoucher.GetLocaltype(lsp[0].SubServiceTypes[0].SubServices[0].SubServiceID);
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher localtype", localtype));

                // xác định voucher thuộc service và subtype
                List<DeviceModel> devices = MBSv4GetListVoucher.GetListDeviceCam(input.Products);
                List<EvoucherService> lstEVC = MBSv4GetListVoucher.getListEvcService(input.Services, logId, input.channel, lstEVCinput, lstRFinput, localtype, prepaid_net_tv, devices);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher lstEVC", lstEVC));

                string contractSuport = string.Empty;
                if (input.Userbranch != null)
                {
                    contractSuport = input.Userbranch.contractGT;
                }

                List<ServicePlatform> lser = MBSv4GetListVoucher.FillerServiceTVNET(input.Services);
                // kiểm tra đã keep voucher
                bool check = MBSv4GetListVoucher.CheckKeepHistoryTem(connection, transaction, lstEVC, input.objId, input.OrderCode, input.saleID, lser, input.CustomerTypes, input.channel);
                if (!check)
                {
                    result = 0;
                    error = "Có voucher chưa keep.";
                    new ResponseModel<bool> { data = false, error = error, result = result };
                }
                #region Phần service
                if (input.Services.Count > 0)
                {
                    // chỉ lấy các service iptv và net
                    foreach (var service in input.Services)
                    {
                        List<EvoucherService> lstEVCservice = lstEVC.Where(x => x.ServiceID == service.ServiceID).ToList();
                        foreach (var item in service.SubServiceTypes)
                        {
                            List<EvoucherService> lstEVCserviceType = lstEVCservice.Where(x => x.ServiceID == service.ServiceID
                                                                                            && x.SubServiceType == item.SubServiceTypeID).ToList();
                            foreach (var evc in lstEVCserviceType)
                            {
                                if (evc.evoucherType.Equals(1)) // voucher chung
                                {
                                    #region voucher chung
                                    bool isAdd = MBSv4GetListVoucher.CheckAddVoucherGCCode(connection, transaction, evc.evoucherCode, input.objId, input.RegCode);
                                    if (!isAdd)
                                    {
                                        status = MBSv4RedeemVoucherGC.RedeemVoucherGC(connection, transaction, evc.evoucherCode,
                                        input.objId, input.OrderCode, prepaid_net_tv, input.saleID, Salesman, localtype, input.RegCode, input.channel, input.locationID, logId, sb, input.Userbranch);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherGC: " + evc.evoucherCode, status));

                                        if (!status)
                                            break;
                                        else
                                        {
                                            if (evc.evoucherCode == eventCode)
                                            {
                                                evc.evoucherCode = privateVoucherCode;
                                                MBSv4RedeemVoucherGC.UpdateStatusPrivateCode(connection, transaction, privateVoucherCode, input.objId);
                                            }
                                            bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherGC updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                        }
                                    }
                                    else
                                    {
                                        if (evc.evoucherCode == eventCode)
                                        {
                                            evc.evoucherCode = privateVoucherCode;
                                        }
                                        bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherGC updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                    }
                                    #endregion
                                }
                                if (evc.evoucherType.Equals(2)) // voucher lẻ GTBB
                                {
                                    bool isRedeem = MBSv4GetListVoucher.CheckRedeemRFCode(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode);
                                    if (!isRedeem)
                                    {
                                        status = MBSv4RedeemVoucherAdvocacyProgram.RedeemVoucherCampaign(connection, transaction, evc.evoucherCode, input.objId,
                                                input.OrderCode, prepaid_net_tv, input.saleID, Salesman, input.channel, localtype, input.RegCode, contractSuport, null, logId, sb);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF SaveHistoryTem: " + evc.evoucherCode, status));

                                        if (!status)
                                            break;
                                        else
                                        {
                                            bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                        }
                                    }
                                    else
                                    {
                                        bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                    }
                                }
                            }
                            if (!status)
                                break;
                        }
                        if (!status)
                            break;
                    }
                }

                #endregion

                #region phần thiết bị lẻ
                if (input.Products.Count > 0)
                {

                    foreach (var product in input.Products)
                    {
                        List<EvoucherService> lstEVCservice = lstEVC.Where(x => x.ServiceID == product.ServiceID).ToList();
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct lstEVCservice", lstEVCservice));

                        if (lstEVCservice.Count == 0) continue;

                        foreach (var item in product.SubServiceTypes)
                        {
                            List<EvoucherService> lstEVCserviceType = lstEVCservice.Where(x => x.ServiceID == product.ServiceID
                                                                                            && x.SubServiceType == item.SubServiceTypeId).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct lstEVCserviceType", lstEVCserviceType));

                            if (lstEVCserviceType.Count == 0) continue;

                            foreach (var evc in lstEVCserviceType)
                            {
                                if (evc.evoucherType.Equals(1)) // voucher chung
                                {
                                    #region voucher chung
                                    bool hasAdded = MBSv4GetListVoucher.CheckAddVoucherGCCode(connection, transaction, evc.evoucherCode, input.objId, input.RegCode);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct hasAdded", hasAdded));

                                    if (!hasAdded)
                                    {
                                        var customerType = input.CustomerTypes.First(x => x.ServiceID == evc.ServiceID);
                                        status = MBSv4RedeemVoucherGC.RedeemVoucherProduct(connection, transaction, evc.evoucherCode,
                                            input.objId, input.saleID, Salesman, localtype, input.RegCode, input.channel, input.locationID,
                                            logId, input.Products, input.Userbranch, customerType.CustomerType, sb);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct status", status));

                                        if (status)
                                        {
                                            bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct updateHistoryTem", updateHistoryTem));
                                        }
                                    }
                                    else
                                    {
                                        bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct updateHistoryTem", updateHistoryTem));
                                    }
                                    #endregion
                                }
                                if (evc.evoucherType.Equals(2)) // voucher lẻ GTBB
                                {
                                    bool isRedeem = MBSv4GetListVoucher.CheckRedeemRFCode(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode);
                                    if (!isRedeem)
                                    {
                                        status = MBSv4RedeemVoucherAdvocacyProgram.RedeemVoucherCampaign(connection, transaction, evc.evoucherCode, input.objId,
                                                input.OrderCode, prepaid_net_tv, input.saleID, Salesman, input.channel, localtype, input.RegCode, contractSuport, devices, logId, sb);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF SaveHistoryTem: " + evc.evoucherCode, status));
                                        if (!status)
                                            break;
                                        else
                                        {
                                            bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                        }
                                    }
                                    else
                                    {
                                        bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion
                if (status)
                {
                    res.data = status;
                    res.result = 1;
                }
                res.data = status;
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher Error", ex.Message));
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "MBS_RedeemVoucher_String: ", logId);
            }
            return res;
        }
    }
}