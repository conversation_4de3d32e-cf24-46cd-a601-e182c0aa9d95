using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using APIMBS.Models.MobileSaleV4;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using APIMBS.Service;
using APIMBS.Models;
using System.Xml.Linq;
using System.Text;
using APIMBS.Models.SalePlatform;
using MobisaleV4.Controllers;
using APIMBS.Controllers;
using APIMBS.Service.EvcPolicyServices;
using APIMBS.Service.Policy;
using APIMBS.Models.Policy;
using Voucher.APIHelper.Util;
using Voucher.APIHelper.ShareModel;

namespace MobisaleV4.Controllers
{
    [Route("API/FPTSale/SalePlatForm/{action}")]
    public class FPTSaleController : ApiController
    {
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModels<Evoucher> GetListEvoucher(SalePolicyGetInforEVC input)///
        {
            var logId = Guid.NewGuid();
            var lstEvc = new ListVoucher();
            var data = new List<Evoucher>();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("------------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input", input));

            var logs = new List<Log_Model>();

            try
            {
                int config_get_list = 0;
                /*
                if (input.debug > 0)
                {
                    config_get_list = input.debug == 1 ? 1 : 0;
                }
                else
                {
                    config_get_list = PolicyService.BaseConfig_Get(logs, null, 38)?.Count > 0 ? 1 : 0;
                }
                */

                if (config_get_list == 0)
                {
                    var res_get_list = PolicyService.Voucher_GetList(logs, input, 1);

                    if (res_get_list.LstVoucher?.Count > 0)
                    {
                        foreach (var item in res_get_list.LstVoucher)
                        {
                            data.Add(new Evoucher
                            {
                                VoucherCode = item.VoucherCode,
                                Description = item.Description,
                                Note = item.Note,
                                Todate = item.ToDate,
                                PolicyGroupID = item.PolicyGroupID,
                                PromotionTypeID = item.PromotionTypeID,
                                RegisterTypeID = item.RegisterTypeID,
                                ApplyTypeID = item.ApplyTypeAltID
                            });
                        }
                        data = data.GroupBy(c => c.VoucherCode).Select(c => c.First()).ToList();
                    }

                    // log to sb
                    foreach (var l in logs)
                    {
                        sb.AppendLine(string.Format("{0} : {1} : {2}", l.date, l.msg, l.obj));
                    }
                }
                else
                {
                    /*
                    lstEvc = GetListServices2.GetListEvoucher(input, sb);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstEvc", lstEvc));


                    if (lstEvc.LstVoucher == null)
                    {
                        return new ResponseModels<Evoucher>
                        {
                            data = new List<Evoucher>(),
                            error = "",
                            result = 0
                        };
                    }

                    foreach (var item in lstEvc.LstVoucher.Where(x => x.VoucherTypeID == 1))
                    {
                        data.Add(new Evoucher
                        {
                            Description = item.Description,
                            Note = item.Note,
                            Todate = item.ToDate,
                            VoucherCode = item.VoucherCode,
                            RegisterTypeID = item.RegisterTypeID,
                            PolicyGroupID = item.PolicyGroupID,
                            PromotionTypeID = item.PromotionTypeID,
                            ApplyTypeID = item.ApplyTypeAltID
                        });
                    }
                    */
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FPTSale GetListEvoucher Exception", ex.ToString()));
                return new ResponseModels<Evoucher> { data = null, error = ex.Message, result = -1 };
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "FPTSale GetListEvoucher", logId);
            }
            return new ResponseModels<Evoucher>
            {
                data = data.Distinct().ToList(),
                error = "",
                result = 1
            };
        }
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModels<SalePlatformVoucherValueInfor> GetEvoucherInfor(SalePolicyRedeemEVC input)
        {
            var deepCopy = JsonConvert.SerializeObject(input);
            var input_backup = JsonConvert.DeserializeObject<SalePolicyRedeemEVC>(deepCopy);

            var logId = Guid.NewGuid();
            var dataTotal = new List<SalePlatformVoucherValueInfor>();
            var voucherError = "";

            ResponseModels<SalePlatformVoucherValueInfor> response = new ResponseModels<SalePlatformVoucherValueInfor> { data = dataTotal, result = 1, error = "" };
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input", input));

            var logs = new List<Log_Model>();

            try
            {
                int config_get_list = 0;
                /*
                if (input.debug > 0)
                {
                    config_get_list = input.debug == 1 ? 1 : 0;
                }
                else
                {
                    config_get_list = PolicyService.BaseConfig_Get(logs, null, 38)?.Count > 0 ? 1 : 0;
                }
                */

                if (input.evoucherList.Count > 0)
                {
                    var multiSelect = GetListServices2.GetValueByKeyConfig("MultiSelect");

                    if (input.evoucherList.Count > multiSelect)
                    {
                        return new ResponseModels<SalePlatformVoucherValueInfor>
                        {
                            data = null,
                            error = "Bạn chỉ được chọn tối đa " + multiSelect.ToString() + " phiếu mua hàng. Vui lòng kiểm tra lại.",
                            result = 0
                        };
                    }
                }

                #region voucher nhập (mã lẻ, GTBB)
                if (input_backup.evoucherList.Where(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "").ToList() != null
                    && input_backup.evoucherList.Where(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "").ToList().Count > 0)
                {
                    ResponseModels<SalePlatformVoucherValueInfor> res = new ResponseModels<SalePlatformVoucherValueInfor> { data = new List<SalePlatformVoucherValueInfor>(), result = 0, error = "Không tìm thấy" };
                    MobisaleV4Controller mobisaleV4 = new MobisaleV4Controller();
                    SalePlatformGetVoucherInfo salePlatform = new SalePlatformGetVoucherInfo();
                    salePlatform.evoucherList = input_backup.evoucherList.Select(x => new EvoucherInput() { evoucherCode = x.evoucherCode, evoucherType = x.evoucherType, RefId = x.RefId })
                                                                         .Where(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "").ToList();

                    salePlatform.Services = input_backup.Services;
                    salePlatform.Products = input_backup.Products;

                    // dính bộ nhớ
                    var CustomerTypes = new List<CustomerTypeModel>();
                    foreach (var item in input_backup.CustomerTypes)
                    {
                        CustomerTypes.Add(new CustomerTypeModel() { ServiceID = item.ServiceID, CustomerType = item.CustomerType == 1 ? 2 : 1 });
                    }

                    salePlatform.CustomerTypes = CustomerTypes;
                    salePlatform.saleID = input_backup.SaleInfor.SaleID;
                    salePlatform.saleTeamId = input_backup.SaleInfor.SaleTeamID;
                    salePlatform.locationID = input_backup.CustomerInfor.LocationID;
                    salePlatform.channel = input_backup.SaleInfor.SaleID > 0 ? 0 : 1;
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GTBB req", salePlatform));

                    try
                    {
                        res = mobisaleV4.GetEvoucherInforV2(salePlatform, logId);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GTBB res", res));
                        if (res.data == null)
                        {
                            if (input_backup.evoucherList.Where(x => x.evoucherType == 2 && x.RefId == 2).ToList()?.Count > 0)
                            {
                                List<CustomerSaleOrderCollections> lstDataOrders = new List<CustomerSaleOrderCollections>();

                                // Get list of services from input
                                var listOptionService_servicesInput = GetListServices2.formatServicesGTBB(input, "Service");
                                if (listOptionService_servicesInput.Count > 0)
                                {
                                    lstDataOrders.AddRange(listOptionService_servicesInput);
                                }

                                // Get list of products from input
                                var listOptionService_productsInput = GetListServices2.formatServicesGTBB(input, "Product");
                                if (listOptionService_productsInput.Count > 0)
                                {
                                    lstDataOrders.AddRange(listOptionService_productsInput);
                                }

                                var service = lstDataOrders.FirstOrDefault();

                                var applyList = new List<Apply>();
                                applyList.Add(new Apply()
                                {
                                    ServiceID = service.serviceId,
                                    SubServiceTypeID = service.subServiceTypeId,
                                    SubServiceID = service.subServiceId,
                                    ServiceCode = service.serviceCode,

                                    DeployTypeID = service.deployTypeId,
                                    StatusID = service.statusId,
                                    RevokeID = service.revokeID,

                                    Value = "Referral",
                                    Discount = 0,
                                    DiscountVAT = 0,
                                    Dismonth = 0,
                                    IsDeductOrder = 1
                                });

                                var ui = new UserInfo();
                                var voucherinfo = new RefInfo();
                                ui = ReferalProgramHiFPT.GetInforUser(input_backup.evoucherList.Where(x => x.evoucherType == 2 && x.RefId == 2).FirstOrDefault().evoucherCode);
                                if (ui != null)
                                {
                                    voucherinfo.fullName = ui.FullNameRef;
                                    voucherinfo.phone = ui.PhoneRef;
                                    voucherinfo.contract = ui.ContractRef;
                                }

                                var refi = new SalePlatformVoucherValueInfor()
                                {
                                    evoucherCode = input_backup.evoucherList.Where(x => x.evoucherType == 2 && x.RefId == 2).FirstOrDefault().evoucherCode,
                                    VoucherType = 2,
                                    RegisterTypeID = 2,
                                    VoucherTypeL2 = 2,
                                    PolicyGroupID = 101,
                                    ApplyTypeID = 2,
                                    PromotionTypeID = 1,
                                    Apply = applyList,
                                    RefInfo = voucherinfo
                                };

                                response.data.Add(refi);
                            }
                            else
                            {
                                return new ResponseModels<SalePlatformVoucherValueInfor>
                                {
                                    data = null,
                                    error = res.error,
                                    result = 0
                                };
                            }
                        }
                        else if (res != null && res.data.Count > 0)
                        {
                            for (int i = 0; i < res.data.Count; i++)
                            {
                                res.data[i].VoucherType = 2;
                                res.data[i].RegisterTypeID = 2;
                                res.data[i].VoucherTypeL2 = 2;
                                res.data[i].PolicyGroupID = 101;
                                res.data[i].ApplyTypeID = 2;
                                res.data[i].PromotionTypeID = 1;
                                foreach (var apply in res.data[i].Apply)
                                {
                                    apply.IsDeductOrder = 1;
                                }
                            }

                            FSaleGetinforVoucherServices.SplitReferralApplyCamera(res.data, input_backup.Products);

                            response.data.AddRange(res.data);
                        }
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GTBB error", ex.Message));
                    }
                }
                #endregion

                #region Voucher chọn
                if (config_get_list == 0)
                {
                    var res_get_info = PolicyService.Voucher_GetInfo(logs, input);

                    if (res_get_info.LstVoucher?.Count > 0)
                    {
                        response.data.AddRange(res_get_info.LstVoucher);
                    }

                    // log to sb
                    foreach (var l in logs)
                    {
                        sb.AppendLine(string.Format("{0} : {1} : {2}", l.date, l.msg, l.obj));
                    }
                    // clear log
                    logs = new List<Log_Model>();

                    if (res_get_info.LstInput?.Count > 0)
                    {
                        var lst_invalid = res_get_info.LstInput
                            .Where(c => res_get_info.LstVoucher == null || !res_get_info.LstVoucher.Any(x => x.evoucherCode.Equals(c.VoucherCode, StringComparison.OrdinalIgnoreCase)))
                            .Select(c => c.VoucherCode).ToList();
                        if (lst_invalid?.Count > 0)
                        {
                            voucherError = string.Join(",", lst_invalid);
                        }
                    }
                }
                else
                {
                    /*
                    if (input.evoucherList.Where(x => x.evoucherType == 1 || VendorManager.CheckFormatVoucher(x.evoucherCode) != "").ToList()?.Any() == true)
                    {
                        var lstEvc = GetListServices2.GetListEvoucher(input, sb);
                        if (lstEvc.LstVoucher?.Count > 0)
                        {
                            // lay ds cs ma le
                            var vendor = lstEvc.LstVoucher.Where(x => x.VoucherTypeID == 2).ToList();
                            if (vendor != null && input.evoucherList.Where(x => VendorManager.CheckFormatVoucher(x.evoucherCode) != "")?.Count() > 0)
                            {
                                var xmlC = VendorManager.ToXML_CampaignID(vendor);
                                var xmlP = VendorManager.ToXML_PrivateCode(input.evoucherList.Where(x => VendorManager.CheckFormatVoucher(x.evoucherCode) != "").ToList());
                                var AddVoucherID = VendorManager.AddVoucherID(xmlC, xmlP);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlC", xmlC));
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlP", xmlP));
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AddVoucherID", AddVoucherID));
                            }

                        }

                        dataTotal = FSaleGetinforVoucherServices.GetVoucherinfo(input, sb);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataTotal", dataTotal));
                        response.data.AddRange(dataTotal.Where(x => x.Apply.Count() > 0).ToList());
                        voucherError = string.Join(", ", dataTotal.Where(x => x.Apply.Count == 0).Select(x => x.evoucherCode));
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucherError1", voucherError));

                        if (string.IsNullOrEmpty(voucherError))
                        {
                            voucherError = string.Join(", ", input.evoucherList.Select(x => x.evoucherCode).Where(x => !response.data.Select(s => s.evoucherCode).ToArray().Contains(x)).ToList());
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucherError2", voucherError));
                        }
                    }
                    */
                }
                #endregion

                #region voucher request
                if (input.OrderCode != null && input.OrderCode.Length > 0)
                {
                    var lst_voucher = PolicyService.Policy_GetVoucherRequestOrder(logs, input.OrderCode);
                    if (lst_voucher?.Count > 0)
                    {
                        response.data.AddRange(lst_voucher);
                    }
                }
                #endregion

                #region fgold check
                if (response.data.Any(c => c.PolicyGroupID == 103 || c.PolicyGroupID == 109))
                {
                    var lst_voucher_fgold = response.data.Where(c => c.PolicyGroupID == 103 || c.PolicyGroupID == 109).ToList();
                    var subServiceTypeIDs = lst_voucher_fgold.SelectMany(c => c.Apply.Select(x => x.SubServiceTypeID)).Distinct().ToList();
                    var expiredDates = lst_voucher_fgold.Where(c => c.ExpiredDate != null).Select(c => c.ExpiredDate).ToList();
                    var strListPrivateCode = string.Join(",", lst_voucher_fgold.Select(x => VendorManager.CheckFormatVoucher(x.evoucherCode)));
                    string err = PolicyService.FGold_Validate(logs, input.objId, subServiceTypeIDs, expiredDates, strListPrivateCode, input.SaleInfor?.SaleID ?? 0);
                    logs.Add(new Log_Model("FGold_Validate", err));

                    // log to sb
                    foreach (var l in logs)
                    {
                        sb.AppendLine(string.Format("{0} : {1} : {2}", l.date, l.msg, l.obj));
                    }
                    // clear log
                    logs = new List<Log_Model>();

                    if (!string.IsNullOrEmpty(err))
                    {
                        return new ResponseModels<SalePlatformVoucherValueInfor>
                        {
                            data = null,
                            error = err,
                            result = 0
                        };
                    }
                }
                #endregion

                #region voucher chong
                string error_limit = "";
                // chi check khi khong loi
                if (response.data.Count > 0)
                {
                    error_limit = PolicyService.Voucher_ApplyConfig_Check(logs, input.Services, input.Products, response.data);

                    // log to sb
                    foreach (var l in logs)
                    {
                        sb.AppendLine(string.Format("{0} : {1} : {2}", l.date, l.msg, l.obj));
                    }
                    // clear log
                    logs = new List<Log_Model>();
                }
                #endregion
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("error_limit", error_limit));

                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucherError", voucherError));

                if (!string.IsNullOrEmpty(error_limit))
                {
                    return new ResponseModels<SalePlatformVoucherValueInfor>
                    {
                        data = null,
                        error = error_limit,
                        result = 0
                    };
                }

                var logitem = FSaleGetinforVoucherServices.get_promotion_loy_map_prepaid(input);//
                var gold_config = new event_prepaid_config();
                if (logitem != null)
                {
                    gold_config = logitem.config;
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("gold_config", logitem));

                if (response.data?.Count > 0)
                {
                    response.data = (from data in response.data
                                     join evc in input.evoucherList on data.evoucherCode equals evc.evoucherCode
                                     select new SalePlatformVoucherValueInfor()
                                     {
                                         evoucherCode = data.evoucherCode,
                                         VoucherType = data.VoucherType == 4 ? 4 : evc.evoucherType,
                                         VoucherTypeL2 = data.VoucherTypeL2,
                                         RegisterTypeID = data.RegisterTypeID,
                                         PolicyGroupID = data.PolicyGroupID,
                                         ApplyTypeID = data.ApplyTypeID,
                                         PromotionTypeID = data.PromotionTypeID,
                                         TypeID = data.TypeID,
                                         Quota = data.Quota,
                                         VendorContract = data.VendorContract,
                                         Gold = data.PolicyGroupID == 110 ? (gold_config?.gold ?? 0) : 0,
                                         Apply = data.Apply,
                                         RefInfo = data.RefInfo
                                     }).ToList();

                    // có vc foxgold , không thỏa cs foxgold
                    var foxgold_err = response.data.Where(d => d.PolicyGroupID == 110 && d.Gold == 0).ToList();
                    if (foxgold_err != null && foxgold_err.Count > 0)
                    {
                        return new ResponseModels<SalePlatformVoucherValueInfor>
                        {
                            data = null,
                            error = "Số tháng trả trước + số tháng sử dụng không khớp với chính sách tặng FGold của PMH " + string.Join(",", foxgold_err.Select(x => x.evoucherCode)),
                            result = 0
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FPTSale GetEvoucherInfor Exception", ex.ToString()));
                return new ResponseModels<SalePlatformVoucherValueInfor>
                {
                    data = null,
                    error = ex.Message,
                    result = -1
                };
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "FPTSale GetEvoucherInfor", logId);
            }

            return response;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModel<bool> CheckVoucher(EvoucherInput input)
        {
            var logId = Guid.NewGuid();
            MBSv4CommonService.WriteToLog(input, "FPTSale CheckVoucher res: ", logId);
            bool trueVoucher = false;
            int result = 0;
            try
            {
                if (VendorManager.CheckFormatVoucher(input.evoucherCode) != "")
                {
                    return new ResponseModel<bool>
                    {
                        data = true,
                        error = "",
                        result = 1
                    };
                }

                if (input.channel.Equals(1))
                {
                    if (input.evoucherType.Equals(1))
                    {
                        trueVoucher = RegisterWebV5.CheckVoucherGC(input.evoucherCode);
                        result = 1;
                    }
                    if (input.evoucherType.Equals(2))
                    {
                        trueVoucher = RegisterWebV5.CheckVoucherRF(input.evoucherCode);
                        result = 1;
                    }
                    return new ResponseModel<bool>
                    {
                        data = trueVoucher,
                        error = "",
                        result = result
                    };
                }
                if (input.evoucherType.Equals(1))
                {
                    trueVoucher = MBSv4GetListVoucher.CheckVoucherGC(input.evoucherCode);
                    result = 1;
                }
                if (input.evoucherType.Equals(2))
                {
                    trueVoucher = MBSv4GetListVoucher.CheckVoucherRF(input.evoucherCode, input.channel);
                    result = 1;
                }
            }
            catch (Exception ex)
            {
                MBSv4CommonService.WriteToLog(ex.Message, "FPTSale CheckVoucher Error: ", logId);
                return new ResponseModel<bool>
                {
                    data = false,
                    error = ex.Message,
                    result = -1
                };

            }
            return new ResponseModel<bool>
            {
                data = trueVoucher,
                error = "",
                result = result
            };
        }
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModel<bool> KeepVoucher(SalePolicyRedeemEVC input)
        {
            var logId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FPTSale KeepVoucher res", input));

            bool res = false;
            int result = 0;
            try
            {
                if (input.evoucherList.Where(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "").ToList().Count > 0)
                {
                    MobisaleV4Controller mobisaleV4 = new MobisaleV4Controller();
                    SalePlatformRedeemVoucher salePlatformRedeem = new SalePlatformRedeemVoucher();

                    // dính bộ nhớ
                    var CustomerTypes = new List<CustomerTypeModel>();
                    foreach (var item in input.CustomerTypes)
                    {
                        CustomerTypes.Add(new CustomerTypeModel() { ServiceID = item.ServiceID, CustomerType = item.CustomerType == 1 ? 2 : 1 });
                    }

                    salePlatformRedeem.CustomerTypes = CustomerTypes;
                    salePlatformRedeem.evoucherList = input.evoucherList.Select(x => new EvoucherInput() { evoucherCode = x.evoucherCode, evoucherType = x.evoucherType })
                                                                        .Where(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "").ToList();
                    salePlatformRedeem.Services = input.Services;
                    salePlatformRedeem.Products = input.Products;
                    salePlatformRedeem.OrderCode = input.OrderCode;
                    salePlatformRedeem.RegCode = input.RegCode;
                    salePlatformRedeem.objId = input.objId;
                    salePlatformRedeem.fptplayCustomerPhone = input.CustomerPhone.FptplayCustomerPhone;
                    salePlatformRedeem.paymentTypeL2 = input.PaymentTypeID;
                    salePlatformRedeem.locationID = input.CustomerInfor.LocationID;
                    salePlatformRedeem.saleID = input.SaleInfor.SaleID;
                    salePlatformRedeem.channel = input.SaleInfor.SaleID > 0 ? 0 : 1;

                    res = mobisaleV4.KeepVoucher(salePlatformRedeem).data;
                }

                List<EvoucherInput> lstEVCinput = input.evoucherList.Select(x => new EvoucherInput() { evoucherCode = x.evoucherCode, evoucherType = x.evoucherType }).Where(x => x.evoucherType == 1).ToList();
                List<EvoucherService> evoucherServices = FPTSaleListVoucherServices.GetListEvcService(logId, lstEVCinput);

                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher evoucherServices", evoucherServices));

                        //kiểm tra + lưu, lấy lần lưu mới nhất, các lân trước update status =4
                        res = FPTSaleKeepVoucherService.SaveHistoryTemp(connection, transaction, evoucherServices, input, sb);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher SaveHistoryTem", res));

                        if (res)
                        {
                            var historyTempIds = new List<int>();
                            foreach (var evc in input.evoucherList)
                            {
                                historyTempIds.AddRange(FPTSaleListVoucherServices.GetHistoryTempIds(connection, transaction, input.OrderCode, input.objId, evc.evoucherCode));
                            }
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher historyTempIds", historyTempIds));

                            bool addExtendHistoryTemp = FPTSaleListVoucherServices.AddExtendHistoryTemp(connection, transaction, historyTempIds, input);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher addExtendHistoryTemp", addExtendHistoryTemp));

                            if (addExtendHistoryTemp)
                            {
                                transaction.Commit();
                                result = 1;
                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("KeepVoucher error", ex.Message));
                return new ResponseModel<bool>
                {
                    data = false,
                    error = ex.Message,
                    result = -1
                };
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "keepVoucher_string: ", logId);
            }

            return new ResponseModel<bool>
            {
                data = true,
                error = "",
                result = 1
            };
        }
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModel<bool> RedeemVoucher(SalePolicyRedeemEVC input)
        {
            var logId = Guid.NewGuid();

            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FPTSale RedeemVoucher req", input));

            bool statusReferral = false, statusGeneral = false;
            string error = string.Empty;
            int result = 0;
            var res = new ResponseModel<bool> { data = false, error = "", result = result };

            var logs = new List<Log_Model>();

            try
            {
                var checkConfig = RedeemVoucherServices.GetBaseConfig(35, 1);
                if (checkConfig != null && checkConfig.Count() > 0)
                {
                    return new ResponseModel<bool>() { data = false, error = "Api đang tạm ngưng!", result = 0 };
                }

                // lấy thông tin saleman
                SalesManInfo Salesman = MBSv4GetListVoucher.GetInfoSalesMan(input.SaleInfor.SaleID);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher Salesman", Salesman));

                //if (Salesman == null && input.SaleInfor.SaleChannelID.Equals(0))
                //{
                //    return new ResponseModel<bool> { data = false, error = "Thông tin SaleMan không tồn tại", result = 0 };
                //}

                //lọc voucher mã chung
                //input.evoucherList = input.evoucherList.Where(x => x.evoucherType == 1).ToList();

                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        if (input.evoucherList.Any(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == ""))
                        {
                            MobisaleV4CloneController mobisaleV4 = new MobisaleV4CloneController();
                            SalePlatformRedeemVoucher salePlatformRedeem = new SalePlatformRedeemVoucher();

                            // dính bộ nhớ
                            var CustomerTypes = new List<CustomerTypeModel>();
                            foreach (var item in input.CustomerTypes)
                            {
                                CustomerTypes.Add(new CustomerTypeModel() { ServiceID = item.ServiceID, CustomerType = item.CustomerType == 1 ? 2 : 1 });
                            }

                            salePlatformRedeem.CustomerTypes = CustomerTypes;
                            salePlatformRedeem.evoucherList = input.evoucherList.Select(x => new EvoucherInput() { evoucherCode = x.evoucherCode, evoucherType = x.evoucherType })
                                                                                .Where(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "").ToList();
                            salePlatformRedeem.Services = input.Services;
                            salePlatformRedeem.Products = input.Products;
                            salePlatformRedeem.OrderCode = input.OrderCode;
                            salePlatformRedeem.RegCode = input.RegCode;
                            salePlatformRedeem.objId = input.objId;
                            salePlatformRedeem.fptplayCustomerPhone = input.CustomerPhone.FptplayCustomerPhone;
                            salePlatformRedeem.paymentTypeL2 = input.PaymentTypeID;
                            salePlatformRedeem.locationID = input.CustomerInfor.LocationID;
                            salePlatformRedeem.saleID = input.SaleInfor.SaleID;
                            salePlatformRedeem.channel = input.SaleInfor.SaleID > 0 ? 0 : 1;
                            if (!string.IsNullOrEmpty(input.CustomerInfor.ContractGT))
                            {
                                salePlatformRedeem.Userbranch = new UserBranchPlatformModel();
                                salePlatformRedeem.Userbranch.contractGT = input.CustomerInfor.ContractGT;
                            }

                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GTBB req", salePlatformRedeem));

                            statusReferral = mobisaleV4.RedeemVoucher(salePlatformRedeem, logId, connection, transaction).data;
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GTBB res", statusReferral));
                        }

                        if (input.evoucherList.Any(x => x.evoucherType == 1 || VendorManager.CheckFormatVoucher(x.evoucherCode) != ""))
                        {
                            /*
                            var checkConfigRedeem = RedeemVoucherServices.GetBaseConfig(connection, transaction, 34, 1);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("checkConfig redeem", checkConfigRedeem));

                            if (checkConfigRedeem != null && checkConfigRedeem.Count() > 0)
                            {
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FPTSaleRedeemVoucherService.RedeemVoucher", ""));
                                statusGeneral = FPTSaleRedeemVoucherService.RedeemVoucher(connection, transaction, input, sb);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("statusGeneral", statusGeneral));
                            }
                            else
                            {
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("RedeemVoucherServices.RedeemVoucher", ""));
                                statusGeneral = RedeemVoucherServices.RedeemVoucher(connection, transaction, input, sb);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("statusGeneral", statusGeneral));
                            }
                            */

                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("PolicyService.Voucher_Redeem", ""));

                            statusGeneral = PolicyService.Voucher_Redeem(logs, connection, transaction, input);

                            // log to sb
                            foreach (var l in logs)
                            {
                                sb.AppendLine(string.Format("{0} : {1} : {2}", l.date, l.msg, l.obj));
                            }

                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("statusGeneral", statusGeneral));
                        }

                        if (input.evoucherList.Any(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "") == statusReferral
                            && input.evoucherList.Any(x => x.evoucherType == 1 || VendorManager.CheckFormatVoucher(x.evoucherCode) != "") == statusGeneral)
                        {
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("transaction commit", true));
                            transaction.Commit();
                            res.data = true;
                            res.result = 1;
                        }
                        else
                        {
                            res.data = false;
                            res.result = 0;
                        }
                    }
                }

                // không ảnh hưởng đến flow add voucher
                var response_submit = RedeemVoucherServices.submit_actionCode_EVC(input, sb);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("response_submit", response_submit));
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FPTSale RedeemVoucher Error", ex));
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "MBS_RedeemVoucher_String: ", logId);
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModel<bool> RemoveQuotakeepForSaleman(RemoveQuotakeepForSalemanInput input)
        {
            var logId = Guid.NewGuid();
            MBSv4CommonService.WriteToLog(input, "FPTSale RemoveQuotakeepForSaleman res: ", logId);
            try
            {
                int isExcu = 0;
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        isExcu = connection.Execute(MBSv4GetListVoucher.OS6_FPTVoucher_RemoveQuotakeepForSaleman, new
                        {
                            action = "RemoveQuotakeepForSaleman",
                            saleID = input.SaleID,
                            orderCode = input.OrderCode
                        }, transaction: transaction, commandType: CommandType.StoredProcedure);
                        transaction.Commit();
                    }
                }
                if (isExcu > 0)
                {
                    return new ResponseModel<bool>
                    {
                        data = true,
                        error = "",
                        result = 1
                    };
                }
                else
                {
                    return new ResponseModel<bool>
                    {
                        data = false,
                        error = "Không thể gỡ đơn hàng đã redeem!",
                        result = 0
                    };
                }
            }
            catch (Exception ex)
            {
                MBSv4CommonService.WriteToLog(ex.Message, "FPTSale RemoveQuotakeepForSaleman Error: ", logId);
                return new ResponseModel<bool>
                {
                    data = false,
                    error = ex.Message,
                    result = -1
                };

            }
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModel<bool> ClearVoucher(List<ClearVoucherInput> input)
        {
            int flag = 0;
            var logId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ClearVoucher", input));
            try
            {
                var cancelEvcRQ = FPTClearVoucherServices.CancelEVCRequest(input, sb);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("cancelEvcRQ", cancelEvcRQ));

                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        foreach (var modelClear in input)
                        {
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog(modelClear.voucherGc, ""));
                            // kiem tra trang thai cua voucher 
                            int statusClearVC = FPTClearVoucherServices.GetStatusrVoucherClear(connection, transaction, modelClear.voucherGc, modelClear.objID, modelClear.regCode);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("statusClearVC", statusClearVC));
                            if (statusClearVC == 0)
                            {
                                return new ResponseModel<bool> { data = true, error = modelClear.voucherGc + " voucher đã được clear", result = 1 };
                            }
                            int clearVouchcer = 0;

                            DataEventID dataEvent = connection.QueryFirstOrDefault<DataEventID>("PowerInside.dbo.OS6_FPTVoucher_FSale", new
                            {
                                actionName = "GetInforClearVoucherV2",
                                voucherCode = modelClear.voucherGc,
                                objID = modelClear.objID,
                                regCode = modelClear.regCode,
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetInfor", dataEvent));

                            if (dataEvent == null)
                            {
                                return new ResponseModel<bool> { data = false, error = "Voucher đã được clear hết!", result = 0 };
                            }
                            else
                            {
                                var PromotionTypeID = FSaleGetinforVoucherServices.GetPromotionType(modelClear.voucherGc);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("PromotionTypeID", PromotionTypeID));

                                clearVouchcer = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_FSale", new
                                {
                                    actionName = "ClearVoucherV2",
                                    generalCodeID = dataEvent.generalCodeID,
                                    voucherBillID = dataEvent.voucherBillID,
                                    objID = modelClear.objID,
                                    regCode = modelClear.regCode,
                                    voucherCode = modelClear.voucherGc,
                                    user = modelClear.user,
                                    PromotionIDNet = PromotionTypeID == 2 ? 1 : 0,
                                    MoneyPromotionIDNet = PromotionTypeID == 1 ? 1 : 0
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Clear", clearVouchcer));

                                if (clearVouchcer == 0)
                                {
                                    return new ResponseModel<bool> { data = false, error = "Clear Voucher không thành công", result = 0 };
                                }

                                //hoàn hạn mức
                                /*
                                var dataBackMoney = FPTClearVoucherServices.DataBackMoney(dataEvent.generalCodeID);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataBackMoney", dataBackMoney));

                                if (dataBackMoney != null)
                                {
                                    var updateApplyStatus = FPTClearVoucherServices.UpdateApplyStatus(connection, transaction, dataEvent.generalCodeID);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("updateApplyStatus", updateApplyStatus));

                                    FPTSaleRedeemVoucherService.CalcQuota(connection, transaction, modelClear.voucherGc, dataBackMoney.TotalDiscount * (-1), dataBackMoney.SaleID, dataBackMoney.LocationID, dataBackMoney.BranchCode, dataBackMoney.DistrictID, dataBackMoney.Year, dataBackMoney.Month);
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("back HM ", "thành công"));
                                }
                                */

                                var rt = PolicyService.GeneralCode_ReturnLimit(connection, transaction, dataEvent.generalCodeID);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ReturnLimit", rt));
                                if (rt != null)
                                {
                                    PolicyService.Voucher_CalcAvailable(connection, transaction, rt.Year, rt.Month, rt.LimitType, rt.SaleID, rt.LocationID);
                                }

                                var loyPendingStatus = FuncShared.LoyaltyActionSubmit(modelClear.voucherGc, VendorManager.CheckFormatVoucher(modelClear.voucherGc), "CANCEL", logId.ToString());
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("loyPendingStatus", loyPendingStatus));

                                flag++;
                            }
                        }

                        if (flag == input.Count)
                        {
                            transaction.Commit();
                        }
                    }
                }

                return new ResponseModel<bool> { data = true, error = null, result = 1 };
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("ClearVoucher error", ex.Message));
                return new ResponseModel<bool>() { data = false, error = ex.Message, result = 0 };
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "ClearVocuher ", logId);
            }
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModel<bool> RecheckVoucher(SalePolicyRedeemEVC input)
        {
            var logId = Guid.NewGuid();

            var erorrService = new ListVoucher();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("------------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input", input));

            var logs = new List<Log_Model>();

            var input_backup = Utility.CloneObject(input);

            try
            {
                int config_get_list = 0;
                /*
                if (input.debug > 0)
                {
                    config_get_list = input.debug == 1 ? 1 : 0;
                }
                else
                {
                    config_get_list = PolicyService.BaseConfig_Get(logs, null, 38)?.Count > 0 ? 1 : 0;
                }
                */

                if (input.evoucherList.Count > 0)
                {
                    var multiSelect = GetListServices2.GetValueByKeyConfig("MultiSelect");

                    if (input.evoucherList.Where(x => x.evoucherType == 1).ToList().Count > multiSelect)
                    {
                        return new ResponseModel<bool>
                        {
                            data = false,
                            error = "Bạn chỉ được chọn tối đa " + multiSelect.ToString() + " phiếu mua hàng. Vui lòng kiểm tra lại.",
                            result = 0
                        };
                    }
                }

                #region voucher nhập (mã lẻ, GTBB)
                var voucher_campain = input.evoucherList.Where(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "").ToList();
                if (voucher_campain != null && voucher_campain.Count > 0)
                {
                    ResponseModels<SalePlatformVoucherValueInfor> res = new ResponseModels<SalePlatformVoucherValueInfor> { data = new List<SalePlatformVoucherValueInfor>(), result = 0, error = "Không tìm thấy" };
                    MobisaleV4Controller mobisaleV4 = new MobisaleV4Controller();
                    SalePlatformGetVoucherInfo salePlatform = new SalePlatformGetVoucherInfo();
                    salePlatform.evoucherList = input.evoucherList.Select(x => new EvoucherInput() { evoucherCode = x.evoucherCode, evoucherType = x.evoucherType, RefId = x.RefId })//
                                                                    .Where(x => x.evoucherType == 2 && VendorManager.CheckFormatVoucher(x.evoucherCode) == "").ToList();
                    salePlatform.Services = input.Services;
                    salePlatform.Products = input.Products;

                    // dính bộ nhớ
                    var CustomerTypes = new List<CustomerTypeModel>();
                    foreach (var item in input.CustomerTypes)
                    {
                        CustomerTypes.Add(new CustomerTypeModel() { ServiceID = item.ServiceID, CustomerType = item.CustomerType == 1 ? 2 : 1 });
                    }

                    salePlatform.CustomerTypes = CustomerTypes;
                    salePlatform.saleID = input.SaleInfor.SaleID;
                    salePlatform.locationID = input.CustomerInfor.LocationID;
                    salePlatform.channel = input.SaleInfor.SaleID > 0 ? 0 : 1;
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GTBB req", salePlatform));

                    try
                    {
                        res = mobisaleV4.GetEvoucherInforV2(salePlatform, logId);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GTBB res", res));
                        if (!string.IsNullOrEmpty(res.error) && input.evoucherList.Where(x => x.RefId == 2)?.Count() == 0)
                        {
                            return new ResponseModel<bool>
                            {
                                data = false,
                                error = res.error,
                                result = 0
                            };
                        }
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("Recheck GTBB error", ex.Message));
                    }
                }
                #endregion

                // data change, load backup
                input = input_backup;

                #region voucher chon
                if (config_get_list == 0)
                {
                    erorrService.Error = new List<string>();
                    var res_get_list = PolicyService.Voucher_GetList(logs, input, -1);

                    if (res_get_list != null)
                    {
                        if (res_get_list.Error?.Count > 0)
                        {
                            erorrService.Error.AddRange(res_get_list.Error);
                        }
                    }

                    if (!res_get_list.Error.Any())
                    {
                        var res_get_info = PolicyService.Voucher_GetInfo(logs, input);
                        if (res_get_info != null)
                        {
                            if (res_get_info.LstInput?.Count > 0)
                            {
                                if (res_get_info.LstVoucher == null
                                    || res_get_info.LstVoucher.Count == 0
                                    || res_get_info.LstInput.Any(c => !res_get_info.LstVoucher.Any(x => x.evoucherCode.Equals(c.VoucherCode, StringComparison.OrdinalIgnoreCase))))
                                {
                                    return new ResponseModel<bool>
                                    {
                                        data = false,
                                        error = "",
                                        result = 0
                                    };
                                }
                            }

                            if (res_get_info.LstVoucher?.Count > 0)
                            {
                                #region voucher chong
                                var error_limit = PolicyService.Voucher_ApplyConfig_Check(logs, input.Services, input.Products, res_get_info.LstVoucher);
                                if (!string.IsNullOrEmpty(error_limit))
                                {
                                    erorrService.Error.Add(error_limit);
                                }
                                #endregion
                            }
                        }
                    }

                    // log to sb
                    foreach (var l in logs)
                    {
                        sb.AppendLine(string.Format("{0} : {1} : {2}", l.date, l.msg, l.obj));
                    }
                }
                else
                {
                    /*
                    erorrService = GetListServices2.GetListEvoucher(input, sb);
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("erorrService", erorrService));

                    if (erorrService != null)
                    {
                        // lay ds cs ma le
                        var vendor = erorrService.LstVoucher.Where(x => x.VoucherTypeID == 2).ToList();
                        if (vendor != null && input.evoucherList.Where(x => VendorManager.CheckFormatVoucher(x.evoucherCode) != "")?.Count() > 0)
                        {
                            var xmlC = VendorManager.ToXML_CampaignID(vendor);
                            var xmlP = VendorManager.ToXML_PrivateCode(input.evoucherList.Where(x => VendorManager.CheckFormatVoucher(x.evoucherCode) != "").ToList());
                            var AddVoucherID = VendorManager.AddVoucherID(xmlC, xmlP);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlC", xmlC));
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("xmlP", xmlP));
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("AddVoucherID", AddVoucherID));

                            if (AddVoucherID == 0)
                            {
                                return new ResponseModel<bool>
                                {
                                    data = false,
                                    error = "Voucher không hợp lệ.",
                                    result = 0
                                };
                            }

                        }
                    }


                    if (!erorrService.Error.Any())
                    {
                        //check rule chong voucher
                        var dataTotal = FSaleGetinforVoucherServices.GetVoucherinfo(input, sb);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("dataTotal", dataTotal));

                        if (dataTotal.Where(x => x.Apply.Count() > 0).Count() != input.evoucherList.Where(x => x.evoucherType == 1 || VendorManager.CheckFormatVoucher(x.evoucherCode) != "").Count())
                        {
                            return new ResponseModel<bool>
                            {
                                data = false,
                                error = "",
                                result = 0
                            };
                        }

                        if (dataTotal.All(x => x.Apply.Any()))
                        {
                            var error_limit = FSaleGetinforVoucherServices.CheckVoucherApplyLimitConfig(input.Services, input.Products, dataTotal, sb);
                            if (!string.IsNullOrEmpty(error_limit)) erorrService.Error.Add(error_limit);
                        }
                    }
                    */
                }
                #endregion
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FPTSale RecheckVoucher Exception", e.ToString()));
                return new ResponseModel<bool>
                {
                    data = false,
                    error = "Lỗi",
                    result = 0
                };

            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "FPTSale RecheckVoucher", logId);
            }
            return new ResponseModel<bool>
            {
                data = erorrService.Error.Any() ? false : true,
                error = string.Join(" ", erorrService.Error),
                result = erorrService.Error.Any() ? 0 : 1
            };
        }

        [HttpPost]
        public ResponseModel<List<DiscountVoucher>> GetDiscountVoucher(List<DiscountVoucherInput> input)
        {
            var logId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("------------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("FPTSale GetDiscountVoucher input", input));

            var lstEvcOutput = new List<DiscountVoucher>();
            string error = "";
            int result = 1;

            try
            {
                using (var conection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    foreach (var item in input)
                    {
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("contract-serviceid", item));
                        var DiscountVoucher = FSaleGetinforVoucherServices.GetDiscountVoucher(conection, sb, item.Contract, item.ServiceID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("DiscountVoucher", DiscountVoucher));
                        if (DiscountVoucher?.Any() == true)
                        {
                            lstEvcOutput.AddRange(DiscountVoucher);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("GetDiscountVoucher ERORR", e));
                error = e.Message;
                result = -1;
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "GetDiscountVoucher ", logId);
            }
            return new ResponseModels<DiscountVoucher>
            {
                data = lstEvcOutput,
                error = error,
                result = result
            };
        }


        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModels<VendorOutput> PrivateVendorCode(List<VendorInput> input)
        {
            var dataResult = new ResponseModels<VendorOutput>() { data = new List<VendorOutput>(), error = "", result = 0 };
            var checkEr = "";
            var sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("input", input));

            try
            {
                var dataCode = VendorManager.ProcessLongChau(input, out checkEr, sb);

                if (dataCode != null)
                {
                    return new ResponseModels<VendorOutput>() { data = dataCode, error = checkEr, result = 1 };
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("PrivateVendorCode Exception", e));
                return new ResponseModels<VendorOutput>() { data = new List<VendorOutput>(), error = "Lỗi", result = 0 };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "PrivateVendorCode", "");
            }

            return dataResult;
        }

        [HttpPost]
        public string gettoken()
        {
            return VendorManager.RequestNewAccessToken();
        }

        /// <summary>
        /// Dùng cho cskh, evc loy tool Quầy call sau khi evc vào bill
        /// </summary>
        /// <param name="BillNumber"></param>
        /// <returns></returns>
        [HttpGet]
        public ResponseModel<bool> PublishBill(string BillNumber, string sourceCode = null)
        {
            var dataOut = true;
            var errorOut = "";
            var resultOut = 1;
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("------");
            try
            {
                using (var conn = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    var i = conn.Execute("PowerInside.dbo.OS6_FPTVoucher_PublishBill", new
                    {
                        BillNumber = BillNumber,
                        SourceCode = sourceCode
                    }, commandType: CommandType.StoredProcedure);

                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("PublishBill", i));
                }
                //# domain
                //#172.20.18.252 cssapi-dev.fpt.net 
                //#172.20.18.252 cssapi-stag.fpt.net 
                //#172.20.17.32 cssapi.fpt.net 
                var apiUrl = Utility.cssapi_fpt_net + "/billing/v1/PrintBill/CreateBillingEInvoice?BillNumber=" + BillNumber;

                using (var httpClient = new HttpClient())
                {
                    try
                    {
                        // Gửi yêu cầu HTTP GET
                        var response = httpClient.GetAsync(apiUrl).Result;

                        // Kiểm tra xem yêu cầu có thành công không
                        response.EnsureSuccessStatusCode();

                        // Đọc nội dung từ phản hồi
                        string content = response.Content.ReadAsStringAsync().Result;
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("content", content));
                    }
                    catch (HttpRequestException ex)
                    {
                        errorOut = ex.Message;
                        dataOut = false;
                        resultOut = 0;
                    }
                }

            }
            catch (Exception e)
            {
                errorOut = e.Message;
                dataOut = false;
                resultOut = 0;

                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("err", e));
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "PublishBill", "");
            }

            return new ResponseModel<bool>() { data = dataOut, error = errorOut, result = resultOut };
        }

        [HttpPost]
        public ResponseModel<bool> test_log2(string a, int b, int c)
        {
            int x = 0;
            StringBuilder sb = new StringBuilder();
            try
            {
                sb.AppendLine("----------");
                x = FSaleGetinforVoucherServices.GetGoldFromDSC_test(a, b, c, sb);
                LoggerKafka.WriteLogKafka(sb, "test_log", "");
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("err", e));
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "test_log", "");
            }
            return new ResponseModel<bool>() { data = true, error = "", result = x };
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SaleClub)]
        public ResponseModel<bool> PendingStatusVoucher(VoucherAccessPendingInput input)
        {
            var logid = Guid.NewGuid().ToString();
            var sb = new StringBuilder();
            var rs = new ResponseModel<bool>() { data = true, result = 1, error = "Success" };

            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("pendingStatusVoucher input ", input));

            try
            {
                foreach (var item in input.Vouchers)
                {
                    var pendingStatusVoucher = FuncShared.LoyaltyActionSubmit(item.VoucherCode, VendorManager.CheckFormatVoucher(item.VoucherCode), "PENDING", logid);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("pendingStatusVoucher fail " + item.VoucherCode, pendingStatusVoucher));
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Exception", ex.Message));
                rs.data = false;
                rs.result = -1;
                rs.error = "Có lỗi";
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "PendingStatusVoucher", logid);
            }

            return rs;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.RegisterWebAndSaleClub)]
        public ResponseModels<Voucher_GetSaleQuota_Res> GetSaleQuota(Voucher_GetSaleQuota_Req req)
        {
            var res = new ResponseModels<Voucher_GetSaleQuota_Res>();
            try
            {
                var data = PolicyService.Voucher_GetSaleQuota(req);
                if (data?.Count > 0)
                {
                    res.data = data;
                    res.result = 1;
                }
                else
                {
                    res.result = -1;
                    res.error = "Không tìm thấy thông tin";
                }
            }
            catch (Exception ex)
            {
                res.result = -1;
                res.error = ex.GetType().Name;
                Utility.LogError(ex.ToString());
            }

            return res;
        }
    }
}
