using APIMBS.Constant;
using APIMBS.Models;
using APIMBS.Service;
using Dapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using System.Xml.Linq;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;

namespace APIMBS.Controllers
{
    [Route("API/MBS-EVoucher/{action}")]
    public class APIMBSController : ApiController
    {
        [HttpPost]
        public ResponseModel<PromotionEventResponse> GetPromotionEvent(PromotionEventRequest input)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), MyFPTService.RamdoneString());
            try
            {
                SalesManInfo Salesman = new SalesManInfo();
                L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), string.Concat("GetPromotionEvent ",keylog));
                PromotionEventResponse response = new PromotionEventResponse();
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        Salesman = GetInfoSalesMan(connection, transaction, input.SalesID);
                    }
                }
                if (Salesman == null)
                {
                    return new ResponseModel<PromotionEventResponse> { data = response, error = "Sale man không thể active dịch vụ", result = 0 };
                }
                if (input.SalesID == 0 || input.LocalType == 0)
                {
                    return new ResponseModel<PromotionEventResponse> { data = response, error = "Thông Tin Input Không Hợp Lệ", result = 0 };
                };

                int IDPaidTimeType = GetPrepaidPE(ref input);
                L.Mes(Level.INFOPREPAID,"IDPaidTimeType :" +JsonConvert.SerializeObject(IDPaidTimeType), string.Concat("GetPromotionEvent ", keylog));
                if (IDPaidTimeType == 0)
                {
                    return new ResponseModel<PromotionEventResponse> { data = response, error = "Thông Tin PrepaidTime Không Hợp Lệ", result = 0 };
                };

                // Get PromotionEvents with :  PaidTimeType, EventCode, LocalType
                #region Voucher chung
                if (!string.IsNullOrEmpty(input.VoucherGeneral))
                {
                    //Get VoucherPE
                    PromotionEventModel res = GetPromotionEventByCondition(IDPaidTimeType, input.LocalType, input.VoucherGeneral).FirstOrDefault();
                    L.Mes(Level.INFOPE, JsonConvert.SerializeObject(res), string.Concat("GetPromotionEvent ", keylog));

                    if (res != null)
                    {
                        if (res.ID2 > 0 || res.PromotionNetAmount > 0 || res.ContainerMonth > 0) { res.ServiceCode2 = input.NETServiceCode; }

                        // Check Quota 
                        int Quota = GetTotalQuotaValiable(res.ID, input.SalesID, GetQuotaGeneralCodeUsed(res.ID));
                        if (Quota <= 0)
                            Quota = GetQuotaV2(res.ID, input.SalesID, input.LocalType, IDPaidTimeType);

                        // Check ServiceCode
                        bool ValidServiceCode = CheckValidServiceCode(
                            new List<int>() { res.ServiceCode1, res.ServiceCode2, res.ServiceCode3, res.ServiceCode4, res.ServiceCode5, 0, res.ServiceCode7 },
                            new List<int>() { input.ServiceCodeTTNet, input.ServiceCodeTTTV, input.ServiceCodeHMNet, input.ServiceCodeHMTV }
                        );

                        L.Mes(Level.QUOTAPE, JsonConvert.SerializeObject(new { QUOTA = Quota, NETServiceCode = input.NETServiceCode, ValidateServiceCode = ValidServiceCode }), string.Concat("GetPromotionEvent ", keylog));
                        if (Quota >= 0 && ValidServiceCode)
                            response.VoucherGeneral = GetItemNode(res, input, false);
                    }
                }
                #endregion
                #region Voucher RF
                if (!string.IsNullOrEmpty(input.VoucherRP))
                {
                    if (input.VoucherRP.ToUpper().IndexOf("FTEL") == -1)
                    {
                        return new ResponseModel<PromotionEventResponse>() { data = null, error = "Voucher không phải GTBB", result = 0 };
                    }
                    int CSIBB = ReferalProgramHiFPT.CheckRuleIBB(input.SalesID);
                    L.Mes(Level.INFORP, JsonConvert.SerializeObject(new {ruleIBBCS = CSIBB}));
                    if (CSIBB.Equals(0))
                    {
                        return new ResponseModel<PromotionEventResponse>() { data = null, error = "IBB không thể dùng voucher GTBB", result = 0 };
                    }
                    bool isMYFPTCode = MyFPTService.checkMyFPTCode(input.VoucherRP, keylog);
                    // Get RPCode
                    PromotionEventModel invite = GetInfoPEByInviteCode(input.VoucherRP, input.LocalType, IDPaidTimeType, isMYFPTCode).FirstOrDefault(x => x.InviteType == 2);
                    L.Mes(Level.INFORP, JsonConvert.SerializeObject(invite));

                    if (invite != null)
                    {
                        if (invite.ID2 > 0) { invite.ServiceCode2 = input.NETServiceCode; }

                        // CheckValidServiceCode
                        Boolean ValidateServiceCode = CheckValidServiceCode(
                            // chỉ check Rule : Giảm Tiền không check Rule X đồng Y tháng
                            new List<int>() { invite.ServiceCode1, invite.ServiceCode2, invite.ServiceCode3, invite.ServiceCode4, invite.ServiceCode5, 0, invite.ServiceCode7 },
                            new List<int>() { input.ServiceCodeTTNet, input.ServiceCodeTTTV, input.ServiceCodeHMNet, input.ServiceCodeHMTV }
                        );

                        if (invite != null  && ValidateServiceCode)
                            response.VoucherRP = GetItemNode(invite, input, true);
                    }
                }
                #endregion
                L.Mes(Level.RESPONSE, JsonConvert.SerializeObject(response));
                L.Mes(Level.ENDREQUEST, null, "GetPromotionEvent");
                return new ResponseModel<PromotionEventResponse>() { data = response, error = null, result = 1 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message);
                return new ResponseModel<PromotionEventResponse> { data = null, error = ex.Message, result = -1 };
            }
        }

        [HttpPost]
        public ResponseModel<List<ListVoucherResponse>> GetlistEVoucher(ListVoucherRequest input)
        {
            try
            {
                L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "MBS - GetlistEVoucher");
                List<ListVoucherResponse> Ps = new List<ListVoucherResponse>();
                List<PromotionEventModel> RowItem = new List<PromotionEventModel>();

                if (input.SalesID == 0) { return new ResponseModel<List<ListVoucherResponse>> { data = null, error = null, result = 0 }; };
                int IDPaidTimeType = -1;

                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        IDPaidTimeType = GetPrepaidList(ref input);
                        L.Mes(Level.INFOPREPAID, IDPaidTimeType.ToString());
                        if (IDPaidTimeType == 0) return new ResponseModel<List<ListVoucherResponse>> { data = null, error = "Thông Tin PrepaidTime Không Hợp Lệ", result = 0 };

                        RowItem = connection.Query<PromotionEventModel>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
                        {
                            ActionName = "GetPromotionEvent",
                            Localtype = input.LocalType,
                            PaidTimeType = IDPaidTimeType,
                            RowAffected = 0
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        L.Mes(Level.INFO, JsonConvert.SerializeObject(RowItem));

                        if (RowItem.Count() == 0) return new ResponseModel<List<ListVoucherResponse>> { data = null, error = "Không Tìm Thấy Chương Trình KM Hợp Lệ", result = 0 };
                    }
                }

                for (int i = 0; i < RowItem.Count(); i++)
                {
                    if (RowItem[i].ID2 > 0 || RowItem[i].PromotionNetAmount > 0 || RowItem[i].ContainerMonth > 0) RowItem[i].ServiceCode2 = input.NETServiceCode;

                    // Check ServiceCode : RULE : the ServiceCode input sent by the customer will be greater or equal the ServiceCode of PE
                    if (!CheckValidServiceCode(
                        new List<int>(){
                            RowItem[i].ServiceCode1,RowItem[i].ServiceCode2,   // 56 or 47
                            RowItem[i].ServiceCode3,RowItem[i].ServiceCode4,   // 82 - 58 
                            RowItem[i].ServiceCode5,                            // 153,240,325
                            0,                                                  // GIFT
                            RowItem[i].ServiceCode7
                        },
                        new List<int>(){
                            input.ServiceCodeTTNet,                           // 56 or 47
                            input.ServiceCodeTTTV,                            // 82
                            input.ServiceCodeHMNet,                           // 9 or 58
                            input.ServiceCodeHMTV 
                        }
                   )) continue;

                    // Check Quota
                    //int Quota = GetTotalQuotaValiable(RowItem[i].ID, input.SalesID, GetQuotaGeneralCodeUsed(RowItem[i].ID));
                    //if (Quota <= 0)
                    //{
                    int Quota = GetQuotaV2(RowItem[i].ID, input.SalesID, input.LocalType, IDPaidTimeType);
                    //}

                    L.Mes(Level.INFO, JsonConvert.SerializeObject(new { Code = RowItem[i].EventCode, Quota = Quota }));
                    if (Quota <= 0) continue;
                    Ps.Add(new ListVoucherResponse() { VoucherCode = RowItem[i].EventCode, Description = ConvertTostring(RowItem[i], input, Quota) });
                }
                if (Ps.Count() == 0) return new ResponseModel<List<ListVoucherResponse>>() { data = null, error = "Voucher Đã Hết Lượng Quota Kích Hoạt Hoặc Thông Tin Yêu Cầu Không Chính Xác", result = 0 };
                return new ResponseModel<List<ListVoucherResponse>>() { data = Ps, error = null, result = Ps.Count() };
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.ToString());
                return new ResponseModel<List<ListVoucherResponse>> { data = null, error = ex.ToString(), result = -1 };
            }
        }

        [HttpPost]
        public async Task<ResponseModel<bool>> RedeemVoucher(RedeemVoucherRequest input)
        {
            try
            {
                string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), MyFPTService.RamdoneString());
                L.Mes(Level.REQUEST,keylog + " " + JsonConvert.SerializeObject(input), "RedeemVoucher");
                int status = 1;
                int? generalCodeID = null;

                PEDiscountModel vcDiscountUpdate = new PEDiscountModel();
                List<PEDiscountModel> rpDiscountUpdate = new List<PEDiscountModel>();

                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        SalesManInfo Salesman = GetInfoSalesMan(connection, transaction, input.SaleID);
                        if (Salesman == null) return new ResponseModel<bool>() { data = false, error = "Thông Tin Salesman Không Tồn Tại", result = 0 };
                        #region Voucher chung
                        if (!string.IsNullOrEmpty(input.VoucherGeneral))
                        {
                            vcDiscountUpdate = GetPE(connection, transaction, input.VoucherGeneral);
                            L.Mes(Level.INFOPE, keylog + " " + JsonConvert.SerializeObject(vcDiscountUpdate));
                            if (vcDiscountUpdate == null) return new ResponseModel<bool>() { data = false, error = "voucher không tồn tại khoặc ngưng kích hoạt", result = 0 };

                            var TempPrepaidTime = GetPrepaidConditon(input.NETID, 0, input.IPTVID);

                            // convert input
                            if (input.NETID == -1) input.IsPrepaidNET = -1;
                            if (input.NETID > 0) input.IsPrepaidNET = TempPrepaidTime.PrepaidTime;
                            if (input.IsPrepaidTV == 1) input.IsPrepaidTV = TempPrepaidTime.MonthQuantity;

                            //check is exits PaidTimeType | if exits return ID of PaidtimeType then not exits return 0
                            int IDPaidTimeType = GetInfoPaidTimeType(input.IsPrepaidNET, input.IsPrepaidTV);

                            //int Quota = GetTotalQuotaValiable(vcDiscountUpdate.ID, input.SaleID, GetQuotaGeneralCodeUsed(vcDiscountUpdate.ID));
                            int Quota = GetQuotaV2(vcDiscountUpdate.ID, input.SaleID, input.LocalType, IDPaidTimeType);

                            L.Mes(Level.INFOPE, keylog + " " + JsonConvert.SerializeObject(new { Eventcode = vcDiscountUpdate.EventCode, Quota = Quota }));
                            if (Quota <= 0)
                                return new ResponseModel<bool>() { data = false, error = "Đã sử dụng hết số lượng quota phân bổ", result = 0 };

                            status = connection.QueryFirstOrDefault<int>(ConstantAPI.OS6_FPTVoucher_GeneralVoucher, new
                            {
                                ActionName = "RedeemVoucherGeneralCode",
                                EventCode = input.VoucherGeneral,
                                PromotionEventID = vcDiscountUpdate.ID,
                                LocationID = Salesman.LOCATION,
                                DepartmentID = Salesman.DEPARTMENT,
                                SaleID = input.SaleID,
                                ObjID = input.ObjID,
                                OrderCode = input.OrderCode,
                                ActiveChannel = 1,
                                BNET = input.NETID,
                                BTV = input.IPTVID,
                                IsPrepaidTV = input.IsPrepaidTV,
                                LocalTypeID = input.LocalType,
                                PaidTimeTypeID = IDPaidTimeType,
                                RowAffected = 0,
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

                            L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { IDRedeem = status }));
                            if (status == 0)
                                return new ResponseModel<bool>() { data = false, error = "Redeem voucher không thành công", result = 0 };
                            if (status > 0)
                                generalCodeID = status;
                        }
                        #endregion

                        #region Giới thiệu bạn bè
                        if (!string.IsNullOrEmpty(input.VoucherRP))
                        {
                            // kiểm tra mã voucher RF thuộc Mã myFPT
                            bool isMYFPTCode = MyFPTService.checkMyFPTCode(input.VoucherRP, keylog);
                            if (isMYFPTCode)
                            {
                                return MyFPTService.RedeemRFMyFPT(input, keylog);
                            }
                            var TempPrepaidTime = GetPrepaidConditon(input.NETID, 0, input.IPTVID);
                            if ((TempPrepaidTime.MonthQuantity == -100 && input.IsPrepaidTV == 1) || (TempPrepaidTime.PrepaidTime == -100 && input.NETID > 1)) return new ResponseModel<bool>() { data = (status == 1), error = null, result = 0 };

                            // convert input
                            if (input.NETID == -1) input.IsPrepaidNET = -1;
                            if (input.NETID > 0) input.IsPrepaidNET = TempPrepaidTime.PrepaidTime;
                            if (input.IsPrepaidTV == 1) input.IsPrepaidTV = TempPrepaidTime.MonthQuantity;

                            //check is exits PaidTimeType | if exits return ID of PaidtimeType then not exits return 0
                            int IDPaidTimeType = GetInfoPaidTimeType(input.IsPrepaidNET, input.IsPrepaidTV);
                            L.Mes(Level.INFOPREPAID,keylog + " " + JsonConvert.SerializeObject(IDPaidTimeType));
                            if (IDPaidTimeType == 0) return new ResponseModel<bool>() { data = false, error = "Redeem voucher không thành công", result = 0 };

                            // get ObjOfUserInvite
                            GetObjInvite(connection, transaction, ref input);

                            // convert Rp to voucher code
                            // query with index = 0 is discount for invited user
                            // query with index = 1 is discount for invite user
                            rpDiscountUpdate = GetPEREF(input.VoucherRP, IDPaidTimeType, BuildPackageType(input));
                            L.Mes(Level.INFORP, keylog + " " + JsonConvert.SerializeObject(rpDiscountUpdate));
                            if (rpDiscountUpdate.Count != 3) return new ResponseModel<bool>() { data = false, error = "Thiếu thông tin quy đổi RP", result = 0 };

                            for (int i = 1; i < rpDiscountUpdate.Count(); i++)
                            {
                                // check quota
                                int Quota = GetTotalQuotaValiable(rpDiscountUpdate[i].ID, input.SaleID, (GetQuotaGeneralCodeUsed(rpDiscountUpdate[i].ID)));
                                //int Quota = GetQuotaV2(rpDiscountUpdate[i].ID, input.SaleID, input.LocalType, IDPaidTimeType);

                                L.Mes(Level.QUOTARP, keylog + " " + JsonConvert.SerializeObject(Quota));
                                if (Quota <= 0)
                                    return new ResponseModel<bool>() { data = false, error = "Redeem voucher không thành công - quotar không đủ", result = 0 };
                            }

                            // check salesman is not active two rp code
                            // active RP code for user invite and invited
                            // 200 is user invite - 222 is user invited
                            // 200 not save Bnet,Btv,ISPrepaidTV
                            var request = new List<RPCodeRedeemModel>{
                                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[1].ID, 
                                    VoucherCode = rpDiscountUpdate[1].EventCode, 
                                    OrderCode = input.OrderCode, 
                                    Salesman = input.SaleID, 
                                    ObjID = input.ObjID,
                                    ActiveChannel = 222,
                                    BNET = input.NETID, 
                                    BTV = input.IPTVID, 
                                    IsPrepaidTV = input.IsPrepaidTV,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = input.LocalType,
                                    PaidTimeTypeID = IDPaidTimeType
                                },
                                new RPCodeRedeemModel
                                { 
                                    Location = Salesman.LOCATION, 
                                    BranchCode = Salesman.BRANCHCODE, 
                                    Department = Salesman.DEPARTMENT,
                                    PromotionEventID = rpDiscountUpdate[2].ID, 
                                    VoucherCode = rpDiscountUpdate[2].EventCode, 
                                    OrderCode = input.OrderCode, 
                                    Salesman = input.SaleID, 
                                    ObjID = input.ObjIDInvite,
                                    ActiveChannel = 200, 
                                    BNET = 0, 
                                    BTV = 0, 
                                    IsPrepaidTV = 0,
                                    SubCompanyID = Salesman.SubCompanyID,
                                    LocalTypeID = input.LocalType,
                                    PaidTimeTypeID = IDPaidTimeType
                                }
                            };
                            L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { REDEEMFOR = request }));
                            FoxGold EventCodeLoy = ReferalProgramHiFPT.GetEventCodeLoy(connection, transaction, input.ObjID);
                            Boolean StatusRedeem = RedeemRPCodeMBS(connection, transaction, request, EventCodeLoy,input.VoucherRP);
                            if (!StatusRedeem) return new ResponseModel<bool>() { data = false, error = "Thông tin RP redeem không hợp lệ", result = 0 };
                            L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { STATUSREDEEMRP = StatusRedeem }));
                            // update status
                            UpdateStatusRPCode(connection, transaction, input.VoucherRP);
                        }
                        #endregion
                        if (status != 0) transaction.Commit();
                    }
                }

                if (status != 0)
                {
                    if (!string.IsNullOrEmpty(input.VoucherGeneral))
                    {
                        L.Mes(Level.INFO, keylog + " " + "SP_VUPA_GC");
                        if (vcDiscountUpdate.NetPromotionID > 0)
                        {
                            UpdateDiscount(
                                input.ObjID,  // objID khách hàng
                                vcDiscountUpdate.NetPromotionID, //XđồngY tháng NET + Tháng
                                vcDiscountUpdate.IPTVPromotionID, //XđồngY tháng TV + Tháng
                                vcDiscountUpdate.MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                                vcDiscountUpdate.MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                                vcDiscountUpdate.EventCode, // Code
                                input.SaleID, true //Sales -TypeVC:True
                            );
                        }
                        else
                        {
                            // tang thang
                            int isEvenMonth = 0;
                            if (vcDiscountUpdate.ContainerID > 0)
                            {
                                // lay clkm nen
                                var monthPromotion = GetMonthPromotion(input.ObjID);
                                if (monthPromotion != null)
                                {
                                    // thang chan le
                                    isEvenMonth = monthPromotion.EventMonth;
                                    // lay promotion net theo thang
                                    int promotionNetID = GetPromotionNetByContainerID(vcDiscountUpdate.ContainerID, monthPromotion.GetAddMonth(), isEvenMonth);
                                    if (promotionNetID > 0)
                                        vcDiscountUpdate.PromotionNetID = promotionNetID;
                                }
                            }

                            L.Mes(Level.INFO, keylog + " " + "AddCustomerDiscountV2: " + JsonConvert.SerializeObject(
                                new
                                {
                                    ObjID = input.ObjID,
                                    PromotionNetID = vcDiscountUpdate.PromotionNetID,
                                    IPTVPromotionID = vcDiscountUpdate.IPTVPromotionID,
                                    MoneyPromotionNETID = vcDiscountUpdate.MoneyPromotionNETID,
                                    MoneyPromotionTVID = vcDiscountUpdate.MoneyPromotionTVID,
                                    EventCode = vcDiscountUpdate.EventCode,
                                    SaleID = input.SaleID,
                                    isEvenMonth = isEvenMonth,
                                    generalCodeID = generalCodeID
                                }
                            ));

                            AddCustomerDiscountV2(
                                input.ObjID,
                                vcDiscountUpdate.PromotionNetID,
                                vcDiscountUpdate.IPTVPromotionID,
                                vcDiscountUpdate.MoneyPromotionNETID,
                                vcDiscountUpdate.MoneyPromotionTVID,
                                vcDiscountUpdate.EventCode,
                                input.SaleID,
                                isEvenMonth,
                                generalCodeID
                            );
                        }
                    }

                    if (!string.IsNullOrEmpty(input.VoucherRP))
                    {
                        L.Mes(Level.INFO, keylog + " " + "SP_VUPA_RP_UserInvited");
                        // update discount for UserInvited
                        UpdateDiscount(
                            input.ObjID,  // objID user invited
                            rpDiscountUpdate[1].NetPromotionID, //XđồngY tháng NET + Tháng
                            rpDiscountUpdate[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                            rpDiscountUpdate[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                            rpDiscountUpdate[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                            rpDiscountUpdate[1].EventCode, // Code
                            input.SaleID, false //Sales -TypeVC:True.
                        );

                        L.Mes(Level.INFO, keylog + " " + "SP_VUPA_RP_UserInvite");
                        // Update Discount for User Invite
                        UpdateDiscount(
                            input.ObjIDInvite,  // objID user invite
                            rpDiscountUpdate[2].NetPromotionID, //XđồngY tháng NET + Tháng
                            rpDiscountUpdate[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                            rpDiscountUpdate[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                            rpDiscountUpdate[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                            rpDiscountUpdate[2].EventCode, // Code
                            input.SaleID, false //Sales -TypeVC:True
                        );

                        UpdateOanhVK(input.ObjIDInvite, input.OrderCode,generalCodeID);

                    }
                    UpdateOanhVK(input.ObjID, input.OrderCode,generalCodeID);
                }

                if (!string.IsNullOrEmpty(input.VoucherRP))
                {
                    //await SenNotify(input.ObjIDInvite, input.ObjID);
                }

                L.Mes(Level.RESPONSE, keylog + " " + JsonConvert.SerializeObject(status != 0));
                L.Mes(Level.ENDREQUEST, null, keylog + " " + "RedeemVoucher");
                return new ResponseModel<bool>() { data = (status != 0), error = null, result = 0 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.ToString());
                return new ResponseModel<bool> { data = false, error = ex.ToString(), result = -1 };
            }
        }    

        private AuthorizationInfor getAuthorInfor()
        {
            L.Mes(Level.INFO, "Start to get token myfpt", "getAuthorInfor");
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                var uri = WebAPIHelper.myFpt_fpt_vn + "/api/oauth-ms/public/auth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("client_id", Utility.my_fpt_api_client_id));
                keyValues.Add(new KeyValuePair<string, string>("client_secret", Utility.my_fpt_api_client_secret));
                keyValues.Add(new KeyValuePair<string, string>("username", Utility.my_fpt_api_username));
                keyValues.Add(new KeyValuePair<string, string>("password", Utility.my_fpt_api_password));
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "password"));

                var content = new FormUrlEncodedContent(keyValues);

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        response.EnsureSuccessStatusCode();
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, "getAuthorInfor");
                        res = JsonConvert.DeserializeObject<AuthorizationInfor>(r);
                    }
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getAuthorInfor - MBS");
                return null;
            }
            return res;
        }

        private async Task<Boolean> SendNotificationReferralMyFPT(dynamic input)
        {
            int iStatusCode = 0;
            LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
            AuthorizationInfor aut = getAuthorInfor();
            try
            {
                if (aut == null)
                    return false;
                var request = new ModelSendNotificationReferralMyFPT()
                {
                    employeeCode = input.StaffIDInvite,
                    Referrer = input.RealMoneyAmount,
                    BookingId = input.Contract,
                    contract_owner = input.FullName,
                    BookingStatus = "pending"
                };

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    //client.DefaultRequestHeaders.Add("Authorization", aut.authorization);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = await client.PostAsync("/api/oauth-ms/public/auth/integration-supplier", data);
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(response));
                    iStatusCode = (int)response.StatusCode;
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "SendNotificationReferralFoxpro");
                return false;
            }
            //dynamic response = new dynamic();

            if (iStatusCode == 200)
            {
                return true;
            }

            return false;
        }

        private Boolean checkFoxproCode(string vouchercode)
        {
            int flag = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    flag = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_Foxpro_MBS", new
                    {
                        ActionName = "CheckCodeFoxpro",
                        voucherCode = vouchercode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    return (flag == 1);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString());
                return false;
            }
        }

        [HttpPost]
        public async Task<ModelRequestNotifyResponse> TSendNotify(int a, int b)
        {
            return await SenNotify(a, b);
        }

        #region Support Method
        private async Task<ModelRequestNotifyResponse> SenNotify(int ObjInvite, int ObjInvited)
        {
            ModelRequestNotifyResponse response = new ModelRequestNotifyResponse();
            try
            {
                Dictionary<int, InfoNotify> infoUser = new Dictionary<int, InfoNotify>();
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        var temp = connection.Query<InfoNotify>(ConstantAPI.OS6_ReferralProgram_SucceedInvite, new
                        {
                            ActionName = "InforPushNotify",
                            ObjIDInvite = ObjInvite,
                            ObjIDInvited = ObjInvited,
                            RowAffected = 0
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                        foreach (var item in temp)
                        {
                            if (item.Id == ObjInvite) infoUser.Add(1, item); else infoUser.Add(2, item);
                        }
                    }
                }

                L.Mes(Level.INFO, JsonConvert.SerializeObject(infoUser));

                var request = new
                {
                    contractIdGT = ObjInvite,
                    data = new
                    {
                        eventType = "referral_code_confirm",
                        nameGT = infoUser[1].FullName,
                        nameDK = infoUser[2].FullName,
                        contractDK = infoUser[2].Contract
                    }
                };
                using (var client = new HttpClient())
                {
                    string url = Utility.hi_fpt_api;
                    client.BaseAddress = new Uri(url);
                    client.DefaultRequestHeaders.Add("Authorization", ("referral::ofjhgiiedhcidkfjeudsalsodejdcfydiejd" + DateTime.Now.ToString("yyyy-dd-MM").ToString()).CreateMD5());
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    response = await client.PostAsync("/hi-customer-local/referral/send-notify-v3", data).Result.Content.ReadAsAsync<ModelRequestNotifyResponse>();
                }

                L.Mes(Level.INFO, JsonConvert.SerializeObject(response));
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, "SenNotify: " + ex.ToString());
            }
            return response;
        }

        private string BuildPackageType(RedeemVoucherRequest input)
        {
            if (input.IsPrepaidNET == -1 && input.IsPrepaidTV != -1) return "TVONLY";
            else if (input.IsPrepaidNET != -1 && input.IsPrepaidTV != -1) return "COMBO";
            else if (input.IsPrepaidNET != -1 && input.IsPrepaidTV == -1) return "NETONLY";
            else return "";
        }

        private void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, PNET, PTV, MNET, MTV, Voucher, SalesManID }));
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }

        private void AddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private void UpdateOanhVK(int ObjID, string OrderCode, int? generalCodeID)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, OrderCode }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@generalCodeID", generalCodeID);
                connection.Execute(ConstantAPI.OS6_FPTVoucher_AddVoucherBill2, p, commandType: CommandType.StoredProcedure);
            }
        }

        private void UpdateStatusRPCode(SqlConnection connection, SqlTransaction transaction, string InviteCode)
        {
            connection.Query<int>(ConstantAPI.OS6_ReferralProgram_InviteCode, new
            {
                InviteCode = InviteCode,
                ActionName = "UpdateInvite",
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        private void GetObjInvite(SqlConnection connection, SqlTransaction transaction, ref RedeemVoucherRequest rpCode)
        {
            int ObjInvite = connection.Query<int>(ConstantAPI.OS6_ReferralProgram_InviteCode, new
            {
                ActionName = "GetObjInvite",
                InviteCode = rpCode.VoucherRP,
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            rpCode.ObjIDInvite = ObjInvite;
        }

        private int GetPrepaidList(ref ListVoucherRequest input)
        {
            var condition = GetPrepaidConditon(input.NETID, input.LocalType, input.IPTVID);
            // check exit prepaid
            // -100 không có data với ID CLKM nền, 0 Có nhưng trả sau, > 0 trà trước theo số tháng output
            if ((condition.MonthQuantity == -100 && input.IsPrepaidTV == 1) || (condition.PrepaidTime == -100 && input.NETID > 1))
                return 0;

            // convert input
            if (input.NETID == -1) input.IsPrepaidNet = -1;
            if (input.NETID != -1 && input.NETID != 0) input.IsPrepaidNet = condition.PrepaidTime;
            if (input.IsPrepaidTV == 1) input.IsPrepaidTV = condition.MonthQuantity;
            if (condition.DLSType == 2) input.NETServiceCode = 56; else input.NETServiceCode = 47;

            return GetInfoPaidTimeType(input.IsPrepaidNet, input.IsPrepaidTV);
        }
        private int GetPrepaidPE(ref PromotionEventRequest input)
        {
            var condition = GetPrepaidConditon(input.NETID, input.LocalType, input.IPTVID);
            // check exit prepaid
            // -100 không có data với ID CLKM nền, 0 Có nhưng trả sau, > 0 trà trước theo số tháng output
            if ((condition.MonthQuantity == -100 && input.IsPrepaidTV == 1) || (condition.PrepaidTime == -100 && input.NETID > 1))
                return 0;

            // convert input
            if (input.NETID == -1) input.IsPrepaidNET = -1;
            if (input.NETID != -1 && input.NETID != 0) input.IsPrepaidNET = condition.PrepaidTime;
            if (input.IsPrepaidTV == 1) input.IsPrepaidTV = condition.MonthQuantity;

            if (condition.DLSType == 2) input.NETServiceCode = 56; else input.NETServiceCode = 47;

            return GetInfoPaidTimeType(input.IsPrepaidNET, input.IsPrepaidTV);
        }

        private PEDiscountModel GetPE(SqlConnection connection, SqlTransaction transaction, string VoucherCode)
        {
            PEDiscountModel PE = connection.Query<PEDiscountModel>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
            {
                ActionName = "GetPEByCode",
                EventCode = VoucherCode,
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return PE;

     }

        private bool RedeemRPCodeMBS(SqlConnection connection, SqlTransaction transaction, List<RPCodeRedeemModel> input,FoxGold eventLoyalty,string voucherRP)
        {
            if (!InsertInviteCodeSuccess(connection, transaction, input, eventLoyalty.EventCode, voucherRP)) return false;

            int RowAffected = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
            {
                ActionName = "InsertGeneralCodeXML",
                XML = CreateXMLRedeemRPcode(input, eventLoyalty),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            L.Mes(Level.INFO, RowAffected.ToString());
            // check row Affected
            return (RowAffected == 3);
        }

        private bool InsertInviteCodeSuccess(SqlConnection connection, SqlTransaction transaction, List<RPCodeRedeemModel> input, string eventLoyalty, string voucherRP)
        {
            int InsertInviteSuccess = connection.Query<int>(ConstantAPI.OS6_ReferralProgram_SucceedInvite, new
            {
                ActionName = "InsertInfoRedeemRPCode",
                XML = CreateXMLRedeemIV(input, eventLoyalty, voucherRP),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StatusUpdateInviteSuccess = (InsertInviteSuccess == 1) }));

            return InsertInviteSuccess == 1;
        }

        private XElement CreateXMLRedeemIV(List<RPCodeRedeemModel> input, string eventLoyalty, string voucherRP)
        {
            var xmlString = new XElement("N",
                new XElement("I",
                    new XElement("RPinvited", voucherRP),  // nguoi giới thiệu
                    new XElement("RPLoyalty", eventLoyalty), // Mã ưu đãi add điểm của Loyalty
                    new XElement("Vinvite", input[1].VoucherCode),
                    new XElement("Vinvited", input[0].VoucherCode),
                    new XElement("Or", input[0].OrderCode),
                    new XElement("Oinvited", input[0].ObjID),
                    new XElement("Oinvite", input[1].ObjID),
                    new XElement("Oinvite", input[1].ObjID)
                )
            );
            return xmlString;
        }

        /// <summary>
        /// get prepaid info
        /// </summary>
        /// <param name="NETID"></param>
        /// <param name="IPTVID"></param>
        /// <returns></returns>
        private PrepaidConditonSearch GetPrepaidConditon(int NETID, int? LocalType, int IPTVID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<PrepaidConditonSearch>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPrepaidConditon",
                    new { NETID = NETID, IPTVID = IPTVID, LocalType = LocalType },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        /// <summary>
        /// get Id prepaid
        /// </summary>
        /// <param name="IsPerpaidNET"></param>
        /// <param name="IsPerpaidTV"></param>
        /// <returns></returns>
        private int GetInfoPaidTimeType(int IsPerpaidNET, int IsPerpaidTV)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
                {
                    ActionName = "CheckContaintPaidTimeType",
                    IsPrepaidTV = IsPerpaidTV,
                    IsPrePaidNET = IsPerpaidNET,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        /// <summary>
        /// find the promotionevent by IDPrepaid, Service , LocalType
        /// </summary>
        /// <param name="PaidTimeTypeID"></param>
        /// <param name="LocalType"></param>
        /// <returns></returns>
        private List<PromotionEventModel> GetPromotionEventByCondition(int PaidTimeTypeID, int LocalType, string EvenCode)
        {
            List<PromotionEventModel> res = new List<PromotionEventModel>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var Request = new DynamicParameters();
                if (!string.IsNullOrEmpty(EvenCode)) Request.Add("@EventCode", EvenCode);
                Request.Add("@ActionName", "GetPromotionEvent");
                Request.Add("@PaidTimeType", PaidTimeTypeID);
                Request.Add("@Localtype", LocalType);
                Request.Add("RowAffected", 0);
                res = connection.Query<PromotionEventModel>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, Request, commandType: CommandType.StoredProcedure).ToList();
            }
            return res;
        }

        private List<PEDiscountModel> GetPEREF(string VoucherRP, int PaidTimeTypePE, string PackageTypeInvited)
        {
            List<PEDiscountModel> res = new List<PEDiscountModel>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var parameters = new DynamicParameters();
                parameters.Add("@ActionName", "GetPEREF");
                parameters.Add("@InviteCode", VoucherRP);
                parameters.Add("@PackageTypeInvited", PackageTypeInvited);
                parameters.Add("@RowAffected", 0);
                parameters.Add("@PaidTimeType", PaidTimeTypePE);
                res = connection.Query<PEDiscountModel>(ConstantAPI.OS6_ReferralProgram_InviteCode, parameters, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
            }
            return res;
        }

        /// <summary>
        /// Get promotion event type of invited
        /// </summary>
        /// <param name="PaidTimeTypeID"></param>
        /// <param name="VoucherRP"></param>
        /// <param name="LocalType"></param>
        /// <returns></returns>
        private List<PromotionEventModel> GetInfoPEByInviteCode(string VoucherRP, int? LocalType, int? PaidTimeTypePE, bool isMyFPT)
        {
            List<PromotionEventModel> res = new List<PromotionEventModel>();
            if (!isMyFPT)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@ActionName", "GetInfoPromotionEventByInviteCode");
                    parameters.Add("@InviteCode", VoucherRP);
                    parameters.Add("@RowAffected", 0);
                    if (PaidTimeTypePE != null) parameters.Add("@PaidTimeType", PaidTimeTypePE);
                    if (LocalType != null) parameters.Add("@LocalType", LocalType);
                    res = connection.Query<PromotionEventModel>(ConstantAPI.OS6_ReferralProgram_InviteCode, parameters, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                }
            }
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@ActionName", "GetInfoPromotionEventByInviteCode_foxpro");
                    //parameters.Add("@InviteCode", VoucherRP);
                    parameters.Add("@RowAffected", 0);
                    if (PaidTimeTypePE != null) parameters.Add("@PaidTimeType", PaidTimeTypePE);
                    if (LocalType != null) parameters.Add("@LocalType", LocalType);
                    res = connection.Query<PromotionEventModel>(ConstantAPI.OS6_ReferralProgram_InviteCode, parameters, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                }
            }

            return res;
        }

        /// <summary>
        /// Get Total Quota will be used
        /// </summary>
        /// <param name="PromotionEventID"></param>
        /// <returns></returns>
        private int GetQuotaGeneralCodeUsed(int PromotionEventID)
        {
            int quota = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                quota = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
                {
                    ActionName = "GetQuotaGeneralCodeUsed",
                    PromotionEvent = PromotionEventID,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return quota;

        }

        /// <summary>
        /// get total quota really to use
        /// </summary>
        /// <param name="PromotionEventID"></param>
        /// <param name="IDSalesMan"></param>
        /// <param name="TotalQuotaUsed"></param>
        /// <returns></returns>
        private int GetTotalQuotaValiable(int PromotionEventID, int IDSalesMan, int TotalQuotaUsed)
        {
            SalesManInfo SalesInfo = new SalesManInfo();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        SalesInfo = GetInfoSalesMan(connection, transaction, IDSalesMan);
                    }
                }
                if (SalesInfo == null) return -1;

                Dictionary<string, int> QuotaAssign = new Dictionary<string, int>
                {
                    { ConstantAction.SALESMAN, 0 },
                    { ConstantAction.DEPARTMENT, 0 },
                    { ConstantAction.BRANCHCODE, 0 },
                    { ConstantAction.LOCATION, 0 },
                    { ConstantAction.REGION, 0 }
                };

                for (int i = 0; i < QuotaAssign.Count(); i++)
                {
                    SalesInfo.Quota = GetQuotaOfNodeIsAssign(QuotaAssign.ElementAt(i).Key, SalesInfo, PromotionEventID, ConstantAction.GetQuota);
                    L.Mes(Level.QUOTAINNODE, string.Format("{0} - {1}", QuotaAssign.ElementAt(i).Key, SalesInfo.Quota));

                    if (i == 0 && SalesInfo.Quota > 0)
                    {
                        SalesInfo.Quota = SalesInfo.Quota - TotalQuotaUsed;
                        break;
                    }

                    if (SalesInfo.Quota > 0 && (QuotaAssign.ElementAt(i - 1).Value == 0))
                    {
                        int SumQuota = GetQuotaOfNodeIsAssign(QuotaAssign.ElementAt(i - 1).Key, SalesInfo, PromotionEventID, ConstantAction.GetQuotaParallelNode);

                        SalesInfo.Quota = SalesInfo.Quota - SumQuota;
                        SalesInfo.Quota = SalesInfo.Quota - TotalQuotaUsed;
                        break;
                    }
                    if ((SalesInfo.Quota > 0) && (QuotaAssign.ElementAt(i - 1).Value == -1))
                    {
                        int CountRow = GetQuotaOfNodeIsAssign(QuotaAssign.ElementAt(i - 1).Key, SalesInfo, PromotionEventID, ConstantAction.CheckContainChildNode);
                        if (CountRow > 0)
                        {
                            return -1;
                        }
                        else
                        {
                            SalesInfo.Quota = SalesInfo.Quota - TotalQuotaUsed;
                            break;
                        }
                        break;
                    }

                    if (i == 4 && SalesInfo.Quota == -1)
                        return -1;
                    QuotaAssign[QuotaAssign.ElementAt(i).Key] = SalesInfo.Quota;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, "GetTotalQuotaValiable: " + ex.Message);
            }
            return SalesInfo.Quota;
        }

        private SalesManInfo GetInfoSalesMan(SqlConnection connection, SqlTransaction transaction, int ObjSalesman)
        {
            SalesManInfo SalesInfo = new SalesManInfo();
            SalesInfo = connection.Query<SalesManInfo>(ConstantAPI.OS6_FPTVoucher_SalesManInfo, new
            {
                ActionName = "SelectByID",
                ID = ObjSalesman
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            return SalesInfo;
        }

        /// <summary>
        /// Get quota of node assign
        /// </summary>
        /// <param name="AssignPosition"></param>
        /// <param name="user"></param>
        /// <param name="PromotionID"></param>
        /// <param name="ActionName"></param>
        /// <returns></returns>
        private int GetQuotaOfNodeIsAssign(string AssignPosition, SalesManInfo user, int PromotionID, string ActionName)
        {
            int Quota = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int Result = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_SalesManInfo, new
                {
                    ActionName = ActionName,
                    QuotaAction = AssignPosition,
                    AccountName = user.Name,
                    REGION = user.REGION,
                    LOCATION = user.LOCATION,
                    BRANCH = user.BRANCHCODE,
                    DEPARTMENT = user.DEPARTMENT,
                    SALESMAN = user.SALESMAN,
                    PromotionID = PromotionID
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                Quota = Result;
            }
            return Quota;
        }

        private int GetQuotaV2(int promotionID, int salesmanID, int localTypeID, int paidTimeTypeID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(ConstantAPI.OS6_FPTVoucher_SalesManInfo, new
                {
                    ActionName = ConstantAction.GetQuotaV2,
                    ID = salesmanID,
                    PromotionID = promotionID,
                    LocalTypeID = localTypeID,
                    PaidTimeTypeID = paidTimeTypeID

                }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }

        /// <summary>
        /// Output discount with condition
        /// </summary>
        /// <param name="input"></param>
        /// <param name="condition"></param>
        /// <param name="isRPCode"></param>
        /// <returns></returns>
        private List<ItemNode> GetItemNode(PromotionEventModel input, PromotionEventRequest condition, Boolean isRPCode)
        {
            List<ItemNode> instance = new List<ItemNode>();
            if (input == null) return instance;
            input.ServiceCode2 = condition.NETServiceCode;

            if (isRPCode) instance.Add(new ItemNode() { Dismonth = 0, PromotionEvent = "DiscountContent", ServiceCode = -2, Value = ConvertTostring(input, condition) });
            // FreeMonth
            if (condition.NETID > 0 && input.ContainerMonth > 0)
            {
                string Paidtype;
                if (condition.IsPrepaidNET == 0) Paidtype = "FreemonPostPaid"; else Paidtype = "FreemonthPrepaid";
                instance.Add(new ItemNode() { Dismonth = 0, PromotionEvent = Paidtype, ServiceCode = 0, Value = input.ContainerMonth.ToString() });
            }

            //Discount TT NET
            bool ConditionDiscountNET = (input.ID1 != 0 && (condition.ServiceCodeTTNet != 0) && (condition.ServiceCodeTTNet == input.ServiceCode1));
            int promotionNetAmount = 0;
            // by PromotionNetID
            bool ConditionDiscountByMonthNET = (input.PromotionNetAmount > 0 && (condition.ServiceCodeTTNet != 0) && (condition.ServiceCodeTTNet == input.ServiceCode2));
            if (ConditionDiscountByMonthNET)
            {
                //promotionNetAmount = input.PromotionNetAmount.VATDiscount();
                // KhanhHC2 2020-06-03 khong ap dung tang thang khi so thang tra truoc nho hop so thang tang
                //if (ConditionDiscountByMonthNET && condition.IsPrepaidNET < input.PromotionNetDuration)
                //    ConditionDiscountByMonthNET = false;

                // dev
                if (input.PromotionNetDuration > 0)
                {
                    int DismonthValidate = GetDismonthValidate(input.PromotionNetDuration, condition.IsPrepaidNET);
                    promotionNetAmount = (input.PromotionNetAmount / input.PromotionNetDuration).VATDiscount() * DismonthValidate;
                }
            }
            else
            {
                // by NetPromotionID
                ConditionDiscountByMonthNET = (input.ID2 > 0 && (condition.ServiceCodeTTNet != 0) && (condition.ServiceCodeTTNet == input.ServiceCode2));
                int DismonthValidate = GetDismonthValidate(input.DisMonth2, condition.IsPrepaidNET);
                promotionNetAmount = input.Value2.VATDiscount() * DismonthValidate;
            }
            if (ConditionDiscountNET || ConditionDiscountByMonthNET)
            {
                instance.Add(new ItemNode()
                {
                    PromotionEvent = "DiscountAmountNET",
                    ServiceCode = ConditionDiscountNET ? input.ServiceCode1 : input.ServiceCode2,
                    Value = TotalDiscountCLKM(ConditionDiscountNET, input.Value1.VATDiscount(), ConditionDiscountByMonthNET, promotionNetAmount),
                    Dismonth = 0
                });
            }

            bool ConditionDiscountTV = (input.ID7 != 0 && (condition.ServiceCodeTTTV != 0) && (condition.ServiceCodeTTTV == input.ServiceCode7));
            bool ConditonDiscountByMonthTV = (input.ID3 != 0 && (condition.ServiceCodeTTTV != 0) && (condition.ServiceCodeTTTV == input.ServiceCode3));
            if (ConditionDiscountTV || ConditonDiscountByMonthTV)
            {
                int DismonthValidate = GetDismonthValidate(input.DisMonth3, condition.IsPrepaidTV);
                instance.Add(new ItemNode()
                {
                    PromotionEvent = "DiscountAmountTV",
                    ServiceCode = ConditionDiscountTV ? input.ServiceCode7 : input.ServiceCode3,
                    Value = TotalDiscountCLKM(ConditionDiscountTV, input.Value7.VATDiscount(), ConditonDiscountByMonthTV, (input.Value3.VATDiscount() * DismonthValidate)),
                    Dismonth = 0
                });
            }


            // CLKM Hòa Mạng NET
            if (input.ID4 != 0 && (condition.ServiceCodeHMNet != 0) && (condition.ServiceCodeHMNet == input.ServiceCode4))
                instance.Add(new ItemNode() { PromotionEvent = "DiscountNewInterConnNET", ServiceCode = input.ServiceCode4, Value = (string)input.Value4.VATDiscount().ToString(), Dismonth = 0 });
            // CLKM Hòa Mạng TV
            if (input.ID5 != 0 && (condition.ServiceCodeHMTV != 0) && (condition.ServiceCodeHMTV == input.ServiceCode5))
                instance.Add(new ItemNode() { PromotionEvent = "DiscountNewInterConnTV", ServiceCode = input.ServiceCode5, Value = (string)input.Value5.VATDiscount().ToString(), Dismonth = 0 });
            // Quà Tặng
            if (input.ID6 != 0)
                instance.Add(new ItemNode() { PromotionEvent = "Gift", ServiceCode = -1, Dismonth = 0, Value = input.Discount6 + input.Model });

            return instance;
        }

        /// <summary>
        ///  check valid month discount x đồng y tháng
        /// nếu CLKM X đồng Y Tháng Của Vouhcher có số tháng giảm bé hơn hoặc bằng CLKM nền thì sẽ sử dụng giảm theo số tháng của voucher
        /// ngược lại nếu lớn hơn chỉ giảm theo số tháng của clkm nền
        /// </summary>
        /// <param name="MonthCLKMVoucher"></param>
        /// <param name="MonthCLKMBase"></param>
        /// <returns></returns>
        private int GetDismonthValidate(int MonthCLKMVoucher, int MonthCLKMBase)
        {
            return (MonthCLKMVoucher <= MonthCLKMBase) ? MonthCLKMVoucher : MonthCLKMBase;
        }

        /// <summary>
        ///  show message discount with conditon
        /// </summary>
        /// <param name="PE"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        private String ConvertTostring(PromotionEventModel PE, PromotionEventRequest request)
        {
            string temp = "";
            // tặng tháng NET
            if (request.NETID > 0 && PE.ContainerMonth > 0)
            {
                string Paidtype = ""; if (request.IsPrepaidNET == 0) Paidtype = "sau"; else Paidtype = "trước";
                temp += string.Format("trả {0} internet tặng {1} tháng cước", Paidtype, PE.ContainerMonth) + "| ";
            }

            //trả trước TV
            if (request.IsPrepaidTV > 0)
            {
                int duration = CheckApplyCLKMTV(PE.ID3, request.IPTVID, true);
                if (duration != 0)
                {
                    temp += string.Format("Trả trước IPTV tặng {0} tháng cước", duration) + "| ";
                }
            }

            if (request.IsPrepaidTV == 0)
            {
                int duration = CheckApplyCLKMTV(PE.ID3, request.IPTVID, false);
                if (duration != 0)
                {
                    temp += string.Format("Trả sau IPTV tặng {0} tháng cước", duration) + "| ";
                }
            }

            if (!string.IsNullOrEmpty(PE.Discount1) && request.ServiceCodeTTNet == PE.ServiceCode1)
                temp += "giảm tiền trực tiếp NET: " + PE.Discount1 + "| ";

            if (!string.IsNullOrEmpty(PE.Discount7) && request.ServiceCodeTTTV == PE.ServiceCode7)
                temp += "giảm tiền trực tiếp TV: " + PE.Discount7 + "| ";

            if (!string.IsNullOrEmpty(PE.Discount2) && request.ServiceCodeTTNet == PE.ServiceCode2)
                temp += "giảm phí NET theo tháng: " + PE.Discount2 + "| ";

            if (!string.IsNullOrEmpty(PE.PromotionNetDiscount) && request.ServiceCodeTTNet == PE.ServiceCode2)
                temp += "giảm phí NET theo tháng: " + PE.PromotionNetDiscount + "| ";

            if (!string.IsNullOrEmpty(PE.Discount3) && request.ServiceCodeTTTV == PE.ServiceCode3)
                temp += "giảm phí IPTV theo tháng: " + PE.Discount3 + "| ";


            if (!string.IsNullOrEmpty(PE.Discount4) && request.ServiceCodeHMNet == PE.ServiceCode4)
                temp += "giảm phí hòa mạng NET: " + PE.Discount4 + "| ";

            if (!string.IsNullOrEmpty(PE.Discount5) && request.ServiceCodeHMTV == PE.ServiceCode5)
                temp += "giảm phí hòa mạng IPTV: " + PE.Discount5 + "| ";

            if (!string.IsNullOrEmpty(PE.Discount6))
                temp += "quà tặng: " + PE.Discount6 + "| ";
            return temp;
        }

        private String ConvertTostring(PromotionEventModel PE, ListVoucherRequest request, int Quota)
        {
            /*
            string temp = "";
            if (request.NETID > 0 && PE.ContainerMonth > 0)
            {
                string Paidtype = ""; if (request.IsPrepaidNet == 0) Paidtype = "sau"; else Paidtype = "trước";
                temp += string.Format("trả {0} internet tặng {1} tháng cước", Paidtype, PE.ContainerMonth) + "| ";
            }

            //trả trước TV
            if (request.IsPrepaidTV > 0)
            {
                L.Mes(Level.INFO, "trả trước TV tặng tháng");
                int duration = CheckApplyCLKMTV(PE.ID3, request.IPTVID, true);
                if (duration != 0)
                    temp += string.Format("Trả trước IPTV tặng {0} tháng cước", duration) + " |";
            }

            if (request.IsPrepaidTV == 0)
            {
                L.Mes(Level.INFO, "Trả sau IPTV tặng tặng tháng");
                int duration = CheckApplyCLKMTV(PE.ID3, request.IPTVID, false);
                if (duration != 0)
                    temp += string.Format("Trả sau IPTV tặng {0} tháng cước", duration) + " |";
            }

            if (!string.IsNullOrEmpty(PE.Discount1))
                temp += PE.Discount1.Trim() + " |";

            if (!string.IsNullOrEmpty(PE.Discount7))
                temp += PE.Discount7.Trim() + " |";

            if (!string.IsNullOrEmpty(PE.Discount2))
                temp += PE.Discount2.Trim() + " |";

            if (!string.IsNullOrEmpty(PE.PromotionNetDiscount))
                temp += PE.PromotionNetDiscount.Trim() + " |";

            if (!string.IsNullOrEmpty(PE.Discount3))
                temp += PE.Discount3.Trim() + " |";

            if (!string.IsNullOrEmpty(PE.Discount4))
                temp += PE.Discount4.Trim() + " |";

            if (!string.IsNullOrEmpty(PE.Discount5))
                temp += PE.Discount5.Trim() + " |";

            if (!string.IsNullOrEmpty(PE.Discount6)) { L.Mes(Level.INFO, PE.Discount6); temp += PE.Discount6.Trim() + " |"; }

            L.Mes(Level.INFO, temp);
            if (temp.Length > 0)
                temp = temp.Remove(temp.Length - 1);
            return string.Format("[ {0} ]-[ {1} ] :[ {2} ]-[ {3} ]", PE.EventCode, PE.Name, temp, Quota.ToString());
            */
            return string.Format("[{0}][{1}][{2}]", PE.EventCode, PE.Name, Quota.ToString());
        }

        private Boolean CheckValidServiceCode(List<int> SPE, List<int> SIP)
        {
            return (SPE.Where(p => p != 0 && !SIP.Any(p2 => p2 == p)).Count() == 0);
        }

        /// <summary>
        ///  Validate CLKM TV
        /// </summary>
        /// <param name="PE"></param>
        /// <param name="CLKMTV"></param>
        /// <param name="Isprepaid"></param>
        /// <returns></returns>
        private int CheckApplyCLKMTV(int PE, int CLKMTV, Boolean Isprepaid)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@IsPrepaidTV", PE);
                    parameters.Add("@IPTVID", CLKMTV);
                    parameters.Add("@RowAffected", 0);

                    if (Isprepaid) parameters.Add("@ActionName", "GetInfoPrepaidTV");
                    else parameters.Add("@ActionName", "CheckInfoPostPaidTV");
                    res = connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, parameters, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return res;
        }

        private XElement CreateXMLRedeemRPcode(List<RPCodeRedeemModel> input, FoxGold eventLoy)
        {
            int PrepaidTimeTV;
            if (input[0].IsPrepaidTV > 0)
                PrepaidTimeTV = 1;
            else if (input[0].IsPrepaidTV < 0)
                PrepaidTimeTV = -1;
            else
                PrepaidTimeTV = 0;

            var xmlString = new XElement("N",
                new XElement("I",
                    new XElement("L", input[0].Location),
                    new XElement("B", input[0].BranchCode),
                    new XElement("D", input[0].Department),
                    new XElement("S", input[0].Salesman),

                    new XElement("C", input[0].VoucherCode),
                    new XElement("P", input[0].PromotionEventID),
                    new XElement("Or", input[0].OrderCode),

                    new XElement("Obj", input[0].ObjID),

                    new XElement("BNET", input[0].BNET),
                    new XElement("BTV", input[0].BTV),
                    new XElement("IsPrepaidTV", PrepaidTimeTV),

                    new XElement("Ac", input[0].ActiveChannel),

                    new XElement("Su", input[0].SubCompanyID),
                    new XElement("Lo", input[0].LocalTypeID),
                    new XElement("Pa", input[0].PaidTimeTypeID)
                )
            );

            xmlString.Add(
                new XElement("I",
                    new XElement("L", input[1].Location),
                    new XElement("B", input[1].BranchCode),
                    new XElement("D", input[1].Department),
                    new XElement("S", input[1].Salesman),

                    new XElement("C", input[1].VoucherCode),
                    new XElement("P", input[1].PromotionEventID),
                    new XElement("Or", input[1].OrderCode),

                    new XElement("BNET", input[1].BNET),
                    new XElement("BTV", input[1].BTV),
                    new XElement("IsPrepaidTV", input[1].IsPrepaidTV),

                    new XElement("Obj", input[1].ObjID),
                    new XElement("Ac", input[1].ActiveChannel),

                    new XElement("Su", input[1].SubCompanyID),
                    new XElement("Lo", input[1].LocalTypeID),
                    new XElement("Pa", input[1].PaidTimeTypeID)
                )
            );

            xmlString.Add(
                new XElement("I",
                    new XElement("L", input[1].Location),
                    new XElement("B", input[1].BranchCode),
                    new XElement("D", input[1].Department),
                    new XElement("S", input[1].Salesman),

                    new XElement("C", eventLoy.EventCode),
                    new XElement("P", eventLoy.ID),
                    new XElement("Or", input[1].OrderCode),

                    new XElement("BNET", input[1].BNET),
                    new XElement("BTV", input[1].BTV),
                    new XElement("IsPrepaidTV", input[1].IsPrepaidTV),

                    new XElement("Obj", input[1].ObjID),
                    new XElement("Ac", input[1].ActiveChannel),

                    new XElement("Su", input[1].SubCompanyID),
                    new XElement("Lo", input[1].LocalTypeID),
                    new XElement("Pa", input[1].PaidTimeTypeID)
                )
            );

            return xmlString;
        }

        /// <summary>
        /// caculate the money with conditon
        /// </summary>
        /// <param name="CondtionDiscount1"></param>
        /// <param name="ValueDiscount1"></param>
        /// <param name="ConditionDiscount2"></param>
        /// <param name="ValueDiscount2"></param>
        /// <returns></returns>
        private string TotalDiscountCLKM(bool CondtionDiscount1, int ValueDiscount1, bool ConditionDiscount2, int ValueDiscount2)
        {
            int value = 0;
            if (CondtionDiscount1) value += ValueDiscount1;
            if (ConditionDiscount2) value += ValueDiscount2;
            return value.ToString();
        }

        private MonthPromotionModel GetMonthPromotion(int ObjID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<MonthPromotionModel>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetMonthPromotion",
                    new { ObjID = ObjID },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private int GetPromotionNetByContainerID(int ContainerID, int AddMonth, int IsEvenMonth = 0)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPromotionNetByContainerID",
                    new { ContainerID = ContainerID, AddMonth = AddMonth, IsEvenMonth = IsEvenMonth },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        #endregion
    }
}
