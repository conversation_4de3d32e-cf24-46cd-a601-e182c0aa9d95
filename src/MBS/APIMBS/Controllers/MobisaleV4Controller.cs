using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using APIMBS.Models.MobileSaleV4;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using APIMBS.Service;
using APIMBS.Models;
using System.Xml.Linq;
using System.Text;
using APIMBS.Service.EvcPolicyServices;

namespace MobisaleV4.Controllers
{
    [Route("API/MBS-EVoucher-v4/SalePlatForm/{action}")]
    public class MobisaleV4Controller : ApiController
    {
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUserAndSaleClub)]
        public ResponseModels<Evoucher> GetListEvoucher(SalePlatformGetListVoucher input)
        {
            var logId = Guid.NewGuid();

            StringBuilder sb = new StringBuilder();
            sb.AppendLine("------------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher req", input));

            SalesManInfo Salesman = new SalesManInfo();

            if (input.channel.Equals(0))
            {
                Salesman = MBSv4GetListVoucher.GetInfoSalesMan(input.saleID);
                if (Salesman == null)
                {
                    return new ResponseModels<Evoucher> { data = null, error = "User không thể active dịch vụ", result = 0 };
                }
            }

            List<Evoucher> lstEvc=new List<Evoucher>();
            var res = new ResponseModels<Evoucher>();
            try
            {
                if (input.CustomerTypes.Count.Equals(0))
                {
                    return res;
                }
                List<ServicePlatform> lsp = input.Services.Where(x => MBSv4GetListVoucher.GetServiceCode(x.ServiceID).ToUpper() == "INT").ToList();
                int localtype = 0;
                if (lsp.Count > 0)
                {

                    foreach (var service in lsp)
                    {
                        foreach (var subServiceType in service.SubServiceTypes)
                        {
                            if (subServiceType.SubServiceTypeID == 1)
                            {
                                localtype = MBSv4GetListVoucher.GetLocaltype(lsp[0].SubServiceTypes[0].SubServices[0].SubServiceID);
                                break;
                            }
                        }
                        if (localtype > 0) break;
                    }
                    if (localtype == 0)
                    {
                        return new ResponseModels<Evoucher> { data = new List<Evoucher>(), error = "", result = 1 };
                    }
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher localtype", localtype));

                Tuple<int, int> prepaid_net_tv = MBSv4GetListVoucher.GetPrepaidtimeNetTV(input.Services);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher prepaid_net_tv", prepaid_net_tv));

                int PrepaidID = MBSv4GetListVoucher.GetPrepaidID(prepaid_net_tv.Item1, prepaid_net_tv.Item2);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher PrepaidID", PrepaidID));

                if (input.Services.Count > 0)
                {
                    #region Service
                    if (input.channel.Equals(0))
                    {
                        lstEvc = MBSv4GetListVoucher.GetListEvc(input, prepaid_net_tv, PrepaidID, localtype, logId, sb);
                    }
                    if (input.channel.Equals(1))
                    {
                        lstEvc = RegisterWebV5.GetListEvc(input, prepaid_net_tv, PrepaidID, localtype, logId, sb);
                    }
                    
                    #endregion
                }

                ////lst Qty
                //List<int> lstQty = new List<int>();
                //foreach (var product in input.Products)
                //{
                //    foreach (var SubServiceType in product.SubServiceTypes)
                //    {
                //        foreach (var SubService in SubServiceType.SubServices)
                //        {
                //            lstQty.Add(SubService.Qty);
                //        }
                //    }
                //}
                //MBSv4CommonService.WriteToLog(lstQty, "MBSv4 GetListEvoucher lstQty", logId);
                if (input.Products.Count > 0)
                {
                    var serviceIds = input.Products.Select(x => x.ServiceID).ToList();
                    var customerTypes = input.CustomerTypes.Where(x => serviceIds.Contains(x.ServiceID))
                        .Select(x => x.CustomerType).ToList();
                    List<EvoucherProduct> voucherProducts = new List<EvoucherProduct>();
                    voucherProducts = RegisterWebV5.GetDeviceVoucherList(input.Products, logId).ToList();
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher voucherProducts", voucherProducts));

                    List<Evoucher> vouchers = voucherProducts
                        .Where(x => x.InviteType == -1 && customerTypes.Contains(x.CustomerTypeId))
                        .GroupBy(x => new { x.VoucherCode, x.Note, x.Description, x.Todate })
                        .Select(x => new Evoucher() 
                        { 
                            VoucherCode = x.Key.VoucherCode, 
                            Description = x.Key.Description,
                            Note = x.Key.Note,
                            Todate = x.Key.Todate
                        })
                        .ToList();

                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher vouchers", vouchers));

                    var result = new List<Evoucher>();
                    
                    if (input.channel.Equals(0)) // MBS v4
                    {
                        vouchers.AsParallel().ForAll(voucher =>
                        {
                            var pe = voucherProducts.FirstOrDefault(x => x.VoucherCode == voucher.VoucherCode);
                            if (pe != null)
                            {
                                int quotaAvailable = MBSv4GetListVoucher.GetQuotaV2(pe.Id, input.saleID, localtype, PrepaidID);
                                int quotaKeep = MBSv4GetListVoucher.GetQuotaKeep(voucher.VoucherCode, input.saleID);

                                if ((quotaAvailable - quotaKeep) > 0)
                                {
                                    result.Add(voucher);
                                }
                            }
                        });
                    }

                    if (input.channel.Equals(1))
                    {
                        vouchers.AsParallel().ForAll(voucher =>
                        {
                            var pe = voucherProducts.FirstOrDefault(x => x.VoucherCode == voucher.VoucherCode);

                            if (pe != null)
                            {
                                int quotaAvailable = RegisterWebV5.GetQuotaUnuse(pe.Id, input.locationID, input.saleID, input.Userbranch, localtype, PrepaidID);
                                int quotaKeep = MBSv4GetListVoucher.GetQuotaKeepDKOL(input.Userbranch, input.locationID, input.saleID, voucher.VoucherCode);//

                                if ((quotaAvailable - quotaKeep) > 0)
                                {
                                    result.Add(voucher);
                                }
                            }
                        });
                    }
                    
                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher result", result));
                    lstEvc.AddRange(result);
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetListEvoucher Error", ex.Message));
                return new ResponseModels<Evoucher> { data =null,error=ex.Message,result=-1};
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "GetListEvoucher_string ", logId);
            }
            return new ResponseModels<Evoucher>
            {
                data = lstEvc,
                error = "",
                result = 1
            };
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUserAndSaleClub)]
        public ResponseModels<SalePlatformVoucherValueInfor> GetEvoucherInforV2(SalePlatformGetVoucherInfo input, Guid? parLogId = null)
        {
            Guid logId = parLogId == null ? Guid.NewGuid() : (Guid)parLogId;

            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input", input));

            var hasPromotionNet = false;
            var hasPromotionCamera = false;
            //MBSv4CommonService.WriteToLog(input, "MBSv4 GetEvoucherInfor res: ", logId);
            var errors = new List<string>();

            if (input.evoucherList.Count > 0)
            {
                // kiểm tra số lượng voucher hợp lệ
                bool isTrue = MBSv4GetListVoucher.CheckTrueRuleVoucher(input.evoucherList, input.channel);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("isTrue", isTrue));

                // kiểm tra voucher có trùng loại, ngoài trừ voucher tặng tiền trực tiếp
                bool isNotDup = MBSv4GetListVoucher.CheckDuplicateKindVoucher(input.evoucherList);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("isNotDup", isNotDup));

                if (!isTrue || !isNotDup)
                {
                    return new ResponseModels<SalePlatformVoucherValueInfor>
                    {
                        data = null,
                        error = "Phiếu mua hàng bạn đã nhập/ chọn vượt quá số lượng cho phép. Vui lòng kiểm tra lại",
                        result = 0
                    };
                }
            }

            var lstDataVoucher = new List<SalePlatformVoucherValueInfor>();
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lstDataVoucher", lstDataVoucher));

            List<ServicePlatform> lsp = input.Services
                .Where(x => MBSv4GetListVoucher.GetServiceCode(x.ServiceID).ToUpper() == "INT").ToList();
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("lsp", lsp));

            var localType = 0;

            if (lsp.Count > 0)
            {
                foreach (var service in lsp)
                {
                    if (service.SubServiceTypes.Any(subServiceType => subServiceType.SubServiceTypeID == 1))
                    {
                        localType = MBSv4GetListVoucher.GetLocaltype(lsp[0].SubServiceTypes[0].SubServices[0].SubServiceID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("localType", localType));
                    }

                    if (localType > 0) break;
                }

                if (localType == 0)
                {
                    errors.Add($"Phiếu mua hàng bạn đã nhập/ chọn không thể áp dụng. Vui lòng kiểm tra lại.");
                }
            }

            try
            {
                //if (input.channel.Equals(0))
                //{
                //    SalesManInfo salesMan = MBSv4GetListVoucher.GetInfoSalesMan(input.saleID);
                //    if (salesMan == null)
                //    {
                //        return new ResponseModels<SalePlatformVoucherValueInfor>
                //        { data = null, error = "Tài khoản salesman không thuộc đội KD, PP. Vui lòng liên hệ bộ phận Quản lý Chính sách.", result = 0 };
                //    }
                //}

                Tuple<int, int> prepaidNetTv = MBSv4GetListVoucher.GetPrepaidtimeNetTV(input.Services);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("prepaidNetTv", prepaidNetTv));

                bool isComboService = prepaidNetTv.Item1 > -1 && prepaidNetTv.Item2 > -1;
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("isComboService", isComboService));

                int prepaidId = MBSv4GetListVoucher.GetPrepaidID(prepaidNetTv.Item1, prepaidNetTv.Item2);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("prepaidId", prepaidId));

                List<EvoucherProduct> voucherProducts = RegisterWebV5.GetDeviceVoucherList(input.Products, logId).ToList();
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("voucherProducts", voucherProducts));

                var privateVoucherCode = input.evoucherList.FirstOrDefault(e => e.evoucherType == 2 && SalePlatformPrivateVoucherService.CheckPrefix(e.evoucherCode))?.evoucherCode;
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("privateVoucherCode", privateVoucherCode));

                var eventCode = SalePlatformPrivateVoucherService.GetEventCode(privateVoucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("eventCode", eventCode));

                if (!string.IsNullOrEmpty(privateVoucherCode))
                {
                    if (!string.IsNullOrEmpty(eventCode))
                    {
                        input.evoucherList = input.evoucherList
                            .Select(e => { if (e.evoucherCode == privateVoucherCode) { e.evoucherType = 1; e.evoucherCode = eventCode; } return e; })
                            .ToList();
                    } else
                    {
                        input.evoucherList = input.evoucherList
                            .Select(e => { if (e.evoucherCode == privateVoucherCode) { e.evoucherType = 1; } return e; })
                            .ToList();
                    }
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input.evoucherList", input.evoucherList));


                foreach (var voucher in input.evoucherList)
                {
                    if (voucherProducts.All(x => x.VoucherCode != voucher.evoucherCode))
                    {
                        #region NET và truyền hình
                        switch (voucher.evoucherType)
                        {
                            case 1: // voucher mã chung
                                bool checkVoucherGc = MBSv4GetListVoucher.CheckVoucherGC(voucher.evoucherCode);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("checkVoucherGc", checkVoucherGc));

                                if (!checkVoucherGc) continue;

                                SalePlatformVoucherValueInfor promotion = MBSv4GetPromotionInfo.GetVoucherGCinfo(
                                    input.Services, input.Products, voucher.evoucherCode, prepaidNetTv, localType, input.saleID,
                                    input.locationID, isComboService, input.channel, logId, sb, input.Userbranch);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("promotion", promotion));

                                if (promotion != null)
                                {
                                    if (promotion.evoucherCode == eventCode)
                                    {
                                        bool checkLocationRule = SalePlatformPrivateVoucherService.CheckLocationRule(eventCode, input.locationID);
                                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("checkLocationRule", checkLocationRule));

                                        if (checkLocationRule)
                                        {
                                            promotion.evoucherCode = privateVoucherCode;
                                        } else
                                        {
                                            errors.Add($"Ưu đãi không khả dụng tại khu vực của bạn.");
                                            continue;
                                        }
                                    }
                                    lstDataVoucher.Add(promotion);
                                }
                                else
                                {
                                    errors.Add($"Phiếu mua hàng bạn đã chọn/nhập {voucher.evoucherCode} không thể áp dụng. Vui lòng kiểm tra lại.");
                                    continue;
                                }
                                break;
                            case 2: // voucher mã nhập
                                if (input.channel.Equals(0))
                                {
                                    /*
                                    int CSIBB = ReferalProgramHiFPT.CheckRuleIBB(input.saleID);
                                    MBSv4CommonService.WriteToLog(CSIBB, " GetVoucherCampaignInfo CheckRuleIBB ", logId);
                                    if (CSIBB.Equals(0) && input.evoucherList.Where(x=>x.RefId == 2).Count() == 0)
                                    {
                                        errors.Add($"Tài khoản salesman không được phép tư vấn chính sách Giới thiệu bạn bè. Vui lòng liên hệ bộ phận Quản lý Chính sách.");
                                        continue;
                                    }
                                    */

                                    // Fix: chỉ cho áp GTBB từ KH vãng lai hoặc từ KHTN
                                    if (input.evoucherList.Where(x => x.RefId == 2).Count() == 0 && !EvoucherServices.CheckSaleTeamGTBB(input.saleTeamId))
                                    {
                                        errors.Add($"Tài khoản salesman không được phép tư vấn chính sách Giới thiệu bạn bè. Vui lòng liên hệ bộ phận Quản lý Chính sách.");
                                        continue;
                                    }
                                }

                                var countVc = input.evoucherList.Count(x => x.evoucherType == 2);
                                if (countVc > 1) continue;

                                bool checkVoucherRf = MBSv4GetListVoucher.CheckVoucherRF(voucher.evoucherCode, input.channel);
                                if (!checkVoucherRf)
                                {
                                    errors.Add($"Mã giới thiệu không tồn tại. Vui lòng kiểm tra lại. ");
                                    continue;
                                }

                                SalePlatformVoucherValueInfor voucherRf;
                                if (input.Userbranch != null && !string.IsNullOrEmpty(input.Userbranch.contractGT))
                                {
                                    if (!RegisterWebV5.CheckContractSupport(input.Userbranch.contractGT, voucher.evoucherCode))
                                    {
                                        errors.Add("Mã giới thiệu không khớp với thuê bao đăng ký hộ.");
                                        continue;
                                    }

                                    voucherRf = MBSv4GetListVoucher.GetVoucherCampaignInfo(input.Services,
                                        voucher.evoucherCode, localType, input.channel, prepaidNetTv, 0, logId);
                                }
                                else
                                {
                                    voucherRf = MBSv4GetListVoucher.GetVoucherCampaignInfo(input.Services,
                                        voucher.evoucherCode, localType, input.channel, prepaidNetTv, 0, logId);
                                }

                                if (voucherRf != null)
                                {
                                    lstDataVoucher.Add(voucherRf);
                                }

                                if (lstDataVoucher.Any())
                                {
                                    hasPromotionNet = true;
                                }
                                
                                break;
                        }
                        #endregion
                    }
                    #region Thiết bị
                    if (input.Products.Count <= 0 || !voucherProducts.Any()) continue;

                    var numberOfCurrentPromotion = lstDataVoucher.Count;
                    switch (voucher.evoucherType)
                    {
                        case 1: //Voucher chung
                        {
                            //Lấy loại khách hàng
                            var serviceIds = input.Products.Select(x => x.ServiceID).ToList();
                            var customerTypes = input.CustomerTypes.Where(x => serviceIds.Contains(x.ServiceID))
                                .Select(x => x.CustomerType).ToList();

                            var generalProductVouchers = voucherProducts.Where(x =>
                                x.VoucherCode == voucher.evoucherCode && x.InviteType == -1 &&
                                customerTypes.Contains(x.CustomerTypeId)).ToList();
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetEvoucherInfor generalProductVoucher", generalProductVouchers));

                            if (generalProductVouchers.Count <= 0) continue;
                            
                            int quotaAvailable = MBSv4GetListVoucher.GetQuotaV2(generalProductVouchers[0].Id, input.saleID, 0, 0);
                            int quotaKeep = MBSv4GetListVoucher.GetQuotaKeep(voucher.evoucherCode, input.saleID);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetEvoucherInfor ProductVoucher quota", quotaAvailable - quotaKeep));
                            
                            if ((quotaAvailable - quotaKeep) > 0)
                            {
                                SalePlatformVoucherValueInfor cameraPromotion =
                                    RegisterWebV5.GetVoucherProductInfo(generalProductVouchers, input.Products,
                                        quotaAvailable - quotaKeep);
                                if (cameraPromotion != null)
                                {
                                    lstDataVoucher.Add(cameraPromotion);
                                }
                            }
                            else
                            {
                                errors.Add($"Voucher bạn đã chọn/nhập {voucher.evoucherCode} không thể áp dụng. Vui lòng kiểm tra lại.");
                            }

                            break;
                        }
                        case 2: //Voucher giới thiệu
                        {
                                if (input.channel.Equals(0))
                                {
                                    /*
                                    int CSIBB = ReferalProgramHiFPT.CheckRuleIBB(input.saleID);
                                    MBSv4CommonService.WriteToLog(CSIBB, " GetVoucherCampaignInfo CheckRuleIBB ", logId);
                                    if (CSIBB.Equals(0) && input.evoucherList.Where(x => x.RefId == 2).Count() == 0)
                                    {
                                        errors.Add($"Tài khoản salesman không được phép tư vấn chính sách Giới thiệu bạn bè. Vui lòng liên hệ bộ phận Quản lý Chính sách.");
                                        continue;
                                    }
                                    */

                                    // Fix: chỉ cho áp GTBB từ KH vãng lai hoặc từ KHTN
                                    if (input.evoucherList.Where(x => x.RefId == 2).Count() == 0 && !EvoucherServices.CheckSaleTeamGTBB(input.saleTeamId))
                                    {
                                        errors.Add($"Tài khoản salesman không được phép tư vấn chính sách Giới thiệu bạn bè. Vui lòng liên hệ bộ phận Quản lý Chính sách.");
                                        continue;
                                    }
                                }
                                var referralProductVouchers = voucherProducts.Where(x => x.InviteType == 5).ToList();
                                if (referralProductVouchers.Count == 0) continue;
                                MBSv4CommonService.WriteToLog(referralProductVouchers, "MBSv4 GetEvoucherInfor referralProductVouchers: ", logId);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetEvoucherInfor referralProductVouchers", referralProductVouchers));

                                SalePlatformVoucherValueInfor referralCameraPromotion = RegisterWebV5.GetReferralCameraPromotion(sb, input.Products,
                                    referralProductVouchers, voucher.evoucherCode, logId);
                                if (referralCameraPromotion != null)
                                {
                                    lstDataVoucher.Add(referralCameraPromotion);
                                }

                                if (lstDataVoucher.Count() > numberOfCurrentPromotion)
                                {
                                    hasPromotionCamera = true;
                                }

                                //Nếu giới thiệu bạn bè có ưu đãi của cả net và camera thì clear ưu đãi
                                if (hasPromotionNet && hasPromotionCamera)
                                {
                                    lstDataVoucher = lstDataVoucher.Take(lstDataVoucher.Count - 2).ToList();
                                    errors.Add($"Phiếu mua hàng bạn đã chọn/nhập {voucher.evoucherCode} không thể áp dụng. Vui lòng kiểm tra lại.");
                                }

                                break;
                        }
                    }
                    
                    #endregion
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 GetEvoucherInfor ERROR", ex.Message));
                return new ResponseModels<SalePlatformVoucherValueInfor>
                {
                    data = null,
                    error = ex.Message,
                    result = -1
                };
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "GetEvoucherInforV2_string ", logId);
            }
            
            if (errors.Count > 0 && lstDataVoucher.Count == 0)
            {
                return new ResponseModels<SalePlatformVoucherValueInfor>()
                {
                    data = null,
                    error = errors[0],
                    result = 0
                };
            }

            var listVoucherError = lstDataVoucher.Where(x => x.Apply.Count == 0).Select(x => x.evoucherCode).ToArray();
            //Clearing element that applies count is zero
            lstDataVoucher.RemoveAll(x => x.Apply.Count == 0);
            
            if (lstDataVoucher.Count == 0)
            {
                return new ResponseModels<SalePlatformVoucherValueInfor>()
                {
                    data = null,
                    error = $"Voucher bạn đã chọn/nhập {string.Join(", ", listVoucherError)} không thể áp dụng. Vui lòng kiểm tra lại.",
                    result = 0
                };
            }

            return new ResponseModels<SalePlatformVoucherValueInfor>
            {
                data = lstDataVoucher,
                error = "",
                result = 1
            };
        }
        
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUserAndSaleClub)]
        public ResponseModel<bool> CheckVoucher(EvoucherInput input)
        {
            var logId = Guid.NewGuid();
            MBSv4CommonService.WriteToLog(input, "MBSv4 CheckVoucher res: ", logId);
            bool trueVoucher = false;
            int result = 0;
            try
            {
                if (input.channel.Equals(1))
                {
                    if (input.evoucherType.Equals(1))
                    {
                        trueVoucher = RegisterWebV5.CheckVoucherGC(input.evoucherCode);
                        result = 1;
                    }
                    if (input.evoucherType.Equals(2))
                    {
                        if (SalePlatformPrivateVoucherService.CheckPrefix(input.evoucherCode))
                        {
                            var eventCode = SalePlatformPrivateVoucherService.GetEventCode(input.evoucherCode);
                            if (!string.IsNullOrEmpty(eventCode))
                            {
                                trueVoucher = true;
                                result = 1;
                            }
                        }
                        else
                        {
                            trueVoucher = RegisterWebV5.CheckVoucherRF(input.evoucherCode);
                            result = 1;
                        }
                    }
                    return new ResponseModel<bool>
                    {
                        data = trueVoucher,
                        error = "",
                        result = result
                    };
                }
                if (input.evoucherType.Equals(1))
                {
                    trueVoucher = MBSv4GetListVoucher.CheckVoucherGC(input.evoucherCode);
                    result = 1;
                }
                if (input.evoucherType.Equals(2))
                {
                    if (SalePlatformPrivateVoucherService.CheckPrefix(input.evoucherCode))
                    {
                        var eventCode = SalePlatformPrivateVoucherService.GetEventCode(input.evoucherCode);
                        if (!string.IsNullOrEmpty(eventCode))
                        {
                            trueVoucher = true;
                            result = 1;
                        }
                    } else
                    {
                        trueVoucher = MBSv4GetListVoucher.CheckVoucherRF(input.evoucherCode, input.channel);
                        result = 1;
                    }
                }
            }
            catch (Exception ex)
            {
                MBSv4CommonService.WriteToLog(ex.Message, "MBSv4 CheckVoucher Error: ", logId);
                return new ResponseModel<bool>
                {
                    data = false,
                    error = ex.Message,
                    result = -1
                };
                
            }
            return new ResponseModel<bool>
            {
                data = trueVoucher,
                error = "",
                result = result
            };
        }
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUserAndSaleClub)]
        public ResponseModel<bool> KeepVoucher(SalePlatformRedeemVoucher input)
        {
            var logId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher res", input));

            bool res = false;
            int result = 0;
            try
            {
                var privateVoucherCode = input.evoucherList.FirstOrDefault(e => e.evoucherType == 2 && SalePlatformPrivateVoucherService.CheckPrefix(e.evoucherCode))?.evoucherCode;
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("privateVoucherCode", privateVoucherCode));

                var eventCode = SalePlatformPrivateVoucherService.GetEventCode(privateVoucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("eventCode", eventCode));

                if (privateVoucherCode != null)
                {
                    if (!string.IsNullOrEmpty(eventCode))
                    {
                        input.evoucherList = input.evoucherList
                            .Select(e => { if (e.evoucherCode == privateVoucherCode) { e.evoucherType = 1; e.evoucherCode = eventCode; } return e; })
                            .ToList();
                    }
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input.evoucherList", input.evoucherList));

                //xác định số tháng trả trước
                List<EvoucherInput> lstEVCinput = input.evoucherList.Where(a => a.evoucherType == 1).ToList();
                List<EvoucherInput> lstRFinput = input.evoucherList.Where(a => a.evoucherType == 2).ToList();
                Tuple<int, int> prepaid_net_tv = MBSv4GetListVoucher.GetPrepaidtimeNetTV(input.Services);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher prepaid_net_tv", prepaid_net_tv));

                // xác đinh localtype
                List<ServicePlatform> lsp = input.Services.Where(x => MBSv4GetListVoucher.GetServiceCode(x.ServiceID).ToUpper() == "INT").ToList();
                int localtype = 0;
                if (lsp.Count > 0)
                {
                    localtype = MBSv4GetListVoucher.GetLocaltype(lsp[0].SubServiceTypes[0].SubServices[0].SubServiceID);
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher localtype", localtype));
                
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        //lấy serviceid, subservicetypeid theo voucher (dành cho service)
                        List<EvoucherService> lstEVCserviceNetTV = new List<EvoucherService>();
                        //lấy serviceid, subservicetypeid theo dịch vụ đăng ký
                        List<ServicePlatform> lserNetTV = new List<ServicePlatform>();

                        //lấy serviceid, subservicetypeid theo thiết bị đăng ký
                        List<DeviceModel> lstDeviceCMR = new List<DeviceModel>();

                        //lấy serviceid, subservicetypeid theo voucher (dành cho product)
                        List<EvoucherService> lstEVCserviceCMR = new List<EvoucherService>();

                        if (input.Services.Count > 0)
                        {
                            if (prepaid_net_tv.Item1 > -1 || prepaid_net_tv.Item2 >-1)
                            {
                                // xác định voucher thuộc service và subtype
                                lstEVCserviceNetTV = MBSv4GetListVoucher.getListEvcService(input.Services, logId, input.channel, lstEVCinput, lstRFinput, localtype, prepaid_net_tv);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher lstEVC", lstEVCserviceNetTV));

                                // lấy service iptv và net
                                lserNetTV = MBSv4GetListVoucher.FillerServiceTVNET(input.Services);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher lserNet", lserNetTV));
                            }
                        }
                        if (input.Products.Count>0)
                        {
                            lstDeviceCMR = MBSv4GetListVoucher.GetListDeviceCam(input.Products);
                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher lstDeviceCMR", lstDeviceCMR));

                            if (lstDeviceCMR.Count > 0)
                            {
                                // lấy service camera
                                lstEVCserviceCMR = RegisterWebV5.getListEvcServiceProduct(logId, input.Products, input.channel, lstEVCinput, lstRFinput, lstDeviceCMR);
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher lstEVCserviceCMR", lstEVCserviceCMR));
                            }
                        }
                        if (!string.IsNullOrEmpty(privateVoucherCode))
                        {
                            lstEVCserviceNetTV = lstEVCserviceNetTV.Select(s => { if (s.evoucherCode == eventCode) { s.evoucherCode = privateVoucherCode; s.evoucherType = 2; }; return s; }).ToList();
                        }
                        //kiểm tra + lưu, lấy lần lưu mới nhất, các lân trước update status =4
                        res = MBSv4GetListVoucher.SaveHistoryTem(connection, transaction, input.objId, input.OrderCode, input.saleID, input.CustomerTypes, input.fptplayCustomerPhone,input.paymentTypeL2, 
                                                                 lserNetTV, lstEVCserviceNetTV, lstDeviceCMR, lstEVCserviceCMR,input.Userbranch,input.channel,input.locationID);
                        sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 KeepVoucher SaveHistoryTem", res));

                        if (res)
                        {
                            transaction.Commit();
                            result = 1;
                        }
                            
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("KeepVoucher error", ex.Message));
                return new ResponseModel<bool>
                {
                    data = false,
                    error = ex.Message,
                    result = -1
                };
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "keepVoucher_string: ", logId);
            }

            return new ResponseModel<bool>
            {
                data = res,
                error = "",
                result = result
            };
        }
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUserAndSaleClub)]
        public ResponseModel<bool> RedeemVoucher(SalePlatformRedeemVoucher input, Guid? parLogId = null)
        {
            Guid logId = parLogId == null ? Guid.NewGuid() : (Guid)parLogId;

            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher req", input));

            bool status = false;
            int result = 0;
            string error="";
            var res = new ResponseModel<bool> { data = status, error = error, result = result };

            try
            {
                var privateVoucherCode = input.evoucherList.FirstOrDefault(e => e.evoucherType == 2 && SalePlatformPrivateVoucherService.CheckPrefix(e.evoucherCode))?.evoucherCode;
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("privateVoucherCode", privateVoucherCode));

                var eventCode = SalePlatformPrivateVoucherService.GetEventCode(privateVoucherCode);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("eventCode", eventCode));

                if (privateVoucherCode != null)
                {
                    if (!string.IsNullOrEmpty(eventCode))
                    {
                        input.evoucherList = input.evoucherList
                            .Select(e => { if (e.evoucherCode == privateVoucherCode) { e.evoucherType = 1; e.evoucherCode = eventCode; } return e; })
                            .ToList();
                    }
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("input.evoucherList", input.evoucherList));

                // lấy thông tin saleman
                SalesManInfo Salesman = MBSv4GetListVoucher.GetInfoSalesMan(input.saleID);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher Salesman", Salesman));

                if (Salesman == null && input.channel.Equals(0))
                {
                    return new ResponseModel<bool> { data = false, error = "Thông tin SaleMan không tồn tại", result = 0 };
                }
                //lọc voucher mã chung và voucher RF
                List<EvoucherInput> lstEVCinput = input.evoucherList.Where(a => a.evoucherType == 1).ToList();
                List<EvoucherInput> lstRFinput = input.evoucherList.Where(a => a.evoucherType == 2).ToList();

                //xác định loại gói thanh toán
                Tuple<int, int> prepaid_net_tv = MBSv4GetListVoucher.GetPrepaidtimeNetTV(input.Services);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher prepaid_net_tv", prepaid_net_tv));

                // xác đinh localtype
                List<ServicePlatform> lsp = input.Services.Where(x => MBSv4GetListVoucher.GetServiceCode(x.ServiceID).ToUpper() == "INT").ToList();

                int localtype = 0;

                if (lsp.Count > 0)
                {
                    localtype = MBSv4GetListVoucher.GetLocaltype(lsp[0].SubServiceTypes[0].SubServices[0].SubServiceID);
                }
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher localtype", localtype));

                // xác định voucher thuộc service và subtype
                List<DeviceModel> devices = MBSv4GetListVoucher.GetListDeviceCam(input.Products);
                List<EvoucherService> lstEVC = MBSv4GetListVoucher.getListEvcService(input.Services, logId, input.channel, lstEVCinput, lstRFinput, localtype, prepaid_net_tv, devices);
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher lstEVC", lstEVC));

                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        string contractSuport = string.Empty;
                        if (input.Userbranch != null)
                        {
                            contractSuport = input.Userbranch.contractGT;
                        }
                        
                        List<ServicePlatform> lser = MBSv4GetListVoucher.FillerServiceTVNET(input.Services);
                        // kiểm tra đã keep voucher
                        bool check = MBSv4GetListVoucher.CheckKeepHistoryTem(connection, transaction, lstEVC, input.objId, input.OrderCode, input.saleID, lser, input.CustomerTypes, input.channel);
                        if (!check)
                        {
                            result = 0;
                            error = "Có voucher chưa keep.";
                            new ResponseModel<bool> { data = false, error = error, result = result };
                        }
                        #region Phần service
                        if (input.Services.Count > 0)
                        {
                            // chỉ lấy các service iptv và net
                            foreach (var service in input.Services)
                            {
                                List<EvoucherService> lstEVCservice = lstEVC.Where(x => x.ServiceID == service.ServiceID).ToList();
                                foreach (var item in service.SubServiceTypes)
                                {
                                    List<EvoucherService> lstEVCserviceType = lstEVCservice.Where(x => x.ServiceID == service.ServiceID
                                                                                                    && x.SubServiceType == item.SubServiceTypeID).ToList();
                                    foreach (var evc in lstEVCserviceType)
                                    {
                                        if (evc.evoucherType.Equals(1)) // voucher chung
                                        {
                                            #region voucher chung
                                            bool isAdd = MBSv4GetListVoucher.CheckAddVoucherGCCode(connection, transaction, evc.evoucherCode, input.objId, input.RegCode);
                                            if (!isAdd)
                                            {
                                                status = MBSv4RedeemVoucherGC.RedeemVoucherGC(connection, transaction, evc.evoucherCode,
                                                input.objId, input.OrderCode, prepaid_net_tv, input.saleID, Salesman, localtype, input.RegCode, input.channel,input.locationID, logId, sb,input.Userbranch);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherGC: " + evc.evoucherCode, status));

                                                if (!status)
                                                    break;
                                                else
                                                {
                                                    if (evc.evoucherCode == eventCode)
                                                    {
                                                        evc.evoucherCode = privateVoucherCode;
                                                        MBSv4RedeemVoucherGC.UpdateStatusPrivateCode(connection, transaction, privateVoucherCode, input.objId);
                                                    }
                                                    bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID,input.RegCode);
                                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherGC updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                                }
                                            }
                                            else
                                            {
                                                if (evc.evoucherCode == eventCode)
                                                {
                                                    evc.evoucherCode = privateVoucherCode;
                                                }
                                                bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherGC updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                            }
                                            #endregion
                                        }
                                        if (evc.evoucherType.Equals(2)) // voucher lẻ GTBB
                                        {
                                            bool isRedeem = MBSv4GetListVoucher.CheckRedeemRFCode(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode);
                                            if (!isRedeem)
                                            {                                                
                                                status = MBSv4RedeemVoucherAdvocacyProgram.RedeemVoucherCampaign(connection, transaction, evc.evoucherCode, input.objId,
                                                        input.OrderCode, prepaid_net_tv, input.saleID, Salesman, input.channel, localtype, input.RegCode, contractSuport,null, logId, sb);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF SaveHistoryTem: " + evc.evoucherCode, status));

                                                if (!status)
                                                    break;
                                                else
                                                {
                                                    bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                                }
                                            }
                                            else
                                            {
                                                bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                            }
                                        }
                                    }
                                    if (!status)
                                        break;
                                }
                                if (!status)
                                    break;
                            }
                        }
                        
                        #endregion 

                        #region phần thiết bị lẻ
                        if (input.Products.Count > 0)
                        {
                            
                            foreach (var product in input.Products)
                            {
                                List<EvoucherService> lstEVCservice = lstEVC.Where(x => x.ServiceID == product.ServiceID).ToList();
                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct lstEVCservice", lstEVCservice));

                                if (lstEVCservice.Count == 0) continue;
                                
                                foreach (var item in product.SubServiceTypes)
                                {
                                    List<EvoucherService> lstEVCserviceType = lstEVCservice.Where(x => x.ServiceID == product.ServiceID
                                                                                                    && x.SubServiceType == item.SubServiceTypeId).ToList();
                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct lstEVCserviceType", lstEVCserviceType));

                                    if (lstEVCserviceType.Count == 0) continue;

                                    foreach (var evc in lstEVCserviceType)
                                    {
                                        if (evc.evoucherType.Equals(1)) // voucher chung
                                        {
                                            #region voucher chung
                                            bool hasAdded = MBSv4GetListVoucher.CheckAddVoucherGCCode(connection, transaction, evc.evoucherCode, input.objId, input.RegCode);
                                            sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct hasAdded", hasAdded));

                                            if (!hasAdded)
                                            {
                                                var customerType = input.CustomerTypes.First(x => x.ServiceID == evc.ServiceID);
                                                status = MBSv4RedeemVoucherGC.RedeemVoucherProduct(connection, transaction, evc.evoucherCode,
                                                    input.objId, input.saleID, Salesman, localtype, input.RegCode, input.channel, input.locationID,
                                                    logId, input.Products, input.Userbranch, customerType.CustomerType, sb);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct status", status));

                                                if (status)
                                                {
                                                    bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct updateHistoryTem", updateHistoryTem));
                                                }
                                            }
                                            else
                                            {
                                                bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherProduct updateHistoryTem", updateHistoryTem));
                                            }
                                            #endregion
                                        }
                                        if (evc.evoucherType.Equals(2)) // voucher lẻ GTBB
                                        {
                                            bool isRedeem = MBSv4GetListVoucher.CheckRedeemRFCode(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode);
                                            if (!isRedeem)
                                            {
                                                status = MBSv4RedeemVoucherAdvocacyProgram.RedeemVoucherCampaign(connection, transaction, evc.evoucherCode, input.objId,
                                                        input.OrderCode, prepaid_net_tv, input.saleID, Salesman, input.channel, localtype, input.RegCode, contractSuport, devices, logId, sb);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF SaveHistoryTem: " + evc.evoucherCode, status));
                                                if (!status)
                                                    break;
                                                else
                                                {
                                                    bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                                    sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                                }
                                            }
                                            else
                                            {
                                                bool updateHistoryTem = MBSv4GetListVoucher.UpdateHistoryTem(connection, transaction, evc.evoucherCode, input.objId, input.OrderCode, evc.ServiceID, input.RegCode);
                                                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucherRF updateHistoryTem: " + evc.evoucherCode, updateHistoryTem));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        #endregion
                        if (status)
                        {
                            transaction.Commit();
                            res.data = status;
                            res.result = 1;
                        }
                        res.data = status;
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(MBSv4GetPromotionInfo.JoinStringToWriteLog("MBSv4 RedeemVoucher Error", ex.Message));
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            finally
            {
                MBSv4CommonService.WriteToLogDontSerializeObject(sb, "MBS_RedeemVoucher_String: ", logId);
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUserAndSaleClub)]
        public ResponseModels<MonthVoucherOutput> GetMonthVoucher(MonthVoucherInput input)
        {
            var logId = Guid.NewGuid();
            MBSv4CommonService.WriteToLog(input, "MBSv4 GetMonthVoucher req: ", logId);
            
            string error = string.Empty;
            var res = new ResponseModels<MonthVoucherOutput> { data = new List<MonthVoucherOutput>(), error = error, result = 0 };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    foreach (string voucher in input.evoucherCodes)
                    {


                        MonthVoucherOutput month = new MonthVoucherOutput();
                        month.evoucherCode = voucher;
                        month.countMonth = MBSv4GetPromotionInfo.GetDisMounth(connection, voucher);
                        if (month.countMonth == 0)
                        {
                            VoucherInfor PE = connection.Query<VoucherInfor>(MBSv4GetListVoucher.OS6_FPTVoucher_SalePlatform_MBSv4, new
                            {
                                ActionName = "GetPEByCode",
                                voucherCode = voucher
                            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                            month.countMonth = MBSv4GetPromotionInfo.GetDisMounthIPTV(PE.IPTVPromotionID);
                        }
                        res.data.Add(month);
                    }
                    MBSv4CommonService.WriteToLog(res.data, "MBSv4 GetMonthVoucher res.data: ", logId);
                    res.result = 1;
                }
            }
            catch (Exception ex)
            {
                return new ResponseModels<MonthVoucherOutput> { data = null, error = ex.Message, result = -1 };
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SuperUserAndSaleClub)]
        public ResponseModel<bool> RemoveQuotakeepForSaleman(RemoveQuotakeepForSalemanInput input)
        {
            var logId = Guid.NewGuid();
            MBSv4CommonService.WriteToLog(input, "MBSv4 RemoveQuotakeepForSaleman res: ", logId);
            try
            {
                int isExcu = 0;
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        isExcu = connection.Execute(MBSv4GetListVoucher.OS6_FPTVoucher_RemoveQuotakeepForSaleman, new
                        {
                            action = "RemoveQuotakeepForSaleman",
                            saleID = input.SaleID,
                            orderCode = input.OrderCode
                        }, transaction: transaction, commandType: CommandType.StoredProcedure);
                        transaction.Commit();
                    }
                }
                if (isExcu > 0)
                {
                    return new ResponseModel<bool>
                    {
                        data = true,
                        error = "",
                        result = 1
                    };
                }
                else
                {
                    return new ResponseModel<bool>
                    {
                        data = false,
                        error = "Không thể gỡ đơn hàng đã redeem!",
                        result = 0
                    };
                }
            }
            catch (Exception ex)
            {
                MBSv4CommonService.WriteToLog(ex.Message, "MBSv4 RemoveQuotakeepForSaleman Error: ", logId);
                return new ResponseModel<bool>
                {
                    data = false,
                    error = ex.Message,
                    result = -1
                };

            }
        }
    }
}
