using CheckPriorityForTINPNC.Models;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;

namespace CheckPriorityForTINPNC.Controllers
{
    public class CheckPriorityForTINPNCController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_Referral_CheckPriorityForTINPNC";
        [Route("API/ReferralProgram/CheckPriorityForTINPNC")]
        public ResponseModel post(CheckPriorityParam input)
        {
            var error = "";
            var _reponse = new List<object>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    _reponse = connection.Query(sp, new { ObjID = input.ObjID }, commandType: CommandType.StoredProcedure).ToList();
                }
            }
            catch(Exception e) { error = e.Message; }
            return new ResponseModel() { data = _reponse, error = error, result = 1 };
        }
    }
}
