using CheckPriorityForTINPNC.Models;
using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Web.Http;
using Voucher.APIHelper;

namespace HistoryPromotion.Controllers
{
    public class HistoryPromotionController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_FPTVoucher_PromotionInfo_Obj";
        [HttpGet]
        [Route("API/ReferralProgram/HistoryPromotion")]
        public ResponseModel<HistoryPromotionModel> get(int objid)
        {
            var error = "";
            var _reponse = new HistoryPromotionModel();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    _reponse = connection.Query<HistoryPromotionModel>(sp, new { @objid = objid }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            catch(Exception e) { error = e.Message; }
            return new ResponseModel<HistoryPromotionModel>() { data = _reponse, error = error, result = 1 };
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.ReportGTBB)]
        [Route("API/ReferralProgram/Export/ToTINPNC")]
        public ResponseModels<ReportTINPNC> ReportTINPNC(ReportTINPNC_Request input)
        {
            var sb = new StringBuilder();
            var data = new List<ReportTINPNC>();
            var err = string.Empty;
            int result = 0;

            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("input", input));

            try
            {
                using (var conn = new SqlConnection(SqlHelper.ConnRead()))
                {
                    DateTime startTimeApi = DateTime.Now;
                    data = conn.Query<ReportTINPNC>("PowerInside..OS6_ReferalProgramToTINPNC", new
                    {
                        fromdate = input.Fromdate,
                        todate = input.Todate,
                        typeSearch = input.TypeSearch
                    },commandType: CommandType.StoredProcedure).ToList();
                    
                    DateTime endTimeApi = DateTime.Now;
                    TimeSpan totalTimeApi = endTimeApi - startTimeApi;
                    if (totalTimeApi.TotalSeconds >= 10)
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("ReportTINPNC warning timeout", totalTimeApi.TotalSeconds));

                    if (data?.Any() == true)
                    {
                        result = 1;
                    }
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Count", data?.Count));
                }
            }
            catch (Exception e)
            {
                err = e.Message;
                result = -1;
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Exception", e));
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "ReportTINPNC", "");
            }

            return new ResponseModels<ReportTINPNC>() { data = data, error = err, result = result};
        }
    }
}
