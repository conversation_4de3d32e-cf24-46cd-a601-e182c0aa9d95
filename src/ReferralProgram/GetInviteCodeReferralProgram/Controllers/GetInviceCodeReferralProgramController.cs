
using GetInviteCodeReferralProgram.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;

namespace GetInviteCodeReferralProgram.Controllers
{
    public class GetInviceCodeReferralProgramController : ApiController
    {
        [Route("API/ReferralProgram/GetInviceCode")]
        public ResponseModel<InfoUserResult> post(InfoUserRequest input)
        {
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "GetInviceCode");
            if (input.ObjID == 0) { return new ResponseModel<InfoUserResult> { data = null, error = null, result = 0 }; };
            const string sp = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";
            InfoUserResult ResultData = new InfoUserResult();
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    int CheckValidObjID = connection.Query<int>(sp, new
                    {
                        ActionName = "CheckValidObjectID",
                        ID = input.ObjID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (CheckValidObjID == 0)
                        return new ResponseModel<InfoUserResult>() { data = null, error = "Mã ObjID Không Tồn Tại", result = 0 };

                    ResultData = connection.Query<InfoUserResult>(sp, new
                    {
                        ActionName = "GetInviteCode",
                        ObjID = input.ObjID,
                        RowAffected = 0,
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (ResultData == null)
                    {
                        return new ResponseModel<InfoUserResult>() { data = null, error = "Thông Tin Invite Không Tồn Tại", result = 0 };
                    }
                    transaction.Commit();
                }
            }
            L.Mes(Level.RESPONSE, JsonConvert.SerializeObject(ResultData));
            L.Mes(Level.ENDREQUEST, null, "GetInviceCode");
            return new ResponseModel<InfoUserResult>() { data = ResultData, error = null, result = 1 };
        }
    }
}
