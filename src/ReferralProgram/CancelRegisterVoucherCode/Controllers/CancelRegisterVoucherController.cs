using CancelRegisterVoucherCode.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;

namespace CancelRegisterVoucherCode.Controllers
{
    public class CancelRegisterVoucherController : ApiController
    {

        const string sp = "PowerInside.dbo.OS6_FPTVoucher_HistotyTempInfo";
        [Route("API/ReferralProgram/CancelRegisterVoucherCode")]
        [Authorize(Roles = AuthorizeRole.Dkol)]
        public ResponseModel<Boolean> post(CancelRegisterCodeRequest input)
        {
            int status = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    status  = connection.Query<int>(sp, new
                    {
                        ActionName = "DeleteHistoryTemp",
                        VoucherCode = input.VoucherCode,
                        OrderCode = input.OrderCode,
                        RowAffected = 0

                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (status == 0)
                        return new ResponseModel<Boolean>() { data = false, error = null, result = 1 };
                    transaction.Commit();
                }
            }
            return new ResponseModel<bool>() { data = (status > 0), error = null, result = 1 };
        }
    }
}
