using Dapper;
using GetPromotionEventByCode.Constant;
using GetPromotionEventByCode.Models;
using GetPromotionEventByCode.Models.AdvocacyProgram;
using GetPromotionEventByCode.Service;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Http;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;

namespace GetPromotionEventByCode.Controllers
{
    public class GetPromotionEventByCodeController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";

        [Route("API/ReferralProgram/GetInfoPromotionEvent")]
        [Authorize(Roles = AuthorizeRole.Dkol)]
        public ResponseModel<InfoVoucherResponse> post(InfoVoucherRequest input)
        {
            if (input.Cameras == null) input.Cameras = new List<Camera>();
            try
            {
                var logId = Guid.NewGuid();

                ResponseModel<InfoVoucherResponse> result = new ResponseModel<InfoVoucherResponse>()
                {
                    data = new InfoVoucherResponse() { PromotionEvent = null, Type = 0 },
                    error = null,
                    result = 0
                };
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "GetInfoPromotionEvent req: ");

                if (!RequestValidate(input))
                {
                    result.error = "Dữ liệu không hợp lệ";
                    return result;
                }    

                //check rule code
                bool isTrueRule = Common.checkRuleApplyVoucher(input.OrderCode, input.VoucherCode);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(isTrueRule), "GetInfoPromotionEvent checkRuleApplyVoucher: ");

                if (!isTrueRule)
                {
                    result.error = "Mã sử dụng không chính xác";
                    return result;
                }

                // RULE add Rp code     :  1 mã RP chỉ đi với 1 Phiếu DK(OrderCode)
                // RULE add VoucherCode :  nhiều mã VoucherCode có thể add với 1 Phiếu DK (OrderCode) nhưng hiện tại chỉ cho add 1
                if (CheckAddedCode(input.OrderCode, input.VoucherCode))
                {
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(""), "GetInfoPromotionEvent CheckAddedCode: Mã Đã Được Gán Với Đơn Hàng Hiện Tại");

                    result.error = "Mã Đã Được Gán Với Đơn Hàng Hiện Tại";
                    return result;
                }

                var condition = GetPrepaidConditon(input.NETID, input.IPTVID);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(condition), "GetInfoPromotionEvent GetPrepaidConditon: ");

                WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now)
                    + "DKOL_GetInfoPromotionEvent condition" + JsonConvert.SerializeObject(condition));
                // check exit prepaid
                // -100 không có data với ID CLKM nền, 0 Có nhưng trả sau, > 0 trà trước theo số tháng output
                if (condition == null || (condition.MonthQuantity == -100 && input.PrepaidTimeTV == 1) || (condition.PrepaidTime == -100 && input.NETID > 1))
                {
                    result.error = string.Format("Preapid Info {0} - {1} - {2}", condition.PrepaidTime, condition.MonthQuantity, condition.DLSType);
                    return result;
                }

                // convert input
                if (input.NETID == -1) input.PrepaidTimeNet = -1;
                if (input.NETID != -1 && input.NETID != 0) input.PrepaidTimeNet = condition.PrepaidTime;
                if (input.IPTVID != -1 && input.IPTVID != 0) input.PrepaidTimeTV = condition.MonthQuantity;



                //Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "CheckAddedCode");

                //check is exits PaidTimeType | if exits return ID of PaidtimeType then not exits return 0
                input.IDPrepaid = GetInfoPaidTimeType(input.PrepaidTimeNet, input.PrepaidTimeTV);

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input.IDPrepaid), "GetInfoPromotionEvent IDPrepaid: ");
                WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetInfoPromotionEvent IDPrepaid" + JsonConvert.SerializeObject(input.IDPrepaid));

                if (input.IDPrepaid == 0 && input.Cameras.Count == 0) return result;

                if (input.VoucherCode.StartsWith("MSB") && input.VoucherCode.EndsWith("FPT"))
                {
                    // MGT dành cho ngân hàng MSB cần phải trả trước 6T hoặc 12T
                    if (!CheckPrepaidTime(input.IDPrepaid))
                    {
                        result.error = "Phải trả trước 6T hoặc 12T mới có thể apply mã giới thiệu MSB";
                        return result;
                    }
                    // MGT dành cho ngân hàng MSB cần check gói dịch vụ phải từ 35 trở lên
                    if (!CheckLocalTypeCode(input.LocalType))
                    {
                        result.error = "Dịch vụ đăng ký phải là gói 35 trở lên!";
                        return result;
                    }
                }

                int Type = CheckTypeCodeInput(input.VoucherCode);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(Type), "GetInfoPromotionEvent CheckTypeCodeInput: ");
                WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetInfoPromotionEvent Type" + JsonConvert.SerializeObject(Type));

                switch (Type)
                {
                    case 1:
                        return HiFPTAdvocacy.GetPromotionEventByInviteCode(input, logId.ToString());
                    case 2:
                        return VoucherGeneralService.GetPromotionEventByVoucherCode(input, logId.ToString());
                    case 3:
                        return VoucherPrivateService.GetInfoPrivateCode(input, logId.ToString());
                }

                return null;

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.ToString());
                return new ResponseModel<InfoVoucherResponse> { result = -1, error = ex.ToString() };
            }
        }

        [HttpGet]
        [Route("API/LoyaltyWeb/GetReferalCode")]
        [Authorize(Roles = AuthorizeRole.AdminRF)]
        public ResponseModel<RFCodeModel> GetReferalCode(string contract) 
        {
            var logId = Guid.NewGuid();
            var res = new ResponseModel<RFCodeModel> { data = new RFCodeModel(), result = 0, error = "không tìm thấy" };
            try
            {
                RFCodeModel data = new RFCodeModel();
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        var codeInfo = connection.Query<RFCodeModel>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyWebRF", new
                        {
                            actionName = "GetRefferCode",
                            contract = contract
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(codeInfo), "GetReferalCode codeInfo ");

                        if (codeInfo.inviteCode.Equals("-1"))
                        {
                            res.result = 0;
                            res.error = "Thông tin hợp đồng không đúng";
                            return res;
                        }
                        if (codeInfo == null)
                        {
                            return res;
                        }
                        transaction.Commit();
                        res.result = 1;
                        res.error = "";
                        res.data = codeInfo;
                        return res;
                    }
                }
            }
            catch (Exception ex)
            {
                return new ResponseModel<RFCodeModel> { error = ex.Message, data = null, result = -1 };
            }
        }

        [HttpGet]
        [Route("API/ReferralProgram/GetInforReferalCode")]
        public ResponseModel<ReferCodeInfoModel> GetInforReferalCode(string ReferralCode)
        {
            var logId = Guid.NewGuid();
            ReferCodeInfoModel rcm = new ReferCodeInfoModel();
            int result = 1;
            string error = string.Empty;
            try
            {
                bool isOTTCode = OTTClientService.checkOTTCode(ReferralCode);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(isOTTCode), "GetInforReferalCode checkOTTCode: ");
                if (isOTTCode)
                {
                    rcm.AppName = "FPT play";
                    rcm.CusName = OTTClientService.GetInforClient(ReferralCode, logId.ToString());
                    rcm.DurationRefCode = "Không giới hạn";
                    rcm.Reason = null;
                    rcm.StatusContract = null;
                    rcm.IsActive = 1;

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(rcm), "GetInforReferalCode rcm: ");
                    return new ResponseModel<ReferCodeInfoModel> { error = "", data = rcm, result = 1 };
                }
                bool iskMyFPTCode = MyFPTService.checkMyFPTCode(ReferralCode);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(iskMyFPTCode), "GetInforReferalCode iskMyFPTCode: ");
                if (iskMyFPTCode)
                {
                    rcm.AppName = "MyFPT";
                    rcm.CusName = MyFPTService.getStaffInfor(ReferralCode, logId.ToString());
                    rcm.DurationRefCode = "Không giới hạn";
                    rcm.Reason = null;
                    rcm.StatusContract = null;
                    rcm.IsActive = 1;
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(rcm), "GetInforReferalCode rcm: ");
                    return new ResponseModel<ReferCodeInfoModel> { error = "", data = rcm, result = 1 };
                }
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        rcm = connection.Query<ReferCodeInfoModel>("PowerInside.dbo.OS6_FPTVoucher_GetInforUserRefer", new
                        {
                            actionName = "GetInforUserReferHiFPT",
                            inviteCode = ReferralCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    }
                }
                if (rcm == null)
                {
                    error = "Không tìm thấy";
                    result = 0;
                }
                return new ResponseModel<ReferCodeInfoModel> { error = error, data = rcm, result = result };
                
            }
            catch (Exception ex)
            {
                return new ResponseModel<ReferCodeInfoModel> { error=ex.Message,data=null,result=0};
            }
           
            
        }

        [HttpGet]
        [Route("API/ReferralProgram/GetHistoryAdvocacyProgram")]
        public ResponseModels<HistoryAdvocacyModel> GetHistoryAdvocacyProgram(int objid)
        {
            List<HistoryAdvocacyModel> lst = new List<HistoryAdvocacyModel>();
            ResponseModels<HistoryAdvocacyModel> data = new ResponseModels<HistoryAdvocacyModel> { data = lst, result = 0, error = "" };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    lst = connection.Query<HistoryAdvocacyModel>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_History", new
                    {
                        objid = objid,
                    }, commandType: CommandType.StoredProcedure).ToList();
                }
                return new ResponseModels<HistoryAdvocacyModel> { data = lst, result = lst.Count, error = null };
            }
            catch (Exception ex)
            {
                return new ResponseModels<HistoryAdvocacyModel> { data = null, result = 0, error = ex.Message };
            }
        }

        [HttpGet]
        [Authorize(Roles = AuthorizeRole.fptvn)]
        [Route("API/ReferralProgram/GetHistoryAdvocacyProgram2")]
        public HistoryOutputModels<HistoryAdvocacyModel2> GetHistoryAdvocacyProgram2(int objid, int page, int rows)
        {
            List<HistoryAdvocacyModel2> lst = new List<HistoryAdvocacyModel2>();
            HistoryOutputModels<HistoryAdvocacyModel2> data = new HistoryOutputModels<HistoryAdvocacyModel2> { data = lst, result = 0, error = "" };
            int result = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    lst = connection.Query<HistoryAdvocacyModel2>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_History2", new
                    {
                        objid = objid,
                    }, commandType: CommandType.StoredProcedure).ToList();
                }

                result = lst.Count;

                if (page != 0 && rows != 0)
                {
                    int skip = (page - 1) * rows;
                    lst = lst.Skip(skip).Take(rows).ToList();
                }
                return new HistoryOutputModels<HistoryAdvocacyModel2> { data = lst, result = result, error = null, page = page, rows = rows };
            }
            catch (Exception ex)
            {
                return new HistoryOutputModels<HistoryAdvocacyModel2> { data = null, result = 0, error = ex.Message };
            }
        }
        
        [HttpGet]
        [Authorize(Roles = AuthorizeRole.TLS)]
        [Route("API/ReferralProgram/GetHistoryAdvocacyProgram3")]
        public HistoryOutputModels<HistoryAdvocacyModel2> GetHistoryAdvocacyProgram3(int objid, int page, int rows)
        {
            try
            {
                List<HistoryAdvocacyModel2> lst;
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    lst = connection.Query<HistoryAdvocacyModel2>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_History3", new
                    {
                        objid = objid,
                    }, commandType: CommandType.StoredProcedure).ToList();
                }

                var result = lst.Count;

                if (page == 0 || rows == 0)
                    return new HistoryOutputModels<HistoryAdvocacyModel2> { data = lst, result = result, error = null, page = page, rows = rows };
                
                var skip = (page - 1) * rows;
                lst = lst.Skip(skip).Take(rows).ToList();
                return new HistoryOutputModels<HistoryAdvocacyModel2> { data = lst, result = result, error = null, page = page, rows = rows };
            }
            catch (Exception ex)
            {
                return new HistoryOutputModels<HistoryAdvocacyModel2> { data = null, result = 0, error = ex.Message };
            }
        }

        [HttpGet]
        [Route("API/ReferralProgram/GetRefferalInfo")]
        [Authorize(Roles = AuthorizeRole.Admin)]
        public ResponseModel<ReferalInfoModel> GetReferalInfo(int objid)
        {
            var logId = Guid.NewGuid();
            var dataInfor = new ReferalInfoModel();
            
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    dataInfor = connection.Query<ReferalInfoModel>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_OmiInfor", new
                    {
                        objid = objid,
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (dataInfor != null)
                    {
                        if (dataInfor.sourceApp.Equals("MyFPT"))
                        {
                            dataInfor.clientName = MyFPTService.getStaffInfor(dataInfor.clientNo, logId.ToString());
                            return new ResponseModel<ReferalInfoModel>{ data=dataInfor, error="", result=1};
                        }
                        if (dataInfor.sourceApp.Equals("FPTPlay"))
                        {
                            dataInfor.clientName = OTTClientService.GetInforClient(dataInfor.clientNo, logId.ToString());
                            return new ResponseModel<ReferalInfoModel> { data = dataInfor, error = "", result = 1 };
                        }
                        return new ResponseModel<ReferalInfoModel> { data = dataInfor, error = "", result = 1 };
                    }
                }
                return new ResponseModel<ReferalInfoModel> { data = null, result = 0, error = "Không tìm thấy" };
            }
            catch (Exception ex)
            {
                return new ResponseModel<ReferalInfoModel> { data = null, result = -1, error = ex.Message };
            }
        }
        public Boolean CheckPrepaidTime(int idprepaid)
        {
            PrepaidTime pr = new PrepaidTime();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    pr = connection.Query<PrepaidTime>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_for_Bank_MSB", new
                    {
                        ActionName = "GetPaidTimeType_MSB",
                        PaidTimeType = idprepaid,
                        //RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            if ((pr.IsPrePaidNET == 6 && (pr.IsPrePaidTV == 6 || pr.IsPrePaidTV == -1)) || (pr.IsPrePaidNET == 12 && (pr.IsPrePaidTV == 12 || pr.IsPrePaidTV == -1))) return true;
            return false;
        }
        public Boolean CheckLocalTypeCode(int localType)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    res = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_for_Bank_MSB", new
                    {
                        ActionName = "checkLocalType",
                        LocalType = localType
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return (res == 1);
        }


        /// <summary>
        /// get Id prepaid
        /// </summary>
        /// <param name="IsPerpaidNET"></param>
        /// <param name="IsPerpaidTV"></param>
        /// <returns></returns>
        private int GetInfoPaidTimeType(int IsPerpaidNET, int IsPerpaidTV)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(ConstantAPI.NameStore_APIMBS, new
                {
                    ActionName = "CheckContaintPaidTimeType",
                    IsPrepaidTV = IsPerpaidTV,
                    IsPrePaidNET = IsPerpaidNET,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        private int CheckTypeCodeInput(string Input)
        {
            if (Input.StartsWith("MRK"))
            {
                return 3;
            }
            bool isRFFPTplay = false;
            int n = 0;
            var isNumeric = int.TryParse(Input, out n);
            L.Mes(Level.INFORP, isNumeric.ToString(), "Check number OTT:");
            if (isNumeric)
            {
                isRFFPTplay = OTTClientService.checkOTTCode(Input);
            }
            if (Input.ToUpper().IndexOf("FTEL") > -1 || (Input.ToUpper().StartsWith("MSB") && Input.ToUpper().EndsWith("FPT")) || isRFFPTplay) return 1;
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    return connection.Query<int>(ConstantAPI.NameStore_PE, new
                    {
                        ActionName = "IsVoucherCode",
                        Name = Input,
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
        }

        private PrepaidConditonSearch GetPrepaidConditon(int NETID, int IPTVID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<PrepaidConditonSearch>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPrepaidConditon",
                    new { NETID = NETID, IPTVID = IPTVID },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        private bool isMKTgeneralCode(string voucherCode)
        {
            int flag = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        flag = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_GeneralCodeMKT", new
                        {
                            actionName = "checkStatusCodeGC",
                            voucherCode = voucherCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        return (flag == 1);
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "isMKTgeneralCode");
                return false;
            }
        }
        private ResponseModel<InfoVoucherResponse> GetPromotionInforByGeneralCode(InfoVoucherRequest input)
        {
            ResponseModel<InfoVoucherResponse> response = new ResponseModel<InfoVoucherResponse>()
            {
                data = new InfoVoucherResponse() { PromotionEvent = null, Type = 1 },
                error = null,
                result = 1
            };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    int quota = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_GeneralCodeMKT", new
                    {
                        actionName = "checkQuotarCodeGC",
                        voucherCode = input.VoucherCode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (quota == 1)
                    {
                        response.error = "Mã Đã Hết Số Lần Kích Hoạt";
                        return response;
                    }

                    PromotionEventFoxpro res = connection.Query<PromotionEventFoxpro>("PowerInside.dbo.OS6_FPTVoucher_GeneralCodeMKT", new
                    {
                        ActionName = "GetPromotionEvent",
                        PaidTimeTypeID = input.IDPrepaid,
                        voucherCode = input.VoucherCode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (res != null)
                    {
                        if (input.PrepaidTimeNet == 0)
                            res.Name = "DiscountAmountPostNET";
                        else
                            res.Name = "DiscountAmountPreNET";
                        decimal valueDiscountAmount = Math.Round(res.ValueDiscountAmount * (decimal)0.11, 0) * 10;
                        List<ItemNode> lst = new List<ItemNode>();
                        lst.Add(new ItemNode() { ContentDiscount = res.Name, Dismonth = 0, ServiceCode = input.ServiceCodeTTNet, Value = valueDiscountAmount.ToString() });
                        //response.data.PromotionEvent = new List<ItemNode>();
                        response.data = new InfoVoucherResponse();
                        response.data.PromotionEvent = lst;
                        response.data.Type = 1;
                        //response.data.FullNameRef = "Mã giới thiệu của Nhân viên FPT";// getStaffInfor(staffid);
                        response.data.PhoneRef = null;

                        AddHistoryTempCode(input.OrderCode, input.VoucherCode, 0, -1, input);

                        return response;
                    }
                    return response;
                }
            }
            catch (Exception ex)
            {
                Mes(Level.ERROR, ex.Message, "GetPromotionInforByGeneralCode");
                return null;
            }
        }


        private string getStaffInfor(string employeeCode)
        {
            try
            {
                if (string.IsNullOrEmpty(employeeCode))
                {
                    L.Mes(Level.INFO, "Mã nhân viên trống", "getStaffInfor");
                    return "Mã giới thiệu của Nhân viên FPT";
                }
                MyFPTModel aut = getAuthorInfor();
                if (aut == null)
                    return "Mã giới thiệu của Nhân viên FPT";
                StaffInfor res = new StaffInfor();

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    //var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    HttpResponseMessage response = client.GetAsync("/api/oauth-ms/public/auth/get-info-employee?employeeCode=" + employeeCode).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, r, "getStaffInfor");
                    res = JsonConvert.DeserializeObject<StaffInfor>(r);
                }

                if (res != null)
                {
                    if (!string.IsNullOrEmpty(res.data.fullname))
                    {
                        return res.data.fullname;
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getStaffInfor");
                return "Mã giới thiệu của Nhân viên FPT";
            }
            return "Mã giới thiệu của Nhân viên FPT";
        }

        private MyFPTModel getAuthorInfor()
        {
            MyFPTModel res = new MyFPTModel();
            try
            {
                var uri = WebAPIHelper.myFpt_fpt_vn + "/api/oauth-ms/public/auth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("client_id", Utility.my_fpt_api_client_id));
                keyValues.Add(new KeyValuePair<string, string>("client_secret", Utility.my_fpt_api_client_secret));
                keyValues.Add(new KeyValuePair<string, string>("username", Utility.my_fpt_api_username));
                keyValues.Add(new KeyValuePair<string, string>("password", Utility.my_fpt_api_password));
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "password"));

                var content = new FormUrlEncodedContent(keyValues);

                L.Mes(Level.INFO, "getAuthorInfor" + JsonConvert.SerializeObject(new { uri = uri, keyValues = keyValues }));

                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        response.EnsureSuccessStatusCode();
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, "getAuthorInfor");
                        res = JsonConvert.DeserializeObject<MyFPTModel>(r);
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getAuthorInfor");
                return null;
            }
            return res;
        }
        private ResponseModel<InfoVoucherResponse> GetPromotionEventByVoucherCode(InfoVoucherRequest condition)
        {
            var logId = Guid.NewGuid();
            WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetPromotionEventByVoucherCode " + JsonConvert.SerializeObject(condition));
            PromotionEventInfo res = new PromotionEventInfo();
            ResponseModel<InfoVoucherResponse> response = new ResponseModel<InfoVoucherResponse>()
            {
                data = new InfoVoucherResponse() { Type = 2, PromotionEvent = null },
                result = 1,
                error = null
            };

            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    res = connection.Query<PromotionEventInfo>(ConstantAPI.NameStore_APIMBS, new
                    {
                        ActionName = "GetPromotionEvent",
                        EventCode = condition.VoucherCode,
                        PaidTimeType = condition.IDPrepaid,
                        Localtype = condition.LocalType,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (res == null)
                    {
                        response.error = "Thông Tin Voucher Không Tồn Tại";
                        return response;
                    }

                    res.QuotaGeneralWSOL = connection.Query<int>(ConstantAPI.NameStore_InviteCode, new
                    {
                        ActionName = "CheckQuotaCode",
                        ID = res.ID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetPromotionEventByVoucherCode QuotaGeneralWSOL " + JsonConvert.SerializeObject(res.QuotaGeneralWSOL));

                    if (res.QuotaGeneralWSOL < 1)
                    {
                        response.error = "Mã Voucher Đã Hết Số Lần Kích Hoạt";
                        return response;
                    }

                    if (res.ID1 != 0 || res.ID2 != 0)
                    {
                        res.ServiceCode2 = connection.Query<int>(ConstantAPI.NameStore_APIMBS, new
                        {
                            ActionName = "GetServiceCodeNET",
                            Localtype = condition.LocalType,
                            RowAffected = 0
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                        WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetPromotionEventByVoucherCode ServiceCode2 " + JsonConvert.SerializeObject(res.ServiceCode2));
                    }
                }

                WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) 
                    + "DKOL_GetPromotionEventByVoucherCode GetPromotionEvent " + JsonConvert.SerializeObject(res));
                //L.Mes(Level.INFOPE, JsonConvert.SerializeObject(res));
            }
            if (res == null || !CheckValidServiceCode(
                    new List<int>() { res.ServiceCode1, res.ServiceCode2, res.ServiceCode3, res.ServiceCode4, res.ServiceCode5, 0, res.ServiceCode7 },
                    new List<int>() { condition.ServiceCodeTTNet, condition.ServiceCodeTTTV, condition.ServiceCodeHMNet, condition.ServiceCodeHMTV }
                ))
                return response;
            response.data.PromotionEvent = GetItemNode(res, condition);

            //L.Mes(Level.RESPONSE, JsonConvert.SerializeObject(response));
            WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetPromotionEventByVoucherCode response " + JsonConvert.SerializeObject(response));
            L.Mes(Level.ENDREQUEST, null, "GetInfoPromotionEvent");

            if (response.data.PromotionEvent.Count() != 0)
                AddHistoryTempCode(condition.OrderCode, condition.VoucherCode, 0, -1, condition);
            WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetPromotionEventByVoucherCode AddHistoryTempCode done");
            return response;
        }

        private Boolean CheckValidServiceCode(List<int> SPE, List<int> SIP)
        {
            return (SPE.Where(p => p != 0 && !SIP.Any(p2 => p2 == p)).Count() == 0);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        private List<ItemNode> GetItemNode(PromotionEventInfo input, InfoVoucherRequest condition)
        {
            List<ItemNode> instance = new List<ItemNode>();
            // X đồng Y tháng NET Trả Sau   : ServiceCode -1
            // Tặng Tháng Cước NET          : ServiceCode -2

            // Tặng Tháng Cước NET (Trả trước + Trả sau)
            if (condition.PrepaidTimeNet > -1)
            {
                // SubsProm
                #region Tặng Tháng Cước NET (Trả trước + Trả sau)
                int MaxMonth = GetInfoNET(condition.NETID);
                var list = new List<Tuple<int, int>>(){ Tuple.Create(input.DisMonth2L2, input.DisRatio2L2),
                                                             Tuple.Create(input.DisMonth2L3, input.DisRatio2L3),
                                                             Tuple.Create(input.DisMonth2L4, input.DisRatio2L4)};
                var current = new List<Tuple<int, int>>();
                for (int i = 0; i < list.Count(); i++)
                {
                    // KhanhHC2 2020-05-04: DisRatio in (100,50)
                    if (list[i].Item1 != 0 && (list[i].Item2 == 100 || list[i].Item2 == 50) && list[i].Item1 == (MaxMonth + 1))
                    {
                        current.Add(list[i]);
                        if (current.Last().Item1 + 1 == list[i + 1].Item1)
                        {
                            current.Add(list[i + 1]);
                        }
                    }
                }
                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { BCLKM = MaxMonth, CLKM = list, QUERY = current }));

                string Paidtype;
                if (condition.PrepaidTimeNet == 0) Paidtype = "PostPaid"; else Paidtype = "Prepaid";
                if (current.Count() != 0) instance.Add(new ItemNode() { ContentDiscount = "FreeMonth" + Paidtype + "NET", ServiceCode = -2, Value = current.Count().ToString(), Dismonth = 0 });

                // Container
                if (condition.NETID > 0 && input.ContainerMonth > 0)
                {
                    //string Paidtype;
                    if (condition.PrepaidTimeNet == 0) Paidtype = "PostPaid"; else Paidtype = "Prepaid";
                    instance.Add(new ItemNode() { ContentDiscount = "FreeMonth" + Paidtype + "NET", ServiceCode = -2, Value = input.ContainerMonth.ToString(), Dismonth = 0 });
                }
                #endregion
            }

            // X đồng Y tháng + Giảm Tiền Trực Tiếp NET TrảTrước
            if (condition.PrepaidTimeNet > 0)
            {
                #region  X đồng Y tháng + Giảm Tiền Trực Tiếp NET TrảTrước
                //Discount TT NET trả trước
                bool ConditionDiscountNET = (input.ID1 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode1);
                if (ConditionDiscountNET)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountPreNET",
                        ServiceCode = input.ServiceCode1,
                        Value = input.Value1.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }

                // SubsProm
                bool ConditionDiscountByMonthNET = 
                    (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode2 && input.Value2 != 0 && input.DisMonth2 != 0);
                if (ConditionDiscountByMonthNET)
                {
                    int MonthValid = GetDismonthValidate(input.DisMonth2, condition.PrepaidTimeNet);
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountNETByMonth",
                        ServiceCode = input.ServiceCode2,
                        Value = (input.Value2.VATDiscount() * MonthValid).ToString(),
                        Dismonth = MonthValid
                    });
                }

                if (!ConditionDiscountByMonthNET)
                {
                    int promotionNetAmount = 0;
                    int dismonthValidate = 0;
                    // by PromotionNetID
                    ConditionDiscountByMonthNET = (input.PromotionNetAmount > 0 && (condition.ServiceCodeTTNet != 0) && (condition.ServiceCodeTTNet == input.ServiceCode2));
                    if (ConditionDiscountByMonthNET)
                    {
                        //promotionNetAmount = input.PromotionNetAmount.VATDiscount();
                        // KhanhHC2 2020-06-03 khong ap dung tang thang khi so thang tra truoc nho hop so thang tang
                        //if (ConditionDiscountByMonthNET && condition.IsPrepaidNET < input.PromotionNetDuration)
                        //    ConditionDiscountByMonthNET = false;

                        // dev
                        if (input.PromotionNetDuration > 0)
                        {
                            dismonthValidate = GetDismonthValidate(input.PromotionNetDuration, condition.PrepaidTimeNet);
                            promotionNetAmount = (input.PromotionNetAmount / input.PromotionNetDuration).VATDiscount() * dismonthValidate;
                        }
                    }
                    else
                    {
                        ConditionDiscountByMonthNET = (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode2 && input.Value2 != 0 && input.DisMonth2 != 0);
                        dismonthValidate = GetDismonthValidate(input.DisMonth2, condition.PrepaidTimeNet);
                        promotionNetAmount = input.Value2.VATDiscount() * dismonthValidate;
                    }
                    if (ConditionDiscountByMonthNET)
                    {

                        instance.Add(new ItemNode()
                        {
                            ContentDiscount = "DiscountAmountNETByMonth",
                            ServiceCode = input.ServiceCode2,
                            Value = promotionNetAmount.ToString(),
                            Dismonth = dismonthValidate
                        });
                    }
                }
                #endregion
            }

            //  X Đồng Y tháng + Giảm Tiền Trực Tiếp NET TrảSau
            if (condition.PrepaidTimeNet == 0)
            {
                #region X Đồng Y tháng + Giảm Tiền Trực Tiếp NET TrảSau
                // X đồng Y Tháng
                if (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && input.ServiceCode2 == condition.ServiceCodeTTNet && input.Value2 != 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountCLKMNET",
                        ServiceCode = -1,
                        Value = input.Value2.VATDiscount().ToString(),
                        Dismonth = input.DisMonth2
                    });
                }
                // Giảm Tiền TT 
                if (input.ID1 != 0 && input.ServiceCode1 == condition.ServiceCodeTTNet && input.Value1 != 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountPostNET",
                        ServiceCode = 56,
                        Value = input.Value1.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }
                #endregion
            }

            // Tặng Tháng Cước TV
            if (condition.PrepaidTimeTV > 0)
            {
                #region Tặng Tháng Cước TV
                int duration = CheckApplyCLKMTV(input.ID3, condition.IPTVID, true);
                L.Mes(Level.INFO, string.Format("{0}{1}", input.ID3, condition.IPTVID));

                if (duration > 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "FreeMonthPrepaidTV",
                        ServiceCode = -3,
                        Value = duration.ToString(),
                        Dismonth = 0
                    });
                }
                #endregion
            }

            if (condition.PrepaidTimeTV == 0)
            {
                int duration = CheckApplyCLKMTV(input.ID3, condition.IPTVID, false);
                if (duration != 0) instance.Add(new ItemNode() { ContentDiscount = "FreeMonthPostPaidTV", ServiceCode = -3, Value = duration.ToString(), Dismonth = 0 });
            }

            // X Đồng Y Tháng TV + Giảm Tiền Trực Tiếp TV TrảTrước
            if (condition.PrepaidTimeTV > 0)
            {
                #region
                bool ConditionDiscountTV = (input.ServiceCode7 == condition.ServiceCodeTTTV && input.Value7 != 0);
                if (ConditionDiscountTV)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTV",
                        ServiceCode = input.ServiceCode7,
                        Value = input.Value7.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }

                bool ConditionDiscountByMonthTV = ((input.ID3 != 0) && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV);

                if (ConditionDiscountByMonthTV)
                {
                    int ValidMonth = GetDismonthValidate(input.DisMonth3, condition.PrepaidTimeTV);
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTVByMonth",
                        ServiceCode = input.ServiceCode3,
                        Value = (input.Value3.VATDiscount() * ValidMonth).ToString(),
                        Dismonth = ValidMonth
                    });

                }
                #endregion
            }

            // X đồng Y tháng TV + Giảm Tiền Trực Tiếp TV TrảSau
            if (condition.PrepaidTimeTV == 0)
            {
                #region X đồng Y tháng TV + Giảm Tiền Trực Tiếp TV TrảSau
                // Giảm Tiền Trực Tiếp
                if (input.ID7 != 0 && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV)
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTV",
                        ServiceCode = input.ServiceCode7,
                        Value = input.Value7.VATDiscount().ToString(),
                        Dismonth = 0
                    });

                // X Dồng Y Tháng TV
                if (input.ID3 != 0 && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountCLKMTV",
                        ServiceCode = -1,
                        Value = input.Value3.VATDiscount().ToString(),
                        Dismonth = input.DisMonth3
                    });
                }
                #endregion
            }

            // CLKM Hòa Mạng NET
            if (input.ID4 != 0 && (condition.ServiceCodeHMNet != 0) && (condition.ServiceCodeHMNet == input.ServiceCode4))
                instance.Add(new ItemNode() { ContentDiscount = "DiscountNewInterConnNET", ServiceCode = input.ServiceCode4, Value = (string)input.Value4.VATDiscount().ToString(), Dismonth = 0 });
            // CLKM Hòa Mạng TV
            if (input.ID5 != 0 && (condition.ServiceCodeHMTV != 0) && (condition.ServiceCodeHMTV == input.ServiceCode5))
                instance.Add(new ItemNode() { ContentDiscount = "DiscountNewInterConnTV", ServiceCode = input.ServiceCode5, Value = (string)input.Value5.VATDiscount().ToString(), Dismonth = 0 });
            // Quà Tặng
            if (input.ID6 != 0)
                instance.Add(new ItemNode() { ContentDiscount = input.Discount6 + input.Model, ServiceCode = 0, Dismonth = 0, Value = "0" });
            if (condition.VoucherCode.StartsWith("MSB") && condition.VoucherCode.EndsWith("FPT"))
            {
                foreach (var item in instance)
                {
                    item.ServiceCode = -4;
                }
            }

            return instance;
        }

        private int GetDismonthValidate(int MonthCLKMVoucher, int MonthCLKMBase)
        {
            return (MonthCLKMVoucher <= MonthCLKMBase) ? MonthCLKMVoucher : MonthCLKMBase;
        }

        /// <summary>
        /// Check status exits of  the VoucherCode with OrderCode(regID) in HistotyTemp (cart)
        /// </summary>
        /// <param name="OrderCode">Col RegId In DB</param>
        /// <param name="VoucherCode">Col VoucherCode</param>
        /// <returns>true is exits , false is not extis</returns>
        private Boolean CheckAddedCode(string OrderCode, string VoucherCode)
        {
            int IDHistoryCart = 0;

            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                IDHistoryCart = connection.Query<int>(ConstantAPI.NameStore_HistoryCart, new
                {
                    ActionName = "CheckIsAddedCode",
                    OrderCode = OrderCode,
                    VoucherCode = VoucherCode,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }

            return (IDHistoryCart > 0);
        }

        /// <summary>
        /// return the totalamount
        /// </summary>
        /// <param name="CondtionDiscount1">boolean</param>
        /// <param name="ValueDiscount1">Int valueDiscount</param>
        /// <param name="ConditionDiscount2">boolean</param>
        /// <param name="ValueDiscount2">int ValueDiscount</param>
        /// <returns>string totalamount</returns>
        private string TotalDiscountCLKM(bool CondtionDiscount1, int ValueDiscount1, bool ConditionDiscount2, int ValueDiscount2)
        {
            int value = 0;
            if (CondtionDiscount1) value += ValueDiscount1;
            if (ConditionDiscount2) value += ValueDiscount2;
            return value.ToString();
        }

        private int GetInfoNET(int NETID)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    res = connection.Query<int>(ConstantAPI.NameStore_APIMBS, new
                    {
                        ActionName = "GetInfoPrePaidNET",
                        IsPrepaidNET = NETID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return res;
        }

        private int CheckApplyCLKMTV(int PE, int CLKMTV, Boolean Isprepaid)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@IsPrepaidTV", PE);  // query trong KM Voucher
                    parameters.Add("@IPTVID", CLKMTV);   // query  trong KM Nền
                    parameters.Add("@RowAffected", 0);

                    if (Isprepaid) parameters.Add("@ActionName", "GetInfoPrepaidTV"); // So Sánh Nền Prepaid
                    else parameters.Add("@ActionName", "CheckInfoPostPaidTV");        // So Sánh Nền Postpaid
                    res = connection.Query<int>(ConstantAPI.NameStore_APIMBS, parameters, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return res;
        }

        /// <summary>
        ///  add Code to cart
        /// </summary>
        /// <param name="OrderCode">Mã OrderCode</param>
        /// <param name="VoucherCode">Mã Code :(RP hoặc Voucher) </param>
        /// <param name="ObjectID">Object hiện tại là  0</param>
        /// <param name="condition">Isprepaid NET, Isprepaid TV : dùng để xác địch Mã Voucher khi quy đổi từ RP code sang</param>
        /// <param name="Type">Xác Định RP : 1 || VC : 2</param>
        /// <returns></returns>
        private Boolean AddHistoryTempCode(string OrderCode, string VoucherCode, int ObjectID, int type, InfoVoucherRequest condition)
        {
            int flag = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                flag = connection.Query<int>(ConstantAPI.NameStore_HistoryCart, new
                {
                    ActionName = "AddCode",
                    VoucherCode = VoucherCode,
                    OrderCode = OrderCode,
                    ObjectID = ObjectID,
                    IsPrepaidNET = condition.PrepaidTimeNet,
                    IsPrepaidTV = condition.PrepaidTimeTV,
                    BNET = condition.NETID,
                    BTV = condition.IPTVID,
                    type = type,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return (flag > 0);
        }



        private void Mes(Level Level, string Message, string APIPart = null)
        {
            try
            {
                System.Threading.Tasks.Task.Run(() => L.Mes(Level, Message, APIPart));
            }
            catch { }
        }

        private bool RequestValidate(InfoVoucherRequest request)
        {
            if (request.NETID < -1 || request.IPTVID < -1 || request.IDPrepaid < -1 || request.PrepaidTimeNet < -1 || request.PrepaidTimeTV < -1)
                return false;

            if (request.ServiceCodeHMNet < 0 || request.ServiceCodeHMTV < 0 || request.ServiceCodeTTNet < 0 || request.ServiceCodeTTTV < 0 || request.LocalType < 0)
                return false;

            return true;
        }
    }
}