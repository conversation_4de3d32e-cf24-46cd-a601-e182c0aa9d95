using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace GetPromotionEventByCode.Constant
{
    public class ConstantAPI
    {
        public const string NameStore_InviteCode = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";
        public const string NameStore_APIMBS = "PowerInside.dbo.OS6_FPTVoucher_MBSAPIVoucher";
        public const string NameStore_HistoryCart = "PowerInside.dbo.OS6_FPTVoucher_HistotyTempInfo";
        public const string NameStore_PE = "PowerInside.dbo.OS6_FPTVoucher_PromotionEvent";
        public const string NameStaore_PEPrivate = "PowerInside.dbo.OS6_FPTVoucher_Private";
        public const string OS6_ReferalProgram_CAM = "PowerInside.dbo.OS6_ReferalProgram_CAM";
    }
}