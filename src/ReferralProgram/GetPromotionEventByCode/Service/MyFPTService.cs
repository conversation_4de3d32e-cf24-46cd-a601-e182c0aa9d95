using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using Voucher.APIHelper.Log4net;
using System.Data.SqlClient;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using GetPromotionEventByCode.Models;
using GetPromotionEventByCode.Constant;
using System.Net.Http.Headers;
using System.Net.Http;

namespace GetPromotionEventByCode.Service
{
    public class MyFPTService
    {
        public static Boolean checkMyFPTCode(string vouchercode)
        {
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(vouchercode), "MyFPT - checkMyFPTCode");
            int flag = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        flag = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                        {
                            ActionName = "CheckCodeFoxpro",
                            voucherCode = vouchercode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, "flag: "+ flag.ToString(), "MyFPT - checkMyFPTCode");
                        return (flag == 1);
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "MYFPT - checkMyFPTCode");
                return false;
            }
        }
        public static ResponseModel<InfoVoucherResponse> GetReferEvent(InfoVoucherRequest input, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "MyFPT GetReferEvent req: ");
            //L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "MyFPT - GetReferEvent");
            ResponseModel<InfoVoucherResponse> response = new ResponseModel<InfoVoucherResponse>();
            response.data = new InfoVoucherResponse();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {                    
                    string staffid = connection.Query<string>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                    {
                        actionName = "GetStaffID",
                        voucherCode = input.VoucherCode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    PromotionEventFoxpro res = connection.Query<PromotionEventFoxpro>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                    {
                        ActionName = "GetPEFoxpro",
                        PaidTimeTypeID = input.IDPrepaid
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(res), "MyFPT GetPEFoxpro: ");

                    if (res != null)
                    {
                        if (input.PrepaidTimeNet == 0)
                            res.Name = "DiscountAmountPostNET";
                        else
                            res.Name = "";
                        decimal valueDiscountAmount = Math.Round(res.ValueDiscountAmount * (decimal)0.11, 0) * 10;
                        response.data = new InfoVoucherResponse();
                        response.data.PromotionEvent = new List<ItemNode>();
                        response.data.PromotionEvent.Add(new ItemNode() { ContentDiscount = res.Name, ServiceCode = input.ServiceCodeTTNet, Value = valueDiscountAmount.ToString(), Dismonth = 0 });
                        response.data.Type = 4;
                        response.data.FullNameRef = getStaffInfor(staffid,logId);
                        response.data.PhoneRef = null;
                        response.result = 1;
                        AddHistoryTempCode(input.OrderCode, input.VoucherCode, 0, input);

                        return response;
                    }

                    return response;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(ex.Message), "MyFPT Error: ");
                return response;
            }
        }
        private static AuthorizationInfor getAuthorInfor()
        {
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                var uri = WebAPIHelper.myFpt_fpt_vn + "/api/oauth-ms/public/auth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("client_id", Utility.my_fpt_api_client_id));
                keyValues.Add(new KeyValuePair<string, string>("client_secret", Utility.my_fpt_api_client_secret));
                keyValues.Add(new KeyValuePair<string, string>("username", Utility.my_fpt_api_username));
                keyValues.Add(new KeyValuePair<string, string>("password", Utility.my_fpt_api_password));
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "password"));

                var content = new FormUrlEncodedContent(keyValues);


                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        L.Mes(Level.INFO, response.Content.ReadAsStringAsync().Result, "getAuthorInfor");
                        response.EnsureSuccessStatusCode();
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, "getAuthorInfor");
                        res = JsonConvert.DeserializeObject<AuthorizationInfor>(r);
                    }
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getAuthorInfor");
                return null;
            }
            return res;
        }

        public static string getStaffInfor(string employeeCode, string logId)
        {
            try
            {
                if (string.IsNullOrEmpty(employeeCode))
                {
                    //L.Mes(Level.INFO, "Mã nhân viên trống", "getStaffInfor");
                    WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "getStaffInfor MyFPT res " + "Mã nhân viên trống");
                    return "Nhân viên FPT";
                }
                AuthorizationInfor aut = getAuthorInfor();
                if (aut == null)
                    return "Nhân viên FPT";
                StaffInfor res = new StaffInfor();
                res.data = new Name();
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    //var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    HttpResponseMessage response = client.GetAsync("/api/oauth-ms/public/auth/get-info-employee?employeeCode=" + employeeCode).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    //L.Mes(Level.INFO, r, "getStaffInfor");
                    WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "getStaffInfor MyFPT res " + JsonConvert.SerializeObject(r));
                    res = JsonConvert.DeserializeObject<StaffInfor>(r);
                }

                if (res != null)
                {
                    if (!string.IsNullOrEmpty(res.data.fullname))
                    {
                        return res.data.fullname;
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getStaffInfor");
                return "Nhân viên FPT";
            }
            return "Nhân viên FPT";
        }

        private static Boolean AddHistoryTempCode(string OrderCode, string VoucherCode, int ObjectID, InfoVoucherRequest condition)
        {
            int flag = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                flag = connection.Query<int>(ConstantAPI.NameStore_HistoryCart, new
                {
                    ActionName = "AddCode",
                    VoucherCode = VoucherCode,
                    OrderCode = OrderCode,
                    ObjectID = ObjectID,
                    IsPrepaidNET = condition.PrepaidTimeNet,
                    IsPrepaidTV = condition.PrepaidTimeTV,
                    BNET = condition.NETID,
                    BTV = condition.IPTVID,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return (flag > 0);
        }
    }
}