using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using Voucher.APIHelper.Log4net;
namespace GetPromotionEventByCode.Service
{
    public class MKTgeneralCode
    {
        public static bool isMKTgeneralCode(string voucherCode)
        {
            int flag = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        flag = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_GeneralCodeMKT", new
                        {
                            actionName = "checkStatusCodeGC",
                            voucherCode = voucherCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        return (flag == 1);
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "isMKTgeneralCode");
                return false;
            }
        } 
    }
}