using Dapper;
using GetPromotionEventByCode.Constant;
using GetPromotionEventByCode.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;
namespace GetPromotionEventByCode.Service
{
    public class OTTClientService
    {
        private const string OS6_FPTVoucher_OTTReferralProgram = "PowerInside.dbo.OS6_FPTVoucher_OTTReferralProgram";
        public static Boolean checkOTTCode(string vouchercode)
        {
            try
            {
                string uri = Utility.apiOtt;
                OTTReferalProgram ott = callapi_CheckOTT(uri+"/api/v1/isc/referral/"+vouchercode);
                L.<PERSON>(Level.INFO, JsonConvert.SerializeObject(ott), "OTT - checkOTTCode");
                if (ott.status == 1)
                {
                    return (ott.data.valid == 1);
                }
                return false;
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "OTT - checkOTTCode");
                return false;
            }
        }
        public static OTTReferalProgram callapi_CheckOTT(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
            wr.Method = _method;

            // timeout
            wr.Timeout = 10000;

            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();

                // log
                L.Mes(Level.INFO, "OTT - checkOTTCode: " + rs);

                OTTReferalProgram ott = new OTTReferalProgram { data = null, message = "Fail", status = 0 };
                ott = JsonConvert.DeserializeObject<OTTReferalProgram>(rs);
                return ott;
            }
        }
        public static ResponseModel<InfoVoucherResponse> GetReferEvent(InfoVoucherRequest input, string logId)
        {
            WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetReferEvent OTT " + JsonConvert.SerializeObject(input));
            ResponseModel<InfoVoucherResponse> response = new ResponseModel<InfoVoucherResponse>()
            {
                data = new InfoVoucherResponse() { PromotionEvent = null, Type = 1 },
                error = null,
                result = 1
            };
            PromotionEventInfo Ps = new PromotionEventInfo();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    Ps = connection.Query<PromotionEventInfo>(OS6_FPTVoucher_OTTReferralProgram, new
                    {
                        actionName = "GetPromotionOTTCode",
                        LocalType = input.LocalType,
                        PaidTimeType = input.IDPrepaid
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 2);
                    WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + "DKOL_GetReferEvent OTT " + JsonConvert.SerializeObject(Ps));
                    //L.Mes(Level.INFO, "GetReferEvent OTT: " + JsonConvert.SerializeObject(Ps));
                    if (Ps == null)
                    {
                        response.error = "Mã RP không hợp lệ";
                        return response;
                    }
                    Ps.QuotaGeneralWSOL = connection.Query<int>(ConstantAPI.NameStore_InviteCode, new
                    {
                        ActionName = "CheckQuotaCode",
                        ID = Ps.ID,
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (Ps.QuotaGeneralWSOL < 1)
                    {
                        response.error = "Mã RP Đã Hết Số Lần Kích Hoạt";
                        return response;
                    }
                    Boolean CheckServiceCode = CheckValidServiceCode(new List<int>() { Ps.ServiceCode1, Ps.ServiceCode2, Ps.ServiceCode3, Ps.ServiceCode4, Ps.ServiceCode5, 0, Ps.ServiceCode7 },
                    new List<int>() { input.ServiceCodeTTNet, input.ServiceCodeTTTV, input.ServiceCodeHMNet, input.ServiceCodeHMTV });
                    if (!CheckServiceCode) return response;

                    Ps.ServiceCode2 = connection.Query<int>(ConstantAPI.NameStore_APIMBS, new
                    {
                        ActionName = "GetServiceCodeNET",
                        Localtype = input.LocalType,
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    response.data.Type = 1;
                    response.data.ContractRef = "";
                    response.data.FullNameRef = GetInforClient(input.VoucherCode,logId);
                    response.data.PhoneRef = "";

                }
                response.data.PromotionEvent = GetItemNode(Ps, input);
                AddHistoryTempCode(input.OrderCode, input.VoucherCode, 0, input);
                return response;
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "OTT - GetReferEvent");
                response.error = ex.Message;
                return response;
            }
        }
        public static string GetInforClient(string clientID, string log,string contentType = "application/json; charset=utf-8")
        {

            try
            {
                var url = Utility.apiOtt + "/api/v1/isc/user/" + clientID;
                HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(url);
                var headers = wr.Headers;
                headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
                wr.Method = "GET";
                // timeout
                wr.Timeout = 10000;
                var httpResponse = (HttpWebResponse)wr.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    var rs = streamReader.ReadToEnd();

                    // log
                    WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", log, DateTime.Now) + "GetInforClient OTT " + JsonConvert.SerializeObject(rs));

                    //OTTReferalProgram ott = new OTTReferalProgram { data = null, message = "Fail", status = 0 };
                    var ott = JsonConvert.DeserializeObject<OTTUserOutput>(rs);
                    if (ott.status == 1)
                    {
                        L.Mes(Level.ERROR, clientID, "GetInforClient clientOTT: " + JsonConvert.SerializeObject(ott));
                        return ott.data.fullname;
                    }
                    else return "";
                }
            }
            catch (Exception e)
            {
                L.Mes(Level.ERROR, e.ToString(), "GetInforClient clientOTT: " + JsonConvert.SerializeObject(clientID));
                return "";
            }
        }
        private static int GetInfoNET(int NETID)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    res = connection.Query<int>(ConstantAPI.NameStore_APIMBS, new
                    {
                        ActionName = "GetInfoPrePaidNET",
                        IsPrepaidNET = NETID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return res;
        }

        private static int CheckApplyCLKMTV(int PE, int CLKMTV, Boolean Isprepaid)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@IsPrepaidTV", PE);  // query trong KM Voucher
                    parameters.Add("@IPTVID", CLKMTV);   // query  trong KM Nền
                    parameters.Add("@RowAffected", 0);

                    if (Isprepaid) parameters.Add("@ActionName", "GetInfoPrepaidTV"); // So Sánh Nền Prepaid
                    else parameters.Add("@ActionName", "CheckInfoPostPaidTV");        // So Sánh Nền Postpaid
                    res = connection.Query<int>(ConstantAPI.NameStore_APIMBS, parameters, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return res;
        }
        private static int GetDismonthValidate(int MonthCLKMVoucher, int MonthCLKMBase)
        {
            return (MonthCLKMVoucher <= MonthCLKMBase) ? MonthCLKMVoucher : MonthCLKMBase;
        }
        private static List<ItemNode> GetItemNode(PromotionEventInfo input, InfoVoucherRequest condition)
        {
            List<ItemNode> instance = new List<ItemNode>();
            // X đồng Y tháng NET Trả Sau   : ServiceCode -1
            // Tặng Tháng Cước NET          : ServiceCode -2

            // Tặng Tháng Cước NET (Trả trước + Trả sau)
            if (condition.PrepaidTimeNet > -1)
            {
                // SubsProm
                int MaxMonth = GetInfoNET(condition.NETID);
                var list = new List<Tuple<int, int>>(){ Tuple.Create(input.DisMonth2L2, input.DisRatio2L2),
                                                             Tuple.Create(input.DisMonth2L3, input.DisRatio2L3),
                                                             Tuple.Create(input.DisMonth2L4, input.DisRatio2L4)};
                var current = new List<Tuple<int, int>>();
                for (int i = 0; i < list.Count(); i++)
                {
                    // KhanhHC2 2020-05-04: DisRatio in (100,50)
                    if (list[i].Item1 != 0 && (list[i].Item2 == 100 || list[i].Item2 == 50) && list[i].Item1 == (MaxMonth + 1))
                    {
                        current.Add(list[i]);
                        if (current.Last().Item1 + 1 == list[i + 1].Item1)
                        {
                            current.Add(list[i + 1]);
                        }
                    }
                }
                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { BCLKM = MaxMonth, CLKM = list, QUERY = current }));

                string Paidtype;
                if (condition.PrepaidTimeNet == 0) Paidtype = "PostPaid"; else Paidtype = "Prepaid";
                if (current.Count() != 0) instance.Add(new ItemNode() { ContentDiscount = "FreeMonth" + Paidtype + "NET", ServiceCode = -2, Value = current.Count().ToString(), Dismonth = 0 });

                // Container
                if (condition.NETID > 0 && input.ContainerMonth > 0)
                {
                    //string Paidtype;
                    if (condition.PrepaidTimeNet == 0) Paidtype = "PostPaid"; else Paidtype = "Prepaid";
                    instance.Add(new ItemNode() { ContentDiscount = "FreeMonth" + Paidtype + "NET", ServiceCode = -2, Value = input.ContainerMonth.ToString(), Dismonth = 0 });
                }
            }

            // X đồng Y tháng + Giảm Tiền Trực Tiếp NET TrảTrước
            if (condition.PrepaidTimeNet > 0)
            {
                //Discount TT NET trả trước
                bool ConditionDiscountNET = (input.ID1 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode1);
                if (ConditionDiscountNET)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountPreNET",
                        ServiceCode = input.ServiceCode1,
                        Value = input.Value1.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }

                // SubsProm
                bool ConditionDiscountByMonthNET = (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode2 && input.Value2 != 0 && input.DisMonth2 != 0);
                if (ConditionDiscountByMonthNET)
                {
                    int MonthValid = GetDismonthValidate(input.DisMonth2, condition.PrepaidTimeNet);
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountNETByMonth",
                        ServiceCode = input.ServiceCode2,
                        Value = (input.Value2.VATDiscount() * MonthValid).ToString(),
                        Dismonth = MonthValid
                    });
                }

                if (!ConditionDiscountByMonthNET)
                {
                    int promotionNetAmount = 0;
                    int dismonthValidate = 0;
                    // by PromotionNetID
                    ConditionDiscountByMonthNET = (input.PromotionNetAmount > 0 && (condition.ServiceCodeTTNet != 0) && (condition.ServiceCodeTTNet == input.ServiceCode2));
                    if (ConditionDiscountByMonthNET)
                    {
                        //promotionNetAmount = input.PromotionNetAmount.VATDiscount();
                        // KhanhHC2 2020-06-03 khong ap dung tang thang khi so thang tra truoc nho hop so thang tang
                        //if (ConditionDiscountByMonthNET && condition.IsPrepaidNET < input.PromotionNetDuration)
                        //    ConditionDiscountByMonthNET = false;

                        // dev
                        if (input.PromotionNetDuration > 0)
                        {
                            dismonthValidate = GetDismonthValidate(input.PromotionNetDuration, condition.PrepaidTimeNet);
                            promotionNetAmount = (input.PromotionNetAmount / input.PromotionNetDuration).VATDiscount() * dismonthValidate;
                        }
                    }
                    else
                    {
                        ConditionDiscountByMonthNET = (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode2 && input.Value2 != 0 && input.DisMonth2 != 0);
                        dismonthValidate = GetDismonthValidate(input.DisMonth2, condition.PrepaidTimeNet);
                        promotionNetAmount = input.Value2.VATDiscount() * dismonthValidate;
                    }
                    if (ConditionDiscountByMonthNET)
                    {

                        instance.Add(new ItemNode()
                        {
                            ContentDiscount = "DiscountAmountNETByMonth",
                            ServiceCode = input.ServiceCode2,
                            Value = promotionNetAmount.ToString(),
                            Dismonth = dismonthValidate
                        });
                    }
                }
            }

            //  X Đồng Y tháng + Giảm Tiền Trực Tiếp NET TrảSau
            if (condition.PrepaidTimeNet == 0)
            {
                // X đồng Y Tháng
                if (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && input.ServiceCode2 == condition.ServiceCodeTTNet && input.Value2 != 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountCLKMNET",
                        ServiceCode = -1,
                        Value = input.Value2.VATDiscount().ToString(),
                        Dismonth = input.DisMonth2
                    });
                }
                // Giảm Tiền TT 
                if (input.ID1 != 0 && input.ServiceCode1 == condition.ServiceCodeTTNet && input.Value1 != 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountPostNET",
                        ServiceCode = 56,
                        Value = input.Value1.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }
            }

            // Tặng Tháng Cước TV
            if (condition.PrepaidTimeTV > 0)
            {
                int duration = CheckApplyCLKMTV(input.ID3, condition.IPTVID, true);
                L.Mes(Level.INFO, string.Format("{0}{1}", input.ID3, condition.IPTVID));

                if (duration > 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "FreeMonthPrepaidTV",
                        ServiceCode = -3,
                        Value = duration.ToString(),
                        Dismonth = 0
                    });
                }
            }

            if (condition.PrepaidTimeTV == 0)
            {
                int duration = CheckApplyCLKMTV(input.ID3, condition.IPTVID, false);
                if (duration != 0) instance.Add(new ItemNode() { ContentDiscount = "FreeMonthPostPaidTV", ServiceCode = -3, Value = duration.ToString(), Dismonth = 0 });
            }

            // X Đồng Y Tháng TV + Giảm Tiền Trực Tiếp TV TrảTrước
            if (condition.PrepaidTimeTV > 0)
            {
                bool ConditionDiscountTV = (input.ServiceCode7 == condition.ServiceCodeTTTV && input.Value7 != 0);
                if (ConditionDiscountTV)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTV",
                        ServiceCode = input.ServiceCode7,
                        Value = input.Value7.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }

                bool ConditionDiscountByMonthTV = ((input.ID3 != 0) && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV);

                if (ConditionDiscountByMonthTV)
                {
                    int ValidMonth = GetDismonthValidate(input.DisMonth3, condition.PrepaidTimeTV);
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTVByMonth",
                        ServiceCode = input.ServiceCode3,
                        Value = (input.Value3.VATDiscount() * ValidMonth).ToString(),
                        Dismonth = ValidMonth
                    });

                }
            }

            // X đồng Y tháng TV + Giảm Tiền Trực Tiếp TV TrảSau
            if (condition.PrepaidTimeTV == 0)
            {
                // Giảm Tiền Trực Tiếp
                if (input.ID7 != 0 && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV)
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTV",
                        ServiceCode = input.ServiceCode7,
                        Value = input.Value7.VATDiscount().ToString(),
                        Dismonth = 0
                    });

                // X Dồng Y Tháng TV
                if (input.ID3 != 0 && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountCLKMTV",
                        ServiceCode = -1,
                        Value = input.Value3.VATDiscount().ToString(),
                        Dismonth = input.DisMonth3
                    });
                }
            }

            // CLKM Hòa Mạng NET
            if (input.ID4 != 0 && (condition.ServiceCodeHMNet != 0) && (condition.ServiceCodeHMNet == input.ServiceCode4))
                instance.Add(new ItemNode() { ContentDiscount = "DiscountNewInterConnNET", ServiceCode = input.ServiceCode4, Value = (string)input.Value4.VATDiscount().ToString(), Dismonth = 0 });
            // CLKM Hòa Mạng TV
            if (input.ID5 != 0 && (condition.ServiceCodeHMTV != 0) && (condition.ServiceCodeHMTV == input.ServiceCode5))
                instance.Add(new ItemNode() { ContentDiscount = "DiscountNewInterConnTV", ServiceCode = input.ServiceCode5, Value = (string)input.Value5.VATDiscount().ToString(), Dismonth = 0 });
            // Quà Tặng
            if (input.ID6 != 0)
                instance.Add(new ItemNode() { ContentDiscount = input.Discount6 + input.Model, ServiceCode = 0, Dismonth = 0, Value = "0" });
            if (condition.VoucherCode.StartsWith("MSB") && condition.VoucherCode.EndsWith("FPT"))
            {
                foreach (var item in instance)
                {
                    item.ServiceCode = -4;
                }
            }

            return instance;
        }
        private static Boolean CheckValidServiceCode(List<int> SPE, List<int> SIP)
        {
            return (SPE.Where(p => p != 0 && !SIP.Any(p2 => p2 == p)).Count() == 0);
        }

        private static Boolean AddHistoryTempCode(string OrderCode, string VoucherCode, int ObjectID, InfoVoucherRequest condition)
        {
            int flag = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                flag = connection.Query<int>(ConstantAPI.NameStore_HistoryCart, new
                {
                    ActionName = "AddCode",
                    VoucherCode = VoucherCode,
                    OrderCode = OrderCode,
                    ObjectID = ObjectID,
                    IsPrepaidNET = condition.PrepaidTimeNet,
                    IsPrepaidTV = condition.PrepaidTimeTV,
                    BNET = condition.NETID,
                    BTV = condition.IPTVID,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return (flag > 0);
        }
    }
}