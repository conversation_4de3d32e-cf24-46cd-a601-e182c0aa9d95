using GetPromotionEventByCode.Constant;
using GetPromotionEventByCode.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;

namespace GetPromotionEventByCode.Service
{
    public class VoucherGeneralService
    {
        public static ResponseModel<InfoVoucherResponse> GetPromotionEventByVoucherCode(InfoVoucherRequest condition, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(condition), "GetPromotionEventByVoucherCode req: ");
            
            PromotionEventInfo res = new PromotionEventInfo();
            ResponseModel<InfoVoucherResponse> response = new ResponseModel<InfoVoucherResponse>()
            {
                data = new InfoVoucherResponse() { Type = 2, PromotionEvent = null },
                result = 1,
                error = null
            };

            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();                
                    res = connection.Query<PromotionEventInfo>(ConstantAPI.NameStore_APIMBS, new
                    {
                        ActionName = "GetPromotionEvent",
                        EventCode = condition.VoucherCode,
                        PaidTimeType = condition.IDPrepaid,
                        Localtype = condition.LocalType,
                        RowAffected = 0
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(res), "GetPromotionEventByVoucherCode PromotionEventInfo: ");

                    if (res == null)
                    {
                        response.error = "Thông Tin Voucher Không Tồn Tại";
                        return response;
                    }

                    res.QuotaGeneralWSOL = connection.Query<int>(ConstantAPI.NameStore_InviteCode, new
                    {
                        ActionName = "CheckQuotaCode",
                        ID = res.ID,
                        RowAffected = 0
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(res.QuotaGeneralWSOL), "GetPromotionEventByVoucherCode QuotaGeneralWSOL: ");

                    if (res.QuotaGeneralWSOL < 1)
                    {
                        response.error = "Mã Voucher Đã Hết Số Lần Kích Hoạt";
                        return response;
                    }

                    if (res.ID1 != 0 || res.ID2 != 0)
                    {
                        res.ServiceCode2 = connection.Query<int>(ConstantAPI.NameStore_APIMBS, new
                        {
                            ActionName = "GetServiceCodeNET",
                            Localtype = condition.LocalType,
                            RowAffected = 0
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(res.ServiceCode2), "GetPromotionEventByVoucherCode res.ServiceCode2: ");
                    }
                

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(res), "GetPromotionEventByVoucherCode full PromotionEventInfo: ");
                
            }
            if (res == null || !HiFPTAdvocacy.CheckValidServiceCode(
                    new List<int>() { res.ServiceCode1, res.ServiceCode2, res.ServiceCode3, res.ServiceCode4, res.ServiceCode5, 0, res.ServiceCode7 },
                    new List<int>() { condition.ServiceCodeTTNet, condition.ServiceCodeTTTV, condition.ServiceCodeHMNet, condition.ServiceCodeHMTV }
                ))
                return response;
            response.data.PromotionEvent = HiFPTAdvocacy.GetItemNode(res, condition);

            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(response), "GetPromotionEventByVoucherCode response: ");
            

            if (response.data.PromotionEvent.Count() != 0)
                HiFPTAdvocacy.AddHistoryTempCode(condition.OrderCode, condition.VoucherCode, 0, condition);

            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject("AddHistoryTempCode done and end"), "GetPromotionEventByVoucherCode response: ");
            
            return response;
        }
    }
}