using GetPromotionEventByCode.Constant;
using GetPromotionEventByCode.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;

namespace GetPromotionEventByCode.Service
{
    public class CameraService
    {        
        public static List<ItemNode> GetPromotionRfCam(string VoucherCode, List<Camera> input, string logId)
        {

            WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + 
                "DKOL_GetInfoPromotionEvent GetPromotionRfCam" + JsonConvert.SerializeObject(input));
            List<ItemNode> instance = new List<ItemNode>(); 

            PromotionEventInfo Ps = new PromotionEventInfo();
            try
            {
                foreach (var item in input)
                {
                    ItemNode itemnode = new ItemNode();
                    using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                    {
                        Ps = connection.Query<PromotionEventInfo>(ConstantAPI.OS6_ReferalProgram_CAM, new
                        {
                            ActionName = "GetPromotionEvent",
                            InviteCode = VoucherCode,
                            quantity = item.QuantityCam,
                            serviceCode = item.ServiceCodeTTCAM,
                            EfId = item.EFCodeID
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 5);

                        WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) +
                            "DKOL_GetPromotionEventByInviteCode Camera GetPE " + JsonConvert.SerializeObject(Ps));
                        Ps.QuotaGeneralWSOL = connection.Query<int>(ConstantAPI.NameStore_InviteCode, new
                        {
                            ActionName = "CheckQuotaCode",
                            ID = Ps.ID,
                            RowAffected = 0
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) +
                            "DKOL_GetPromotionEventByInviteCode Camera QuotaGeneralWSOL " + JsonConvert.SerializeObject(Ps.QuotaGeneralWSOL));

                        if (Ps.QuotaGeneralWSOL < 1)
                        {
                            //response.error = "Mã RP Đã Hết Số Lần Kích Hoạt";
                            //return response;
                        }
                    }
                    itemnode = GetItemNodeCam(Ps,item);
                    instance.Add(itemnode);
                }                               
            }
            catch (Exception ex)
            {
                WebAPIHelper.LogInfo("RF", string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + 
                    "DKOL_GetPromotionEventByInviteCode GetPE Camera " + JsonConvert.SerializeObject(ex.Message));
                return instance;
            }
            return instance;
        }
        private static ItemNode GetItemNodeCam(PromotionEventInfo input, Camera condition)
        {
            var item = new ItemNode();             
                // Giảm Tiền TT Camera
            if ( input.ServiceCode8 == condition.ServiceCodeTTCAM && input.Value8 != 0)
            {
                item.ContentDiscount = "DiscountAmountCamera";
                item.ServiceCode = input.ServiceCode8;
                item.Value = input.Value8.VATDiscount().ToString();
                item.Dismonth = 0;
            }
            return item;
        }
    }
}