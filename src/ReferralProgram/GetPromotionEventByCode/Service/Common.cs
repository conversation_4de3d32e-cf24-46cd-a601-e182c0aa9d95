using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using Voucher.APIHelper.Log4net;
using System.Data.SqlClient;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using GetPromotionEventByCode.Models;
using GetPromotionEventByCode.Constant;
using System.Xml.Linq;

namespace GetPromotionEventByCode.Service
{
    public class Common
    {
        private static List<VoucherCodeHistory> GetAllVoucherHistory(string ordercode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    return connection.Query<VoucherCodeHistory>("PowerInside.dbo.OS6_FPTVoucher_GetVoucherHistory", new
                    {
                        ordercode = ordercode
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                }
            }
        }

        public static bool checkRuleApplyVoucher(string orderCode, string voucher)
        {
            List<VoucherCodeHistory> listVoucher = GetAllVoucherHistory(orderCode);
            if (listVoucher.Count() <= 3)
            {
                int n;
                listVoucher.Add(new VoucherCodeHistory { voucherCode = voucher });
                
                int iCode = listVoucher.Where(c => c.voucherCode.ToUpper().IndexOf("FTEL") > -1 || c.voucherCode.ToUpper().StartsWith("MRK") || c.voucherCode.ToUpper().Equals("SINHNHAT")
                    || c.voucherCode.ToUpper().Equals("DKOLT05") || int.TryParse(c.voucherCode, out n) == true).Count();
                if (iCode >= 2)   
                {
                    return false;
                }
                return true;
            }
            else
            {
                return false;
            }
        }

        public static int GetDismonthValidate(int MonthCLKMVoucher, int MonthCLKMBase)
        {
            return (MonthCLKMVoucher <= MonthCLKMBase) ? MonthCLKMVoucher : MonthCLKMBase;
        }
        public static int CheckApplyCLKMTV(int PE, int CLKMTV, Boolean Isprepaid)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@IsPrepaidTV", PE);  // query trong KM Voucher
                    parameters.Add("@IPTVID", CLKMTV);   // query  trong KM Nền
                    parameters.Add("@RowAffected", 0);

                    if (Isprepaid) parameters.Add("@ActionName", "GetInfoPrepaidTV"); // So Sánh Nền Prepaid
                    else parameters.Add("@ActionName", "CheckInfoPostPaidTV");        // So Sánh Nền Postpaid
                    res = connection.Query<int>(ConstantAPI.NameStore_APIMBS, parameters, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return res;
        }
        #region bo sung rule áp dụng mã voucher lẻ của CTKM 
        public static int CheckPrivateCodeExist(List<VoucherCodeHistory> voucherList)
        {
            int checkPC = 0;
            using(var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                checkPC = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_AddRuleVoucherPrivateCode", new
                {
                    Action = "CheckPrivateCodeExist",
                    Xml = CreateXMLVoucherCode(voucherList)
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return checkPC;
        }
        public static int CheckPrivateAndGTBB(List<VoucherCodeHistory> voucherList)
        {
            int checkRF = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                checkRF = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_AddRuleVoucherPrivateCode", new
                {
                    Action = "CheckPrivateCodeAndGTBB",
                    Xml = CreateXMLVoucherCode(voucherList)
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return checkRF;
        }
        public static XElement CreateXMLVoucherCode(List<VoucherCodeHistory> voucherList)
        {
            var xmlString = new XElement("N", // Nodes
            from item in voucherList
            select new XElement("E",
                new XElement("Code", item.voucherCode)
            ));
            return xmlString;
        }
        #endregion
    }
}