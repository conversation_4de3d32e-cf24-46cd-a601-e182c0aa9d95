using GetPromotionEventByCode.Constant;
using GetPromotionEventByCode.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;

namespace GetPromotionEventByCode.Service
{
    public class HiFPTAdvocacy
    {
        private static ResponseModel<InfoVoucherResponse> GetPromotionInforByGeneralCode(InfoVoucherRequest input)
        {
            ResponseModel<InfoVoucherResponse> response = new ResponseModel<InfoVoucherResponse>()
            {
                data = new InfoVoucherResponse() { PromotionEvent = null, Type = 1 },
                error = null,
                result = 1
            };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    int quota = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_GeneralCodeMKT", new
                    {
                        actionName = "checkQuotarCodeGC",
                        voucherCode = input.VoucherCode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (quota == 1)
                    {
                        response.error = "Mã Đã Hết Số Lần Kích Hoạt";
                        return response;
                    }

                    PromotionEventFoxpro res = connection.Query<PromotionEventFoxpro>("PowerInside.dbo.OS6_FPTVoucher_GeneralCodeMKT", new
                    {
                        ActionName = "GetPromotionEvent",
                        PaidTimeTypeID = input.IDPrepaid,
                        voucherCode = input.VoucherCode
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (res != null)
                    {
                        if (input.PrepaidTimeNet == 0)
                            res.Name = "DiscountAmountPostNET";
                        else
                            res.Name = "DiscountAmountPreNET";
                        decimal valueDiscountAmount = Math.Round(res.ValueDiscountAmount * (decimal)0.11, 0) * 10;
                        List<ItemNode> lst = new List<ItemNode>();
                        lst.Add(new ItemNode() { ContentDiscount = res.Name, Dismonth = 0, ServiceCode = input.ServiceCodeTTNet, Value = valueDiscountAmount.ToString() });
                        //response.data.PromotionEvent = new List<ItemNode>();
                        response.data = new InfoVoucherResponse();
                        response.data.PromotionEvent = lst;
                        response.data.Type = 1;
                        //response.data.FullNameRef = "Mã giới thiệu của Nhân viên FPT";// getStaffInfor(staffid);
                        response.data.PhoneRef = null;

                        AddHistoryTempCode(input.OrderCode, input.VoucherCode, 0, input);

                        return response;
                    }
                    return response;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "GetPromotionInforByGeneralCode");
                return null;
            }
        }
        public static Boolean AddHistoryTempCode(string OrderCode, string VoucherCode, int ObjectID, InfoVoucherRequest condition)
        {
            int flag = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                int CamQuantity = 0;
                if (condition.Cameras.Count > 0)
                {
                    foreach (var camera in condition.Cameras)
                    {
                        CamQuantity = CamQuantity + camera.QuantityCam;
                        if (VoucherCode.ToUpper().IndexOf("FTEL") > -1)
                        {
                            connection.Execute("PowerInside.dbo.OS6_ReferalProgram_CAM", new
                            {
                                ActionName = "AddOrderCam",
                                Code = VoucherCode,
                                OrderCode = OrderCode,
                                CamQuantity = camera.QuantityCam,
                                BCAM = camera.CAM,
                                serviceCode = camera.ServiceCodeTTCAM,
                                EfId = camera.EFCodeID
                            }, commandType: CommandType.StoredProcedure);
                        }
                    }
                }
                flag = connection.Query<int>(ConstantAPI.NameStore_HistoryCart, new
                {
                    ActionName = "AddCode",
                    VoucherCode = VoucherCode,
                    OrderCode = OrderCode,
                    ObjectID = ObjectID,
                    IsPrepaidNET = condition.PrepaidTimeNet,
                    IsPrepaidTV = condition.PrepaidTimeTV,
                    BNET = condition.NETID,
                    BTV = condition.IPTVID,
                    ContractSupport = condition.ContractGT,
                    CamQuantity = CamQuantity,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();

            }
            return (flag > 0);
        }
        private static bool isMKTgeneralCode(string voucherCode)
        {
            int flag = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        flag = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_GeneralCodeMKT", new
                        {
                            actionName = "checkStatusCodeGC",
                            voucherCode = voucherCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        return (flag == 1);
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "isMKTgeneralCode");
                return false;
            }
        }

        
        public static ResponseModel<InfoVoucherResponse> GetPromotionEventByInviteCode(InfoVoucherRequest input, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "GetPromotionEventByInviteCode CheckTypeCodeInput: ");
            PromotionEventInfo Ps = new PromotionEventInfo();            

            ResponseModel<InfoVoucherResponse> response = new ResponseModel<InfoVoucherResponse>()
            {
                data = new InfoVoucherResponse(),
                error = null,
                result = 1
            };
            // đăng ký hộ đúng, kiểm tra đúng mã giới thiệu với hợp đồng
            if (!string.IsNullOrEmpty(input.ContractGT))
            {
                bool isTrue = FriendSellService.CheckContractSupport(input.ContractGT, input.VoucherCode);
                if (!isTrue)
                {
                    response.data = null;
                    response.error = "Mã giới thiệu không khớp với thuế bao đăng ký hộ";
                    response.result = 0;
                    return response;
                }
            }
            response.data.PromotionEvent = new List<ItemNode>();
            #region GTBB
            // OTT code
            bool isOTTCode = OTTClientService.checkOTTCode(input.VoucherCode);
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(isOTTCode), "GetPromotionEventByInviteCode checkOTTCode: ");
            if (isOTTCode)
            {
                return OTTClientService.GetReferEvent(input, logId.ToString());
            }
            // kiểm tra code MKT FTEL
            bool checkMKTcode = isMKTgeneralCode(input.VoucherCode);
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(checkMKTcode), "GetPromotionEventByInviteCode checkMKTcode: ");
            if (checkMKTcode)
            {
                return GetPromotionInforByGeneralCode(input);
            }
            // MYFPT
            bool iskMyFPTCode = MyFPTService.checkMyFPTCode(input.VoucherCode);
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(iskMyFPTCode), "GetPromotionEventByInviteCode iskMyFPTCode: ");
            if (iskMyFPTCode)
            {
                return MyFPTService.GetReferEvent(input, logId.ToString());
            }
            #endregion
            if (input.NETID > 0)
            {
                #region NEt + TH
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    if (input.VoucherCode.StartsWith("MSB") && input.VoucherCode.EndsWith("FPT"))
                    {
                        #region MSB
                        Ps = connection.Query<PromotionEventInfo>(ConstantAPI.NameStore_InviteCode, new
                        {
                            ActionName = "GetInfoPromotionEventByInviteCode_MSB",
                            InviteCode = input.VoucherCode,
                            LocalType = input.LocalType,
                            PaidTimeType = input.IDPrepaid,
                            RowAffected = 0
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 4);
                        #endregion
                    }
                    else
                    {
                        Ps = connection.Query<PromotionEventInfo>(ConstantAPI.NameStore_InviteCode, new
                        {
                            ActionName = "GetInfoPromotionEventByInviteCode",
                            InviteCode = input.VoucherCode,
                            LocalType = input.LocalType,
                            PaidTimeType = input.IDPrepaid,
                            RowAffected = 0
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 2);
                    }

                    if (Ps == null || string.IsNullOrEmpty(Ps.EventCode))
                    {
                        response.error = "Mã RP không hợp lệ";
                        response.data.PromotionEvent = null;
                        response.result = 0;
                        return response;
                    }
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(Ps), "GetPromotionEventByInviteCode GetPE: ");

                    Ps.QuotaGeneralWSOL = connection.Query<int>(ConstantAPI.NameStore_InviteCode, new
                    {
                        ActionName = "CheckQuotaCode",
                        ID = Ps.ID,
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(Ps.QuotaGeneralWSOL), "GetPromotionEventByInviteCode Ps.QuotaGeneralWSOL: ");
                    if (Ps.QuotaGeneralWSOL < 1)
                    {
                        //response.error = "Mã RP Đã Hết Số Lần Kích Hoạt";
                        return response;
                    }

                    // ValidateServiceCode
                    Boolean CheckServiceCode = CheckValidServiceCode(new List<int>() { Ps.ServiceCode1, Ps.ServiceCode2, Ps.ServiceCode3, Ps.ServiceCode4, Ps.ServiceCode5, 0, Ps.ServiceCode7 },
                        new List<int>() { input.ServiceCodeTTNet, input.ServiceCodeTTTV, input.ServiceCodeHMNet, input.ServiceCodeHMTV });

                    if (!CheckServiceCode) return response;

                    Ps.ServiceCode2 = connection.Query<int>(ConstantAPI.NameStore_APIMBS, new
                    {
                        ActionName = "GetServiceCodeNET",
                        Localtype = input.LocalType,
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(Ps.ServiceCode2), "GetPromotionEventByInviteCode Ps.ServiceCode2: ");
                    List<ItemNode> inode = GetItemNode(Ps, input);
                    response.data.PromotionEvent.AddRange(inode);
                }
                #endregion
            }

            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                UserInfo userInfo = connection.Query<UserInfo>(ConstantAPI.NameStore_InviteCode, new
                {
                    ActionName = "GetInfoUserByInviteCode",
                    InviteCode = input.VoucherCode,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(userInfo), "GetPromotionEventByInviteCode userInfo: ");

                if (input.VoucherCode.StartsWith("MSB") && input.VoucherCode.EndsWith("FPT"))
                {
                    response.data.Type = 1;
                    response.data.ContractRef = "MSB";
                    response.data.FullNameRef = "MSB";
                    response.data.PhoneRef = "MSBphone";
                }
                else if (userInfo == null)
                {
                    response.error = "Thông tin người giơi thiệu không chính sác"; return response;
                }
                else
                {
                    response.data.Type = 1;
                    response.data.ContractRef = userInfo.ContractRef;
                    response.data.FullNameRef = userInfo.FullNameRef;
                    response.data.PhoneRef = userInfo.PhoneRef;
                }
            }

            if (input.Cameras.Count > 0 && input.VoucherCode.IndexOf("FTEL") > -1)
            {
                #region CAM
                List<ItemNode> camnode = CameraService.GetPromotionRfCam(input.VoucherCode, input.Cameras, logId.ToString());

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(camnode), "GetPromotionEventByInviteCode camnode: ");

                if (camnode != null && camnode.Count >0)
                {
                    response.data.PromotionEvent.AddRange(camnode);
                }
                else
                {
                    response.result = 0;
                }
                #endregion
            }
            if (input.NETID.Equals(0) && input.Cameras.Count > 0)
            {
                AddHistoryTempCode(input.OrderCode, input.VoucherCode, 0, input);
                return response;
            }

            if (response.data.PromotionEvent.Count() != 0)
                AddHistoryTempCode(input.OrderCode, input.VoucherCode, 0, input);

            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(response), "GetPromotionEventByInviteCode responseData: ");
            return response;
        }
        public static Boolean CheckValidServiceCode(List<int> SPE, List<int> SIP)
        {
            return (SPE.Where(p => p != 0 && !SIP.Any(p2 => p2 == p)).Count() == 0);
        }
        private static int GetInfoNET(int NETID)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    res = connection.Query<int>(ConstantAPI.NameStore_APIMBS, new
                    {
                        ActionName = "GetInfoPrePaidNET",
                        IsPrepaidNET = NETID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return res;
        }
        public static List<ItemNode> GetItemNode(PromotionEventInfo input, InfoVoucherRequest condition)
        {
            List<ItemNode> instance = new List<ItemNode>();
            // X đồng Y tháng NET Trả Sau   : ServiceCode -1
            // Tặng Tháng Cước NET          : ServiceCode -2

            // Tặng Tháng Cước NET (Trả trước + Trả sau)
            if (condition.PrepaidTimeNet > -1)
            {
                // SubsProm
                int MaxMonth = GetInfoNET(condition.NETID);
                var list = new List<Tuple<int, int>>(){ Tuple.Create(input.DisMonth2L2, input.DisRatio2L2),
                                                             Tuple.Create(input.DisMonth2L3, input.DisRatio2L3),
                                                             Tuple.Create(input.DisMonth2L4, input.DisRatio2L4)};
                var current = new List<Tuple<int, int>>();
                for (int i = 0; i < list.Count(); i++)
                {
                    // KhanhHC2 2020-05-04: DisRatio in (100,50)
                    if (list[i].Item1 != 0 && (list[i].Item2 == 100 || list[i].Item2 == 50) && list[i].Item1 == (MaxMonth + 1))
                    {
                        current.Add(list[i]);
                        if (current.Last().Item1 + 1 == list[i + 1].Item1)
                        {
                            current.Add(list[i + 1]);
                        }
                    }
                }
                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { BCLKM = MaxMonth, CLKM = list, QUERY = current }));

                string Paidtype;
                if (condition.PrepaidTimeNet == 0) Paidtype = "PostPaid"; else Paidtype = "Prepaid";
                if (current.Count() != 0) instance.Add(new ItemNode() { ContentDiscount = "FreeMonth" + Paidtype + "NET", ServiceCode = -2, Value = current.Count().ToString(), Dismonth = 0 });

                // Container
                if (condition.NETID > 0 && input.ContainerMonth > 0)
                {
                    //string Paidtype;
                    if (condition.PrepaidTimeNet == 0) Paidtype = "PostPaid"; else Paidtype = "Prepaid";
                    instance.Add(new ItemNode() { ContentDiscount = "FreeMonth" + Paidtype + "NET", ServiceCode = -2, Value = input.ContainerMonth.ToString(), Dismonth = 0 });
                }
            }

            // X đồng Y tháng + Giảm Tiền Trực Tiếp NET TrảTrước
            if (condition.PrepaidTimeNet > 0)
            {
                //Discount TT NET trả trước
                bool ConditionDiscountNET = (input.ID1 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode1);
                if (ConditionDiscountNET)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountPreNET",
                        ServiceCode = input.ServiceCode1,
                        Value = input.Value1.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }

                // SubsProm
                bool ConditionDiscountByMonthNET = (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode2 && input.Value2 != 0 && input.DisMonth2 != 0);
                if (ConditionDiscountByMonthNET)
                {
                    int MonthValid = Common.GetDismonthValidate(input.DisMonth2, condition.PrepaidTimeNet);
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountNETByMonth",
                        ServiceCode = input.ServiceCode2,
                        Value = (input.Value2.VATDiscount() * MonthValid).ToString(),
                        Dismonth = MonthValid
                    });
                }

                if (!ConditionDiscountByMonthNET)
                {
                    int promotionNetAmount = 0;
                    int dismonthValidate = 0;
                    // by PromotionNetID
                    ConditionDiscountByMonthNET = (input.PromotionNetAmount > 0 && (condition.ServiceCodeTTNet != 0) && (condition.ServiceCodeTTNet == input.ServiceCode2));
                    if (ConditionDiscountByMonthNET)
                    {
                        //promotionNetAmount = input.PromotionNetAmount.VATDiscount();
                        // KhanhHC2 2020-06-03 khong ap dung tang thang khi so thang tra truoc nho hop so thang tang
                        //if (ConditionDiscountByMonthNET && condition.IsPrepaidNET < input.PromotionNetDuration)
                        //    ConditionDiscountByMonthNET = false;

                        // dev
                        if (input.PromotionNetDuration > 0)
                        {
                            dismonthValidate = Common.GetDismonthValidate(input.PromotionNetDuration, condition.PrepaidTimeNet);
                            promotionNetAmount = (input.PromotionNetAmount / input.PromotionNetDuration).VATDiscount() * dismonthValidate;
                        }
                    }
                    else
                    {
                        ConditionDiscountByMonthNET = (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && condition.ServiceCodeTTNet == input.ServiceCode2 && input.Value2 != 0 && input.DisMonth2 != 0);
                        dismonthValidate = Common.GetDismonthValidate(input.DisMonth2, condition.PrepaidTimeNet);
                        promotionNetAmount = input.Value2.VATDiscount() * dismonthValidate;
                    }
                    if (ConditionDiscountByMonthNET)
                    {

                        instance.Add(new ItemNode()
                        {
                            ContentDiscount = "DiscountAmountNETByMonth",
                            ServiceCode = input.ServiceCode2,
                            Value = promotionNetAmount.ToString(),
                            Dismonth = dismonthValidate
                        });
                    }
                }
            }

            //  X Đồng Y tháng + Giảm Tiền Trực Tiếp NET TrảSau
            if (condition.PrepaidTimeNet == 0)
            {
                // X đồng Y Tháng
                if (input.ID2 != 0 && condition.ServiceCodeTTNet != 0 && input.ServiceCode2 == condition.ServiceCodeTTNet && input.Value2 != 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountCLKMNET",
                        ServiceCode = -1,
                        Value = input.Value2.VATDiscount().ToString(),
                        Dismonth = input.DisMonth2
                    });
                }
                // Giảm Tiền TT 
                if (input.ID1 != 0 && input.ServiceCode1 == condition.ServiceCodeTTNet && input.Value1 != 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountPostNET",
                        ServiceCode = 56,
                        Value = input.Value1.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }
            }

            // Tặng Tháng Cước TV
            if (condition.PrepaidTimeTV > 0)
            {
                int duration = Common.CheckApplyCLKMTV(input.ID3, condition.IPTVID, true);
                L.Mes(Level.INFO, string.Format("{0}{1}", input.ID3, condition.IPTVID));

                if (duration > 0)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "FreeMonthPrepaidTV",
                        ServiceCode = -3,
                        Value = duration.ToString(),
                        Dismonth = 0
                    });
                }
            }

            if (condition.PrepaidTimeTV == 0)
            {
                int duration = Common.CheckApplyCLKMTV(input.ID3, condition.IPTVID, false);
                if (duration != 0) instance.Add(new ItemNode() { ContentDiscount = "FreeMonthPostPaidTV", ServiceCode = -3, Value = duration.ToString(), Dismonth = 0 });
            }

            // X Đồng Y Tháng TV + Giảm Tiền Trực Tiếp TV TrảTrước
            if (condition.PrepaidTimeTV > 0)
            {
                bool ConditionDiscountTV = (input.ServiceCode7 == condition.ServiceCodeTTTV && input.Value7 != 0);
                if (ConditionDiscountTV)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTV",
                        ServiceCode = input.ServiceCode7,
                        Value = input.Value7.VATDiscount().ToString(),
                        Dismonth = 0
                    });
                }

                bool ConditionDiscountByMonthTV = ((input.ID3 != 0) && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV);

                if (ConditionDiscountByMonthTV)
                {
                    int ValidMonth = Common.GetDismonthValidate(input.DisMonth3, condition.PrepaidTimeTV);
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTVByMonth",
                        ServiceCode = input.ServiceCode3,
                        Value = (input.Value3.VATDiscount() * ValidMonth).ToString(),
                        Dismonth = ValidMonth
                    });

                }
            }

            // X đồng Y tháng TV + Giảm Tiền Trực Tiếp TV TrảSau
            if (condition.PrepaidTimeTV == 0)
            {
                // Giảm Tiền Trực Tiếp
                if (input.ID7 != 0 && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV)
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountAmountTV",
                        ServiceCode = input.ServiceCode7,
                        Value = input.Value7.VATDiscount().ToString(),
                        Dismonth = 0
                    });

                // X Dồng Y Tháng TV
                if (input.ID3 != 0 && condition.ServiceCodeTTTV != 0 && input.ServiceCode3 == condition.ServiceCodeTTTV)
                {
                    instance.Add(new ItemNode()
                    {
                        ContentDiscount = "DiscountCLKMTV",
                        ServiceCode = -1,
                        Value = input.Value3.VATDiscount().ToString(),
                        Dismonth = input.DisMonth3
                    });
                }
            }

            // CLKM Hòa Mạng NET
            if (input.ID4 != 0 && (condition.ServiceCodeHMNet != 0) && (condition.ServiceCodeHMNet == input.ServiceCode4))
                instance.Add(new ItemNode() { ContentDiscount = "DiscountNewInterConnNET", ServiceCode = input.ServiceCode4, Value = (string)input.Value4.VATDiscount().ToString(), Dismonth = 0 });
            // CLKM Hòa Mạng TV
            if (input.ID5 != 0 && (condition.ServiceCodeHMTV != 0) && (condition.ServiceCodeHMTV == input.ServiceCode5))
                instance.Add(new ItemNode() { ContentDiscount = "DiscountNewInterConnTV", ServiceCode = input.ServiceCode5, Value = (string)input.Value5.VATDiscount().ToString(), Dismonth = 0 });
            // Quà Tặng
            if (input.ID6 != 0)
                instance.Add(new ItemNode() { ContentDiscount = input.Discount6 + input.Model, ServiceCode = 0, Dismonth = 0, Value = "0" });
            if (condition.VoucherCode.StartsWith("MSB") && condition.VoucherCode.EndsWith("FPT"))
            {
                foreach (var item in instance)
                {
                    item.ServiceCode = -4;
                }
            }

            return instance;
        }
    }
}