using GetPromotionEventByCode.Constant;
using GetPromotionEventByCode.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;

namespace GetPromotionEventByCode.Service
{
    public class FriendSellService
    {
        const string OS6_FPTVoucher_FriendSellEvent = "PowerInside.dbo.OS6_FPTVoucher_FriendSellEvent";
        public static bool CheckContractSupport(string contract, string code)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {                
                int flag = connection.Query<int>(OS6_FPTVoucher_FriendSellEvent, new
                {
                    actionName = "CheckContractSell",
                    @contractSell = contract,
                    @code=code
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return (flag == 1);                
            }
        }
    }
}