using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace GetPromotionEventByCode.Models
{
    public class MyFPTModel
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string refresh_token { set; get; }
        public string accessTokenExpiresOn { set; get; }
        public string refreshTokenExpiresOn { set; get; }
    }
    public class StaffInfor 
    { 
        public Name data { set; get; }
    }
    public class Name
    {
        public string fullname { set; get; }
        public string email { set; get; }
    }
    public class AuthorizationInfor
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string refresh_token { set; get; }
        public string accessTokenExpiresOn { set; get; }
        public string refreshTokenExpiresOn { set; get; }
    }
}