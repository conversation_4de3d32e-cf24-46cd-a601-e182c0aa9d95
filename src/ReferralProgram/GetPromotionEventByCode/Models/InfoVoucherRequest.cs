using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace GetPromotionEventByCode.Models
{
    public class InfoVoucherRequest
    {
        public string VoucherCode { get; set; }
        public string OrderCode { get; set; }

        public int NETID { get; set; }
        public string ContractGT { get; set; }
        public int IPTVID { get; set; }
        public List<Camera> Cameras { get; set; }

        public int LocalType { get; set; }
        [JsonIgnore]
        public int PrepaidTimeNet { get; set; }
        [JsonIgnore]
        public int IDPrepaid { get; set; }
        
        public int PrepaidTimeTV { get; set; }


        public int ServiceCodeTTNet { get; set; }

        public int ServiceCodeTTTV { get; set; }

        public int ServiceCodeHMNet { get; set; }
        public int ServiceCodeHMTV { get; set; }
    }

    public class Camera
    {
        public int QuantityCam { get; set; }
        public int EFCodeID { get; set; }
        public int CAM { get; set; }
        public int ServiceCodeTTCAM { get; set; }
    }
}