using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace GetPromotionEventByCode.Models
{
    public class InfoVoucherResponse
    {
        public int Type { get; set; }
        public List<ItemNode> PromotionEvent { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string ContractRef { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string FullNameRef { get; set; }
        public string PhoneRef { get; set; }
    }

    public class ReferCodeInfoModel 
    {
        
        public string AppName { set; get; }
        public string CusName { set; get; }
        public string Contract { set; get; }
        public string DurationRefCode { set; get; }
        public string Reason { set; get; }
        public int IsActive { set; get; }
        public string StatusContract { set; get; }
    }
    public class ItemNode
    {
        public string ContentDiscount { get; set; }
        public int ServiceCode { get; set; }
        public string Value { get; set; }
        public int Dismonth { get; set; }
    }

    public class RFCodeModel
    {
        public string inviteCode { set; get; }
        public string shortLink { set; get; }
    }
    
    public class OutPutPublishbillReport
    {
        public List<DataMoney> data { get; set; }
        public int result { set; get; }
        public string message { set; get; }

    }

    public class Money
    {
        public DateTime date { set; get; }
        public int money { set; get; }
        public string note { set; get; }
        public string billNumber { set; get; }

    }

    public class DataMoney
    {
        public int MoneyReturn { set; get; }
        public List<DataTemp> MoneyIn { set; get; }
        public List<DataTemp> MoneyOut { set; get; }
    }

    public class DataTemp
    {
        public string voucher { set; get; }
        public DateTime date { set; get; }
        public int money { set; get; }
        public string note { set; get; }
        public string billNumber { set; get; }
    }
}