using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Voucher.APIHelper;

namespace GetPromotionEventByCode.Models.AdvocacyProgram
{
    public class HistoryAdvocacyModel
    {
        public string ObjIDGT { set; get; }
        public string ContractGT { set; get; }
        public string ObjIDDK { set; get; }
        public string ContractDK { set; get; }
        public string Fullname { set; get; }
        public string FirstAccess { set; get; }
        public string Phone { set; get; }
        public string Reward { set; get; }
        public string CodeFPTPlay { set; get; }
        public string RewardDK { set; get; }
        public string FoxGold { set; get; }
        public int ResourceReg { set; get; }
    }

    public class HistoryAdvocacyModel2
    {
        public string ObjIDGT { set; get; }
        public string ContractGT { set; get; }
        public string ObjIDDK { set; get; }
        public string ContractDK { set; get; }
        public string Fullname { set; get; }
        public string FirstAccess { set; get; }
        public string Phone { set; get; }
        public string Reward { set; get; }
        public string CodeFPTPlay { set; get; }
        public string RewardDK { set; get; }
        public string FoxGold { set; get; }
        public int ResourceReg { set; get; }
    }

    public class ReferalInfoModel
    {
        public string sourceApp { set; get; }
        public string clientNo { set; get; }
        public string clientName { set; get; }
        public string clientEmail { set; get; }
        public string clientPhone { set; get; }
        public string clientInviteCode { set; get; }
        public string referalDate { set; get; }
    }

    public class HistoryOutputModels<T>
    { 
        public List<T> data { set; get; }
        public int page { set; get; } = 0;
        public int rows { set; get; } = 0;
        public int result { get; set; }
        public string error { get; set; }
    }
}