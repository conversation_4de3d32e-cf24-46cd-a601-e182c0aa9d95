using RedeemInviteCode.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using RedeemInviteCode.Contant;
using System.Xml.Linq;
using RedeemVoucher.Models;
using RedeemInviteCode.Models;
using Voucher.APIHelper.Util;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Net.Http.Formatting;
using System.Net.Http.Headers;
using System.Text;
using System.IO;
using System.Configuration;
using RedeemInviteCode.Service;
using System.Web;

namespace RedeemInviteCode.Controllers
{
    public class RedeemInviteCodeController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";

        [Route("API/ReferralProgram/RedeemInviteCode")]
        [Authorize(Roles = AuthorizeRole.Dkol)]
        public async Task<ResponseModel<bool>> post(RedeemInviteCodeRequest input)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), ReferalProgramMyFPT.RamdoneString());
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "RedeemInviteCode");
            bool statusRedeem = true;
            try
            {
                if (!RequestValidate(input))
                    return new ResponseModel<bool>() { result = 0, error = "Dữ liệu không hợp lệ", data = false };

                if (!CheckCodeInput(input))
                    return new ResponseModel<bool>() { result = 0, error = "Code Chưa Được Xác Định", data = false };


                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        List<ItemRequest> InstanceRequestRP = new List<ItemRequest>();  //List RP code
                        List<ItemRequest> InstanceRequestGC = new List<ItemRequest>(); // list GeneralCode
                        for (int i = 0; i < input.VoucherCodes.Count(); i++)
                        {

                            // fix redeem bi lap
                            InstanceRequestRP = new List<ItemRequest>();
                            InstanceRequestGC = new List<ItemRequest>();
                            if (input.VoucherCodes[i].Type == 1 || input.VoucherCodes[i].Type == 4)
                            {
                                int exist = connection.Query<int>(ConstantAPI.StoreName_InviteSuccess, new
                                {
                                    ActionName = "Check_ObjectInvited",
                                    ObjIDInvited = input.ObjectID,
                                    RowAffected = 0
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                                if (exist.Equals(1))
                                {
                                    statusRedeem = false;
                                    break;
                                }
                            }
                            if (input.VoucherCodes[i].Type == 1)
                            {
                                #region MKT
                                int n;
                                Boolean isMKTCode = MKTgeneralCode.isMKTgeneralCode(input.VoucherCodes[i].Code);
                                L.Mes(Level.INFO, isMKTCode.ToString(), "RedeemInviteCode isMKTCode ");
                                var isNumber = int.TryParse(input.VoucherCodes[i].Code, out n);
                                if (!string.IsNullOrEmpty(input.ContractGT))
                                {
                                    int objID = connection.Query<int>(ConstantAPI.StoreName_InviteCode, new
                                    {
                                        ActionName = "GetObjInvite",
                                        InviteCode = input.VoucherCodes[i].Code,
                                        RowAffected = 0
                                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                                    InstanceRequestRP.Add(new ItemRequest()
                                    {
                                        ObjecInvite = objID,
                                        Objectinvited = input.ObjectID,
                                        OrderCode = input.OrderCode,
                                        VoucherCode = input.VoucherCodes[i].Code
                                    });
                                    var isOwnerCamera = ReferralProgramCamera.IsOwnerCamera(connection, transaction, objID, keylog);
                                    if (isOwnerCamera)
                                    {
                                        statusRedeem = await ReferralProgramCamera.RedeemInviteCodeFriendSell(connection, transaction, InstanceRequestRP, input.ContractGT, keylog);
                                        L.Mes(Level.INFO, statusRedeem.ToString(), keylog + " RedeemInviteCode DKH statusRedeem ");
                                    }
                                    else
                                    {
                                        statusRedeem = await FriendSellService.RedeemInviteCode(connection, transaction, InstanceRequestRP, input.ContractGT, keylog);
                                        L.Mes(Level.INFO, statusRedeem.ToString(), keylog + " RedeemInviteCode DKH statusRedeem ");
                                    }
                                }
                                else
                                if (isNumber)
                                {
                                    List<ItemRequest> lstCodeOTT = new List<ItemRequest>();
                                    lstCodeOTT.Add(new ItemRequest() { ObjecInvite = 0, Objectinvited = input.ObjectID, OrderCode = input.OrderCode, VoucherCode = input.VoucherCodes[i].Code });
                                    statusRedeem = OttReferalProgramService.RedeemReferalOTTCode(connection, transaction, lstCodeOTT, keylog);
                                    L.Mes(Level.INFO, statusRedeem.ToString(), keylog +" RedeemInviteCode statusRedeem ");
                                } else
                                if (isMKTCode)
                                {
                                    List<ItemRequest> lstCodeMKT = new List<ItemRequest>();
                                    lstCodeMKT.Add(new ItemRequest() { ObjecInvite = 0, Objectinvited = input.ObjectID, OrderCode = input.OrderCode, VoucherCode = input.VoucherCodes[i].Code });
                                    statusRedeem = MKTgeneralCode.RedeemMKTgeneralCode(connection, transaction, lstCodeMKT);
                                    L.Mes(Level.INFO, statusRedeem.ToString(), keylog + "RedeemInviteCode statusRedeem ");
                                }
                                #endregion 
                                else
                                {
                                    int objID = connection.Query<int>(ConstantAPI.StoreName_InviteCode, new
                                    {
                                        ActionName = "GetObjInvite",
                                        InviteCode = input.VoucherCodes[i].Code,
                                        RowAffected = 0
                                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                                    InstanceRequestRP.Add(new ItemRequest()
                                    {
                                        ObjecInvite = objID,
                                        Objectinvited = input.ObjectID,
                                        OrderCode = input.OrderCode,
                                        VoucherCode = input.VoucherCodes[i].Code
                                    });
                                    // redeem RP code
                                    //statusRedeem = await RedeemInviteCode(connection, transaction, InstanceRequestRP, keylog);

                                    var isOwnerCamera = ReferralProgramCamera.IsOwnerCamera(connection, transaction, objID, keylog);
                                    if (isOwnerCamera)
                                    {
                                        statusRedeem = await ReferralProgramCamera.RedeemInviteCode(connection, transaction, InstanceRequestRP, keylog);
                                    }
                                    else
                                    {
                                        statusRedeem = await ReferalProgramHiFPT.RedeemInviteCode(connection, transaction, InstanceRequestRP, keylog);
                                    }
                                }
                            }
                            #region voucher ma chung
                            if (input.VoucherCodes[i].Type == 2)
                            {
                                // getInfo Prepaid of InviteCode (vì mã RP có nhiều Prepaid nên phải chọn chính xác mã voucher quy đổi nào chính xác)
                                // item1 :  PrepaidNET
                                // item2 :  PrepaidTV
                                // Item3 :  IDPaidTimeType
                                // item4 :  Base ID NET
                                // item5 : Base ID TV
                                var PrepaidInfo = connection.Query<Tuple<int, int, int, int, int>>(ConstantAPI.StoreName_InviteCode, new
                                {
                                    ActionName = "GetPrepaidOfInviteCode",
                                    OrderCode = input.OrderCode,
                                    Code = input.VoucherCodes[i].Code,
                                    RowAffected = 0
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                                L.Mes(Level.INFO, JsonConvert.SerializeObject(PrepaidInfo), "RedeemInviteCode VoucherCodeGC ");

                                InstanceRequestGC.Add(new ItemRequest() { ObjecInvite = 0, Objectinvited = input.ObjectID, OrderCode = input.OrderCode, VoucherCode = input.VoucherCodes[i].Code });
                                bool updateHistoryTemp = UpdateHistoryTemp(connection, transaction, InstanceRequestGC);
                                if (!updateHistoryTemp) statusRedeem = false;
                                L.Mes(Level.INFO, updateHistoryTemp.ToString(), "RedeemInviteCode VoucherCodeGC updateHistoryTemp ");

                                bool insertGCcode = InsertGeneralCodeGC(connection, transaction, InstanceRequestGC, PrepaidInfo);
                                if (!insertGCcode) statusRedeem = false;
                                L.Mes(Level.INFO, updateHistoryTemp.ToString(), "RedeemInviteCode VoucherCodeGC insertGCcode");
                            }
                            #endregion 

                            #region Vc ma le
                            if (input.VoucherCodes[i].Type == 3)
                            {
                                // check Quota
                                Tuple<int, int> InsertGC = connection.Query<Tuple<int, int>>(ConstantAPI.StoreName_Private, new
                                {
                                    ActionName = "GetQuotaPrivateCode",
                                    Code = input.VoucherCodes[i].Code,
                                    RowAffected = 0
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                                L.Mes(Level.INFO, "quotar: " + InsertGC.Item1 + ", promotionID: " + InsertGC.Item2, "VoucherCodes type = 3 - RedeemInviteCode:");
                                if (InsertGC.Item1 == 0)
                                {
                                    statusRedeem = false;
                                }
                                else if (!PrivateGCCode(connection, transaction, input.VoucherCodes[i].Code, InsertGC.Item2, input.OrderCode, input.ObjectID)) statusRedeem = false;
                            }
                            #endregion
                            #region MyfPT
                            if (input.VoucherCodes[i].Type == 4)
                            {
                                //type = 4 là voucher nhập mã giới thiệu của nhân viên (Foxpro)

                                InstanceRequestRP.Add(new ItemRequest() { Objectinvited = input.ObjectID, OrderCode = input.OrderCode, VoucherCode = input.VoucherCodes[i].Code });
                                //statusRedeem = await RedeemInviteCodeMyFPT(connection, transaction, InstanceRequestRP);

                                Boolean UpdateHistoryTempMSB = UpdateHistoryTemp(connection, transaction, InstanceRequestRP);
                                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StsUpdateHistoryTemp = UpdateHistoryTempMSB }), keylog + " UpdateHistoryTemp ");
                                if (UpdateHistoryTempMSB)
                                {
                                    dynamic PrepaidInfo = connection.Query("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                                    {
                                        ActionName = "GetPrepaidOfInviteCode",
                                        OrderCode = input.OrderCode,
                                        inviteCode = input.VoucherCodes[i].Code
                                        //RowAffected = 0
                                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                                    L.Mes(Level.INFO, JsonConvert.SerializeObject(PrepaidInfo), keylog + " RedeemInviteCode Foxpro");

                                    FoxproUseInviteCode insertInviteCode = connection.Query<FoxproUseInviteCode>("PowerInside.dbo.OS6_FPTVoucher_FoxPro", new
                                    {
                                        actionName = "InsertInviteCode",
                                        objID = input.ObjectID,
                                        orderCode = input.OrderCode,
                                        inviteCode = input.VoucherCodes[i].Code,
                                        PaidTimeTypeID = PrepaidInfo.PaidTimeTypeID,
                                        Bnet = PrepaidInfo.BNET
                                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                                    if (insertInviteCode.res == 0)
                                    {
                                        statusRedeem = false;
                                    }
                                    else
                                    {
                                        //int objID = insertInviteCode.ObjID;
                                        int MNET = insertInviteCode.MoneyPromotionNETID;
                                        int MTV = insertInviteCode.MoneyPromotionTVID;
                                        int StaffID = Convert.ToInt32(insertInviteCode.StaffIDInvite);
                                        SendNotificationReferralMyFPT(insertInviteCode, keylog);
                                        UpdateDiscount(
                                             input.ObjectID,  // objID khách hàng
                                             0, //XđồngY tháng NET + Tháng
                                             0, //XđồngY tháng TV + Tháng
                                             MNET, //Giảm Tiền Trực Tiếp NET
                                             MTV, //Giảm Tiền Trực Tiếp TV
                                             insertInviteCode.EventCode, // Code
                                             StaffID, false);
                                        UpdateOanhVK(input.ObjectID, input.OrderCode, insertInviteCode.EventCode, insertInviteCode.generalCodeID);
                                        statusRedeem = true;
                                    }
                                }
                                else statusRedeem = false;
                            }
                            #endregion
                        }
                        if (statusRedeem) transaction.Commit();
                        L.Mes(Level.INFO, keylog + " End Redeem Code ");
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "RedeemInviteCode");
                return new ResponseModel<bool>() { result = -1, error = ex.Message, data = false };
            }

            return new ResponseModel<bool>() { result = 1, error = "", data = statusRedeem };
        }

        [Route("API/ReferralProgram/CheckRFforBankMSB")]
        public ResponseModel<CheckVCforBankOutput> CheckRFforBankMSB(CheckVCforBank input)
        {
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "CheckRFforBankMSB");
            CheckVCforBankOutput res = new CheckVCforBankOutput();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    res = connection.Query<CheckVCforBankOutput>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_for_Bank_MSB", new
                    {
                        ActionName = "CheckContactUseVC",
                        voucherCode = input.Ref,
                        contact = input.Contact
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "CheckRFforBankMSB");
                return new ResponseModel<CheckVCforBankOutput>() { data = null, error = ex.Message, result = -1 };
            }
            L.Mes(Level.INFO, JsonConvert.SerializeObject(res), "CheckRFforBankMSB");
            return new ResponseModel<CheckVCforBankOutput>() { data = res, error = null, result = 1 };
        }

        [Route("API/ReferralProgram/AddMoneyForRfMSB")]
        public ResponseModel<CheckVCforBankOutput> AddMoneyForRfMSB(CheckVCforBank input)
        {
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "AddMoneyForRfMSB");
            int res = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    res = connection.Execute("PowerInside.dbo.OS6_MSB_AddPromotion", new
                    {
                        voucher = input.Ref,
                        contact = input.Contact
                    }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "AddMoneyForRfMSB");
                return new ResponseModel<CheckVCforBankOutput>() { data = new CheckVCforBankOutput { Status = 0, Message = "Kích hoạt không thành công" }, error = ex.Message, result = -1 };
            }

            if (res > 0)
            {
                L.Mes(Level.INFO, "Add Money Successfully", "AddMoneyForRfMSB");
                return new ResponseModel<CheckVCforBankOutput>() { data = new CheckVCforBankOutput { Status = 1, Message = "Kích hoạt thành công" }, error = null, result = 1 };
            }
            return new ResponseModel<CheckVCforBankOutput>() { data = new CheckVCforBankOutput { Status = 0, Message = "Kích hoạt không thành công" }, error = "Đã xảy ra lỗi", result = -1 };
        }

        [Route("API/ReferralProgram/SendNotifActiveReferralMyFPT")]
        [Authorize(Roles = AuthorizeRole.SuperUser)]
        [HttpPost]
        public async Task<ResponseModel<bool>> SendNotifActiveReferralMyFPT(ModelSendNotifActiveReferralMyFPT input)
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), ReferalProgramMyFPT.RamdoneString());
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), string.Concat(keylog," SendNotifActiveReferralMyFPT"));
            if (string.IsNullOrEmpty(input.FirstAccess))
            {
                return new ResponseModel<bool>() { result = 1, error = "Chưa được active", data = false };
            }
            var res = new MyFPTapiOutput();
            try
            {
                int sourceId = int.Parse(Utility.sourceId);
                int roleId = int.Parse(Utility.roleId);
                var req = new AddCustomerReq
                {
                    customerKey = input.StaffIDInvite,
                    customerName = ReferalProgramMyFPT.getStaffInfor(input.StaffIDInvite,keylog),
                    customerPhone = input.StaffIDInvite,
                    registerDate = !string.IsNullOrEmpty(input.FirstAccess) ? string.Concat(input.FirstAccess, " 00:00:00") : "",
                    sourceId = sourceId,
                    roleId = roleId,
                    //sourceId = 23,
                    //roleId = 2149,
                    LocationID= 0
                };
                L.Mes(Level.REQUEST, JsonConvert.SerializeObject(req), string.Concat(keylog, "SendNotifActiveReferralMyFPT - Add Customer "));
                //mã quay dự thưởng
                string rs_AddCustomer = ReferalProgramMyFPT.AddCustomer(req);
                L.Mes(Level.INFO, rs_AddCustomer, string.Concat(keylog," SendNotifActiveReferralMyFPT - resultLottery "));
                try
                {
                    int isSavePromotion = ProcessCommon.AddCustomerPromotion(input.Contract, "MyFPT", sourceId, roleId, rs_AddCustomer);
                }
                catch (Exception ex)
                {

                }
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = getAuthorInfor();
                var request = new ModelSendNotificationReferralMyFPT()
                {
                    employeeCode = input.StaffIDInvite,
                    Referrer = input.incentivesReceivedEmp,
                    BookingId = input.Contract,
                    contract_owner = input.FullName,
                    BookingStatus = "Completed",
                    timeActive = input.FirstAccess,
                    codeRefer = input.InviteCode,
                    moneyRefer = input.incentivesReceivedCus,
                    nameContract = input.Description,
                    subjectEmail = "FPT Telecom",
                    attendPrizeCode = rs_AddCustomer
                };
                L.Mes(Level.INFO, JsonConvert.SerializeObject(request), string.Concat(keylog,"SendNotifActiveReferralMyFPT"));
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    L.Mes(Level.INFO, "start to send data",string.Concat(keylog, " SendNotificationReferralMyFPT"));
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = await client.PostAsync("/api/oauth-ms/public/auth/integration-supplier", data);
                    string r = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, r,string.Concat(keylog, "SendNotifActiveReferralMyFPT"));
                    res = JsonConvert.DeserializeObject<MyFPTapiOutput>(r);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, string.Concat(keylog," SendNotifActiveReferralMyFPT"));
                return new ResponseModel<bool>() { result = 1, error = ex.Message, data = false };
            }

            if (res.status == 200)
            {
                ReferalProgramMyFPT.updateStatusRewardMyFPT(input.StaffIDInvite, input.Contract);
                return new ResponseModel<bool>() { result = 1, error = "", data = true };
            }
            return new ResponseModel<bool>() { result = 1, error = "", data = false };
        }
        [HttpGet]
        [Route("API/ReferralProgram/TestAPINoti")]
        public string TestAPINoti()
        {
            var dataAddCustomer = new
            {
                contractNo = "DNFD90746",
                type = "lottery_code",
                data = new
                {
                    code = "123456"
                }
            };
            var url = HelperConfigs.url_hifpt + NotifyConfig.action_notify_lotery;
            string dataSerialize = JsonConvert.SerializeObject(dataAddCustomer);
            string result = IHttpRequest.callapi_hifpt(url, "POST", dataSerialize, "application/json; charset=utf-8");
            return result;
        }

        [HttpPost]
        [Route("API/ReferralProgram/ProcessActiveVoucher")]
        [Authorize(Roles = "SuperUser")]
        public async Task<ResponseModel<bool>> ProcessActiveVoucher()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Start ProcessActiveVoucher",""));

            try
            {
                //var sendmail = ProcessReferalProgram.SendMailDemo();
                //sb.AppendLine(LoggerKafka.JoinStringToWriteLog("sendmail", sendmail));

                //1.Store RealRedeem
                //ProcessReferalProgram.RealRedeem();
                List<Referal_model> referalModelList = new List<Referal_model>();

                //2.get list
                List<Referal_model> listReferal = ProcessReferalProgram.GetListReferal();
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("listReferal", listReferal));

                foreach (Referal_model referalModel in listReferal)
                {
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Start - referalModel", referalModel));
                    Guid logGuid = new Guid();
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("logGuid", logGuid));

                    string codeCloud = string.Empty;
                    var isOwnerCamera = false;
                    using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                    {
                        isOwnerCamera = ReferralProgramCamera.IsOwnerCamera(connection, null, referalModel.ObjIDGT, logGuid.ToString());
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("isOwnerCamera", isOwnerCamera));
                    }
                    if (isOwnerCamera)
                    {
                        //Update code cloud
                        var cameraModel = ReferralProgramCamera.UpdateCodeCloudInvite(referalModel.ObjIDDK, referalModel.ObjIDGT, logGuid.ToString(), referalModel.ChannelRegister);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("cameraModel", cameraModel));

                        if (cameraModel != null)
                        {
                            codeCloud = cameraModel.data;
                        }
                    }

                    if (true)
                    {
                        try
                        {
                            //3.SendEmail
                            var sendMail = ProcessReferalProgram.SendToMail("<EMAIL>", referalModel, isOwnerCamera);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("sendMail", sendMail));
                        }
                        catch (Exception ex)
                        {
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("sendMail error", ex));
                        }
                    }
                    // lấy code FPT play
                    #region Code FPT play

                    List<CodeModel> codeModelList = new List<CodeModel>();
                    /*
                    try
                    {
                        codeModelList = ProcessReferalProgram.GetCodeFPTplay(referalModel.ID);
                        logs = logs + " FPTplayCode: " + JsonConvert.SerializeObject(codeModelList);
                    }
                    catch (Exception ex)
                    {
                        error = error + "Error GetCodeFPTplay: " + ex.Message + ";";
                    }
                     */
                    #endregion
                    //4.Send api notify HiFPT
                    try
                    {
                        if (isOwnerCamera)
                        {
                            string response = await NotiHiFPTManager.SendNotifyHiFPT_Dv_OwnerCam(referalModel, codeCloud);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("SendNotifyHiFPT_Dv_OwnerCam", response));
                        }
                        else
                        {
                            var quantityRF = ProcessReferalProgram.CountContractReferral(referalModel.ObjIDGT);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("quantityRF", quantityRF));

                            string response = await NotiHiFPTManager.SendNotifyHiFPT_Dv(referalModel);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("SendNotifyHiFPT_Dv", response));

                            //if (source.Count<HifptModel>() > 0)
                            //{
                            //    logs += "send_hifpt:{";
                            //    foreach (HifptModel hifptModel in source)
                            //        logs += JsonConvert.SerializeObject((object)hifptModel);
                            //    logs += "},";
                            //}
                        }
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("notify HiFPT", ex));
                    }

                    #region quay so trung thuong
                    /*
                    try
                    {
                        // 5. call api add thong tin quay so
                        
                        if (referalModel.GeneralCodeID > 0)
                        {
                        #region chương trình quay số
                            if (ProcessReferalProgram.CheckPersonalContract(referalModel.ContractGT))
                            {
                                int sourceId = int.Parse(Utility.sourceId);
                                int roleId = int.Parse(Utility.roleId);
                                var req = new AddCustomerReq
                                {
                                    customerKey = referalModel.ContractGT,
                                    customerName = referalModel.ReceiveFullname,
                                    customerPhone = referalModel.Location_Phone,
                                    registerDate = referalModel.RedeemDate.HasValue ? referalModel.RedeemDate.Value.ToString("yyyy-MM-dd HH:mm:ss") : "",
                                    sourceId = sourceId,
                                    roleId = roleId,
                                    //sourceId = 23,
                                    //roleId = 2149,
                                    LocationID = ProcessReferalProgram.GetLocation(referalModel.ContractGT)
                                };
                                var rs_AddCustomer = ProcessReferalProgram.AddCustomer(req);
                                logs += "promotion_addcustomer:{" + rs_AddCustomer + "},";
                                addCustomerModel resModel = new addCustomerModel()
                                {
                                    hasError = true,
                                    data = new DataResModel()
                                };
                                resModel = JsonConvert.DeserializeObject<addCustomerModel>(rs_AddCustomer);
                                L.Mes(Level.INFO, "Start to sent noti lotery ", "ProcessActiveVoucher");
                                try
                                {
                                    int isSavePromotion = ProcessCommon.AddCustomerPromotion(referalModel.ContractDK,"HiFPT", sourceId, roleId, resModel.data.resultLottery[0].ToString());
                                }
                                catch (Exception ex)
                                {
                                    
                                }
                                var dataAddCustomer = new {
                                    contractNo=referalModel.ContractGT,
                                    type = "lottery_code_v2",
                                    data = new {
                                        code = resModel.data.resultLottery[0].ToString()
                                    }
                                };
                                var url = HelperConfigs.url_hifpt + NotifyConfig.action_notify_lotery;
                                string dataSerialize = JsonConvert.SerializeObject(dataAddCustomer);
                                string result = IHttpRequest.callapi_hifpt(url, "POST", dataSerialize, "application/json; charset=utf-8");
                                L.Mes(Level.INFO, "result sent noti lotery " + result, "ProcessActiveVoucher");
                            }
                        #endregion
                            bool rollGift = ProcessReferalProgram.RollGiftMKT8(referalModel.ObjIDGT, referalModel.ObjIDDK, logGuid.ToString(), referalModel.ChannelRegister);

                            if (rollGift)
                            {
                                if (!string.IsNullOrEmpty(referalModel.Location_Phone))
                                {
                                    try
                                    {
                                        List<SmsResult> source = ProcessReferalProgram.SendSmsMKTp6(referalModel); // gửi sms code FFPT
                                        logs = logs + "send_smsMKTP8:{" + string.Join(";", source.Select<SmsResult, string>((Func<SmsResult, string>)(a => a.Message))) + "},";
                                    }
                                    catch (Exception ex)
                                    {
                                        error = error + " Error send_smsMKTP7: " + ex.Message + ";";
                                    }
                                }
                            }
                            #region mktp9
                            //loai hd nguoi duoc GT
                            string packageTypeInvited = ProcessReferalProgram.GetPackageTypeInviteByObjid(referalModel.ObjIDDK);
                            packageTypeInvited = packageTypeInvited == "NETONLY" ? "NET" : "COMBO";

                            //loai hd NGT

                            WriteToLog("", "bat-dau-set-data", logGuid);
                            var data = new
                            {
                                data = new
                                {
                                    referrer_code = referalModel.InviteCode,
                                    contract_of_referrer = referalModel.ContractGT,
                                    branch = ProcessReferalProgram.GetLocationCodeByID(referalModel.ObjIDGT),
                                    phone_of_referrer = referalModel.Location_Phone,
                                    contract = referalModel.ContractDK,
                                    contract_type = packageTypeInvited,
                                    contract_referrer_info = ProcessReferalProgram.GetContracTypeInvite(referalModel.ContractGT, referalModel.ObjIDGT)
                                }
                            };
                            WriteToLog(data, "xet-xongdata", logGuid);
                            string dataStr = JsonConvert.SerializeObject(data);
                            string domain = ProcessReferalProgram.GetValueByKeyConfig("hifptdomain");
                            List<HeaderAPI> headerapi = new List<HeaderAPI>();
                            headerapi.Add(new HeaderAPI { key = "secret-key", value = "hifpt_isc_001" });
                            WriteToLog(data, "data-before-call-api-hifpt", logGuid);
                            string rs = Common.callapi(domain + "/hi-ecom-seller-api/v1/web/add-spins", "POST", dataStr, "application/json; charset=utf-8",headerapi);
                            WriteToLog(rs, "data-after-call-api-hifpt", logGuid);
                            #endregion
                        }
                    }
                    catch (Exception ex)
                    {
                        //error = error + " Error dataPrizeCode: " + ex.Message + ";";
                    }
                     */
                    #endregion
                    
                    //6. gửi sms cho số điện thoại
                    if (!string.IsNullOrEmpty(referalModel.Location_Phone) || !string.IsNullOrEmpty(referalModel.PhoneDK))
                    {
                        try
                        {
                            List<SmsResult> source = ProcessReferalProgram.SendSms(referalModel, codeModelList);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("SMS", source));
                        }
                        catch (Exception ex)
                        {
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("SMS error", ex));
                        }
                    }
                    //7. gửi sự kiện add điểm loy cho hợp đồng giới thiệu và hđ đăng ký mới
                    if (!string.IsNullOrEmpty(referalModel.GenenalCodeLoyInvite))
                    {
                        try
                        {
                            var data = new { 
                                objId = referalModel.ObjIDGT, 
                                actionCode = referalModel.GenenalCodeLoyInvite, 
                                contractNew = referalModel.ContractDK, 
                                referralCode = referalModel.InviteCode,
                                objIdNew = referalModel.ObjIDDK,
                                contract = referalModel.ContractGT,
                                mobile = referalModel.Location_Phone,
                                mobileNew = referalModel.PhoneDK
                            };

                            string req = JsonConvert.SerializeObject(data);
                            string endpointAPI = Utility.loyaltyapi + "/loyalty-services/api/integration/send-action";

                            int idRequest = CallbackLoyaltyServices.AddRequestRF(
                                Convert.ToInt32(referalModel.ObjIDGT),
                                Convert.ToInt32(referalModel.ObjIDDK),
                                req,
                                referalModel.ChannelRegister,
                                endpointAPI,
                                false);

                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("idRequest", idRequest));

                            string resLoyAPI = CallbackLoyaltyServices.callapi_loyV2(logGuid.ToString(), idRequest, endpointAPI, "POST", req);
                            //string resLoyAPI = "{\"status\":\"FAIL\",\"message\":\"Không thành công\"}";
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("resLoyAPI", resLoyAPI));

                            try
                            {
                                var lc = JsonConvert.DeserializeObject<LoyaltyCallbackModel>(resLoyAPI);
                                if (lc.status.ToUpper().Equals("SUCCESS"))
                                {
                                    CallbackLoyaltyServices.UpdateSuccess(idRequest);
                                }
                            }
                            catch (Exception ex)
                            {
                                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("UpdateSuccess", ex));
                            }
                        }
                        catch (Exception ex)
                        {
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("send loy", ex));
                        }
                    }
                    //Mask send mail
                    ProcessReferalProgram.MaskSentmail(referalModel.ID);
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("ProcessActiveVoucher error", ex));
                return new ResponseModel<bool>() { result = 1, error = ex.Message, data = false };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "ProcessActiveVoucher_String", "");
            }
            ResponseModel<bool> responseModel1 = new ResponseModel<bool>();
            return new ResponseModel<bool>() { result = 1, error = "", data = true };
        }

        [HttpPost]
        [Route("API/ReferralProgram/UpdateStatusGift")]
        [Authorize(Roles = "SuperUser")]
        public ResponseModel<bool> UpdateStatusGift(UpdateStatusInput input)
        {
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        int statusGiftCode = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                        {
                            ActionName = "StatusGiftCode",
                            GiftCode = input.GiftCode,
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                        if(statusGiftCode != 1)
                        {
                            return new ResponseModel<bool>() { result = 0, error = "Mã đã được active", data = false }; 
                        }

                        MKTp6Type typeVoucher = connection.Query<MKTp6Type>("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                        {
                            ActionName = "GetTypeGift",
                            GiftCode = input.GiftCode,
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                        int isUpdate= connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                        {
                            ActionName = "UpdateStatus",
                            GiftCode = input.GiftCode,
                        },transaction:transaction,commandTimeout:null, commandType: CommandType.StoredProcedure);

                        /*if (typeVoucher.TypeGift.Equals(0))
                        {
                            int isAddPrepaid = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                            {
                                ActionName = "InsertToPrepaid",
                                GiftCode = input.GiftCode,
                                ObjID = typeVoucher.ObjID,
                                ValueVoucher = typeVoucher.ValueVoucher
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        }*/

                        //int quantity = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                        //{
                        //    ActionName = "GetQuantity",
                        //    GiftCode = input.GiftCode,
                        //}, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        if(isUpdate>0)
                        {
                            transaction.Commit();
                            return new ResponseModel<bool>() { result = 1, error = null, data = true };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ResponseModel<bool>() { result = 0, error = ex.Message, data = false };
            }
            return new ResponseModel<bool>() { result = 0, error = "Đã có lỗi xảy ra", data = false };
        }
        private void SendNotificationReferralMyFPT(dynamic input,string keylog)
        {
            var res = new MyFPTapiOutput();
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = getAuthorInfor();
                var request = new ModelSendNotificationReferralMyFPT()
                {
                    employeeCode = input.StaffIDInvite,
                    codeRefer = input.InviteCode,
                    moneyRefer = input.NET,
                    nameContract = input.Description,
                    timeActive = input.FirstAccess,
                    Referrer = input.RealMoneyAmount,
                    BookingId = input.Contract,
                    contract_owner = input.FullName,
                    BookingStatus = "pending",
                    subjectEmail = "FPT Telecom"
                };
                L.Mes(Level.INFO, JsonConvert.SerializeObject(request),keylog + " SendNotificationReferralMyFPT request - log before send: ");
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    L.Mes(Level.INFO, "start to send data", keylog + " SendNotificationReferralMyFPT");
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/api/oauth-ms/public/auth/integration-supplier", data).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, r, keylog + " SendNotificationReferralMyFPT res:");
                    res = JsonConvert.DeserializeObject<MyFPTapiOutput>(r);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, keylog + " SendNotificationReferralMyFPT");
                return;
            }

            if (res.status == 200)
            {
                L.Mes(Level.INFO, res.status.ToString(), keylog + " SendNotificationReferralMyFPT Status");
                return;
            }
            return;
        }
        private AuthorizationInfor getAuthorInfor()
        {
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                var uri = WebAPIHelper.myFpt_fpt_vn + "/api/oauth-ms/public/auth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("client_id", Utility.my_fpt_api_client_id));
                keyValues.Add(new KeyValuePair<string, string>("client_secret", Utility.my_fpt_api_client_secret));
                keyValues.Add(new KeyValuePair<string, string>("username", Utility.my_fpt_api_username));
                keyValues.Add(new KeyValuePair<string, string>("password", Utility.my_fpt_api_password));
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "password"));

                var content = new FormUrlEncodedContent(keyValues);


                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        L.Mes(Level.INFO, response.Content.ReadAsStringAsync().Result, "getAuthorInfor");
                        response.EnsureSuccessStatusCode();
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, "getAuthorInfor");
                        res = JsonConvert.DeserializeObject<AuthorizationInfor>(r);
                    }
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getAuthorInfor");
                return null;
            }
            return res;
        }
        private bool PrivateGCCode(SqlConnection connection, SqlTransaction transaction, string voucherCode, int promotionID, string orderCode, int ObjID)
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, "Start to Active privateCode", "PrivateGCCode");
            //int InsertGC = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
            //{
            //    ActionName = "InsertGeneralCode",
            //    VoucherCode = voucherCode,
            //    PromotionEventID = promotionID,
            //    OrderCode = orderCode,
            //    LocationID = 0,
            //    DepartmentID = 0,
            //    SaleID = 0,
            //    ObjID = ObjID,
            //    Status = 1,
            //    RowAffected = 0
            //}, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            //int InsertPrivate = connection.Query<int>(ConstantAPI.StoreName_Private, new
            //{
            //    ActionName = "RedeemPrivateCode",
            //    Code = voucherCode,
            //    Status = 4,
            //    ObjID = ObjID,
            //    RowAffected = 0
            //}, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            try
            {
                dynamic promotionInfor = connection.Query("PowerInside.dbo.OS6_FPTVoucher_Private",
                    new
                    {
                        ActionName = "ActivePrivateCode",
                        Code = voucherCode,
                        ObjID = ObjID,
                        PromotionEventID = promotionID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                L.Mes(Level.INFO, JsonConvert.SerializeObject(promotionInfor), "PrivateGCCode");
                if (promotionInfor != null)
                {
                    #region code cũ
                    //int Mnet = Convert.ToInt32(promotionInfor.MNET);
                    //int Mtv = Convert.ToInt32(promotionInfor.MTV);
                    //UpdateDiscount(
                    //    ObjID,  // objID khách hàng
                    //    0, //XđồngY tháng NET + Tháng
                    //    0, //XđồngY tháng TV + Tháng
                    //    Mnet, //Giảm Tiền Trực Tiếp NET
                    //    Mtv, //Giảm Tiền Trực Tiếp TV
                    //    voucherCode, // Code
                    //    0, true);
                    #endregion

                    #region vui kết lộc xuân
                    connection.Query("PowerInside.dbo.OS6_FPTVoucher_Private", new
                    {
                        ActionName = "UpdateHistotyTemp",
                        Code = voucherCode,
                        ObjID = ObjID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                    PEDiscountModel pdm = new PEDiscountModel();
                    pdm = GetPE(connection, transaction, voucherCode);

                    //Tuple<int, int> prepaid_net_tv = new Tuple<int, int>(6, 6);//GetPrepaidtimeNetTV(input.Services);

                    int isEvenMonth = 0;
                    if (pdm.ContainerID > 0)
                    {
                        // lay clkm nen
                        var monthPromotion = GetMonthPromotion(ObjID);
                        if (monthPromotion != null)
                        {
                            // thang chan le
                            isEvenMonth = monthPromotion.EventMonth;
                            // lay promotion net theo thang
                            int promotionNetID = GetPromotionNetByContainerID(pdm.ContainerID, monthPromotion.GetAddMonth(), isEvenMonth);
                            if (promotionNetID > 0)
                                pdm.PromotionNetID = promotionNetID;
                        }
                    }

                    var datalog = new
                    {
                        ObjID = ObjID,
                        PromotionNetID = pdm.PromotionNetID,
                        IPTVPromotionID = pdm.IPTVPromotionID,
                        MoneyPromotionNETID = pdm.MoneyPromotionNETID,
                        MoneyPromotionTVID = pdm.MoneyPromotionTVID,
                        EventCode = pdm.EventCode,
                        SaleID = 0,
                        isEvenMonth = isEvenMonth,
                        generalCodeID = 0
                    };
                    WriteToLog(datalog, "Call AddCustomerDiscountV2 ", logId);

                    // get ID PrivateCode
                    int PrivateCodeID = connection.QueryFirstOrDefault<int>("PowerInside.dbo.OS6_FPTVoucher_Private", new
                    {
                        ActionName = "GetIDByPrivateCode",
                        Code = voucherCode,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

                    int addNet = SPFAddCustomerDiscountV2(
                        ObjID,
                        pdm.PromotionNetID,
                        pdm.IPTVPromotionID,
                        pdm.MoneyPromotionNETID,
                        pdm.MoneyPromotionTVID,
                        pdm.EventCode,
                        0,
                        isEvenMonth,
                        0, PrivateCodeID
                    );
                    WriteToLog(addNet, " RedeemVoucherGC addNet ", logId);
                    if (pdm.MoneyPromotionTVID > 0 || pdm.IPTVPromotionID > 0)
                    {
                        In4VoucherTHmodel voucherTh = new In4VoucherTHmodel();
                        if (pdm.MoneyPromotionTVID > 0)
                        {
                            voucherTh = GetIn4VoucherTH(ObjID, pdm.MoneyPromotionTVID);
                            WriteToLog(voucherTh, " RedeemVoucherGC voucherTh ", logId);
                        }
                        int updateTH = UpdateDiscountTH(voucherCode, ObjID, voucherTh.locationId, 0, pdm.IPTVPromotionID, voucherTh.amountTv, voucherTh.addBy, logId);
                        WriteToLog(updateTH, " RedeemVoucherGC updateTH ", logId);
                    }
                    if (pdm.MoneyPromotionNETID > 0)
                    {
                        AddVoucherBill_VuiKetLocXuan(ObjID, orderCode, voucherCode, pdm.MoneyPromotionNETID);
                    }
                    #endregion

                    return true;
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "PrivateGCCode");
                return false;
            }

            return false;
        }

        private async Task<Boolean> RedeemInviteCode(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input,string keylog)
        {
            bool rfBank = input[0].VoucherCode.StartsWith("MSB") && input[0].VoucherCode.EndsWith("FPT") ? true : false;
            // kiểm tra nhập nhiều hơn 1 mã RP hoặc không có trong giỏ
            if (input.Any(x => x.ObjecInvite == 0) || input.Count() != 1) return false;
            // getInfo Prepaid of InviteCode (vì mã RP có nhiều Prepaid nên phải chọn chính xác mã voucher quy đổi nào chính xác)
            // item1 :  IDPaidTimeType
            // item2 :  IsPrepaidNET
            // Item3 :  IsPrepaidTV
            // item4 :  Base ID NET
            // item5 :  Base ID TV

            var PrepaidInfo = connection.Query<Tuple<int, int, int, int, int>>(ConstantAPI.StoreName_InviteCode, new
            {
                ActionName = "GetPrepaidOfInviteCode",
                OrderCode = input[0].OrderCode,
                Code = input[0].VoucherCode,
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            L.Mes(Level.INFOPE, keylog + " " + "PrepaidInfo " + JsonConvert.SerializeObject(PrepaidInfo));
            //get Info promotionEvent invite and Invited
            List<InfoRP> InviteInfo = new List<InfoRP>();
            if (rfBank)
            {
                #region GTBB MSBFPT
                Boolean UpdateHistoryTempMSB = UpdateHistoryTemp(connection, transaction, input);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StsUpdateHistoryTemp = UpdateHistoryTempMSB }), "UpdateHistoryTemp");
                if (!UpdateHistoryTempMSB) return false;
                InviteInfo = connection.Query<InfoRP>(ConstantAPI.StoreName_InviteCode, new
                {
                    InviteCode = input[0].VoucherCode,
                    ActionName = "GetPEREF_MSB",
                    //PackageTypeInvited = BuildPackageType(PrepaidInfo.Item2, PrepaidInfo.Item3),
                    PaidTimeType = PrepaidInfo.Item1,
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                if (InviteInfo.Count == 0) return false;
                // các trường hợp MGT của ngân hàng thì chỉ lấy infor đơn vị sử dụng MGT và insert vào bàng general Code
                int InsertGCSuccess = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_for_Bank_MSB", new
                {
                    ActionName = "InsertGeneralCode",
                    objID = input[0].Objectinvited,
                    voucherCode = input[0].VoucherCode,
                    code = InviteInfo[0].EventCode,
                    promotionEventID = InviteInfo[0].ID
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                if (InsertGCSuccess > 0)
                {
                    UpdateDiscount(
                    input[0].Objectinvited,  // objID khách hàng sử dụng MGT
                    InviteInfo[0].NetPromotionID, //XđồngY tháng NET + Tháng
                    InviteInfo[0].IPTVPromotionID, //XđồngY tháng TV + Tháng
                    InviteInfo[0].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    InviteInfo[0].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    InviteInfo[0].EventCode, // Code
                    input[0].ObjecInvite, false); //Object Invite -TypeVC:True    
                    UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, InviteInfo[0].EventCode, InsertGCSuccess);
                    return true;
                }
                else return false;
                #endregion
            }
            else
            {
                InviteInfo = connection.Query<InfoRP>(ConstantAPI.StoreName_InviteCode, new
                {
                    InviteCode = input[0].VoucherCode,
                    ActionName = "GetPEREF",
                    PackageTypeInvited = BuildPackageType(PrepaidInfo.Item2, PrepaidInfo.Item3),
                    PaidTimeType = PrepaidInfo.Item1,
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
            }
            //L.Mes(Level.INFOPE, JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] }));
            //if (InviteInfo.Count() != 2) return false;

            if (InviteInfo.Count() != 3)
            {
                L.Mes(Level.INFOPE, keylog +" "+ JsonConvert.SerializeObject(InviteInfo));
                return false;
            }
            else if (InviteInfo.Count() == 3) { L.Mes(Level.INFOPE, keylog + " " + JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] })); }

            // insert data to table InviteSuccess
            // [1] : người được giới thiệu
            // [2] : người giới thiệu
            string EventCodeLoy = ReferalProgramHiFPT.GetEventCodeLoy(input[0].Objectinvited);
            int InsertInviteSuccess = connection.Query<int>(ConstantAPI.StoreName_InviteSuccess, new
            {
                ActionName = "InsertInfoRedeemRPCode",
                XML = CreateXMLRedeem(input, InviteInfo[2].EventCode, InviteInfo[1].EventCode, EventCodeLoy),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusUpdateInviteSuccess = (InsertInviteSuccess == 1) }));
            if (InsertInviteSuccess != input.Count()) return false;

            // update History_temp
            Boolean StsUpdateHistoryTemp = UpdateHistoryTemp(connection, transaction, input);
            L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StsUpdateHistoryTemp = StsUpdateHistoryTemp }));
            if (!StsUpdateHistoryTemp) return false;

            //update Status InviteCode
            UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);

            // insert data to table generalcode
            List<GeneralCodeInsert> stsAddGeneralCode = InsertGeneralCodeRPgetGCID(connection, transaction, input, InviteInfo, PrepaidInfo);
            //Boolean stsAddGeneralCode = InsertGeneralCodeRP(connection, transaction, input, InviteInfo, PrepaidInfo);
            L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusAddGeneralCode = stsAddGeneralCode }));
            if (stsAddGeneralCode.Count != 3) return false;

            // update Discount RpCode User Invited
            UpdateDiscount(
                 input[0].Objectinvited,  // objID khách hàng
                 InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                 InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                 InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                 InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                 InviteInfo[1].EventCode, // Code
                 input[0].ObjecInvite, false); //Object Invite -TypeVC:True    

            // Update Discount RpCode User Invite
            UpdateDiscount(
                 input[0].ObjecInvite,  // objID khách hàng
                 InviteInfo[2].NetPromotionID, //XđồngY tháng NET + Tháng
                 InviteInfo[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                 InviteInfo[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                 InviteInfo[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                 InviteInfo[2].EventCode, // Code
                 input[0].ObjecInvite, false); //Object Invite -TypeVC:True    

            L.Mes(Level.INFO, keylog + " " + "Call Voucher Bill");
            // add voucher bill invited
            UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, InviteInfo[1].EventCode, stsAddGeneralCode[1].id);
            // add voucher bill invite
            UpdateOanhVK(input[0].ObjecInvite, input[0].OrderCode, InviteInfo[2].EventCode, stsAddGeneralCode[2].id);
            L.Mes(Level.INFO, keylog + " " + "SendMess");
            await SenNotify(input[0].ObjecInvite, input[0].Objectinvited);

            return true;
        }

        private void UpdateStatusRPCode(SqlConnection connection, SqlTransaction transaction, string InviteCode)
        {
            connection.Query<int>(ConstantAPI.StoreName_InviteCode, new
            {
                InviteCode = InviteCode,
                ActionName = "UpdateInvite",
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        private bool InsertGeneralCodeGC(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            PromotionEventInfo PEInfo = new PromotionEventInfo();

            //Get ID  PromotionEvent 
            for (int i = 0; i < input.Count(); i++)
            {
                PEInfo = connection.Query<PromotionEventInfo>(ConstantAPI.StoreName_MBS, new
                {
                    ActionName = "GetPromotionEventByEventCode",
                    EventCode = input[i].VoucherCode,
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                // GetCLKM FREEMONTH
                try
                {
                    int IDFreeMonthNET = connection.Query<int>(ConstantAPI.StoreName_MBS, new
                    {
                        ActionName = "CheckValidFreeMonth",
                        EventCode = input[i].VoucherCode,
                        NETID = PrepaidInfo.Item4,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    input[i].FreeMonthNet = IDFreeMonthNET;
                }
                catch { }
                input[i].PromotionEventID = PEInfo.ID;
            }

            var generalCodeIDs = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
            {
                ActionName = "InsertGeneralCodeXMLV2",
                XML = CreateXMLUpdateGeneralCodeGC(input, PrepaidInfo),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

            L.Mes(Level.INFO, JsonConvert.SerializeObject(PEInfo), "InsertGeneralCodeGC event");
            // update discount VC Code. mã voucher code không có người giới tho
            if (PEInfo.ID2 > 0)
            {
                UpdateDiscount(
                     input[0].Objectinvited,  // objID khách hàng
                     PEInfo.ID2, //XđồngY tháng NET + Tháng
                     PEInfo.ID3, //XđồngY tháng TV + Tháng
                     PEInfo.ID1, //Giảm Tiền Trực Tiếp NET
                     PEInfo.ID7, //Giảm Tiền Trực Tiếp TV
                     PEInfo.EventCode, // Code
                     0, true); //Object Invite -TypeVC:True    
            }
            else
            {
                // tang thang
                int isEvenMonth = 0;
                if (PEInfo.ContainerID > 0)
                {
                    // lay clkm nen
                    var monthPromotion = GetMonthBase(input[0].Objectinvited);
                    if (monthPromotion != null)
                    {
                        // thang chan le
                        isEvenMonth = monthPromotion.EventMonth;
                        // lay promotion net theo thang
                        int promotionNetID = GetPromotionNetByContainerID(PEInfo.ContainerID, monthPromotion.GetAddMonth(), isEvenMonth);
                        if (promotionNetID > 0)
                            PEInfo.PromotionNetID = promotionNetID;
                    }
                }
                AddCustomerDiscountV2(
                    input[0].Objectinvited,
                    PEInfo.PromotionNetID,
                    PEInfo.ID3,
                    PEInfo.ID1,
                    PEInfo.ID7,
                    PEInfo.EventCode,
                    0,
                    isEvenMonth,
                    generalCodeIDs.FirstOrDefault()
                );
            }

            // add voucher Bill

            //kiểm tra nguồi đăng ký là OBS và voucher DKOL50K thì không add voucher bill
            int isOBS = MKTgeneralCode.GetInputTypeObjID(input[0].Objectinvited);
            if (isOBS.Equals(5) && PEInfo.EventCode.ToUpper().IndexOf(Utility.VoucherOBS) > -1)
            {
                return (generalCodeIDs.Count() == input.Count());
            }
            UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, PEInfo.EventCode, generalCodeIDs.FirstOrDefault());

            // check row Affected
            return (generalCodeIDs.Count() == input.Count());
        }

        private List<GeneralCodeInsert> InsertGeneralCodeRPgetGCID(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            List<GeneralCodeInsert> lst = new List<GeneralCodeInsert>();
            for (int i = 0; i < InviteInfo.Count; i++)
            {
                GeneralCodeInsert gc = new GeneralCodeInsert();
                gc.eventCode = InviteInfo[i].EventCode;
                gc.id = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
                {
                    ActionName = "InsertGeneralCodeXMLGCID",
                    XML = CreateXMLUpdateGeneralCodeRPbyGC(input, InviteInfo[i], PrepaidInfo, i),
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                lst.Add(gc);
            }
            return lst;
        }

        private Boolean InsertGeneralCodeRP(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            int InsertGeneralCode = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
            {
                ActionName = "InsertGeneralCodeXML",
                XML = CreateXMLUpdateGeneralCodeRP(input, InviteInfo, PrepaidInfo),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // check row Affected
            return (InsertGeneralCode == 3);
        }

        private Boolean UpdateHistoryTemp(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input)
        {
            int RowAffected = connection.Query<int>(ConstantAPI.StoreName_HistoryTemp, new
            {
                ActionName = "UpdateHistory",
                XML = CreateXMLRedeem(input),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            // check records update to tbale History_temp
            return (RowAffected == input.Count());
        }

        private void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                var data = new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        };
                L.Mes(Level.INFO, JsonConvert.SerializeObject(data), "UpdateDiscount " + ObjID);
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }

        private void AddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            L.Mes(Level.INFO, "AddCustomerDiscountV2:" + JsonConvert.SerializeObject(new
            {
                ObjID = ObjID,
                PromotionNetID = PromotionNetID,
                IPTVPromotionID = IPTVPromotionID,
                MoneyPromotionNETID = MoneyPromotionNETID,
                MoneyPromotionTVID = MoneyPromotionTVID,
                VoucherCode = VoucherCode,
                SalesID = SalesID,
                IsEvenMonth = IsEvenMonth,
                GeneralCodeID = GeneralCodeID,
                PrivateCodeID = PrivateCodeID
            }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private MonthPromotion GetMonthBase(int obj)
        {
            MonthPromotion res;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.QueryFirstOrDefault<MonthPromotion>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetMonthPromotion",
                    new { ObjID = obj },
                    commandType: CommandType.StoredProcedure);
            }
            return res;
        }

        private int GetPromotionNetByContainerID(int ContainerID, int AddMonth, int IsEvenMonth = 0)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPromotionNetByContainerID",
                    new { ContainerID = ContainerID, AddMonth = AddMonth, IsEvenMonth = IsEvenMonth },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private bool CheckCodeInput(RedeemInviteCodeRequest input)
        {
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    return (connection.QueryFirstOrDefault<int>(ConstantAPI.StoreName_HistoryTemp, new
                    {
                        ActionName = "CheckCartContainCode",
                        XML = CreateXMLNode(input.VoucherCodes, input.OrderCode),
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure) == 0);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "CheckCodeInput");
                return false;
            }
        }

        private List<checkCodeOutput> CheckCodeInputFoxpro(RedeemInviteCodeRequest input)
        {
            var res = new List<checkCodeOutput>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    res = connection.Query<checkCodeOutput>(ConstantAPI.StoreName_HistoryTemp, new
                    {
                        ActionName = "CheckCartContainCode_Foxpro",
                        XML = CreateXMLNode(input.VoucherCodes, input.OrderCode),
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure).ToList();
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "CheckCodeInputFoxpro");
                return null;
            }
            return res;
        }

        private XElement CreateXMLUpdateGeneralCodeGC(List<ItemRequest> input, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            int PrepaidTimeTV;
            if (PrepaidInfo.Item3 > 0) PrepaidTimeTV = 1; else if (PrepaidInfo.Item3 < 0) PrepaidTimeTV = -1; else PrepaidTimeTV = 0;

            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("C", item.VoucherCode),
                           new XElement("Or", item.OrderCode),
                           new XElement("O", item.Objectinvited),
                           new XElement("P", item.PromotionEventID),
                           new XElement("Ac", 2),

                           new XElement("BNET", PrepaidInfo.Item4),
                           new XElement("BTV", PrepaidInfo.Item5),
                           new XElement("FM", item.FreeMonthNet),
                           new XElement("IsPrepaidTV", PrepaidTimeTV),

                           new XElement("L", 0),
                           new XElement("Br", 0),
                           new XElement("D", 0),
                           new XElement("S", 0)
                       ));
            return xmlString;
        }

        private XElement CreateXMLUpdateGeneralCodeRPbyGC(List<ItemRequest> input, InfoRP promotionEvent, Tuple<int, int, int, int, int> PrepaidInfo,int i)
        {
            int PrepaidTimeTV;
            if (PrepaidInfo.Item3 > 0) PrepaidTimeTV = 1; else if (PrepaidInfo.Item3 < 0) PrepaidTimeTV = -1; else PrepaidTimeTV = 0;
            if (i.Equals(0))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // Mã loy cho người giới thiệu status = 100
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));
                return xmlString;
            }
            if (i.Equals(1))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", PrepaidInfo.Item4),
                            new XElement("BTV", PrepaidInfo.Item5),
                            new XElement("IsPrepaidTV", PrepaidTimeTV),
                            new XElement("Ac", 111)));
                return xmlString;
            }
            if (i.Equals(2))
            {
                var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent.EventCode),  // nguoi giới thiệu
                                        new XElement("P", promotionEvent.ID),
                                        new XElement("Or", input[0].OrderCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
                return xmlString;
            }
            return null;
        }

        private XElement CreateXMLUpdateGeneralCodeRP(List<ItemRequest> input, List<InfoRP> promotionEvent, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            int PrepaidTimeTV;
            if (PrepaidInfo.Item3 > 0) PrepaidTimeTV = 1; else if (PrepaidInfo.Item3 < 0) PrepaidTimeTV = -1; else PrepaidTimeTV = 0;

			var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent[2].EventCode),  // nguoi giới thiệu
                                        new XElement("P", promotionEvent[2].ID),
                                        new XElement("Or", input[0].OrderCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));

            xmlString.Add(new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent[1].EventCode),
                            new XElement("P", promotionEvent[1].ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", PrepaidInfo.Item4),
                            new XElement("BTV", PrepaidInfo.Item5),
                            new XElement("IsPrepaidTV", PrepaidTimeTV),
                            new XElement("Ac", 111)));

            xmlString.Add(new XElement("I",  // Mã loy cho người giới thiệu status = 111
                            new XElement("C", promotionEvent[0].EventCode),
                            new XElement("P", promotionEvent[0].ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));

            return xmlString;
        }


        private XElement CreateXMLRedeem(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.OrderCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite)  // object của người giới thiệu
                       ));
            return xmlString;
        }

        private XElement CreateXMLNode(List<ItemCodeNode> inputCode, string OrderCode)
        {
            var xmlString = new XElement("N",
            from item in inputCode
            select new XElement("I",
                           new XElement("V", item.Code),
                           new XElement("O", OrderCode),
                           new XElement("T", item.Type)
                       ));

            return xmlString;
        }

        private string BuildPackageType(int IsPrepaidNET, int IsPrepaidTV)
        {
            if (IsPrepaidNET == -1 && IsPrepaidTV != -1) return "TVONLY";
            else if (IsPrepaidNET != -1 && IsPrepaidTV != -1) return "COMBO";
            else if (IsPrepaidNET != -1 && IsPrepaidTV == -1) return "NETONLY";
            else return "";
        }

        private async Task<ModelRequestNotifyResponse> SenNotify(int ObjInvite, int ObjInvited)
        {
            ModelRequestNotifyResponse response = new ModelRequestNotifyResponse();
            try
            {
                Dictionary<int, InfoNotify> infoUser = new Dictionary<int, InfoNotify>();
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    var temp = connection.Query<InfoNotify>(ConstantAPI.StoreName_InviteSuccess, new
                    {
                        ActionName = "InforPushNotify",
                        ObjIDInvite = ObjInvite,
                        ObjIDInvited = ObjInvited,
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure).ToList();

                    foreach (var item in temp)
                    {
                        if (item.Id == ObjInvite) infoUser.Add(1, item); else infoUser.Add(2, item);
                    }
                }

                L.Mes(Level.INFO, JsonConvert.SerializeObject(infoUser));

                //var request = new ModelRequestNotify()
                //{
                //    contractIdGT = ObjInvite,
                //    titleVi = "Một người bạn đã dùng Mã giới thiệu đăng ký DV FPT",
                //    titleEn = "A friend used referral code to register FTP service",
                //    messageEn = string.Format("Congrat to : {0}, your {1} used your referral code to register for FPT DV", infoUser[1].FullName, infoUser[2].FullName),
                //    messageVi = string.Format("Chúc mừng Quý khách : {0}, bạn {1} đã dùng mã giới thiệu của bạn để đăng ký DV FPT", infoUser[1].FullName, infoUser[2].FullName),
                //    data = new data() { eventType = "referral_code_confirm" }
                //};

     var request = new
                {
                    contractIdGT = ObjInvite,
                    data = new
                    {
                        eventType = "referral_code_confirm",
                        nameGT = infoUser[1].FullName,
                        nameDK = infoUser[2].FullName,
                        contractDK = infoUser[2].Contract
                    }
                };
                using (var client = new HttpClient())
                {
                    string url = Utility.hi_fpt_api;
                    client.BaseAddress = new Uri(url);
                    client.DefaultRequestHeaders.Add("Authorization", ("referral::ofjhgiiedhcidkfjeudsalsodejdcfydiejd" + DateTime.Now.ToString("yyyy-dd-MM").ToString()).CreateMD5());
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    response = await client.PostAsync("/hi-customer-local/referral/send-notify-v3", data).Result.Content.ReadAsAsync<ModelRequestNotifyResponse>();
                }

      L.Mes(Level.INFO, JsonConvert.SerializeObject(response));
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.ToString());
            }
            return response;
        }


        private void UpdateOanhVK(int ObjID, string OrderCode, string EventCode,int GCcode)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, OrderCode }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@EventCode", EventCode);
                p.Add("@generalCodeID", GCcode);
                connection.Execute(ConstantAPI.spOanhVK, p, commandType: CommandType.StoredProcedure);
            }
        }

        [HttpPost]
        [Route("API/Voucher/ProcessActive")]
        public async Task<bool> ProcessActive()
        {
            Task SpringLottery = ProcessCommon.CallSpringLottery();
            Task ProcessActive = OldProcessActive();
            Task callBackRF = CallbackLoyaltyServices.ProcessCallBackRF();
            await SpringLottery;
            await ProcessActive;
            await callBackRF;
            return true;
        }

        //[HttpPost]
        //[Route("API/Voucher/OldProcessActive")]
        public static async Task OldProcessActive()
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), ReferalProgramMyFPT.RamdoneString());
            try
            {

                Task taskProcessActive = new Task
                (
                    (object ob) =>
                    {
                try { ProcessCommon.SentToFPTplay(); }
                catch (Exception ex) { L.Mes(Level.ERROR, ex.ToString(), "ProcessActive - SentToFPTplay"); }
                //ProcessCommon.CallSpringLottery(keylog);

                // Evoucher cước loy
                try { LoyaltyProcessService.ProcessLoyaltyVoucher(); }
                catch (Exception ex) { L.Mes(Level.ERROR, ex.ToString(), "ProcessActive - ProcessLoyaltyVoucher"); }
                // GTBB camera
                try { CameraService.ServiceCamera(); }
                catch (Exception e) { L.Mes(Level.ERROR, e.ToString(), "ProcessActive - ServiceCamera"); }
                    }, "taskProcessActive"
                );
                taskProcessActive.Start();
                await taskProcessActive;                
                //// GTBB FPT play
                //try { ProcessCommon.SentToFPTplay(); }
                //catch (Exception ex) { L.Mes(Level.ERROR, ex.ToString(), "ProcessActive - SentToFPTplay"); }
                ////ProcessCommon.CallSpringLottery(keylog);

                //// Evoucher cước loy
                //try { LoyaltyProcessService.ProcessLoyaltyVoucher(); }
                //catch (Exception ex) { L.Mes(Level.ERROR, ex.ToString(), "ProcessActive - ProcessLoyaltyVoucher"); }
                //// GTBB camera
                //try { CameraService.ServiceCamera(); }
                //catch (Exception e) { L.Mes(Level.ERROR, e.ToString(), "ProcessActive - ServiceCamera"); }
                //// kiểm tra vipVPN
                //try { VipVPNService.CheckStatusVipVPN(); }
                //catch (Exception e) { L.Mes(Level.ERROR, e.ToString(), "ProcessActive - CheckStatusVipVPN"); }
                ////mktp6
                //try { ProcessReferalProgram.MKT6_sendNoti2(); }
                //catch (Exception e) { L.Mes(Level.ERROR, e.ToString(), "ProcessActive - MKT6_sendNoti2"); }
                return;
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.ToString(), "ProcessActive");
                return;
            }
        }

        [HttpGet]
        [Route("API/Voucher/Test")]
        public object Test()
        {
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                return getAuthorInfor();
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }


        private bool RequestValidate(RedeemInviteCodeRequest request)
        {
            if (request.ObjectID < 0)
                return false;

            foreach (var vc in request.VoucherCodes)
            {
                if (vc.Type < 0)
                    return false;
            }

            return true;
        }
        #region voucher Vui Kết Lộc Xuân
        public static void WriteToLog(object input, string mes, Guid logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(input), mes);
        }
        private static PEDiscountModel GetPE(SqlConnection connection, SqlTransaction transaction, string VoucherCode)
        {
            PEDiscountModel PE = connection.Query<PEDiscountModel>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
            {
                ActionName = "GetPEByPrivateCode",
                EventCode = VoucherCode,
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return PE;
        }
        private static MonthPromotionModel GetMonthPromotion(int ObjID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<MonthPromotionModel>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetMonthPromotion",
                    new { ObjID = ObjID },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        private static int SPFAddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_SPF_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        public static In4VoucherTHmodel GetIn4VoucherTH(int ObjID, int MTV)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, MTV, }));
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<In4VoucherTHmodel>(
                    "PowerInside.dbo.OS6_FPTVoucher_SPF_AddCustomerDiscount",
                    new
                    {
                        actionName = "GetIn4VoucherTH",
                        ObjID = ObjID,
                        MoneyPromotionIDTV = MTV
                    },
                    commandType: CommandType.StoredProcedure
                ).FirstOrDefault();
            }
        }
        public static int UpdateDiscountTH(string voucherCode, int objID, int locationID, int iptvid, int promotionIDTV, int amountTV, int addBy, Guid logId)
        {
            var dataparram = new
            {
                VoucherCode = voucherCode,
                ObjID = objID,
                LocationID = locationID,
                IPTVID = iptvid,
                PromotionIDTV = promotionIDTV,
                AmountTV = amountTV,
                AddBy = addBy
            };
            WriteToLog(dataparram, " UpdateDiscountTH ", logId);
            string connIptv = Utility.ConnIPTV;
            using (var connection = new SqlConnection(connIptv))
            {
                return connection.Execute(
                    "IPTV.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    dataparram,
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        public static void AddVoucherBill_VuiKetLocXuan(int ObjID, string OrderCode, string privateCode, int MoneyPromotionNETID)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Query("PowerInside.dbo.OS6_FPTVoucher_AddVoucherBill_VuiKetLocXuan", new
                {
                    ObjID = ObjID,
                    OrderCode = OrderCode,
                    MoneyPromotionNETID = MoneyPromotionNETID,
                    PrivateCode = privateCode
                }, commandType: CommandType.StoredProcedure);
            }
        }
        private static int GetInfoPaidTimeType(int IsPerpaidNET, int IsPerpaidTV)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>(ConstantAPI.OS6_FPTVoucher_MBSAPIVoucher, new
                {
                    ActionName = "CheckContaintPaidTimeType",
                    IsPrepaidTV = IsPerpaidTV,
                    IsPrePaidNET = IsPerpaidNET,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static Tuple<int, int> GetPrepaidtimeNetTV(List<ServicePlatform> lstService)
        {
            int prepaidNet = -1;
            int prepaidTv = -1;
            foreach (var service in lstService)
            {
                string CodeService = GetServiceCode(service.ServiceID);
                if (CodeService.ToUpper().Equals("INT"))
                {
                    prepaidNet = service.SubServiceTypes[0].SubServices[0].PrePaid;
                }
                if (CodeService.ToUpper().Equals("PLAYN"))
                {
                    prepaidTv = service.SubServiceTypes[0].SubServices[0].PrePaid;
                }
            }
            return new Tuple<int, int>(prepaidNet, prepaidTv);
        }
        public static string GetServiceCode(int service)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<string>("OS6_FPTVoucher_SalePlatform_MBSv4", new
                {
                    actionName = "GetCodeService",
                    @serviceid = service
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        #endregion
    }
}

