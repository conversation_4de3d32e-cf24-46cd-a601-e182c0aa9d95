using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using RedeemInviteCode.Model;
using System.Net;
using System.IO;
using Newtonsoft.Json.Linq;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Voucher.APIHelper.Util;
using Voucher.APIHelper.Log4net;
using System.Data;
using System.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;

namespace RedeemInviteCode.Service
{
    public class ReferalProgramMyFPT
    {
        public static string AddCustomer(AddCustomerReq req)
        {
            var url = PromotionConfig.Url + "/api/promotion/addCustomer";
            var data = JsonConvert.SerializeObject(req);
            string res = IHttpRequest.callapi_CR(url, "POST", data, "application/json; charset=utf-8");
            addCustomerModel resModel = new addCustomerModel()
            {
                hasError = true,
                data = new DataResModel()
            };
            resModel = JsonConvert.DeserializeObject<addCustomerModel>(res);
            if (!resModel.hasError)
            {
                return resModel.data.resultLottery[0];
            }
            return "";
        }
        public static void updateStatusRewardMyFPT(string staffid, string contract)
        {
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    int res = 0;
                    res = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_FoxPro_Report",
                    new
                    {
                        actionName = "activeReward",
                        staffid=staffid,
                        contract = contract
                    },
                    commandType: CommandType.StoredProcedure);
                    L.Mes(Level.INFO, res.ToString(), string.Concat("updateStatusRewardMyFPT ", staffid, " ", contract));
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, string.Concat("updateStatusRewardMyFPT ", staffid, " ",contract, " Error:"));
            }
        }
        private static AuthorizationInfor getAuthorInfor()
        {
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                var uri = WebAPIHelper.myFpt_fpt_vn + "/api/oauth-ms/public/auth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("client_id", Utility.my_fpt_api_client_id));
                keyValues.Add(new KeyValuePair<string, string>("client_secret", Utility.my_fpt_api_client_secret));
                keyValues.Add(new KeyValuePair<string, string>("username", Utility.my_fpt_api_username));
                keyValues.Add(new KeyValuePair<string, string>("password", Utility.my_fpt_api_password));
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "password"));

                var content = new FormUrlEncodedContent(keyValues);


                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var httpClient = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        L.Mes(Level.INFO, response.Content.ReadAsStringAsync().Result, "getAuthorInfor");
                        response.EnsureSuccessStatusCode();
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, "getAuthorInfor");
                        res = JsonConvert.DeserializeObject<AuthorizationInfor>(r);
                    }
                }

            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getAuthorInfor");
                return null;
            }
            return res;
        }
        public static string getStaffInfor(string employeeCode, string keyLog)
        {
            try
            {
                if (string.IsNullOrEmpty(employeeCode))
                {
                    L.Mes(Level.INFO, "Mã nhân viên trống", "getStaffInfor");
                    return "Nhân viên FPT";
                }
                AuthorizationInfor aut = getAuthorInfor();
                if (aut == null)
                    return "Nhân viên FPT";
                StaffInfor res = new StaffInfor();
                res.data = new Name();
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(WebAPIHelper.myFpt_fpt_vn);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    //var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    HttpResponseMessage response = client.GetAsync("/api/oauth-ms/public/auth/get-info-employee?employeeCode=" + employeeCode).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, r, "getStaffInfor");
                    res = JsonConvert.DeserializeObject<StaffInfor>(r);
                }

                if (res != null)
                {
                    if (!string.IsNullOrEmpty(res.data.fullname))
                    {
                        return res.data.fullname;
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getStaffInfor");
                return "Nhân viên FPT";
            }
            return "Nhân viên FPT";
        }
        public static string RamdoneString()
        {
            Random rd = new Random();
            return rd.Next(1, 10000).ToString();
        }
    }
    public class StaffInfor
    {
        public Name data { set; get; }
    }
    public class Name
    {
        public string fullname { set; get; }
        public string email { set; get; }
    }
    public class addCustomerModel
    {
        public bool hasError { set; get; }
        public DataResModel data { set; get; }
    }
    public class DataResModel
    {
        public int resultId { set; get; }
        public string resultDesc { set; get; }
        public List<string> resultLottery { set; get; }
        public string paging { set; get; }
    }
}