using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace RedeemInviteCode.Service
{
    public class IHttpRequest
    {
        public static bool UseProxy = false;
        public static string callapi_hifpt(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            headers.Add("clientKey", "referral");
            headers.Add("secretKey", "ofjhgiiedhcidkfjeudsalsodejdcfydiejd");
            var author = MD5Hash.GetMD5("referral::ofjhgiiedhcidkfjeudsalsodejdcfydiejd" + DateTime.Now.ToString("yyyy-dd-MM"));
            headers.Add("Authorization", author);
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }
        public static string callapi_smsworld(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8", string header = "")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            if (header.Length > 0)
            {
                var headers = wr.Headers;
                headers.Add("tokenSMS", header);
            }
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };

                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }
        public static string callapi_CR(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                wr.ContentType = contentType;
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }
        public string callapi_HR(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8", string header = "")
        {

            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            if (header.Length > 0)
            {
                var headers = wr.Headers;
                headers.Add("Authorization", "Bearer " + header);
                //using (var client = new System.Net.Http.HttpClient())
                //{
                //    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", header);
                //    var data = new System.Net.Http.StringContent(_data, Encoding.UTF8, "application/json");
                //    var response = client.PostAsync(_url, data).Result;
                //    return response.StatusCode + "";
                //}
            }
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };

                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }

        }
    }
}