using RedeemInviteCode.Model.LoyaltyModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Net;
using System.Net.Http;
using System.Configuration;
using System.Text;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using System.IO;

namespace RedeemInviteCode.Service
{
    public class VipVPNService
    {
        private const string OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN";
        public static void CheckStatusVipVPN()
        {
            var logId = Guid.NewGuid();
            try
            {
                List<VipVpnModel> listAccount = Getdata();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(listAccount), "Process CheckStatusVipVPN lst Data");
                if (listAccount.Count == 0)
                {
                    return;
                }

                VipVPNCheckStatusModel vcsm = new VipVPNCheckStatusModel{success=false,data=new List<DataVPNStatusModel>(), message=""};
                List<string> lstdata = new List<string>();
                foreach (var item in listAccount)
                {
                    lstdata.Add(item.Account);
                }
               
                string url = Utility.UrlVPN.ToString();
                string endpointStatus = Utility.vpn_endpoint_status.ToString();
                string fileKey = @"~/Keys/VPNkeys/public.key";

                string responseAPI = CallAPIVPN(url, endpointStatus, fileKey, lstdata);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(responseAPI), "Process_camera CheckStatusVipVPN responseAPI ");
                vcsm = JsonConvert.DeserializeObject<VipVPNCheckStatusModel>(responseAPI);
                if (vcsm.data.Count > 0)
                {
                    foreach (var item in vcsm.data)
                    {
                        var dataVoucher = listAccount.Where(x => x.Account == item.username).FirstOrDefault();
                        if (!string.IsNullOrEmpty(item.activeDate))
                        {
                            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                            {
                                connection.Open();
                                using (var transaction = connection.BeginTransaction())
                                {
                                    int isUpdateRedeemDate = updateRedeemDate(connection, transaction, item, dataVoucher.Code,"UpdateRedeemDate");
                                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(isUpdateRedeemDate), "Process_camera CheckStatusVipVPN isUpdateRedeemDate " +item.username + " ");

                                    
                                    bool changLoyalty = LoyaltyProcessService.ChangeStatusLoyalty("", dataVoucher.Code, logId.ToString());
                                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(changLoyalty), "Process_camera CheckStatusVipVPN changLoyalty " + item.username + " ");

                                    if (changLoyalty)
                                    {
                                        int isChangeStatusLoy = updateRedeemDate(connection, transaction, item, dataVoucher.Code, "UpdateStatusLoy");
                                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                            JsonConvert.SerializeObject(isUpdateRedeemDate), "Process_camera CheckStatusVipVPN UpdateStatusLoy " + item.username + " ");
                                        if (isChangeStatusLoy > 0)
                                        {
                                            transaction.Commit();
                                        }
                                    }
                                    else
                                    {
                                        //gửi mail thông báo trường hợp lỗi
                                        var reqMail = new MailModelInput
                                        {
                                            FromEmail = "<EMAIL>",
                                            Recipients = "<EMAIL> ",
                                            CarbonCopys = "",
                                            BlindCarbonCopys = "",
                                            Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                                            Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công (call Loy) VPN", voucher = dataVoucher.Code, date = DateTime.Now.ToString() }),
                                            AttachFile = "",
                                            AttachUrl = ""
                                        };
                                        SendMail(reqMail);
                                    }
                                }                                
                            } 
                        }                                              
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "Process CheckStatusVipVPN Error");
            }
        }
        public static void SendMail(MailModelInput input)
        {
            var logid = Guid.NewGuid();
            L.Mes(Level.INFO, JsonConvert.SerializeObject(input), logid + "SendMail: ");
            try
            {
                string url = string.Concat(Utility.emailApi, "/api/SendMailSMTP/InsertInfoSendMailSMTP");
                string data = JsonConvert.SerializeObject(input);
                string res = Common.callapi(url, "POST", data);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(res), logid + "SendMail: ");
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), logid + "SendMail: ");
            }
        }
        private static int updateRedeemDate(SqlConnection connection, SqlTransaction transaction, DataVPNStatusModel input,string voucherCode, string actionName)
        {
            return connection.Execute(OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN, new
            {
                ActionName = actionName,
                redeemDate = input.activeDate,
                usernamevpn = input.username,
                code = voucherCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }
        private static List<VipVpnModel> Getdata()
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<VipVpnModel>(OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN, new
                {
                    ActionName = "GetVPNCodeCheck",
                }, commandType: CommandType.StoredProcedure).ToList();
            }
        }
        private static string CallAPIVPN(string _url, string endpoint, string pathFile, List<string> userlist)
        {
            try
            {
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                    //Address = new Uri("http://isc-proxy.hcm.fpt.vn:80")
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                var multiForm = new MultipartFormDataContent();
                foreach (var user in userlist)
                {
                    multiForm.Add(new StringContent(user), "userList");
                }
                
                FileStream fs = File.OpenRead(System.Web.Hosting.HostingEnvironment.MapPath(pathFile));
                multiForm.Add(new StreamContent(fs), "publicKey", Path.GetFileName(System.Web.Hosting.HostingEnvironment.MapPath(pathFile)));
                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);

                    client.DefaultRequestHeaders.Add("token", Utility.vpn_token.ToString());

                    var response = client.PostAsync(_url + endpoint, multiForm).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    return r;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), "CallAPIVPN Error: ");
                return null;
            }
        }
    }
}