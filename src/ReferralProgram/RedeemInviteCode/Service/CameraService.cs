using RedeemInviteCode.Contant;
using RedeemInviteCode.Model.CameraModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Dapper;
using Voucher.APIHelper;
using System.Configuration;
using Newtonsoft.Json;
using System.Xml.Linq;
using Newtonsoft.Json.Linq;
using Voucher.APIHelper.Log4net;
using System.Globalization;
using System.IO;
using RedeemInviteCode.Model.LoyaltyModel;
using RedeemInviteCode.Model;

namespace RedeemInviteCode.Service
{
    public class CameraService
    {
        public static async void ServiceCamera()
        {
            var logIda = Guid.NewGuid();
            try
            {
                List<CameraSucceedModel> dataJob = GetDataJob();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logIda, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(dataJob), "Process_camera dataJob ");

                if (dataJob.Count == 0)
                {
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logIda, DateTime.Now) + " " +
                        "Không có dữ liệu", "Process_camera dataJob ");
                    return;
                }                
                
                foreach (var item in dataJob)
                {
                    var logId = Guid.NewGuid();
                    InforCameraModel icm = GetInfoCam(item.IDDK, item.RegID, logId);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(icm), "Process_camera GetInfoCam ");

                    bool isValid = false;
                    var isOwnerCamera = false;
                    var addLoy = "";
                    var gold = 0;
                    using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                    {
                        isOwnerCamera = ReferralProgramCamera.IsOwnerCamera(connection, null, item.IDGT, logId.ToString());
                    }
                    string referralProgram;
                    string locationPhone;
                    if (icm.statuscode.Equals(0) || icm.data.Count == 0)
                    {
                        #region Hủy TSD
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {
                            connection.Open();
                            using (var transaction = connection.BeginTransaction())
                            {
                                int updateCancel = UpdateCancelCam(connection,transaction,item.CamSucceedID);
                                
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(updateCancel), "Process_camera updateCancel ");
                                transaction.Commit();
                            }
                        }
                        continue;   
                        #endregion
                    }
                    if (icm.statuscode.Equals(1) || icm.data.Count > 0)
                    {
                        #region chờ TK + Active
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {
                            connection.Open();
                            using (var transaction = connection.BeginTransaction())
                            {
                                List<EventCodeEF> newEventCodes = new List<EventCodeEF>();
                                var _historyCam = GetinforCamHistory(connection,transaction,item.IDDK,item.RegCode);
                                _historyCam.AddRange(GetInforCamHistory_SPF(connection, transaction, item.IDDK, item.RegCode));

                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(_historyCam), "Process_camera historyCam ");

                                foreach (var his in _historyCam)
                                {
                                    var realCamActive = icm.data.Where(x => x.servicecode == his.ServiceCodeCamera && !string.IsNullOrEmpty(x.activedate) && x.supid==his.EFcodeID).Count();
                                    if (realCamActive > 0)
                                    newEventCodes.Add(GetEventCodeByQuantityEF(connection, transaction, his.EFcodeID, realCamActive, his.ServiceCodeCamera));                                    
                                }

                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(newEventCodes), "Process_camera NewEventCode ");

                                if (newEventCodes.Count > 2 || newEventCodes.Count == 0)
                                {
                                    continue;
                                }
                                isValid = true;
                                // cập  nhật lại mã trong voucher gC
                                int updateVoucherCame = UpdateVoucherGCCamera(connection,transaction,newEventCodes,item.IDDK,item.RegCode);
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(updateVoucherCame), "Process_camera Update GCCode ");

                                // cập nhật lại mã trong SuceeedInvite
                                foreach (var code in newEventCodes)
                                {
                                    connection.Execute(ConstantAPI.OS6_ReferalProgram_CAM, new
                                    {
                                        ActionName = "UpdateVoucherCameraSucceed",
                                        id = item.CamSucceedID,
                                        Code= code.EventCode,
                                        serviceCode=code.ServiceCode
                                    },transaction:transaction, commandType: CommandType.StoredProcedure);
                                }
                                // add ưu đãi giới thiệu camera
                                if (item.temRedeemID > 0)
                                {
                                    int addPrepaid = AddPrepaidInviteCamera(connection, transaction, item.temRedeemID);
                                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(addPrepaid), "Process_camera AddPrepaid ");
                                }

                                referralProgram = connection.Query<string>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                                {
                                    ActionName = "GetReferalProgramCamera",
                                    ObjID = item.IDDK,
                                    InviteCode = item.InviteCode
                                }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " "
                                    + JsonConvert.SerializeObject(referralProgram), "Process_camera referralProgram: ");

                                locationPhone = connection.Query<string>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                                {
                                    ActionName = "GetLocationPhone",
                                    ObjID = item.IDGT
                                }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " "
                                    + JsonConvert.SerializeObject(locationPhone), "Process_camera phone: ");

                                try
                                {
                                    // add ưu đãi Loyalty và update succeedInvite
                                    addLoy = GetPromotionEventLoy(connection, transaction, item.IDGT, item.RegCode, item.CamSucceedID);
                                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(addLoy), "Process_camera LoyEvent ");
                                    if (!string.IsNullOrEmpty(addLoy))
                                    {
                                        gold = GetFoxGold(addLoy);
                                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(gold), "Process_camera fox gold: ");

                                        var data = new { objId = item.IDGT, actionCode = addLoy, contractNew = item.ContractDK, referralCode = item.InviteCode };
                                        string req = JsonConvert.SerializeObject(data);
                                        string apiUrl = Utility.loyaltyapi + "/loyalty-services/api/integration/send-action";
                                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(req), "Process_camera resLoyAPI req ");

                                        int idRequest = CallbackLoyaltyServices.AddRequestRF(
                                            Convert.ToInt32(item.IDGT),
                                            Convert.ToInt32(item.IDDK),
                                            req,
                                            referralProgram,
                                            apiUrl,
                                            false);

                                        string resLoyAPI = CallbackLoyaltyServices.callapi_loyV2(logId.ToString(), idRequest, apiUrl, "POST", req);
                                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(resLoyAPI), "Process_camera resLoyAPI GT " + item.IDGT.ToString() + " DK " + item.IDDK.ToString());

                                        var lc = JsonConvert.DeserializeObject<LoyaltyCallbackModel>(resLoyAPI);
                                        if (lc.status.ToUpper().Equals("SUCCESS"))
                                        {
                                            CallbackLoyaltyServices.UpdateSuccess(idRequest);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(ex.Message), "Process_camera AddLoy GT - sendHi Eror " + item.IDGT.ToString() + " DK " + item.IDDK.ToString() + " ");

                                }

                                transaction.Commit();

                                if (!isOwnerCamera)
                                {
                                    if (string.IsNullOrEmpty(addLoy)) continue;
                                    var quantity = ProcessReferalProgram.CountContractReferral(connection, transaction, item.IDGT);
                                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " +
                                                      JsonConvert.SerializeObject(quantity),
                                        "Process_camera quantityRF GT" + item.IDGT.ToString() + " DK " +
                                        item.IDDK.ToString());

                                    // gửi thông báo HiFPT
                                    string hi = await NotiHiFPTManager.SendNotifyHiFPT_Cmr(item, icm.data[0].activedate.Replace("T", " ").Replace("t", " "));
                                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " +
                                                      JsonConvert.SerializeObject(hi),
                                        "Process_camera resSendHi GT " + item.IDGT.ToString() + " DK " +
                                        item.IDDK.ToString());
                                }
                            }
                        }
                        #endregion
                        if (isValid)
                        {
                            //ProcessReferalProgram.RollGiftMKT7(item.IDGT, item.IDDK, logId.ToString(), "Camera");

                            #region mktp9
                            //loai hd nguoi duoc GT
                            //string packageTypeInvited = ReferralProgramCamera.GetPackageTypeInvited(item.IDDK);
                            // var packageTypeInvited = "CAMERA";

                            // //loai hd NGT

                            // L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " + "bat-dau-set-data ");
                            // var data = new
                            // {
                            //     data = new
                            //     {
                            //         referrer_code = item.InviteCode,
                            //         contract_of_referrer = item.ContractGT,
                            //         branch = ProcessReferalProgram.GetLocationCodeByID(item.IDGT),
                            //         phone_of_referrer = ProcessReferalProgram.GetPhoneByObjID(item.IDGT),
                            //         contract = item.ContractDK,
                            //         contract_type = packageTypeInvited,
                            //         contract_referrer_info = ProcessReferalProgram.GetContracTypeInvite(item.ContractGT, item.IDGT)
                            //     }
                            // };
                            // L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " + "xet-xongdata " + JsonConvert.SerializeObject(data));
                            // string dataStr = JsonConvert.SerializeObject(data);
                            // string domain = ProcessReferalProgram.GetValueByKeyConfig("hifptdomain");
                            // List<HeaderAPI> headerapi = new List<HeaderAPI>();
                            // headerapi.Add(new HeaderAPI { key = "secret-key", value = "hifpt_isc_001" });
                            // L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " + "data-before-call-api-hifpt " + JsonConvert.SerializeObject(data));
                            // string rs = Common.callapi(domain + "/hi-ecom-seller-api/v1/web/add-spins", "POST", dataStr, "application/json; charset=utf-8", headerapi);
                            // L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " + "data-after-call-api-hifpt " + JsonConvert.SerializeObject(rs));
                            #endregion

                            if (!string.IsNullOrEmpty(item.Email))
                            {
                                try
                                {
                                    string sendmail = SendToMail("<EMAIL>", item, referralProgram,
                                        isOwnerCamera, gold,
                                        icm.data[0].activedate.Replace("T", " ").Replace("t", " "));
                                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " +
                                                      JsonConvert.SerializeObject(sendmail),
                                        "Process_camera sendmail GT " + item.IDGT.ToString() + " DK " +
                                        item.IDDK.ToString());
                                }
                                catch (Exception ex)
                                {
                                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " +
                                                      JsonConvert.SerializeObject(ex.Message),
                                        "Process_camera sendmail GT Error " + item.IDGT.ToString() + " DK " +
                                        item.IDDK.ToString() + " ");
                                }
                            }

                            if (!string.IsNullOrEmpty(locationPhone))
                            {
                                try
                                {
                                    List<SmsResult> source = SendSms(locationPhone, referralProgram);
                                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " +
                                                      JsonConvert.SerializeObject(source),
                                        "Process_camera SendSms GT " + item.IDGT.ToString() + " DK " +
                                        item.IDDK.ToString());
                                }
                                catch (Exception ex)
                                {
                                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " +
                                                      JsonConvert.SerializeObject(ex.Message),
                                        "Process_camera SendSms GT " + item.IDGT.ToString() + " DK " +
                                        item.IDDK.ToString() + " ");
                                }
                            }
                                
                            if (isOwnerCamera)
                            {
                                var cameraModel = ReferralProgramCamera.UpdateCodeCloudInvite(item.IDDK, item.IDGT, logId.ToString(), "CMR");
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                            JsonConvert.SerializeObject(cameraModel), "Process_camera UpdateCodeCloudInvite GT " + item.IDGT.ToString() + " DK " + item.IDDK.ToString() + " ");
                                if (cameraModel != null)
                                {
                                    // gửi thông báo HiFPT
                                    string hi = await NotiHiFPTManager.SendNotifyHiFPT_Cmr(item, icm.data[0].activedate.Replace("T", " ").Replace("t", " "));
                                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + " " +
                                                      JsonConvert.SerializeObject(hi),
                                        "Process_camera resSendHi GT " + item.IDGT.ToString() + " DK " +
                                        item.IDDK.ToString());
                                }                                
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logIda, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "Process_camera Error ");
                return;
            }
        }

        private static HifptModel SendNotifyHiFPTOwnerCamera(CameraSucceedModel model, string cloudCode, string referralProgram, string locationPhone, DateTime firstAccessInvited)
        {
            string url = string.Empty;
            string result = string.Empty;
            string packageTypeInvited = ReferralProgramCamera.GetPackageTypeInvited(model.IDDK);

            packageTypeInvited = packageTypeInvited == "NETONLY" ? "Internet" : packageTypeInvited[0] + packageTypeInvited.Substring(1).ToLower();
            int promotionalGT = packageTypeInvited == "Internet" ? 1 : 2;

            if (referralProgram.Equals("HiFPT"))
            {
                url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;

                var modelapi = new
                {
                    contractIdGT = model.IDGT,
                    contractIdDK = model.IDDK,
                    data = new
                    {
                        eventType = "camera_owner_referral_success",
                        nameDK = model.NameDK,
                        nameGT = model.NameGT,
                        inviteCode = model.InviteCode,
                        contractDK = model.ContractDK,
                        promotionalDK = 100000,
                        promotionalGoldGT = model.FoxGold,
                        contractType = packageTypeInvited,
                        phone = locationPhone,
                        promotionalGT = promotionalGT,
                        giftCode = cloudCode
                    }
                };

                L.Mes(Level.INFO, JsonConvert.SerializeObject(modelapi), "ProcessActiveVoucher - send hi FPT req");
                string dataapi = JsonConvert.SerializeObject(modelapi);
                result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
            }

            if (referralProgram.Equals("DKH"))
            {
                url = HelperConfigs.url_hifpt + "/hi-customer-local/inside/push-notify";
                var modelapi = new
                {
                    contractNo = model.ContractGT,
                    type = "camera_referral_successfully_activated",
                    data = new
                    {
                        registrationContractNo = model.ContractDK,
                        registrationName = model.NameDK,
                        activeTime = firstAccessInvited.ToString("yyyy/MM/dd HH:mm:ss"),
                        promotionalDK = 100000,
                        promotionalDKH = promotionalGT,
                        gold = model.FoxGold,
                        giftCode = cloudCode,
                        contractType = packageTypeInvited,
                        phone = locationPhone
                    }
                };
                L.Mes(Level.INFO, JsonConvert.SerializeObject(modelapi), "ProcessActiveVoucher - send hi FPT req");
                string dataapi = JsonConvert.SerializeObject(modelapi);
                result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
            }

            if (string.IsNullOrEmpty(result))
                return null;

            var rs = JsonConvert.DeserializeObject<HifptModel>(result);
            return rs;
        }

        private static SmsResult Login(string userName, string passWord)
        {
            var model = new SmsResult();
            var url = HelperConfigs.url_apisms + SmsConfig.action_Login;
            var model_api = new { UserName = userName, Password = passWord };
            string loginResult = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8");
            //string rs = JsonConvert.SerializeObject(loginResult);
            var jsonapi = JObject.Parse(loginResult);
            if (jsonapi != null)
            {
                model = jsonapi.ToObject<SmsResult>();
            }
            return model;
        }

        private static List<SmsResult> SendSms(string locationPhone, string referralProgram)
        {
            var rs_api = new List<SmsResult>();
            var model = new SmsResult();
            try { model = Login(SmsConfig.username, SmsConfig.password); }
            catch { }
            if (model != null)
            {
                var rs_sendsms = new SmsResult();
                string template_msg = string.Empty;
                if (referralProgram.Equals("DKH"))
                    template_msg = "Chuc mung Quy khach da Dang ky ho thanh cong 1 hop dong dich vu FPT Telecom. Truy cap app Hi FPT de biet them thong tin chi tiet. Xin cam on!";
                if (referralProgram.Equals("HiFPT"))
                    template_msg = "Chuc mung Quy khach da gioi thieu thanh cong 1 HD dich vu FPT Telecom. Truy cap http://fpt.vn/member hoac app Hi FPT de biet them thong tin chi tiet!";
                if (string.IsNullOrEmpty(template_msg))
                    return null;
                var sms_md = new Smsdata();
                sms_md.Message = template_msg;
                sms_md.PhoneNumber = locationPhone;

                // AesScript model sms
                var param_api = AesCrypt.GetAesCrypt(JsonConvert.SerializeObject(sms_md), SmsConfig.password);
                // Convert aesScript to base64string
                string base64string = System.Convert.ToBase64String(param_api);
                // call api send sms
                var url = HelperConfigs.url_apisms + SmsConfig.action_Sendsms;
                var model_api = new { data = base64string };
                string sendsms_status = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8", model.Detail.AccessToken);
                //string rs = JsonConvert.SerializeObject(loginResult);
                var jsonapi = JObject.Parse(sendsms_status);
                if (jsonapi != null)
                {
                    rs_sendsms = jsonapi.ToObject<SmsResult>();
                    rs_api.Add(rs_sendsms);
                }

            }
            return rs_api;
        }

        private static string SendToMail(string from_email, CameraSucceedModel item, string referralProgram, bool isOwnerCamera, int gold, string deployment)
        {
            L.Mes(Level.INFO, "Start to sendmail to " + item.Email + " parram:" + JsonConvert.SerializeObject(item), "Sendmail");
            TemplateMail template = new TemplateMail();
            int quantityRF =  ProcessReferalProgram.CountContractReferral(item.IDGT);

            string subjectMail = string.Empty;
            CultureInfo cul = CultureInfo.GetCultureInfo("vi-VN");
            string body_html = string.Empty;
            if (isOwnerCamera)
            {
                string cloudPackage = ReferralProgramCamera.GetCloudPackage(item.IDDK);
                if (referralProgram == "HiFPT")
                {
                    template = ProcessReferalProgram.GetTemplateMail("OWNER-CMR-GTBB");
                }
                if (referralProgram == "DKH")
                {
                    template = ProcessReferalProgram.GetTemplateMail("OWNER-CMR-DKH");
                }

                subjectMail = template.Subject;
                body_html = template.Body.Replace("{0}", item.NameGT)
                                            .Replace("{1}", item.NameDK).Replace("{2}", "Camera FPT")
                                            .Replace("{3}", item.ContractDK).Replace("{4}", deployment)
                                            .Replace("{5}", Convert.ToInt32(item.ValueDiscountAmountDK).ToString("#,###", cul.NumberFormat))
                                            .Replace("{6}", cloudPackage);
            } else
            {
                if (referralProgram == "HiFPT")
                {
                    if (quantityRF > 1)
                    {
                        template = ProcessReferalProgram.GetTemplateMail("GTBB-CMR-2");
                        subjectMail = template.Subject;
                        body_html = template.Body.Replace("{0}", item.NameGT)
                                                 .Replace("{1}", quantityRF.ToString())
                                                 .Replace("{2}", item.NameDK)
                                                 .Replace("{3}", item.InviteCode)
                                                 .Replace("{4}", "Camera FPT")
                                                 .Replace("{5}", Convert.ToInt32(item.ValueDiscountAmountDK).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{6}", Convert.ToInt32(item.ValueDiscountAmountGT).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{7}", item.FoxGold.ToString());
                    }
                    else
                    {
                        template = ProcessReferalProgram.GetTemplateMail("GTBB-CMR");
                        subjectMail = template.Subject;
                        body_html = template.Body.Replace("{0}", item.NameGT)
                                                 .Replace("{1}", item.NameDK)
                                                 .Replace("{2}", item.InviteCode)
                                                 .Replace("{3}", "Camera FPT")
                                                 .Replace("{4}", Convert.ToInt32(item.ValueDiscountAmountDK).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{5}", Convert.ToInt32(item.ValueDiscountAmountGT).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{6}", item.FoxGold.ToString());
                    }
                }

                if (referralProgram == "DKH")
                {
                    if (quantityRF > 1)
                    {
                        template = ProcessReferalProgram.GetTemplateMail("DKH-2");
                        subjectMail = template.Subject;
                        body_html = template.Body.Replace("{0}", item.NameGT)
                                                 .Replace("{1}", quantityRF.ToString())
                                                 .Replace("{2}", item.NameDK)
                                                 .Replace("{3}", "Camera FPT")
                                                 .Replace("{4}", item.ContractDK)
                                                 .Replace("{5}", deployment)
                                                 .Replace("{6}", Convert.ToInt32(item.ValueDiscountAmountGT).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{7}", item.FoxGold.ToString());
                    }
                    else
                    {
                        template = ProcessReferalProgram.GetTemplateMail("DKH");
                        subjectMail = template.Subject;
                        body_html = template.Body.Replace("{0}", item.NameGT)
                                                 .Replace("{1}", item.NameDK)
                                                 .Replace("{2}", "Camera FPT")
                                                 .Replace("{3}", item.ContractDK)
                                                 .Replace("{4}", deployment)
                                                 .Replace("{5}", Convert.ToInt32(item.ValueDiscountAmountGT).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{6}", item.FoxGold.ToString());
                    }
                }
            }
            var req = new MailModelInput
            {
                FromEmail = from_email,
                Recipients = item.Email,
                CarbonCopys = "",
                BlindCarbonCopys = "",
                Subject = subjectMail,
                Body = body_html,
                AttachFile = "",
                AttachUrl = ""
            };

            return ProcessReferalProgram.SendMail(req);
        }
        public static int GetFoxGold(string eventCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<int>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                {
                    ActionName = "GetFoxGold",
                    EventCode = eventCode
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        private static HifptModel sendHiFPTCamSuccess(CameraSucceedModel model,string eventLoy,string deployment, string referralProgram, int quantityRF)
        {
            int Foxgold = 0;
            if (!string.IsNullOrEmpty(eventLoy))
            {
                Foxgold = GetFoxGold(eventLoy);
            }

            string packageTypeInvited = ProcessReferalProgram.GetPackageTypeInvited(model.IDDK);

            var rs = new HifptModel();
            if (referralProgram == "HiFPT")
            {
                var url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;
                var modelapi = new
                {
                    contractIdGT = model.IDGT,
                    contractIdDK = model.IDDK,
                    data = new
                    {
                        eventType = "referral_code_success",
                        nameGT = model.NameGT,
                        nameDK = model.NameDK,
                        successTimes = quantityRF,
                        inviteCode = model.InviteCode,
                        contractDK = model.ContractDK,
                        contractType = packageTypeInvited,
                        deploymentTime = deployment,
                        promotionalMoneyDK = model.ValueDiscountAmountDK,
                        promotionalMoneyGT = model.ValueDiscountAmountGT,
                        promotionalGoldGT = Foxgold
                    }
                };

                string dataapi = JsonConvert.SerializeObject(modelapi);
                string result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
                var jsonapi = JObject.Parse(result);
                if (jsonapi != null)
                {
                    rs = jsonapi.ToObject<HifptModel>();
                }
            }

            if (referralProgram == "DKH")
            {
                var url = HelperConfigs.url_hifpt + "/hi-customer-local/inside/push-notify";
                var modelapi = new
                {
                    contractNo = model.ContractGT,
                    type = "successfully_activated_registration",
                    data = new
                    {
                        registrationContractNo = model.ContractDK,
                        registrationName = model.NameDK,
                        activeTime = deployment,
                        money = model.ValueDiscountAmountGT,
                        gold = Foxgold,
                        contractType = packageTypeInvited,
                        successTimes = quantityRF,
                    }
                };

                string dataapi = JsonConvert.SerializeObject(modelapi);
                string result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
                var jsonapi = JObject.Parse(result);
                if (jsonapi != null)
                {
                    rs = jsonapi.ToObject<HifptModel>();
                }
            }


            return rs;
        }
        private static string GetPromotionEventLoy(SqlConnection connection, SqlTransaction transaction, int objid, string ordercode, int idSucceed)
        {
            return connection.Query<string>(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "insertGCLoyCam",
                objid=objid,
                OrderCode = ordercode,
                id = idSucceed
            }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        private static int AddPrepaidInviteCamera(SqlConnection connection, SqlTransaction transaction, int temRedeemID)
        {
            return connection.Execute(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "AddPrepaidInviteCamera",
                id = temRedeemID
            }, transaction: transaction, commandType: CommandType.StoredProcedure);
        }
        private static int UpdateVoucherGCCamera(SqlConnection connection, SqlTransaction transaction, List<EventCodeEF> events, int objid, string regCode)
        {
            return connection.Execute(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "UpdateVoucherGCCamera",
                objid = objid,
                OrderCode =  regCode,
                xml = CreateXMLGC(events,objid,regCode)
            },transaction:transaction, commandType: CommandType.StoredProcedure);
        }

        private static XElement CreateXMLGC(List<EventCodeEF> events,int objid,string regCode)
        {
            var xmlString = new XElement("N",
            from item in events
            select new XElement("I",
                           new XElement("ID", item.ID),
                           new XElement("ServiceCode", item.ServiceCode),
                           new XElement("Name", item.Name),
                           new XElement("EventCode", item.EventCode),
                           new XElement("Objid", objid),
                           new XElement("Ordercoder", regCode)
                       ));
            return xmlString;
        }
        private static EventCodeEF GetEventCodeByQuantityEF(SqlConnection connection, SqlTransaction transaction, int efid, int quantity, int servicecode)
        {
            return connection.Query<EventCodeEF>(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "GetEventCodeByQuantityEF",
                quantity = quantity,
                serviceCode = servicecode,
                EfId = efid
            }, transaction:transaction, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 5); 
        }
        private static List<HistoryCamInfo> GetinforCamHistory(SqlConnection connection, SqlTransaction transaction, int objid, string regcode)
        {
            return connection.Query<HistoryCamInfo>(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "GetinforCamHistory",
                OrderCode = regcode,
                objid=objid
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
        }

        private static List<HistoryCamInfo> GetInforCamHistory_SPF(SqlConnection connection, SqlTransaction transaction, int objid, string regcode)
        {
            return connection.Query<HistoryCamInfo>("PowerInside.dbo.OS6_ReferralProgram_DKOLV5", new
            {
                ActionName = "GetInfoCamHistoryTemp",
                RegCode = regcode,
                ObjID = objid
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
        }
        private static int UpdateCancelCam(SqlConnection connection, SqlTransaction transaction,int idRfCam)
        {
            return connection.Execute(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "UpdateCancelCamera",
                id = idRfCam
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }
        private static List<CameraSucceedModel> GetDataJob()
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<CameraSucceedModel>(ConstantAPI.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "GetDataJob",
                }, commandType: CommandType.StoredProcedure).ToList();
            }
        }
        private static InforCameraModel GetInfoCam(int objid, int regid, Guid logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(objid), "Process_camera GetInfoCam for " + objid.ToString());
            try
            {
                InforCameraModel icm = new InforCameraModel { data=new List<CameraData>()};
                string uri = Utility.camera_api;
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(uri), objid.ToString() + "Process_camera GetInfoCam response URI");

                string api = uri + "/API/Evoucher/GetinfocamByObjID?ObjID={0}&RegID={1}";
                
                string url = string.Format(api, objid, regid);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(url), objid.ToString() + "Process_camera GetInfoCam response API");

                string infoCam = Common.callapi(url,"GET");
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(infoCam), objid.ToString() + "Process_camera GetInfoCam response ");

                icm = JsonConvert.DeserializeObject<InforCameraModel>(infoCam);
                return icm;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(ex.Message),objid.ToString() + "Process_camera GetInfoCam Error ");
                return null;
            }
        }
    }
}
























































