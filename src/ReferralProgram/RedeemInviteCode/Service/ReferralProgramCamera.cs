using Dapper;
using Newtonsoft.Json;
using RedeemInviteCode.Contant;
using RedeemInviteCode.Model;
using RedeemVoucher.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Xml.Linq;
using RedeemInviteCode.Model.CameraModel;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;

namespace RedeemInviteCode.Service
{
    public class ReferralProgramCamera
    {
        private const string OS6_FPTVoucher_FriendSellEvent = "PowerInside.dbo.OS6_FPTVoucher_FriendSellEvent";

        
        public static bool IsOwnerCamera(SqlConnection connection, SqlTransaction transaction, long objId, string logId)
        {
            try
            {
                var contract = connection.Query<string>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                    {
                        ActionName = "GetContract",
                        ObjID = objId
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure)
                    .FirstOrDefault();
                
                using (var client = new HttpClient())
                {
                    var url = Utility.camera_api;
                    client.BaseAddress = new Uri(url);
                    var data = new StringContent(JsonConvert.SerializeObject(new {contract}), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/API/Info/CheckUsingCamera", data).Result.Content.ReadAsAsync<CheckUsingCameraResponse>();
                    L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + JsonConvert.SerializeObject(response), "CheckOwnerCameraResponse");
                    var result = response.Result;
                    return result.StatusCode == 1 && result.Data.IsUsingCamOnly;
                }
            }
            catch (Exception e)
            {
                L.Mes(Level.INFO, $"[{logId} {DateTime.Now:HH:mm:ss}] " + JsonConvert.SerializeObject(e.Message), "CheckOwnerCameraError");
                return false;
            }
        }

        public static async Task<Boolean> RedeemInviteCode(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, string keylog)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                       JsonConvert.SerializeObject(input), "RedeemInviteCode resLoyAPI GT ");

            // kiểm tra nhập nhiều hơn 1 mã RP hoặc không có trong giỏ
            if (input.Any(x => x.ObjecInvite == 0) || input.Count() != 1) return false;

            // lay thong tin cua history tem
            HistoryTemInfo historyTemInfor = connection.Query<HistoryTemInfo>(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "GetVoucher_HistotyTemp",
                OrderCode = input[0].OrderCode,
                Code = input[0].VoucherCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            if (historyTemInfor == null) return false;

            #region Active NET hoac TV            
            if (historyTemInfor.IsPrepaidNET > -1)
            {
                var PrepaidInfo = connection.Query<Tuple<int, int, int, int, int>>(ConstantAPI.StoreName_InviteCode, new
                {
                    ActionName = "GetPrepaidOfInviteCode",
                    OrderCode = input[0].OrderCode,
                    Code = input[0].VoucherCode,
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(PrepaidInfo), "RedeemInviteCode PrepaidInfo ");

                //get Info promotionEvent invite and Invited
                List<InfoRP> InviteInfo = new List<InfoRP>();

                InviteInfo = connection.Query<InfoRP>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                {
                    ActionName = "GetPEREF",
                    PackageTypeInvited = ReferalProgramHiFPT.BuildPackageType(PrepaidInfo.Item2, PrepaidInfo.Item3),
                    PaidTimeType = PrepaidInfo.Item1
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(InviteInfo), "RedeemInviteCode InviteInfo ");
                if (InviteInfo.Count() != 3)
                {
                    return false;
                }
                else if (InviteInfo.Count() == 3)
                {
                    L.Mes(Level.INFOPE, keylog + " " + JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] }));
                }

                // insert data to table InviteSuccess
                // [1] : người được giới thiệu
                // [2] : người giới thiệu
                int InsertInviteSuccess = connection.Query<int>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                {
                    ActionName = "InsertInfoRedeemRPCode",
                    XML = CreateXMLRedeem(input, InviteInfo[2].EventCode, InviteInfo[1].EventCode, "")
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(InsertInviteSuccess), "RedeemInviteCode InsertInviteSuccess ");
                if (InsertInviteSuccess != input.Count()) return false;

                if (historyTemInfor.CamQuantity.Equals(0))
                {
                    ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                }
                // insert data to table generalcode
                List<GeneralCodeInsert> stsAddGeneralCode = InsertGeneralCodeRPgetGCID(connection, transaction, input, InviteInfo, PrepaidInfo);

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(stsAddGeneralCode), "RedeemInviteCode stsAddGeneralCode ");
                if (stsAddGeneralCode.Count() != 2) return false;

                // update Discount RpCode User Invited
                ReferalProgramHiFPT.UpdateDiscount(
                     input[0].Objectinvited,  // objID khách hàng
                     InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                     InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                     InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                     InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                     InviteInfo[1].EventCode, // Code
                     input[0].Objectinvited, false); //Object Invite -TypeVC:True    

                ReferalProgramHiFPT.UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, InviteInfo[1].EventCode, stsAddGeneralCode[1].id);
                L.Mes(Level.INFO, keylog + " " + "SendMess");
            }
            #endregion

            #region Hop dong co CAM
            if (historyTemInfor.CamQuantity > 0)
            {
                List<HistoryTemInfoCam> cameras = connection.Query<HistoryTemInfoCam>(ConstantAPI.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "GetHistoryTemInfoCam",
                    InviteCode = input[0].VoucherCode,
                    OrderCode = input[0].OrderCode
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(cameras), "RedeemInviteCode cameras ");
                if (cameras.Count > 2) return false;
                List<InfoRFCAM> listRFcam = new List<InfoRFCAM>();

                var camGT = connection.Query<InfoRFCAM>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                {
                    ActionName = "GetPECAM",
                    InviteCode = input[0].VoucherCode,
                    PackageTypeInvited = "COMBO"
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 1);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(camGT), "RedeemInviteCode camGT ");

                if (camGT == null || camGT.QuotaGeneralCode < 1) return false;
                listRFcam.Add(new InfoRFCAM { EventCode = camGT.EventCode, ID = camGT.ID, InviteType = camGT.InviteType, QuotaGeneralCode = camGT.QuotaGeneralCode });
                EventCamera ec = new EventCamera();
                foreach (var camera in cameras)
                {
                    var camDK = connection.Query<InfoRFCAM>(ConstantAPI.OS6_ReferalProgram_CAM, new
                    {
                        ActionName = "GetPromotionEvent",
                        InviteCode = input[0].VoucherCode,
                        quantity = camera.CamQuantity,
                        serviceCode = camera.ServiceCodeCamera,
                        EfId = camera.EFcodeID
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 5);
                    if (camera.ServiceCodeCamera == 322) ec.EventCodeCamIn = camDK.EventCode;
                    if (camera.ServiceCodeCamera == 323 || camera.ServiceCodeCamera == 443) ec.EventCodeCamOut = camDK.EventCode;
                    listRFcam.Add(new InfoRFCAM { EventCode = camDK.EventCode, ID = camDK.ID, InviteType = camDK.InviteType, QuotaGeneralCode = camDK.QuotaGeneralCode });

                }
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(listRFcam), "RedeemInviteCode listRFcam ");
                if (ec == null) return false;

                int InsertInviteSuccess = connection.Query<int>(ConstantAPI.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "InsertInfoRedeemRPCodeCAM",
                    XML = ReferalProgramHiFPT.CreateXMLRedeemCAM(input, ec, camGT.EventCode, "")
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(InsertInviteSuccess), "RedeemInviteCode InsertInviteSuccess ");
                if (InsertInviteSuccess != 1) return false;

                Boolean stsupdateCam = ReferalProgramHiFPT.UpdateHistoryTempCamera(connection, transaction, input);

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(stsupdateCam), "RedeemInviteCode stsupdateCam ");
                if (!stsupdateCam) return false;

                if (historyTemInfor.CamQuantity > 0 && historyTemInfor.IsPrepaidNET.Equals(-1))
                {
                    ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                }
                // insert data to table generalcode
                Boolean stsAddGeneralCode = ReferalProgramHiFPT.InsertGeneralCodeRPCAM(connection, transaction, input, listRFcam, historyTemInfor);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(stsAddGeneralCode), "RedeemInviteCode stsAddGeneralCode ");
                //L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusAddGeneralCode = stsAddGeneralCode }));

                //await SenNotify(input[0].ObjecInvite, input[0].Objectinvited, "camera_referral_code_confirm");

                if (!stsAddGeneralCode) return false;
            }
            #endregion
            if (historyTemInfor.CamQuantity > 0 && historyTemInfor.IsPrepaidNET >= 0)
            {
                ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
            }
            Boolean StsUpdateHistoryTemp = ReferalProgramHiFPT.UpdateHistoryTemp(connection, transaction, input);
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(StsUpdateHistoryTemp), "RedeemInviteCode StsUpdateHistoryTemp ");

            if (!StsUpdateHistoryTemp) return false;

            return true;
        }

        public static async Task<Boolean> RedeemInviteCodeFriendSell(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, string ContractGT, string keylog)
        {

            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(input), "RedeemInviteCode DKH ");

            try
            {
                // kiểm tra nhập nhiều hơn 1 mã RP hoặc không có trong giỏ
                if (input.Any(x => x.ObjecInvite == 0) || input.Count() != 1) return false;

                // lay thong tin cua history tem
                HistoryTemInfo historyTemInfor = connection.Query<HistoryTemInfo>(ConstantAPI.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "GetVoucher_HistotyTemp",
                    OrderCode = input[0].OrderCode,
                    Code = input[0].VoucherCode
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(input), "RedeemInviteCode DKH historyTemInfor ");
                if (historyTemInfor == null) return false;

                #region Active NET hoac TV
                if (historyTemInfor.IsPrepaidNET > -1)
                {
                    var PrepaidInfo = connection.Query<Tuple<int, int, int, int, int>>(ConstantAPI.StoreName_InviteCode, new
                    {
                        ActionName = "GetPrepaidOfInviteCode",
                        OrderCode = input[0].OrderCode,
                        Code = input[0].VoucherCode,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(PrepaidInfo), "RedeemInviteCode PrepaidInfo ");
                    ///L.Mes(Level.INFOPE, keylog + " " + "PrepaidInfo " + JsonConvert.SerializeObject(PrepaidInfo));
                    //get Info promotionEvent invite and Invited
                    List<InfoRP> InviteInfo = new List<InfoRP>();


                    InviteInfo = connection.Query<InfoRP>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                    {
                        ActionName = "GetPEREF",
                        PackageTypeInvited = ReferalProgramHiFPT.BuildPackageType(PrepaidInfo.Item2, PrepaidInfo.Item3),
                        PaidTimeType = PrepaidInfo.Item1
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(InviteInfo), "RedeemInviteCode InviteInfo ");

                    //L.Mes(Level.INFOPE, JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] }));
                    //if (InviteInfo.Count() != 2) return false;

                    if (InviteInfo.Count() != 3)
                    {
                        return false;
                    }
                    else if (InviteInfo.Count() == 3)
                    {
                        L.Mes(Level.INFOPE, keylog + " " + JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] }));
                    }

                    // insert data to table InviteSuccess
                    // [1] : người được giới thiệu
                    // [2] : người giới thiệu
                    int InsertInviteSuccess = connection.Query<int>(OS6_FPTVoucher_FriendSellEvent, new
                    {
                        ActionName = "InsertInfoRedeemRPCode",
                        XML = ReferalProgramHiFPT.CreateXMLRedeem(input, InviteInfo[2].EventCode, InviteInfo[1].EventCode, "")
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    //L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusUpdateInviteSuccess = (InsertInviteSuccess == 1) }));

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(InsertInviteSuccess), "RedeemInviteCode InsertInviteSuccess ");
                    if (InsertInviteSuccess != input.Count()) return false;

                    //if (historyTemInfor.CamQuantity.Equals(0))
                    //{ 
                    //    ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                    //}

                    ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                    // insert data to table generalcode
                    List<GeneralCodeInsert> stsAddGeneralCode = InsertGeneralCodeRPgetGCID(connection, transaction, input, InviteInfo, PrepaidInfo);

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(stsAddGeneralCode), "RedeemInviteCode stsAddGeneralCode ");
                    if (stsAddGeneralCode.Count() != 2) return false;

                    // update Discount RpCode User Invited
                    ReferalProgramHiFPT.UpdateDiscount(
                         input[0].Objectinvited,  // objID khách hàng
                         InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                         InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                         InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                         InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                         InviteInfo[1].EventCode, // Code
                         input[0].Objectinvited, false); //Object Invite -TypeVC:True    

                    // add voucher bill invited
                    ReferalProgramHiFPT.UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, InviteInfo[1].EventCode, stsAddGeneralCode[1].id);       
                }
                #endregion



                if (historyTemInfor.CamQuantity > 0)
                {
                    #region Hop dong co CAM
                    
                    List<HistoryTemInfoCam> cameras = connection.Query<HistoryTemInfoCam>(ConstantAPI.OS6_ReferalProgram_CAM, new
                    {
                        ActionName = "GetHistoryTemInfoCam",
                        InviteCode = input[0].VoucherCode,
                        OrderCode = input[0].OrderCode
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(cameras), "RedeemInviteCode cameras ");
                    if (cameras.Count > 2) return false;
                    List<InfoRFCAM> listRFcam = new List<InfoRFCAM>();

                    var camGT = connection.Query<InfoRFCAM>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                    {
                        ActionName = "GetPECAM",
                        InviteCode = input[0].VoucherCode,
                        PackageTypeInvited = "COMBO"
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 1);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(camGT), "RedeemInviteCode camGT ");

                    if (camGT == null || camGT.QuotaGeneralCode < 1) return false;
                    listRFcam.Add(new InfoRFCAM { EventCode = camGT.EventCode, ID = camGT.ID, InviteType = camGT.InviteType, QuotaGeneralCode = camGT.QuotaGeneralCode });
                    EventCamera ec = new EventCamera();
                    foreach (var camera in cameras)
                    {
                        var camDK = connection.Query<InfoRFCAM>(ConstantAPI.OS6_ReferalProgram_CAM, new
                        {
                            ActionName = "GetPromotionEvent",
                            InviteCode = input[0].VoucherCode,
                            quantity = camera.CamQuantity,
                            serviceCode = camera.ServiceCodeCamera,
                            EfId = camera.EFcodeID
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 5);
                        if (camera.ServiceCodeCamera == 322) ec.EventCodeCamIn = camDK.EventCode;
                        if (camera.ServiceCodeCamera == 323 || camera.ServiceCodeCamera == 443) ec.EventCodeCamOut = camDK.EventCode;
                        listRFcam.Add(new InfoRFCAM { EventCode = camDK.EventCode, ID = camDK.ID, InviteType = camDK.InviteType, QuotaGeneralCode = camDK.QuotaGeneralCode });

                    }
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(listRFcam), "RedeemInviteCode listRFcam ");
                    if (ec == null) return false;

                    int InsertInviteSuccess = connection.Query<int>(ConstantAPI.OS6_ReferalProgram_CAM, new
                    {
                        ActionName = "InsertInfoRedeemRPCodeCAM",
                        XML = CreateXMLRedeemCAMFriendSell(input, ec, camGT.EventCode, "")
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(InsertInviteSuccess), "RedeemInviteCode InsertInviteSuccess ");
                    if (InsertInviteSuccess != 1) return false;

                    Boolean stsupdateCam = ReferalProgramHiFPT.UpdateHistoryTempCamera(connection, transaction, input);

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(stsupdateCam), "RedeemInviteCode stsupdateCam ");
                    if (!stsupdateCam) return false;

                    if (historyTemInfor.CamQuantity > 0 && historyTemInfor.IsPrepaidNET.Equals(-1))
                    {
                        ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                    }
                    // insert data to table generalcode
                    Boolean stsAddGeneralCode = ReferalProgramHiFPT.InsertGeneralCodeRPCAM(connection, transaction, input, listRFcam, historyTemInfor);

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(stsAddGeneralCode), "RedeemInviteCode stsAddGeneralCode ");

                    if (!stsAddGeneralCode) return false;
                    
                    #endregion
                }
                if (historyTemInfor.CamQuantity > 0 && historyTemInfor.IsPrepaidNET >= 0)
                {
                    ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                }
                Boolean StsUpdateHistoryTemp = ReferalProgramHiFPT.UpdateHistoryTemp(connection, transaction, input);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(StsUpdateHistoryTemp), "RedeemInviteCode StsUpdateHistoryTemp ");

                if (!StsUpdateHistoryTemp) return false;

                return true;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(ex.Message), "RedeemInviteCode StsUpdateHistoryTemp ");
                return false;
            }
        }

        public static Boolean InsertGeneralCodeFoxGold(SqlConnection connection, SqlTransaction transaction, InfoRP promotionEvent, List<ItemRequest> input)
        {
            int InsertGeneralCode = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
            {
                ActionName = "InsertGeneralCodeXML",
                XML = CreateXMLUpdateGeneralCode(promotionEvent, input),
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            return (InsertGeneralCode <= 1);
        }

        public static string CallApiCamera(string logId, string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            try
            {
                HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
                wr.Method = _method;
                if (_method.ToUpper().Equals("POST"))
                {
                    wr.ContentType = contentType;
                    // Set the data to send.
                    using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                    {
                        streamWriter.Write(_data);
                    }
                }
                var httpResponse = (HttpWebResponse)wr.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    var rs = streamReader.ReadToEnd();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(rs), "ProcessActiveVoucher - call api camera: ");
                    return rs;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "ProcessActiveVoucher - call api camera error: ");
                return "";
            }
        }

        public static int GetRegIDDK(long objIDInvited)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<int>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                {
                    ActionName = "GetRegIDDK",
                    ObjID = objIDInvited,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }    

        public static string GetPackageTypeInvited(long objIDInvited)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
               return connection.Query<string>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                {
                    ActionName = "GetPackageTypeInvited",
                    ObjID = objIDInvited,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                
            }
        }

        public static string GetPackageTypeInvitedCamera(long objIDInvited)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<string>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                {
                    ActionName = "GetPackageTypeInvitedCamera",
                    ObjID = objIDInvited,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            }
        }

        public static XElement CreateXMLUpdateGeneralCode(InfoRP promotionEvent, List<ItemRequest> input)
        {
            var xmlString = new XElement("N",
                            new XElement("I",  // Mã loy cho người giới thiệu status = 100
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));
            return xmlString;
        }

        public static XElement CreateXMLRedeem(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.OrderCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite),// object của người giới thiệu
                           new XElement("TypeRF", "CAMERA")
                       ));
            return xmlString;
        }

        public static string CreateString(int length)
        {
            const string valid = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
            StringBuilder res = new StringBuilder();
            Random rnd = new Random();
            while (0 < length--)
            {
                res.Append(valid[rnd.Next(valid.Length)]);
            }
            return res.ToString();
        }

        public static XElement CreateXMLRedeemCAMFriendSell(List<ItemRequest> input, EventCamera ec, string GeneralCodeInvite = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited-in", ec.EventCodeCamIn),
                           new XElement("Vinvited-out", ec.EventCodeCamOut),
                           new XElement("Or", item.OrderCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite),// object của người giới thiệu
                           new XElement("RF", "DKH")
                       ));
            return xmlString;
        }

        public static HifptModel SendNotifyHiFPT(Referal_model model, string cloudCode)
        {
            string url = string.Empty;
            string result = string.Empty;
            string packageTypeInvited = GetPackageTypeInvited(model.ObjIDDK);

            packageTypeInvited = packageTypeInvited == "NETONLY" ? "Internet" : packageTypeInvited[0] + packageTypeInvited.Substring(1).ToLower();
            int promotionalGT = packageTypeInvited == "Internet" ? 1 : 2;

            if (model.ChannelRegister.Equals("GTBB"))
            {
                url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;
                
                var modelapi = new
                {
                    contractIdGT = model.ObjIDGT,
                    contractIdDK = model.ObjIDDK,
                    data = new
                    {
                        eventType = "camera_owner_referral_success",
                        nameDK = model.Fullname,
                        nameGT = model.ReceiveFullname,
                        inviteCode = model.InviteCode,
                        contractDK = model.ContractDK,
                        promotionalDK = model.Reward,
                        promotionalGoldGT = model.FoxGold,
                        contractType = packageTypeInvited,
                        phone = model.Location_Phone,
                        promotionalGT = promotionalGT,
                        giftCode = cloudCode
                    }
                };               

                L.Mes(Level.INFO, JsonConvert.SerializeObject(modelapi), "ProcessActiveVoucher - send hi FPT req");
                string dataapi = JsonConvert.SerializeObject(modelapi);
                result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
            }

            if (model.ChannelRegister.Equals("DKH"))
            {
                url = HelperConfigs.url_hifpt + "/hi-customer-local/inside/push-notify";
                var modelapi = new
                {
                    contractNo = model.ContractGT,
                    type = "camera_referral_successfully_activated",
                    data = new
                    {
                        registrationContractNo = model.ContractDK,
                        registrationName = model.Fullname,
                        activeTime = model.FirstAccess.ToString("yyyy/MM/dd HH:mm:ss"),
                        promotionalDK = model.Reward,
                        promotionalDKH = promotionalGT,
                        gold = model.FoxGold,
                        giftCode = cloudCode,
                        contractType = packageTypeInvited,
                        phone = model.Location_Phone
                    }
                };
                L.Mes(Level.INFO, JsonConvert.SerializeObject(modelapi), "ProcessActiveVoucher - send hi FPT req");
                string dataapi = JsonConvert.SerializeObject(modelapi);
                result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
            }

            if (string.IsNullOrEmpty(result))
                return null;

            var rs = JsonConvert.DeserializeObject<HifptModel>(result);
            return rs;
        }

        public static CameraModel UpdateCodeCloudInvite(long objIDDK, long objIDGT, string logGuid, string channel)
        {
            string keylog = string.Format("[{0} {1:HH:mm:ss}] ", logGuid, DateTime.Now);
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    int regIDDK = connection.Query<int>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                    {
                        ActionName = "GetRegIDDK",
                        ObjID = objIDDK,
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(regIDDK), string.Concat(keylog, " UpdateCodeCloudInvite regIDDK "));

                    string packageTypeInvited = connection.Query<string>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                    {
                        ActionName = "GetPackageTypeInvited",
                        ObjID = objIDDK,
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(packageTypeInvited), string.Concat(keylog, " UpdateCodeCloudInvite packageTypeInvited "));

                    byte daysave = 0, duration = 0;
                    if (packageTypeInvited == "NETONLY")
                    {
                        daysave = 1;
                        duration = 3;
                    }
                    else
                    {
                        daysave = 1;
                        duration = 6;
                    }
                    var data = new
                    {
                        objIDGT = objIDGT,
                        objIDDK = objIDDK,
                        regIDDK = regIDDK,
                        daysave = daysave,
                        duration = duration
                    };
                    var req = JsonConvert.SerializeObject(data);

                    L.Mes(Level.INFO, JsonConvert.SerializeObject(req), string.Concat(keylog, " UpdateCodeCloudInvite Call camera api "));
                    string response = CallApiCamera(keylog, Utility.camera_api + "/API/Evoucher/GetCodeGTBB", "POST", req);
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(response), string.Concat(keylog, " UpdateCodeCloudInvite response api "));
                    if (string.IsNullOrEmpty(response))
                    {
                        L.Mes(Level.INFO, JsonConvert.SerializeObject(""), string.Concat(keylog, "Call camera api res: Error "));
                    }
                    else
                    {
                        var cameraModel = JsonConvert.DeserializeObject<CameraModel>(response);
                        if (cameraModel.statusCode == 1)
                        {
                            using (var transaction = connection.BeginTransaction())
                            {
                                if (channel.Equals("GTBB"))
                                {
                                    int executeData = connection.Execute("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                                    {
                                        ActionName = "UpdateCodeCloudInviteGTBB",
                                        ObjID = objIDDK,
                                        CodeCloudInvite = cameraModel.data
                                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                    L.Mes(Level.INFO, JsonConvert.SerializeObject(executeData), string.Concat(keylog, " UpdateCodeCloudInvite UpdateCodeCloudInviteGTBB "));
                                }

                                if (channel.Equals("DKH"))
                                {
                                    int executeData = connection.Execute("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                                    {
                                        ActionName = "UpdateCodeCloudInviteDKH",
                                        ObjID = objIDDK,
                                        CodeCloudInvite = cameraModel.data
                                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                    L.Mes(Level.INFO, JsonConvert.SerializeObject(executeData), string.Concat(keylog, " UpdateCodeCloudInvite UpdateCodeCloudInviteDKH "));
                                }

                                if (channel.Equals("CMR"))
                                {
                                    int executeData = connection.Execute("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                                    {
                                        ActionName = "UpdateCodeCloudInviteCMR",
                                        ObjID = objIDDK,
                                        CodeCloudInvite = cameraModel.data
                                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                    L.Mes(Level.INFO, JsonConvert.SerializeObject(executeData), string.Concat(keylog, " UpdateCodeCloudInvite UpdateCodeCloudInviteCMR "));
                                }

                                transaction.Commit();
                            }

                            return cameraModel;
                        }
                        else
                        {
                            L.Mes(Level.INFO, JsonConvert.SerializeObject(""), string.Concat(keylog, "Call camera api res: Error " + cameraModel.error));
                        }
                    }
                }
            } catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(""), string.Concat(keylog, "Error call api camera: " + ex.Message));
            }

            return null;
        }

        public static string GetCloudPackage(long objIdInvited)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                string packageTypeInvited = connection.Query<string>("PowerInside.dbo.OS6_ReferralProgram_Camera", new
                {
                    ActionName = "GetPackageTypeInvited",
                    ObjID = objIdInvited,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                if (packageTypeInvited == "NETONLY")
                    return "1 Ngày 3 Tháng";

                return "1 Ngày 6 Tháng";
            }
        }

        public static List<GeneralCodeInsert> InsertGeneralCodeRPgetGCID(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            List<GeneralCodeInsert> lst = new List<GeneralCodeInsert>();
            for (int i = 1; i < InviteInfo.Count; i++)
            {
                GeneralCodeInsert gc = new GeneralCodeInsert();
                gc.eventCode = InviteInfo[i].EventCode;
                gc.id = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
                {
                    ActionName = "InsertGeneralCodeXMLGCID",
                    XML = ReferalProgramHiFPT.CreateXMLUpdateGeneralCodeRPbyGC(input, InviteInfo[i], PrepaidInfo, i),
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                lst.Add(gc);
            }
            return lst;
        }       
    }
}