using System;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RedeemInviteCode.Model.CameraModel;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;

namespace RedeemInviteCode.Service
{
    public class NotiHiFPTManager
    {
        private static string _cachedToken;
        private static DateTime _tokenExpiration;
        private static readonly HttpClient _httpClient = new HttpClient();

        private static async Task<string> FetchTokenAsync()
        {
            if (!string.IsNullOrEmpty(_cachedToken) && _tokenExpiration != null && _tokenExpiration > DateTime.Now)
            {
                return _cachedToken;
            }
            var requestBody = new
            {
                clientId = Utility.get_appsettings("HiFPTFG_token_clientId"),
                clientSecret = Utility.get_appsettings("HiFPTFG_token_clientSecret")
            };

            var response = await _httpClient.PostAsJsonAsync(Utility.get_appsettings("HiFPTFG")+Utility.get_appsettings("HiFPTFG_token_endpoint"), requestBody);
            var content = await response.Content.ReadAsStringAsync();
            var jsonResponse = JObject.Parse(content);

            if ((int)jsonResponse["statusCode"] == 0)
            {
                _cachedToken = (string)jsonResponse["data"]["token"];
                _tokenExpiration = DateTime.Parse((string)jsonResponse["data"]["expire"]);
                return _cachedToken;
            }

            throw new Exception("Failed to fetch token: " + (string)jsonResponse["message"]);
        }

        public static async Task<string> GetTokenAsync()
        {
            if (string.IsNullOrEmpty(_cachedToken) || DateTime.UtcNow >= _tokenExpiration)
            {
                return await FetchTokenAsync();
            }

            return _cachedToken;
        }

        public static async Task<string> SendNotificationAsync(int templateId, string contarct, string refRegister, string fullname, string service, string refCode, string firstAccess)
        {

            string token = await GetTokenAsync();

            var req = new
            {
                templateId = templateId,
                contractNo = contarct,
                dataReplace = new
                {
                    P1 = new string[] { "DKH", "ĐKH" }.Contains(refRegister.ToUpper()) == true ? "Đăng ký hộ" : "Giới thiệu",
                    P2 = fullname,
                    P3 = service,
                    P4 = refCode,
                    P5 = firstAccess,
                    E1 = new string[] { "DKH", "ĐKH" }.Contains(refRegister.ToUpper()) == true ? "Đăng ký hộ" : "Giới thiệu",
                    E2 = fullname,
                    E3 = service,
                    E4 = refCode,
                    E5 = firstAccess
                },
                data = new
                {
                    contractNo = contarct
                }
            };

            _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            var response = await _httpClient.PostAsJsonAsync(Utility.get_appsettings("HiFPTFG")+Utility.get_appsettings("HiFPTFG_noti_endpoint"), req);
            var content = await response.Content.ReadAsStringAsync();

            return content;
        }

        public static async Task<string> SendNotifyHiFPT_Dv_OwnerCam(Referal_model model, string cloudCode)
        {
            string result = string.Empty;
            string packageTypeInvited = ReferralProgramCamera.GetPackageTypeInvited(model.ObjIDDK);

            string service = "Camera";

            if (packageTypeInvited.ToUpper() == "NETONLY")
            {
                service = "Internet";
            }
            if (packageTypeInvited.ToUpper() == "COMBO")
            {
                service = "Combo Internet - Truyền hình FPT Play";
            }

            string resGT = await SendNotificationAsync(314, model.ContractGT, model.ChannelRegister, model.Fullname, service, model.InviteCode, model.FirstAccess.ToString());

            result = result + "resGT: " + resGT + "\n";

            string resDK = await SendNotificationAsync(315, model.ContractDK, model.ChannelRegister, model.FullNameDK, service, model.InviteCode, model.FirstAccess.ToString());

            result = result + "resDK: " + resDK + "\n";

            return result;
        }

        public static async Task<string> SendNotifyHiFPT_Dv(Referal_model model)
        {
            string result = string.Empty;
            string packageTypeInvited = ReferralProgramCamera.GetPackageTypeInvited(model.ObjIDDK);

            string service = "Camera";

            if (packageTypeInvited.ToUpper() == "NETONLY")
            {
                service = "Internet";
            }
            if (packageTypeInvited.ToUpper() == "COMBO")
            {
                service = "Combo Internet - Truyền hình FPT Play";
            }

            string resGT = await SendNotificationAsync(303, model.ContractGT, model.ChannelRegister, model.Fullname, service, model.InviteCode, model.FirstAccess.ToString());

            result = result + "resGT: " + resGT + "\n";

            string resDK = await SendNotificationAsync(315, model.ContractDK, model.ChannelRegister, model.FullNameDK, service, model.InviteCode, model.FirstAccess.ToString());

            result = result + "resDK: " + resDK + "\n";

            return result;
        }

        public static async Task<string> SendNotifyHiFPT_Cmr(CameraSucceedModel model, string activeDate)
        {
            string result = string.Empty;

            //packageTypeInvited = packageTypeInvited == "NETONLY" ? "Internet" : packageTypeInvited[0] + packageTypeInvited.Substring(1).ToLower();
            //int promotionalGT = packageTypeInvited == "Internet" ? 1 : 2;

            string resGT = await SendNotificationAsync(313, model.ContractGT, model.Register, model.NameGT, "Camera", model.InviteCode, activeDate);

            result = result + "resGT: " + resGT + "\n";

            string resDK = await SendNotificationAsync(316, model.ContractDK, model.Register, model.NameDK, "Camera", model.InviteCode, activeDate);

            result = result + "resDK: " + resDK + "\n";

            return result;
        }
    }
}
