using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;
using RedeemVoucher.Models;
using RedeemInviteCode.Model;
using RedeemInviteCode.Contant;
using System.Threading.Tasks;
using System.Net;
using System.IO;
using RedeemInviteCode.Model.FriendSellModel;
using System.Configuration;
using System.Xml.Linq;

namespace RedeemInviteCode.Service
{
    public class FriendSellService
    {
        private const string OS6_FPTVoucher_FriendSellEvent = "PowerInside.dbo.OS6_FPTVoucher_FriendSellEvent";
        public static async Task<Boolean> RedeemInviteCode(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, string ContractGT, string keylog)
        {

            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(input), "RedeemInviteCode DKH ");

            try
            {
                // kiểm tra nhập nhiều hơn 1 mã RP hoặc không có trong giỏ
                if (input.Any(x => x.ObjecInvite == 0) || input.Count() != 1) return false;

                // lay thong tin cua history tem
                HistoryTemInfo historyTemInfor = connection.Query<HistoryTemInfo>(ConstantAPI.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "GetVoucher_HistotyTemp",
                    OrderCode = input[0].OrderCode,
                    Code = input[0].VoucherCode
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(input), "RedeemInviteCode DKH historyTemInfor ");
                if (historyTemInfor == null) return false;

                #region Active NET hoac TV
                if (historyTemInfor.IsPrepaidNET > -1)
                {
                    var PrepaidInfo = connection.Query<Tuple<int, int, int, int, int>>(ConstantAPI.StoreName_InviteCode, new
                    {
                        ActionName = "GetPrepaidOfInviteCode",
                        OrderCode = input[0].OrderCode,
                        Code = input[0].VoucherCode,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(PrepaidInfo), "RedeemInviteCode PrepaidInfo ");
                    ///L.Mes(Level.INFOPE, keylog + " " + "PrepaidInfo " + JsonConvert.SerializeObject(PrepaidInfo));
                    //get Info promotionEvent invite and Invited
                    List<InfoRP> InviteInfo = new List<InfoRP>();


                    InviteInfo = connection.Query<InfoRP>("PowerInside.dbo.OS6_ReferralProgram_DKOLV5", new
                    {
                        InviteCode = input[0].VoucherCode,
                        ActionName = "GetFriendSellPE",
                        PackageTypeInvited = ReferalProgramHiFPT.BuildPackageType(PrepaidInfo.Item2, PrepaidInfo.Item3),
                        PaidTimeType = PrepaidInfo.Item1,
                        ObjID = input[0].ObjecInvite
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(InviteInfo), "RedeemInviteCode InviteInfo ");
                
                    //L.Mes(Level.INFOPE, JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] }));
                    //if (InviteInfo.Count() != 2) return false;

                    if (InviteInfo.Count() != 3)
                    {
                        return false;
                    }
                    else if (InviteInfo.Count() == 3)
                    {
                        L.Mes(Level.INFOPE, keylog + " " + JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] }));
                    }

                    // insert data to table InviteSuccess
                    // [1] : người được giới thiệu
                    // [2] : người giới thiệu
                    //string EventCodeLoy = ReferalProgramHiFPT.GetEventCodeLoy(input[0].Objectinvited);

                    //L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                    //                    JsonConvert.SerializeObject(EventCodeLoy), "RedeemInviteCode EventCodeLoy ");
                    int InsertInviteSuccess = connection.Query<int>(OS6_FPTVoucher_FriendSellEvent, new
                    {
                        ActionName = "InsertInfoRedeemRPCode",
                        XML = ReferalProgramHiFPT.CreateXMLRedeem(input, InviteInfo[2].EventCode, InviteInfo[1].EventCode, InviteInfo[0].EventCode)                       
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    //L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusUpdateInviteSuccess = (InsertInviteSuccess == 1) }));

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(InsertInviteSuccess), "RedeemInviteCode InsertInviteSuccess ");
                    if (InsertInviteSuccess != input.Count()) return false;

                    //if (historyTemInfor.CamQuantity.Equals(0))
                    //{
                    //    ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                    //}
                    ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                    // insert data to table generalcode
                    List<GeneralCodeInsert> stsAddGeneralCode = ReferalProgramHiFPT.InsertGeneralCodeRPgetGCID(connection, transaction, input, InviteInfo, PrepaidInfo);
                

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(stsAddGeneralCode), "RedeemInviteCode stsAddGeneralCode ");
                    if (stsAddGeneralCode.Count() != 3) return false;

                    // update Discount RpCode User Invited
                    ReferalProgramHiFPT.UpdateDiscount(
                         input[0].Objectinvited,  // objID khách hàng
                         InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                         InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                         InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                         InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                         InviteInfo[1].EventCode, // Code
                         input[0].Objectinvited, false); //Object Invite -TypeVC:True    

                    // Update Discount RpCode User Invite
                    ReferalProgramHiFPT.UpdateDiscount(
                         input[0].ObjecInvite,  // objID khách hàng
                         InviteInfo[2].NetPromotionID, //XđồngY tháng NET + Tháng
                         InviteInfo[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                         InviteInfo[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                         InviteInfo[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                         InviteInfo[2].EventCode, // Code
                         input[0].Objectinvited, false); //Object Invite -TypeVC:True    

                    //L.Mes(Level.INFO, keylog + " " + "Call Voucher Bill");
                    // add voucher bill invited
                    ReferalProgramHiFPT.UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, InviteInfo[1].EventCode, stsAddGeneralCode[1].id);
                    // add voucher bill invite
                    ReferalProgramHiFPT.UpdateOanhVK(input[0].ObjecInvite, input[0].OrderCode, InviteInfo[2].EventCode, stsAddGeneralCode[2].id);
                    L.Mes(Level.INFO, keylog + " " + "SendMess");

                    //SenNotifyDKH(ContractGT, input[0].Objectinvited, "successful_service_registration", keylog);                
                }
                #endregion

                
                
                if (historyTemInfor.CamQuantity > 0)
                {
                    #region Hop dong co CAM
                    List<HistoryTemInfoCam> cameras = connection.Query<HistoryTemInfoCam>(ConstantAPI.OS6_ReferalProgram_CAM, new
                    {
                        ActionName = "GetHistoryTemInfoCam",
                        InviteCode = input[0].VoucherCode,
                        OrderCode = input[0].OrderCode
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(cameras), "RedeemInviteCode cameras ");
                    if (cameras.Count > 2) return false;
                    List<InfoRFCAM> listRFcam = new List<InfoRFCAM>();

                    var camGT = connection.Query<InfoRFCAM>("PowerInside.dbo.OS6_ReferralProgram_DKOLV5", new
                    {
                        ActionName = "GetCameraPE",
                        InviteCode = input[0].VoucherCode,
                        Quantity = 0,
                        ServiceCode = 0,
                        ObjID = input[0].ObjecInvite
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 1);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(camGT), "RedeemInviteCode camGT ");

                    int insertTemRedeem = connection.Execute(ConstantAPI.OS6_ReferalProgram_CAM, new
                    {
                        ActionName = "AddTemRedeemInvite",
                        vdan = camGT.ID1,
                        objid = input[0].ObjecInvite,
                        voucherCode = camGT.EventCode,
                        AddBy = input[0].Objectinvited
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(insertTemRedeem), "RedeemInviteCode insertTemRedeem ");

                    if (camGT == null || camGT.QuotaGeneralCode < 1) return false;
                    listRFcam.Add(new InfoRFCAM { EventCode = camGT.EventCode, ID = camGT.ID, InviteType = camGT.InviteType, QuotaGeneralCode = camGT.QuotaGeneralCode });
                    EventCamera ec = new EventCamera();
                    foreach (var camera in cameras)
                    {
                        var camDK = connection.Query<InfoRFCAM>(ConstantAPI.OS6_ReferalProgram_CAM, new
                        {
                            ActionName = "GetPromotionEvent",
                            InviteCode = input[0].VoucherCode,
                            quantity = camera.CamQuantity,
                            serviceCode = camera.ServiceCodeCamera,
                            EfId = camera.EFcodeID
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 5);
                        if (camera.ServiceCodeCamera == 322) ec.EventCodeCamIn = camDK.EventCode;
                        if (camera.ServiceCodeCamera == 323 || camera.ServiceCodeCamera == 443) ec.EventCodeCamOut = camDK.EventCode;
                        listRFcam.Add(new InfoRFCAM { EventCode = camDK.EventCode, ID = camDK.ID, InviteType = camDK.InviteType, QuotaGeneralCode = camDK.QuotaGeneralCode });

                    }
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(listRFcam), "RedeemInviteCode listRFcam ");
                    if (ec == null) return false;

                    FoxGold foxgold = connection.Query<FoxGold>("PowerInside.dbo.OS6_ReferralProgram_DKOLV5", new
                    {
                        ActionName = "GetFoxGoldCAM"
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(foxgold), "RedeemInviteCode foxgold ");
                    int InsertInviteSuccess = connection.Query<int>(ConstantAPI.OS6_ReferalProgram_CAM, new
                    {
                        ActionName = "InsertInfoRedeemRPCodeCAM",
                        XML = CreateXMLRedeemCAM(input, ec, camGT.EventCode, foxgold.EventCode)
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(InsertInviteSuccess), "RedeemInviteCode InsertInviteSuccess ");
                    if (InsertInviteSuccess != 1) return false;

                    Boolean stsupdateCam = ReferalProgramHiFPT.UpdateHistoryTempCamera(connection, transaction, input);

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(stsupdateCam), "RedeemInviteCode stsupdateCam ");
                    if (!stsupdateCam) return false;

                    if (historyTemInfor.CamQuantity > 0 && historyTemInfor.IsPrepaidNET.Equals(-1))
                    {
                        ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                    }
                    // insert data to table generalcode
                    Boolean stsAddGeneralCode = ReferalProgramHiFPT.InsertGeneralCodeRPCAM(connection, transaction, input, listRFcam, historyTemInfor);

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(stsAddGeneralCode), "RedeemInviteCode stsAddGeneralCode ");
                    //L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusAddGeneralCode = stsAddGeneralCode }));

                    //await ReferalProgramHiFPT.SenNotify(input[0].ObjecInvite, input[0].Objectinvited, "camera_referral_code_confirm");

                    if (!stsAddGeneralCode) return false;
                    #endregion
                }
                if (historyTemInfor.CamQuantity > 0 && historyTemInfor.IsPrepaidNET >= 0)
                {
                    ReferalProgramHiFPT.UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                }
                Boolean StsUpdateHistoryTemp = ReferalProgramHiFPT.UpdateHistoryTemp(connection, transaction, input);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(StsUpdateHistoryTemp), "RedeemInviteCode StsUpdateHistoryTemp ");

                if (!StsUpdateHistoryTemp) return false;

                return true;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(ex.Message), "RedeemInviteCode StsUpdateHistoryTemp ");
                return false;
            }
        }

        public static FriendSellNotiOutputModel SenNotifyDKH(string contractGT, int ObjInvited, string type,string keylog)
        {
            FriendSellNotiOutputModel response = new FriendSellNotiOutputModel();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    if (type == "successful_service_registration")
                    {
                        var res = connection.Query<FriendSellNotiConfirmModel>(OS6_FPTVoucher_FriendSellEvent, new
                        {
                            actionName = "GetInfoNotiConfirm",
                            objid = ObjInvited
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        FriendSellConfirm dataPost = new FriendSellConfirm { contractNo = contractGT, type = type, data = res };
                        string data = JsonConvert.SerializeObject(dataPost);
                        L.Mes(Level.INFO, data, string.Concat("FriendSell  - Send HiFPT Confirm start to send ", keylog));

                        string url = string.Concat(Utility.apiHiFptcustomer.ToString(), "/hi-customer-local/inside/push-notify");
                        string responseAPI = FriendSellService.callapi_hifpt(url, "POST", data);
                        L.Mes(Level.INFO, JsonConvert.SerializeObject(responseAPI), string.Concat("FriendSell  - Send HiFPT response ", keylog));
                        response = JsonConvert.DeserializeObject<FriendSellNotiOutputModel>(responseAPI);                        
                    }
                }
            }
            catch(Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), string.Concat("FriendSell  - Send HiFPT Error ", keylog));                
            }
                
            return response;
        }
        public static string callapi_hifpt(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            headers.Add("clientKey", "referral");
            headers.Add("secretKey", "ofjhgiiedhcidkfjeudsalsodejdcfydiejd");
            var author = MD5Hash.GetMD5("referral::ofjhgiiedhcidkfjeudsalsodejdcfydiejd" + DateTime.Now.ToString("yyyy-dd-MM"));
            headers.Add("Authorization", author);
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }


        public static XElement CreateXMLRedeemCAM(List<ItemRequest> input, EventCamera ec, string GeneralCodeInvite = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited-in", ec.EventCodeCamIn),
                           new XElement("Vinvited-out", ec.EventCodeCamOut),
                           new XElement("Or", item.OrderCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite),// object của người giới thiệu
                           new XElement("RF", "DKH")
                       ));
            return xmlString;
        }
    }
}