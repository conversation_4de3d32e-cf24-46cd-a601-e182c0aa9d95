using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using Voucher.APIHelper.Log4net;
using RedeemVoucher.Models;
using RedeemInviteCode.Model;
using RedeemInviteCode.Contant;
using Newtonsoft.Json;
using System.Xml.Linq;

namespace RedeemInviteCode.Service
{
    public class MKTgeneralCode
    {
        public static Boolean isMKTgeneralCode(string voucherCode)
        {
            int flag = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        flag = connection.Query<int>(ConstantAPI.StoreName_MKTgeneralCode, new
                        {
                            actionName = "checkStatusCodeGC",
                            voucherCode = voucherCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        return (flag == 1);
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "isMKTgeneralCode");
                return false;
            }
        }

        public static int GetInputTypeObjID(int objid)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<int>(ConstantAPI.StoreName_MKTgeneralCode, new
                {
                    actionName = "GetInputType",
                    objID = objid
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static bool RedeemMKTgeneralCode(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input)
        {
            try
            {
                // kiểm tra nhập nhiều hơn 1 mã hoặc không có trong giỏ
                L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "RedeemMKTgeneralCode");
                if (input.Count() != 1) return false;
                // getInfo Prepaid of InviteCode (vì mã RP có nhiều Prepaid nên phải chọn chính xác mã voucher quy đổi nào chính xác)
                // item1 :  IDPaidTimeType
                // item2 :  IsPrepaidNET
                // Item3 :  IsPrepaidTV
                // item4 :  Base ID NET
                // item5 :  Base ID TV
                var PrepaidInfo = connection.Query<Tuple<int, int, int, int, int>>(ConstantAPI.StoreName_InviteCode, new
                {
                    ActionName = "GetPrepaidOfInviteCode",
                    OrderCode = input[0].OrderCode,
                    Code = input[0].VoucherCode,
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                //get Info promotionEvent invite and Invited
                List<InfoRP> InviteInfo = new List<InfoRP>();


                InviteInfo = connection.Query<InfoRP>(ConstantAPI.StoreName_MKTgeneralCode, new
                {
                    voucherCode = input[0].VoucherCode,
                    actionName = "GetPEREF",
                    PaidTimeTypeID = PrepaidInfo.Item1
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                if (InviteInfo.Count() == 0)
                {
                    L.Mes(Level.INFOPE, "No promotion event", "RedeemMKTgeneralCode");
                    return false;
                }

                // update History_temp
                Boolean StsUpdateHistoryTemp = UpdateHistoryTemp(connection, transaction, input);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StsUpdateHistoryTemp = StsUpdateHistoryTemp }), "RedeemMKTgeneralCode");
                if (!StsUpdateHistoryTemp) return false;


                // insert data to table generalcode
                int? generalCodeID = InsertGeneralCodeMKT(connection, transaction, input, InviteInfo, PrepaidInfo);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StatusAddGeneralCode = generalCodeID > 0 }), "add general code - RedeemMKTgeneralCode");
                if (generalCodeID == null || generalCodeID == 0) return false;

                // update Discount
                if (StsUpdateHistoryTemp && generalCodeID > 0)
                {
                    var PEInfo = InviteInfo[0];
                    UpdateDiscount(
                    input[0].Objectinvited,  // objID khách hàng
                    PEInfo.NetPromotionID, //XđồngY tháng NET + Tháng
                    PEInfo.IPTVPromotionID, //XđồngY tháng TV + Tháng
                    PEInfo.MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    PEInfo.MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    PEInfo.EventCode, // Code
                    input[0].ObjecInvite, true); //Object Invite -TypeVC:True  
                    
                    L.Mes(Level.INFO, "Call Voucher Bill");
                    // add voucher bill invited
                    UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, PEInfo.EventCode);
                    L.Mes(Level.INFO, "SendMess");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, ex.Message, "RedeemMKTgeneralCode Error");
                return false;
            }
        }
        private static XElement CreateXMLUpdateGeneralCodeRP(List<ItemRequest> input, List<InfoRP> promotionEvent, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            int PrepaidTimeTV;
            if (PrepaidInfo.Item3 > 0) PrepaidTimeTV = 1; else if (PrepaidInfo.Item3 < 0) PrepaidTimeTV = -1; else PrepaidTimeTV = 0;

            var xmlString = new XElement("N",
                            new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent[0].EventCode),
                            new XElement("P", promotionEvent[0].ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", PrepaidInfo.Item4),
                            new XElement("BTV", PrepaidInfo.Item5),
                            new XElement("IsPrepaidTV", PrepaidTimeTV),
                            new XElement("Ac", 2)));

            return xmlString;
        }
        private static int? InsertGeneralCodeMKT(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            var GeneralCodeIDs = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
            {
                ActionName = "InsertGeneralCodeXMLV2",
                XML = CreateXMLUpdateGeneralCodeRP(input, InviteInfo, PrepaidInfo),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
            return GeneralCodeIDs.FirstOrDefault();
        }
        private static string BuildPackageType(int IsPrepaidNET, int IsPrepaidTV)
        {
            if (IsPrepaidNET == -1 && IsPrepaidTV != -1) return "TVONLY";
            else if (IsPrepaidNET != -1 && IsPrepaidTV != -1) return "COMBO";
            else if (IsPrepaidNET != -1 && IsPrepaidTV == -1) return "NETONLY";
            else return "";
        }
        private static void UpdateOanhVK(int ObjID, string OrderCode, string EventCode)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, OrderCode }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@EventCode", EventCode);
                connection.Execute(ConstantAPI.spOanhVK, p, commandType: CommandType.StoredProcedure);
            }
        }

        private static XElement CreateXMLRedeem(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.OrderCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite)  // object của người giới thiệu
                       ));
            return xmlString;
        }
        private static Boolean UpdateHistoryTemp(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input)
        {
            int RowAffected = connection.Query<int>(ConstantAPI.StoreName_HistoryTemp, new
            {
                ActionName = "UpdateHistory",
                XML = CreateXMLRedeem(input),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            // check records update to tbale History_temp
            return (RowAffected == input.Count());
        }
        private static void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }

        private static void AddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            L.Mes(Level.INFO, "AddCustomerDiscountV2:" + JsonConvert.SerializeObject(new
            {
                ObjID = ObjID,
                PromotionNetID = PromotionNetID,
                IPTVPromotionID = IPTVPromotionID,
                MoneyPromotionNETID = MoneyPromotionNETID,
                MoneyPromotionTVID = MoneyPromotionTVID,
                VoucherCode = VoucherCode,
                SalesID = SalesID,
                IsEvenMonth = IsEvenMonth,
                GeneralCodeID = GeneralCodeID,
                PrivateCodeID = PrivateCodeID
            }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private static MonthPromotion GetMonthBase(int obj)
        {
            MonthPromotion res;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.QueryFirstOrDefault<MonthPromotion>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetMonthPromotion",
                    new { ObjID = obj },
                    commandType: CommandType.StoredProcedure);
            }
            return res;
        }

        private static int GetPromotionNetByContainerID(int ContainerID, int AddMonth, int IsEvenMonth = 0)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPromotionNetByContainerID",
                    new { ContainerID = ContainerID, AddMonth = AddMonth, IsEvenMonth = IsEvenMonth },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
    }
}