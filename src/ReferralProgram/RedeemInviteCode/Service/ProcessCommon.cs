using Newtonsoft.Json;
using RedeemInviteCode.Contant;
using RedeemInviteCode.Model;
using RedeemVoucher.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using Voucher.APIHelper.Log4net;
using System.Xml.Linq;
using System.Net;
using System.Configuration;
using System.Globalization;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;
using RedeemInviteCode.Model.LoyaltyModel;

namespace RedeemInviteCode.Service
{
    public class ProcessCommon
    {
        private static readonly string OS6_FPTVoucher_Lottery2021 = "PowerInside.dbo.OS6_FPTVoucher_Lottery2021";
        private static readonly string OS6_FPTVoucher_PromotionCustomer_MKT = "PowerInside.dbo.OS6_FPTVoucher_PromotionCustomer_MKT";
        private static readonly string OS6_FPTVoucher_OTTReferralProgram = "PowerInside.dbo.OS6_FPTVoucher_OTTReferralProgram";
        private static readonly string OSU6_ReferralProgram_FptPlay = "PowerInside.dbo.OSU6_ReferralProgram_FptPlay";

        #region GTBB FPTplay
        public static void SentToFPTplay()
        {
            try
            {
                List<DataActiveOTT> listData = Getdata();
                foreach (var item in listData)
                {
                    string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), ReferalProgramMyFPT.RamdoneString()); ;
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(item), keylog + " " + item.ObjIDDK + " data clientOTT: ");
                    try
                    {
                        int sourceId = int.Parse(Utility.sourceId);
                        int roleId = int.Parse(Utility.roleId);
                        var req = new AddCustomerReq
                        {
                            customerKey = item.OttClient,
                            customerName = item.FullnameGT,
                            customerPhone = item.OttClient,
                            registerDate = item.Date != null ? string.Concat(item.Date, " 00:00:00") : "",
                            sourceId = sourceId,
                            roleId = roleId,
                            //sourceId = 23,
                            //roleId = 2149,
                            LocationID = 0
                        };
                        string lucky_number = AddCustomerP5(req, keylog);
                        CallbackReferralOTT cbr = new CallbackReferralOTT();
                        cbr.additional_info = "";
                        if (string.IsNullOrEmpty(item.IPTV))
                        {
                            cbr.campaign_id = "isc_net";
                        }
                        else
                        {
                            cbr.campaign_id = "isc_net_tv";
                        }
                        cbr.transaction_id = item.ContractDK;
                        cbr.user_phone = item.Location_Phone;
                        cbr.lucky_number = lucky_number;
                        cbr.user_fullname = item.FullnameDK;
                        L.Mes(Level.INFO, JsonConvert.SerializeObject(cbr), keylog + " " + item.ObjIDDK + " AddCustomerReq clientOTT: ");

                        var url = Utility.apiOtt + "/api/v1/isc/referral/" + item.OttClient;
                        string data = JsonConvert.SerializeObject(cbr);
                        L.Mes(Level.INFO, data, keylog + " " + "SentToFPTplay request: ");
                        try {
                            string sendRequestOTT = sendSuccessToOTT(url, "POST", data);
                            L.Mes(Level.INFO, sendRequestOTT, keylog + " " + item.ObjIDDK + " SentToFPTplay res: ");
                        } catch (Exception e) {
                            L.Mes(Level.ERROR, e.Message, keylog + "SentToFPTplay active gift error: ");
                        }

                        SendRequestOTTOutput res = new SendRequestOTTOutput() { status = 1 };//JsonConvert.DeserializeObject<SendRequestOTTOutput>(sendRequestOTT);
                        int isSaveLuckyNum = AddCustomerPromotion(item.ContractDK, "FPTplay",sourceId,roleId,lucky_number);
                        L.Mes(Level.INFO, isSaveLuckyNum.ToString(), keylog + " " + item.ObjIDDK + " isSaveLuckyNum res: ");
                        if (res.status == 1)
                        {
                            int isUpdate = UpdateResponse(item.IDSuccessInvice);
                            L.Mes(Level.INFO, JsonConvert.SerializeObject(isUpdate), keylog + " " + item.ObjIDDK + " update Successinvite: ");
                            MaskSentmail(item.ID);

                            if (!string.IsNullOrEmpty(item.EmailGT))
                            {
                                var template = ProcessReferalProgram.GetTemplateMail("FPTPLAY");
                                var subjectMail = template.Subject;
                                var body = template.Body.Replace("{0}", item.FullnameGT).Replace("{1}", item.FullnameDK)
                                    .Replace("{2}", item.InviteCode).Replace("{3}", item.ContractDK)
                                    .Replace("{4}", item.FirstAccess).Replace("{5}", item.FullnameDK)
                                    .Replace("{6}", Convert.ToInt32(item.RewardDK).ToString("#,###", CultureInfo.GetCultureInfo("vi-VN").NumberFormat))
                                    .Replace("{7}", item.RewardGT);
                                
                                var requestSendMail = new MailModelInput
                                {
                                    FromEmail = "<EMAIL>",
                                    Recipients = item.EmailGT,
                                    CarbonCopys = "",
                                    BlindCarbonCopys = "",
                                    Subject = subjectMail,
                                    Body = body,
                                    AttachFile = "",
                                    AttachUrl = ""
                                };
                                L.Mes(Level.INFO, JsonConvert.SerializeObject(requestSendMail), keylog + " " + item.ObjIDDK + "Request Send Mail: ");

                                var responseSendMail = ProcessReferalProgram.SendMail(requestSendMail);
                                L.Mes(Level.INFO, JsonConvert.SerializeObject(responseSendMail), keylog + " " + item.ObjIDDK + "Response Send Mail: ");
                            }

                            if (!string.IsNullOrEmpty(item.PhoneGT))
                            {
                                var smsResults = new List<SmsResult>();
                                SmsResult model = ProcessReferalProgram.Login(SmsConfig.username,SmsConfig.password);

                                if (model != null)
                                {
                                    const string templateMsg = "Chuc mung ban da nhan duoc uu dai dac biet tu chuong trinh Gioi thieu ban be tren FPT Play! Chi tiet xem tai: https://tinyurl.com/2hzfmb2v";

                                    var smsData = new Smsdata();
                                    smsData.Message = templateMsg;
                                    smsData.PhoneNumber = item.PhoneGT;
                                    
                                    var paramApi = AesCrypt.GetAesCrypt(JsonConvert.SerializeObject(smsData), SmsConfig.password);
                                    // Convert aesScript to base64string
                                    string base64String = Convert.ToBase64String(paramApi);
                                    // call api send sms
                                    var uri = HelperConfigs.url_apisms + SmsConfig.action_Sendsms;
                                    var modelApi = new { data = base64String };
                                    string sendSmsStatus = IHttpRequest.callapi_smsworld(uri, ApiMethod.Post, JsonConvert.SerializeObject(modelApi), "application/json; charset=utf-8", model.Detail.AccessToken);
                                    //string rs = JsonConvert.SerializeObject(loginResult);
                                    var jsonapi = JObject.Parse(sendSmsStatus);
                                    if (jsonapi != null)
                                    {
                                        var result = jsonapi.ToObject<SmsResult>();
                                        smsResults.Add(result);
                                    }
                                }
                            }
                        }                        
                    }
                    catch (Exception ex)
                    {
                        L.Mes(Level.ERROR, ex.ToString(), "SentToFPTplay clientOTT: " + JsonConvert.SerializeObject(item));
                    }                    
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.ToString(), "SentToFPTplay");
            }            
        }
        public static string GetInforClient(string clientID, string contentType = "application/json; charset=utf-8" )
        {
            try
            {
                var url = Utility.apiOtt + "/api/v1/isc/user/" + clientID;
                HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(url);
                var headers = wr.Headers;
                headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
                wr.Method = "GET";
                // timeout
                wr.Timeout = 10000;
                var httpResponse = (HttpWebResponse)wr.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    var rs = streamReader.ReadToEnd();

                    // log
                    L.Mes(Level.INFO, "OTT - checkOTTCode: " + rs);

                    //OTTReferalProgram ott = new OTTReferalProgram { data = null, message = "Fail", status = 0 };
                    var ott = JsonConvert.DeserializeObject<OTTUserOutput>(rs);
                    if (ott.status == 1)
                    {
                        if (string.IsNullOrEmpty(ott.data.fullname))
                        {
                            return "";
                        }
                        L.Mes(Level.ERROR, clientID, "GetInforClient clientOTT: " + JsonConvert.SerializeObject(ott));
                        return ott.data.fullname;
                    }
                    else return "";
                }
            }
            catch (Exception e)
            {
                L.Mes(Level.ERROR, e.ToString(), "GetInforClient clientOTT: " + JsonConvert.SerializeObject(clientID));
                return "";
            }
        }
        private static string AddCustomerP5(AddCustomerReq req, string keylog)
        {
            try
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(req), keylog + " req AddCustomerP5 clientOTT: ");
                var url = PromotionConfig.Url + "/api/promotion/addCustomer";
                var data = JsonConvert.SerializeObject(req);
                string res = IHttpRequest.callapi_CR(url, "POST", data, "application/json; charset=utf-8");
                L.Mes(Level.INFO, JsonConvert.SerializeObject(res), keylog + " AddCustomerP5 clientOTT: ");
                addCustomerModel resModel = new addCustomerModel()
                {
                    hasError = true,
                    data = new DataResModel()
                };
                resModel = JsonConvert.DeserializeObject<addCustomerModel>(res);

                if (!resModel.hasError)
                {
                    return resModel.data.resultLottery[0];
                }
                return "";
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), keylog + " AddCustomerP5 clientOTT: ");
                return "";
            }
            
        }
        public static int AddCustomerPromotion(string key, string clientType, int sourceId, int roleId, string Code, string note = null, string description = null)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                return connection.Execute(
                    OS6_FPTVoucher_PromotionCustomer_MKT,
                    new { actionName = "AddPromotionCustomer", @key = key, @clientType = clientType, @prizeCode = Code, @sourceId = sourceId, @roleId = roleId },
                    commandTimeout: null, commandType: CommandType.StoredProcedure);
            };
        }
        public static List<DataActiveOTT> Getdata()
        {
            var data = new List<DataActiveOTT>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    data = connection.Query<DataActiveOTT>(OSU6_ReferralProgram_FptPlay, new { actionName = "GetDataJob" }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                    return data;
                }
            }
        }
        private static string sendSuccessToOTT(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                string rs = streamReader.ReadToEnd();
                L.Mes(Level.INFO, "sendSuccessToOTT: " + JsonConvert.SerializeObject(rs));
                return rs;
            }
        }
        private static int UpdateResponse(int id)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Execute(OS6_FPTVoucher_OTTReferralProgram,  new { actionName = "UpdateSuccessInvite",id=id }, commandTimeout: null, commandType: CommandType.StoredProcedure);
            } 
        }
        public static void MaskSentmail(int ID)
        {
            using (IDbConnection connect = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var sp = StoreProcedure.Schema + StoreProcedure.OS6_ReferralProgram_MarkSentMail;
                connect.Execute(sp, new { ID }, commandType: CommandType.StoredProcedure);
            }
        }
        #endregion 

        #region Rinh Van Qua Tet
        public static void WriteToLog(object input, string mes, Guid logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(input), mes);
        }
        public static async Task CallSpringLottery()
        {
            Guid logid = Guid.NewGuid();
            try
            {
                Task taskCallSpringLottery = new Task
                (
                    (object ob) =>
                    {
                        CallSpringLottery(logid.ToString());
                    }, "CallSpringLottery"
                );
                taskCallSpringLottery.Start();
                await taskCallSpringLottery;
            }
            catch (Exception ex)
            {
                WriteToLog(ex.Message, "taskCallSpringLottery error", logid);
            }
        }
        public static void CallSpringLottery(string keylog)
        {
            L.Mes(Level.INFO, string.Concat(keylog, " CallSpringLottery"));
            try
            {
                string timeHours = DateTime.Now.ToString("HH");
                int hour = int.Parse(timeHours);
                if (hour >= 0)
                {
                    
                    using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                    {
                        connection.Open();
                        List<ObjectInfor> listObj = connection.Query<ObjectInfor>(OS6_FPTVoucher_Lottery2021, new { actionName = "GetInforObject" }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        if (listObj.Count() == 0)
                        {
                            return;
                        }
                        L.Mes(Level.INFO, string.Concat(keylog, " CallSpringLottery bat dau for"));
                        foreach (var obj in listObj)
                        {
                            Guid logId = Guid.NewGuid();
                            WriteToLog(obj, "taskCallSpringLottery start ", logId);
                            try
                            {
                                int roleid = 2155;//GetRoleID(obj.Id, keylog);
                                if (roleid > 0)
                                {
                                    var req = new AddCustomerReq
                                    {
                                        customerKey = obj.Contract,
                                        customerName = obj.FullName,
                                        customerPhone = obj.Location_Phone,
                                        registerDate = obj.Date,
                                        sourceId = 27,
                                        roleId = roleid,
                                        LocationID = obj.LocationID
                                    };
                                    WriteToLog(req, "taskCallSpringLottery AddCustomerReq ", logId);
                                    List<string> SpringLottery = AddCustomer(req);
                                    WriteToLog(SpringLottery, "taskCallSpringLottery SpringLottery ", logId);
                                    // gui SMS va mail
                                    if (SpringLottery.Count > 0)
                                    {
                                        string Lotteries = string.Join(",", SpringLottery);
                                        #region mail
                                        /*
                                        try                                        
                                        {
                                            if (string.IsNullOrEmpty(obj.Email))
                                            {
                                                L.Mes(Level.INFO, string.Concat(keylog, " Email empty: " + obj.Id));
                                            }
                                            else
                                            {
                                                string readFile = File.ReadAllText(System.Web.Hosting.HostingEnvironment.MapPath(@"~/Template/mailTemplate.txt"));
                                                string contentMail = readFile.Replace("{0}", SpringLottery.Count().ToString()).Replace("{1}", Lotteries).Replace("{2}", obj.FullName);
                                                string mailLog = Sendmail("<EMAIL>", MailConfig.subject_mail_spring, obj.Email, keylog, Lotteries, contentMail);
                                            }                                            
                                        }
                                        catch (Exception e) { L.Mes(Level.INFO, keylog + " Sendmail to " + obj.Email.ToString() + " " + e.Message.ToString()); }
                                         */
                                        #endregion
                                        //sms                                        
                                        try
                                        {
                                            if (string.IsNullOrEmpty(obj.Location_Phone))
                                            {
                                                WriteToLog("", "taskCallSpringLottery Location_Phone empty: ", logId);
                                            }
                                            else
                                            {
                                                string contentSMS = string.Format(SmsMKTConfig.content_sms_spring_lottery, Lotteries);
                                                var sms = SendSms(obj.Location_Phone, contentSMS);
                                                WriteToLog(sms, "taskCallSpringLottery SendSms to: " + obj.Location_Phone, logId);
                                            }
                                        }
                                        catch (Exception e) { WriteToLog(e.Message, "taskCallSpringLottery SendSms to: " + obj.Location_Phone + " Error ", logId); }

                                        //Insert Obj Indexing
                                        try
                                        {
                                            int isInsert = InsertObjIndexing(obj.Id, Lotteries, logId.ToString());
                                            WriteToLog(isInsert, "taskCallSpringLottery InsertObjIndexing to: " + obj.Id + " value code ", logId);
                                        }
                                        catch (Exception e) { WriteToLog(e.Message, "taskCallSpringLottery InsertObjIndexing to: " + obj.Id + " Error ", logId); }
                                    }
                                }
                                else
                                {
                                    int isInsert = InsertObjIndexing(obj.Id, "0", keylog);
                                    WriteToLog(isInsert, "taskCallSpringLottery InsertObjIndexing to: " + obj.Id + " value 0 ", logId);
                                }
                            }
                            catch (Exception e)
                            {
                                WriteToLog(e.Message, "taskCallSpringLottery to: " + obj.Id + " Error ", logId);
                            }
                        }
                        L.Mes(Level.INFO, string.Concat(keylog, " CallSpringLottery ket thuc for"));
                    }
                }
                else
                {
                    L.Mes(Level.INFO,"CallSpringLottery " + string.Concat(keylog, " ", " Ngoai gio hanh chinh"));
                    return;
                }                
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.ToString(),keylog + " CallSpringLottery: ");                
            }            
        }
        private static int InsertObjIndexing(int  objId, string lotery, string keylog)
        {
            int res = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    res = connection.Execute(
                        OS6_FPTVoucher_Lottery2021,
                        new { actionName = "InsertObjIndexing", objID = objId, lottery = lotery },
                        commandTimeout: null, commandType: CommandType.StoredProcedure);
                };
                L.Mes(Level.INFO, res.ToString(), keylog + " InsertObjIndexing: " + objId.ToString() + " " );  
                return res;
            }
            catch (Exception e)
            {
                L.Mes(Level.ERROR, e.ToString(), keylog + " InsertObjIndexing: " + objId.ToString() + " ");  
                return 0;
            }
        }
        private static int GetRoleID(int objId,string keylog)
        {
            int res = 0;
            int isCMRobj = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    isCMRobj = connection.Query<int>(
                        OS6_FPTVoucher_Lottery2021,
                        new { actionName = "CheckObjCMR", objID = objId },
                        commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                };
                if (isCMRobj == 1)
                {
                    L.Mes(Level.INFO, "2153", keylog + " GetRoleID: CMR " + objId.ToString() + " ");
                    return 2153;
                }
                Prepaid pr = new Prepaid();
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    pr = connection.Query<Prepaid>(
                        OS6_FPTVoucher_Lottery2021,
                        new { actionName = "CheckPrepaid", objID = objId},
                        commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                };
                // các trường hợp nhận 1 mã dự thưởng
                if (pr.PrepaidNET == 6 && pr.PrepaidTV == -1 )
                {
                    res = 2152;
                }
                else // các trường hợp nhận 2 mã dự thưởng
                    if ((pr.PrepaidNET == 12 && pr.PrepaidTV == -1) || (pr.PrepaidNET == 6 && pr.PrepaidTV == 6) || (pr.PrepaidNET > 6 && pr.PrepaidNET < 12 && pr.PrepaidTV < 6 && pr.PrepaidTV >-1))
                {
                    res = 2153;
                }
                else // các trường hợp nhận 4 mã dự thưởng                
                if ((pr.PrepaidNET == 12 && pr.PrepaidTV == 12))
                {
                    res = 2154;
                }
                L.Mes(Level.INFO, res.ToString(), keylog + " GetRoleID: " + objId.ToString() + " ");
                return res;
            }
            catch (Exception e)
            {
                L.Mes(Level.ERROR, e.ToString(), keylog + " GetRoleID: " + objId.ToString() + " ");
                return 0;
            }
        }
        public static string Sendmail(string from_email, string subject, string to_email, string keylog, string lotery, string content)
        {
            L.Mes(Level.INFO,lotery + " Start to sendmail to " + to_email );            
            string MailLog = Helper.HttpRequest(MailConfig.url_sendmail, MailConfig.url_action, ApiMethod.Post
            , JsonConvert.SerializeObject(new
            {
                ID = "",
                FromEmail = from_email,
                FromMailAlias = "",
                Recipients = to_email,
                CarbonCopys = "",
                BlindCarbonCopys = "",
                Subject = subject,
                Body = content,
                AttachFile = "",
                IsBodyHtml = "1",
                StoreProcedure = "",
                Resource = "",
                LinkID = "",
                CreateDate = "",
                UpdateDate = "",
                Status = "",
                ResultID = "",
                ResultDesc = "",
                TemplateName = "",
                AttachUrl = "",
                NameFileAttach = "",
            }));
            L.Mes(Level.INFO, keylog + " Start to sendmail to " + to_email + " Status:" + MailLog, "Sendmail");
            return MailLog;
        }
        private static SmsResult Login()
        {
            var model = new SmsResult();
            var url = HelperConfigs.url_apisms + SmsConfig.action_Login;
            var model_api = new { UserName = SmsMKTConfig.username, Password = SmsMKTConfig.password };
            string loginResult = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8");
            //string rs = JsonConvert.SerializeObject(loginResult);
            var jsonapi = JObject.Parse(loginResult);
            if (jsonapi != null)
            {
                model = jsonapi.ToObject<SmsResult>();
            }
            return model;
        }
        public static List<SmsResult> SendSms(string phone, string content)
        {
            var rs_api = new List<SmsResult>();
            var model = new SmsResult();
            try { model = Login(); }
            catch { }
            if (model != null)
            {
                var rs_sendsms = new SmsResult();                
                var sms_md = new Smsdata();
                sms_md.Message = content;
                sms_md.PhoneNumber = phone;

                var param_api = AesCrypt.GetAesCrypt(JsonConvert.SerializeObject(sms_md), SmsMKTConfig.password);
                
                string base64string = System.Convert.ToBase64String(param_api);
                // call api send sms
                var url = HelperConfigs.url_apisms + SmsConfig.action_Sendsms;
                var model_api = new { data = base64string };
                string sendsms_status = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8", model.Detail.AccessToken);
                
                var jsonapi = JObject.Parse(sendsms_status);
                if (jsonapi != null)
                {
                    rs_sendsms = jsonapi.ToObject<SmsResult>();
                    rs_api.Add(rs_sendsms);
                } 
            }
            return rs_api;
        }
        private static List<string> AddCustomer(AddCustomerReq req)
        {
            var url = PromotionConfig.Url + "/api/promotion/addCustomer";
            var data = JsonConvert.SerializeObject(req);
            string res = IHttpRequest.callapi_CR(url, "POST", data, "application/json; charset=utf-8");
            addCustomerModel resModel = new addCustomerModel()
            {
                hasError = true,
                data = new DataResModel()
            };
            resModel = JsonConvert.DeserializeObject<addCustomerModel>(res);
            if (!resModel.hasError)
            {
                return resModel.data.resultLottery;
            }
            return null;
        }

        #endregion
        
    }
}
