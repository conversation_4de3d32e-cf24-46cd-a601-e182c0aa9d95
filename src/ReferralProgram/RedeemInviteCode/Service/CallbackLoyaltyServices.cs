using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Dapper;
using System.Data;
using Voucher.APIHelper;
using System.Data.SqlClient;
using System.Net;
using System.IO;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Threading.Tasks;
using RedeemInviteCode.Model;

namespace RedeemInviteCode.Service
{
    public class CallbackLoyaltyServices
    {
        private const string OS6_Loyalty_CallbackRF = "PowerInside.dbo.OS6_Loyalty_CallbackRF";
        public static int AddRequestRF(int objidgt, int objiddk,string req,string typeRF,string endpoint,bool isSuccess)
        {
            var pars = new DynamicParameters();
            pars.AddDynamicParams(new
            {
                actionName ="addreq",
                @req = req,
                @objidgt = objidgt,
                @objiddk = objiddk,
                @typeRF=typeRF,
                @endpoint = endpoint,
                @isSuccess= isSuccess,
            });
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                return connection.Query<int>(OS6_Loyalty_CallbackRF, pars, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static int AddReponseRF(string res, int idreq)
        {
            var pars = new DynamicParameters();
            pars.AddDynamicParams(new
            {
                actionName = "addres",
                res = res,
                idreq = idreq
            });
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                return connection.Query<int>(OS6_Loyalty_CallbackRF, pars, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }   
        }
        public static void UpdateEx(string ex, int idRes, int idReq)
        {
            var pars = new DynamicParameters();
            pars.AddDynamicParams(new
            {
                actionName = "updateEx",
                ex = ex,
                idres = idRes,
                idreq = idReq
            });
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                connection.Execute(OS6_Loyalty_CallbackRF, pars, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }
        public static void UpdateSuccess(int idreq)
        {
            var pars = new DynamicParameters();
            pars.AddDynamicParams(new
            {
                actionName = "updateSuccess",
                idreq =idreq
            });
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                connection.Execute(OS6_Loyalty_CallbackRF, pars, commandTimeout: null, commandType: CommandType.StoredProcedure);
            }
        }
        public static List<CallbackModel> GetDataCallBack()
        {
            var pars = new DynamicParameters();
            pars.AddDynamicParams(new
            {
                actionName = "getDataCallBack",
            });
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<CallbackModel>(OS6_Loyalty_CallbackRF, pars, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
        }
        public static async Task ProcessCallBackRF()
        {
            try
            {
                Task taskCallBackRF = new Task
                (
                    (object ob) =>
                    {
                        CallBackRF();
                    }, "taskCallBackRF"
                );
                taskCallBackRF.Start();
                await taskCallBackRF;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", "", DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "CallBackRF -eror: ");
            }
        }
        public static void CallBackRF()
        {
            try
            {
                var lstData = GetDataCallBack();
                if (lstData.Count == 0) return;
                foreach (var item in lstData)
                {
                    var logid = Guid.NewGuid();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(item), "CallBackRF - for: ");
                    string resAPI = callapi_loyV2(logid.ToString(), item.id, item.endpoint, "POST", item.request);
                    if (!string.IsNullOrEmpty(resAPI))
                    {
                        LoyaltyCallbackModel lc = new LoyaltyCallbackModel();
                        lc = JsonConvert.DeserializeObject<LoyaltyCallbackModel>(resAPI);
                        if (lc.status.ToUpper().Equals("SUCCESS"))
                        {
                            UpdateSuccess(item.id);
                        }
                    }                    
                }
            }
            catch (Exception)
            {
                
                throw;
            }
        }


        public static string callapi_loyV2(string logId, int idreq,string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            int idres = 0;
            try
            {
                string token = ProcessReferalProgram.getBaseTokenLoyalty();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(token), "ProcessActiveVoucher - callapi_loy token: ");   
                HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
                wr.Headers.Add("Authorization", "Bearer " + token);
                wr.Method = _method;
                if (_method.ToUpper().Equals("POST"))
                {
                    //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                    wr.ContentType = contentType;
                    // Set the data to send.
                    using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                    {
                        streamWriter.Write(_data);
                    }
                }
                var httpResponse = (HttpWebResponse)wr.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    var rs = streamReader.ReadToEnd();
                    idres = AddReponseRF(rs,idreq);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(rs), "ProcessActiveVoucher - callapi_loy rs: ");
                    return rs;
                }
            }
            catch (Exception ex)
            {
                UpdateEx(ex.Message, idres, idreq);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "ProcessActiveVoucher - callapi_loy error: ");
                return "";
            }
        }

        public static string callapi_loeror(string logId, int idreq, string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            int idres = 0;
            try
            {
                string rs = "{\"status\":\"FAIL\",\"message\":\"Không thành công\"}";
                idres = AddReponseRF(rs, idreq);
                return rs;
            }
            catch (Exception ex)
            {
                UpdateEx(ex.Message, idres, idreq);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "ProcessActiveVoucher - callapi_loy error: ");
                return "";
            }
        }
    }
}