using RedeemInviteCode.Contant;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Dapper;
using Voucher.APIHelper;
using System.Data;
using System.Threading.Tasks;
using RedeemVoucher.Models;
using RedeemInviteCode.Model;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Xml.Linq;
using System.Configuration;
using System.Net.Http;
using System.Text;
using Voucher.APIHelper.Util;

namespace RedeemInviteCode.Service
{
    public class ReferalProgramHiFPT
    {
        public static string GetEventCodeLoy(int objInvited)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<string>(ConstantAPI.StoreName_InviteSuccess, new
                {
                    ActionName = "GetEventCodeLoy",
                    ObjIDInvited = objInvited
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static async Task<Boolean> RedeemInviteCode(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, string keylog)
        {
            
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(input), "RedeemInviteCode resLoyAPI GT ");
            bool rfBank = input[0].VoucherCode.StartsWith("MSB") && input[0].VoucherCode.EndsWith("FPT") ? true : false;
            // kiểm tra nhập nhiều hơn 1 mã RP hoặc không có trong giỏ
            if (input.Any(x => x.ObjecInvite == 0) || input.Count() != 1) return false;

            // lay thong tin cua history tem
            HistoryTemInfo historyTemInfor = connection.Query<HistoryTemInfo>(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "GetVoucher_HistotyTemp",
                OrderCode = input[0].OrderCode,
                Code = input[0].VoucherCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            if (historyTemInfor == null) return false;
            
            #region Active NET hoac TV            
            if (historyTemInfor.IsPrepaidNET > -1)
            {
                var PrepaidInfo = connection.Query<Tuple<int, int, int, int, int>>(ConstantAPI.StoreName_InviteCode, new
                {
                    ActionName = "GetPrepaidOfInviteCode",
                    OrderCode = input[0].OrderCode,
                    Code = input[0].VoucherCode,
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(PrepaidInfo), "RedeemInviteCode PrepaidInfo ");
                ///L.Mes(Level.INFOPE, keylog + " " + "PrepaidInfo " + JsonConvert.SerializeObject(PrepaidInfo));
                //get Info promotionEvent invite and Invited
                List<InfoRP> InviteInfo = new List<InfoRP>();
                if (rfBank)
                {
                    #region GTBB MSBFPT
                    Boolean UpdateHistoryTempMSB = UpdateHistoryTemp(connection, transaction, input);
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StsUpdateHistoryTemp = UpdateHistoryTempMSB }), "UpdateHistoryTemp");
                    if (!UpdateHistoryTempMSB) return false;
                    InviteInfo = connection.Query<InfoRP>(ConstantAPI.StoreName_InviteCode, new
                    {
                        InviteCode = input[0].VoucherCode,
                        ActionName = "GetPEREF_MSB",
                        //PackageTypeInvited = BuildPackageType(PrepaidInfo.Item2, PrepaidInfo.Item3),
                        PaidTimeType = PrepaidInfo.Item1,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                    if (InviteInfo.Count == 0) return false;
                    // các trường hợp MGT của ngân hàng thì chỉ lấy infor đơn vị sử dụng MGT và insert vào bàng general Code
                    int InsertGCSuccess = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_ReferalProgram_for_Bank_MSB", new
                    {
                        ActionName = "InsertGeneralCode",
                        objID = input[0].Objectinvited,
                        voucherCode = input[0].VoucherCode,
                        code = InviteInfo[0].EventCode,
                        promotionEventID = InviteInfo[0].ID
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (InsertGCSuccess > 0)
                    {
                        UpdateDiscount(
                        input[0].Objectinvited,  // objID khách hàng sử dụng MGT
                        InviteInfo[0].NetPromotionID, //XđồngY tháng NET + Tháng
                        InviteInfo[0].IPTVPromotionID, //XđồngY tháng TV + Tháng
                        InviteInfo[0].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                        InviteInfo[0].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                        InviteInfo[0].EventCode, // Code
                        input[0].ObjecInvite, false); //Object Invite -TypeVC:True    
                        UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, InviteInfo[0].EventCode, InsertGCSuccess);
                        return true;
                    }
                    else return false;
                    #endregion
                }
                else
                {
                    InviteInfo = connection.Query<InfoRP>("PowerInside.dbo.OS6_ReferralProgram_DKOLV5", new
                    {
                        InviteCode = input[0].VoucherCode,
                        ActionName = "GetReferralPE",
                        PackageTypeInvited = BuildPackageType(PrepaidInfo.Item2, PrepaidInfo.Item3),
                        PaidTimeType = PrepaidInfo.Item1,
                        ObjID = input[0].ObjecInvite
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
                    
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(InviteInfo), "RedeemInviteCode InviteInfo ");
                }
                //L.Mes(Level.INFOPE, JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] }));
                //if (InviteInfo.Count() != 2) return false;

                if (InviteInfo.Count() != 3)
                {
                    return false;
                }
                else if (InviteInfo.Count() == 3) 
                {
                    L.Mes(Level.INFOPE, keylog + " " + JsonConvert.SerializeObject(new { INVITED = InviteInfo[0], INVITE = InviteInfo[1] })); 
                }

                // insert data to table InviteSuccess
                // [1] : người được giới thiệu
                // [2] : người giới thiệu
                //string EventCodeLoy = ReferalProgramHiFPT.GetEventCodeLoy(input[0].Objectinvited);
                
                //L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                //                    JsonConvert.SerializeObject(EventCodeLoy), "RedeemInviteCode EventCodeLoy ");
                int InsertInviteSuccess = connection.Query<int>(ConstantAPI.StoreName_InviteSuccess, new
                {
                    ActionName = "InsertInfoRedeemRPCode",
                    XML = CreateXMLRedeem(input, InviteInfo[2].EventCode, InviteInfo[1].EventCode, InviteInfo[0].EventCode),
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                //L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusUpdateInviteSuccess = (InsertInviteSuccess == 1) }));
                
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(InsertInviteSuccess), "RedeemInviteCode InsertInviteSuccess ");
                if (InsertInviteSuccess != input.Count()) return false;                

                if(historyTemInfor.CamQuantity.Equals(0))
                {
                    UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                }
                // insert data to table generalcode
                List<GeneralCodeInsert> stsAddGeneralCode = InsertGeneralCodeRPgetGCID(connection, transaction, input, InviteInfo, PrepaidInfo);
                //Boolean stsAddGeneralCode = InsertGeneralCodeRP(connection, transaction, input, InviteInfo, PrepaidInfo);
                ///L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusAddGeneralCode = stsAddGeneralCode }));
                
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(stsAddGeneralCode), "RedeemInviteCode stsAddGeneralCode ");
                if (stsAddGeneralCode.Count() !=3) return false;

                // update Discount RpCode User Invited
                UpdateDiscount(
                     input[0].Objectinvited,  // objID khách hàng
                     InviteInfo[1].NetPromotionID, //XđồngY tháng NET + Tháng
                     InviteInfo[1].IPTVPromotionID, //XđồngY tháng TV + Tháng
                     InviteInfo[1].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                     InviteInfo[1].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                     InviteInfo[1].EventCode, // Code
                     input[0].Objectinvited, false); //Object Invite -TypeVC:True    

                // Update Discount RpCode User Invite
                UpdateDiscount(
                     input[0].ObjecInvite,  // objID khách hàng
                     InviteInfo[2].NetPromotionID, //XđồngY tháng NET + Tháng
                     InviteInfo[2].IPTVPromotionID, //XđồngY tháng TV + Tháng
                     InviteInfo[2].MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                     InviteInfo[2].MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                     InviteInfo[2].EventCode, // Code
                     input[0].Objectinvited, false); //Object Invite -TypeVC:True    
                
                //L.Mes(Level.INFO, keylog + " " + "Call Voucher Bill");
                // add voucher bill invited
                UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode, InviteInfo[1].EventCode, stsAddGeneralCode[1].id);
                // add voucher bill invite
                UpdateOanhVK(input[0].ObjecInvite, input[0].OrderCode, InviteInfo[2].EventCode, stsAddGeneralCode[2].id);
                L.Mes(Level.INFO, keylog + " " + "SendMess");
                //await SenNotify(input[0].ObjecInvite, input[0].Objectinvited, "referral_code_confirm");
            }
            #endregion
            
            #region Hop dong co CAM
            if (historyTemInfor.CamQuantity > 0)
            {
                List<HistoryTemInfoCam> cameras = connection.Query<HistoryTemInfoCam>(ConstantAPI.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "GetHistoryTemInfoCam",
                    InviteCode = input[0].VoucherCode,
                    OrderCode = input[0].OrderCode
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(cameras), "RedeemInviteCode cameras ");
                if (cameras.Count > 2) return false;
                List<InfoRFCAM> listRFcam = new List<InfoRFCAM>();

                var camGT = connection.Query<InfoRFCAM>("PowerInside.dbo.OS6_ReferralProgram_DKOLV5", new
                {
                    ActionName = "GetCameraPE",
                    InviteCode = input[0].VoucherCode,
                    Quantity = 0,
                    ServiceCode = 0,
                    ObjID = input[0].ObjecInvite
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 1);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(camGT), "RedeemInviteCode camGT ");

                int insertTemRedeem = connection.Execute(ConstantAPI.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "AddTemRedeemInvite",
                    vdan = camGT.ID1,
                    objid = input[0].ObjecInvite,
                    voucherCode = camGT.EventCode,
                    AddBy = input[0].Objectinvited
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(insertTemRedeem), "RedeemInviteCode insertTemRedeem ");

                if (camGT == null || camGT.QuotaGeneralCode <1) return false;
                listRFcam.Add(new InfoRFCAM { EventCode = camGT.EventCode, ID = camGT.ID, InviteType = camGT.InviteType, QuotaGeneralCode = camGT.QuotaGeneralCode });
                EventCamera ec = new EventCamera();
                foreach(var camera in cameras)
                {                    
                    var camDK = connection.Query<InfoRFCAM>(ConstantAPI.OS6_ReferalProgram_CAM, new
                    {
                        ActionName = "GetPromotionEvent",
                        InviteCode = input[0].VoucherCode,
                        quantity = camera.CamQuantity,
                        serviceCode = camera.ServiceCodeCamera,
                        EfId = camera.EFcodeID
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault(x => x.InviteType == 5);                    
                    if(camera.ServiceCodeCamera==322) ec.EventCodeCamIn = camDK.EventCode;
                    if(camera.ServiceCodeCamera==323 || camera.ServiceCodeCamera==443 ) ec.EventCodeCamOut = camDK.EventCode;
                    listRFcam.Add(new InfoRFCAM { EventCode = camDK.EventCode, ID = camDK.ID, InviteType = camDK.InviteType, QuotaGeneralCode = camDK.QuotaGeneralCode });
                    
                }
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(listRFcam), "RedeemInviteCode listRFcam ");
                if (ec == null) return false;

                FoxGold foxgold = connection.Query<FoxGold>("PowerInside.dbo.OS6_ReferralProgram_DKOLV5", new
                {
                    ActionName = "GetFoxGoldCAM"
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(foxgold), "RedeemInviteCode foxgold ");

                int InsertInviteSuccess = connection.Query<int>(ConstantAPI.OS6_ReferalProgram_CAM, new
                {
                    ActionName = "InsertInfoRedeemRPCodeCAM",
                    XML = CreateXMLRedeemCAM(input, ec, camGT.EventCode, foxgold.EventCode)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(InsertInviteSuccess), "RedeemInviteCode InsertInviteSuccess ");
                if (InsertInviteSuccess != 1) return false;

                Boolean stsupdateCam = UpdateHistoryTempCamera(connection, transaction, input);
                
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(stsupdateCam), "RedeemInviteCode stsupdateCam ");
                if (!stsupdateCam) return false;                

                if (historyTemInfor.CamQuantity > 0 && historyTemInfor.IsPrepaidNET.Equals(-1))
                {
                    UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
                }
                // insert data to table generalcode
                Boolean stsAddGeneralCode = InsertGeneralCodeRPCAM(connection, transaction, input, listRFcam, historyTemInfor);
                
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(stsAddGeneralCode), "RedeemInviteCode stsAddGeneralCode ");
                //L.Mes(Level.INFO, keylog + " " + JsonConvert.SerializeObject(new { StatusAddGeneralCode = stsAddGeneralCode }));

                //await SenNotify(input[0].ObjecInvite, input[0].Objectinvited, "camera_referral_code_confirm");

                if (!stsAddGeneralCode) return false;
            }
            #endregion
            if (historyTemInfor.CamQuantity > 0 && historyTemInfor.IsPrepaidNET >= 0)
            {
                UpdateStatusRPCode(connection, transaction, input[0].VoucherCode);
            }
            Boolean StsUpdateHistoryTemp = UpdateHistoryTemp(connection, transaction, input);
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(StsUpdateHistoryTemp), "RedeemInviteCode StsUpdateHistoryTemp ");

            if (!StsUpdateHistoryTemp) return false;            

            return true;
        }
        public static XElement CreateXMLUpdateGeneralCodeRPbyGC(List<ItemRequest> input, InfoRP promotionEvent, Tuple<int, int, int, int, int> PrepaidInfo, int i)
        {
            int PrepaidTimeTV;
            if (PrepaidInfo.Item3 > 0) PrepaidTimeTV = 1; else if (PrepaidInfo.Item3 < 0) PrepaidTimeTV = -1; else PrepaidTimeTV = 0;
            if (i.Equals(0))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // Mã loy cho người giới thiệu status = 100
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));
                return xmlString;
            }
            if (i.Equals(1))
            {
                var xmlString = new XElement("N",
                            new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent.EventCode),
                            new XElement("P", promotionEvent.ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", PrepaidInfo.Item4),
                            new XElement("BTV", PrepaidInfo.Item5),
                            new XElement("IsPrepaidTV", PrepaidTimeTV),
                            new XElement("Ac", 111)));
                return xmlString;
            }
            if (i.Equals(2))
            {
                var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent.EventCode),  // nguoi giới thiệu
                                        new XElement("P", promotionEvent.ID),
                                        new XElement("Or", input[0].OrderCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
                return xmlString;
            }
            return null;
        }
        public static List<GeneralCodeInsert> InsertGeneralCodeRPgetGCID(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            List<GeneralCodeInsert> lst = new List<GeneralCodeInsert>();
            for (int i = 0; i < InviteInfo.Count; i++)
            {
                GeneralCodeInsert gc = new GeneralCodeInsert();
                gc.eventCode = InviteInfo[i].EventCode;
                gc.id = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
                {
                    ActionName = "InsertGeneralCodeXMLGCID",
                    XML = CreateXMLUpdateGeneralCodeRPbyGC(input, InviteInfo[i], PrepaidInfo, i),
                    RowAffected = 0
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                lst.Add(gc);
            }
            return lst;
        }
        public static Boolean InsertGeneralCodeRPCAM(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRFCAM> camrf, HistoryTemInfo his)
        {
            // camrf[0] : người đăng ký
            // camrf[1] : người giới thiệu
            int InsertGeneralCode = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
            {
                ActionName = "InsertGeneralCodeXML",
                XML = CreateXMLUpdateGeneralCodeRPCAM(input, camrf, his),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // check row Affected
            return (InsertGeneralCode <= 3);
        }
        public static XElement CreateXMLUpdateGeneralCodeRPCAM(List<ItemRequest> input, List<InfoRFCAM> camrf, HistoryTemInfo his)
        {
            int PrepaidTimeTV;
            if (his.IsPrepaidTV > 0) PrepaidTimeTV = 1; else if (his.IsPrepaidTV < 0) PrepaidTimeTV = -1; else PrepaidTimeTV = 0;
            //var xmlString = new List<XElement>();
            int voucherRF = camrf.Count() - 1;
            var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", camrf[0].EventCode),  // nguoi giới thiệu
                                        new XElement("P", camrf[0].ID),
                                        new XElement("Or", input[0].OrderCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));
            for (int i = 1; i <= voucherRF; i++)
            {
                xmlString.Add(new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", camrf[i].EventCode),
                            new XElement("P", camrf[i].ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", his.BNET),
                            new XElement("BTV", his.BTV),
                            new XElement("IsPrepaidTV", PrepaidTimeTV),
                            new XElement("Ac", 111)));
            }            
            //xmlString.Add(new XElement("I",  // người được giới thiệu status = 111
            //                new XElement("C", camrf[0].EventCode),
            //                new XElement("P", camrf[0].ID),
            //                new XElement("Or", input[0].OrderCode),
            //                new XElement("L", 0),
            //                new XElement("D", 0),
            //                new XElement("S", 0),
            //                new XElement("O", input[0].Objectinvited),
            //                new XElement("BNET", his.IsPrepaidNET),
            //                new XElement("BTV", his.IsPrepaidTV),
            //                new XElement("IsPrepaidTV", PrepaidTimeTV),
            //                new XElement("Ac", 111)));

            return xmlString;
        }
        public static XElement CreateXMLUpdateGeneralCodeRP(List<ItemRequest> input, List<InfoRP> promotionEvent, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            int PrepaidTimeTV;
            if (PrepaidInfo.Item3 > 0) PrepaidTimeTV = 1; else if (PrepaidInfo.Item3 < 0) PrepaidTimeTV = -1; else PrepaidTimeTV = 0;

            var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent[2].EventCode),  // nguoi giới thiệu
                                        new XElement("P", promotionEvent[2].ID),
                                        new XElement("Or", input[0].OrderCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].ObjecInvite),
                                        new XElement("Ac", 100),    // 100
                                        new XElement("BNET", 0),
                                        new XElement("BTV", 0),
                                        new XElement("IsPrepaidTV", 0)
                                        ));

            xmlString.Add(new XElement("I",  // người được giới thiệu status = 111
                            new XElement("C", promotionEvent[1].EventCode),
                            new XElement("P", promotionEvent[1].ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].Objectinvited),
                            new XElement("BNET", PrepaidInfo.Item4),
                            new XElement("BTV", PrepaidInfo.Item5),
                            new XElement("IsPrepaidTV", PrepaidTimeTV),
                            new XElement("Ac", 111)));

            xmlString.Add(new XElement("I",  // Mã loy cho người giới thiệu status = 111
                            new XElement("C", promotionEvent[0].EventCode),
                            new XElement("P", promotionEvent[0].ID),
                            new XElement("Or", input[0].OrderCode),
                            new XElement("L", 0),
                            new XElement("D", 0),
                            new XElement("S", 0),
                            new XElement("O", input[0].ObjecInvite),
                            new XElement("BNET", 0),
                            new XElement("BTV", 0),
                            new XElement("IsPrepaidTV", 0),
                            new XElement("Ac", 100)));

            return xmlString;
        }
        public static Boolean InsertGeneralCodeRP(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            int InsertGeneralCode = connection.Query<int>(ConstantAPI.StoreName_GeneralCode, new
            {
                ActionName = "InsertGeneralCodeXML",
                XML = CreateXMLUpdateGeneralCodeRP(input, InviteInfo, PrepaidInfo),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            // check row Affected
            return (InsertGeneralCode == 3);
        }
        public static async Task<ModelRequestNotifyResponse> SenNotify(int ObjInvite, int ObjInvited, string type)
        {
            ModelRequestNotifyResponse response = new ModelRequestNotifyResponse();
            try
            {
                Dictionary<int, InfoNotify> infoUser = new Dictionary<int, InfoNotify>();
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    var temp = connection.Query<InfoNotify>(ConstantAPI.StoreName_InviteSuccess, new
                    {
                        ActionName = "InforPushNotify",
                        ObjIDInvite = ObjInvite,
                        ObjIDInvited = ObjInvited,
                        RowAffected = 0
                    }, commandType: CommandType.StoredProcedure).ToList();

                    foreach (var item in temp)
                    {
                        if (item.Id == ObjInvite) infoUser.Add(1, item); else infoUser.Add(2, item);
                    }
                }

                L.Mes(Level.INFO, JsonConvert.SerializeObject(infoUser));

                var request = new
                {
                    contractIdGT = ObjInvite,
                    data = new
                    {
                        eventType = type,
                        nameGT = infoUser[1].FullName,
                        nameDK = infoUser[2].FullName,
                        contractDK = infoUser[2].Contract
                    }
                };
                using (var client = new HttpClient())
                {
                    string url = Utility.hi_fpt_api;
                    client.BaseAddress = new Uri(url);
                    client.DefaultRequestHeaders.Add("Authorization", ("referral::ofjhgiiedhcidkfjeudsalsodejdcfydiejd" + DateTime.Now.ToString("yyyy-dd-MM").ToString()).CreateMD5());
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    response = await client.PostAsync("/hi-customer-local/referral/send-notify-v3", data).Result.Content.ReadAsAsync<ModelRequestNotifyResponse>();
                }

                L.Mes(Level.INFO, JsonConvert.SerializeObject(response));
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.ToString());
            }
            return response;
        }
        public static void UpdateStatusRPCode(SqlConnection connection, SqlTransaction transaction, string InviteCode)
        {
            connection.Query<int>(ConstantAPI.StoreName_InviteCode, new
            {
                InviteCode = InviteCode,
                ActionName = "UpdateInvite",
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static void UpdateOanhVK(int ObjID, string OrderCode, string EventCode, int GCcode)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, OrderCode }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@EventCode", EventCode);
                p.Add("@generalCodeID", GCcode);
                connection.Execute(ConstantAPI.spOanhVK, p, commandType: CommandType.StoredProcedure);
            }
        }
        public static void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                var data = new
                {
                    ObjID = ObjID,
                    PromotionIDNet = PNET,
                    PromotionIDTV = PTV,
                    MoneyPromotionIDNet = MNET,
                    MoneyPromotionIDTV = MTV,
                    VoucherCode = Voucher,
                    AddBy = SalesManID
                };
                L.Mes(Level.INFO, JsonConvert.SerializeObject(data), "UpdateDiscount " + ObjID);
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }
        public static Boolean UpdateHistoryTemp(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input)
        {
            int RowAffected = connection.Query<int>(ConstantAPI.StoreName_HistoryTemp, new
            {
                ActionName = "UpdateHistory",
                XML = CreateXMLRedeem(input),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            // check records update to tbale History_temp
            return (RowAffected == input.Count());
        }

        public static Boolean UpdateHistoryTempCamera(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input)
        {
            int RowAffected = connection.Execute(ConstantAPI.OS6_ReferalProgram_CAM, new
            {
                ActionName = "UpdateHistoryCamera",
                objid = input[0].Objectinvited,
                Code = input[0].VoucherCode,
                OrderCode = input[0].OrderCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

            // check records update to tbale History_temp
            return (RowAffected >0);
        }
        public static XElement CreateXMLRedeemCAM(List<ItemRequest> input, EventCamera ec, string GeneralCodeInvite = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited-in", ec.EventCodeCamIn),
                           new XElement("Vinvited-out", ec.EventCodeCamOut),
                           new XElement("Or", item.OrderCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite),// object của người giới thiệu
                           new XElement("RF", "HiFPT")
                       ));
            return xmlString;
        }
        public static XElement CreateXMLRedeem(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "", string eventCodeLoy = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("RPLoyalty", eventCodeLoy), // Mã ưu đãi add điểm của Loyalty
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.OrderCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite)// object của người giới thiệu
                       ));
            return xmlString;
        }
        public static string BuildPackageType(int IsPrepaidNET, int IsPrepaidTV)
        {
            if (IsPrepaidNET == -1 && IsPrepaidTV != -1) return "TVONLY";
            else if (IsPrepaidNET != -1 && IsPrepaidTV != -1) return "COMBO";
            else if (IsPrepaidNET != -1 && IsPrepaidTV == -1) return "NETONLY";
            else return "";
        }
    }    
}