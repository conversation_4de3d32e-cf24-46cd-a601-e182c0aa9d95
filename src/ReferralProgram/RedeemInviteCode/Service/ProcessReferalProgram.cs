using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Voucher.APIHelper.Log4net;
using System.Net.Http;
using System.Configuration;
using System.Net;
using System.IO;
using System.Net.Http.Headers;
using RedeemInviteCode.Model.LoyaltyModel;
using System.Globalization;
using RedeemInviteCode.Model;
using RedeemInviteCode.Model.CameraModel;

namespace RedeemInviteCode.Service
{
    public class ProcessReferalProgram
    {
        private const string DKH = "DKH";
        private const string GTBB = "GTBB";
        private static AuthorizationInfoLoy _auth;

        public static List<Referal_model> GetListReferal()
        {
            using (IDbConnection connect = new SqlConnection(SqlHelper.ConnRead()))
            {
                var sp1 = StoreProcedure.Schema + StoreProcedure.OS6_ReferralProgram_GetListMail;
                var data = new List<Referal_model>();
                data = connect.Query<Referal_model>(sp1, commandType: CommandType.StoredProcedure).ToList();
                return data;
            }
        }
        public static TemplateMail GetTemplateMail(string code)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<TemplateMail>("PowerInside.dbo.OSU6_ReferralProgram_TemplateMail", new
                {
                    ActionName = "GetTemplateByCode",
                    Code = code,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static string SendMailDemo()
        {
            var template = GetTemplateMail("GTBB");
            template.Body = template.Body.Replace("{0}", "Nguyễn Hoàng Huy")
                         .Replace("{1}", "Test")
                         .Replace("{2}", "FTEL123")
                         .Replace("{3}", "Internet FPT")
                         .Replace("{4}", "100.000")
                         .Replace("{5}", "50.000")
                         .Replace("{6}", "50");
            var req = new MailModelInput
            {
                FromEmail = "<EMAIL>",
                Recipients = "<EMAIL>",
                CarbonCopys = "",
                BlindCarbonCopys = "",
                Subject = template.Subject,
                Body = template.Body,
                AttachFile = "",
                AttachUrl = ""
            };

            return SendMail(req);
        }
        public static string SendToMail(string from_email, Referal_model item, bool isOwnerCamera)
        {
            var result = "";
            string package = string.Empty;
            string subjectMail = string.Empty;
            TemplateMail template = new TemplateMail();
            var quantityRF = CountContractReferral(item.ObjIDGT);
            CultureInfo cul = CultureInfo.GetCultureInfo("vi-VN");

            if (!string.IsNullOrEmpty(item.IPTV))
                package="Internet & Truyền hình FPT Play";
            else package=item.Internet;
            string body_html = string.Empty;
            if (isOwnerCamera)
            {
                string cloudPackage = ReferralProgramCamera.GetCloudPackage(item.ObjIDDK);
                if (item.ChannelRegister == "GTBB")
                {
                    template = GetTemplateMail("OWNER-CMR-GTBB");
                }
                if (item.ChannelRegister == "DKH")
                {
                    template = GetTemplateMail("OWNER-CMR-DKH");
                }

                subjectMail = template.Subject;
                body_html = template.Body.Replace("{0}", item.ReceiveFullname)
                                            .Replace("{1}", item.Fullname).Replace("{2}", package)
                                            .Replace("{3}", item.ContractDK).Replace("{4}", item.FirstAccess.ToString())
                                            .Replace("{5}", Convert.ToInt32(item.Reward).ToString("#,###", cul.NumberFormat))
                                            .Replace("{6}", cloudPackage);

            } else
            {
                if (item.ChannelRegister == "GTBB")
                {
                    if (quantityRF > 1)
                    {
                        template = GetTemplateMail("GTBB-2");
                        subjectMail = template.Subject;
                        body_html = template.Body.Replace("{0}", item.ReceiveFullname)
                                                 .Replace("{1}", quantityRF.ToString())
                                                 .Replace("{2}", item.Fullname)
                                                 .Replace("{3}", item.InviteCode)
                                                 .Replace("{4}", package)
                                                 .Replace("{5}", Convert.ToInt32(item.Reward).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{6}", Convert.ToInt32(item.RewardGT).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{7}", item.FoxGold.ToString());
                    }
                    else
                    {
                        template = GetTemplateMail("GTBB");
                        subjectMail = template.Subject;
                        body_html = template.Body.Replace("{0}", item.ReceiveFullname)
                                                 .Replace("{1}", item.Fullname)
                                                 .Replace("{2}", item.InviteCode)
                                                 .Replace("{3}", package)
                                                 .Replace("{4}", Convert.ToInt32(item.Reward).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{5}", Convert.ToInt32(item.RewardGT).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{6}", item.FoxGold.ToString());
                    }

                    if (!string.IsNullOrEmpty(item.MailDK))
                    {
                        //int goldAdd = !string.IsNullOrEmpty(item.Internet) && !string.IsNullOrEmpty(item.IPTV) ? 10000 : 5000;
                        int goldAdd = 30000;
                        var templateDK = GetTemplateMail("GTBBed");
                        templateDK.Body = templateDK.Body.Replace("{CustomerName}", item.FullNameDK)
                             .Replace("{InviteCode}", item.InviteCode)
                             .Replace("{FirstAccess}", item.FirstAccess.ToString())
                             .Replace("{Contract}", item.ContractDK)
                             .Replace("{Fgold}", Convert.ToInt32(goldAdd).ToString("#,###", cul.NumberFormat));

                        var reqMail = new MailModelInput
                        {
                            FromEmail = "<EMAIL>",
                            Recipients = item.MailDK,
                            CarbonCopys = "",
                            BlindCarbonCopys = "",
                            Subject = templateDK.Subject,
                            Body = templateDK.Body,
                            AttachFile = "",
                            AttachUrl = ""
                        };

                        //send mail nguoi gt
                        result += "GTBBed: " + SendMail(reqMail);
                    }
                }

                if (item.ChannelRegister == "DKH")
                {
                    if (quantityRF > 1)
                    {
                        template = GetTemplateMail("DKH-2");
                        subjectMail = template.Subject;
                        body_html = template.Body.Replace("{0}", item.ReceiveFullname)
                                                 .Replace("{1}", quantityRF.ToString())
                                                 .Replace("{2}", item.Fullname)
                                                 .Replace("{3}", package)
                                                 .Replace("{4}", item.ContractDK)
                                                 .Replace("{5}", item.FirstAccess.ToString())
                                                 .Replace("{6}", Convert.ToInt32(item.RewardGT).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{7}", item.FoxGold.ToString());
                    } else
                    {
                        template = GetTemplateMail("DKH");
                        subjectMail = template.Subject;
                        body_html = template.Body.Replace("{0}", item.ReceiveFullname)
                                                 .Replace("{1}", item.Fullname)
                                                 .Replace("{2}", package)
                                                 .Replace("{3}", item.ContractDK)
                                                 .Replace("{4}", item.FirstAccess.ToString())
                                                 .Replace("{5}", Convert.ToInt32(item.RewardGT).ToString("#,###", cul.NumberFormat))
                                                 .Replace("{6}", item.FoxGold.ToString());
                    }

                    if (!string.IsNullOrEmpty(item.MailDK))
                    {
                        //int goldAdd = !string.IsNullOrEmpty(item.Internet) && !string.IsNullOrEmpty(item.IPTV) ? 10000 : 5000;
                        int goldAdd = 30000;
                        var templateDK = GetTemplateMail("GTBBed-dkh");
                        templateDK.Body = templateDK.Body.Replace("{CustomerName}", item.FullNameDK)
                             .Replace("{InviteCode}", item.InviteCode)
                             .Replace("{FirstAccess}", item.FirstAccess.ToString())
                             .Replace("{Contract}", item.ContractDK)
                             .Replace("{Fgold}", Convert.ToInt32(goldAdd).ToString("#,###", cul.NumberFormat))
                             .Replace("{ReceiveFullname}", item.ReceiveFullname);

                        var reqMail = new MailModelInput
                        {
                            FromEmail = "<EMAIL>",
                            Recipients = item.MailDK,
                            CarbonCopys = "",
                            BlindCarbonCopys = "",
                            Subject = templateDK.Subject,
                            Body = templateDK.Body,
                            AttachFile = "",
                            AttachUrl = ""
                        };

                        //send mail nguoi gt
                        result += "GTBBed-dkh: " + SendMail(reqMail);
                    }
                }
            }
            var req = new MailModelInput
            {
                FromEmail = from_email,
                Recipients = item.ReceiveEmail,
                CarbonCopys = "",
                BlindCarbonCopys = "",
                Subject = subjectMail,
                Body = body_html,
                AttachFile = "",
                AttachUrl = ""
            };

            //send mail nguoi gt
            result += "Mail GT: " + SendMail(req);

            return result;
        }
        public static string SendMail(MailModelInput input)
        {
            var logid = Guid.NewGuid();
            L.Mes(Level.INFO, JsonConvert.SerializeObject(input), logid + "SendMail: ");
            try
            {
                string url = string.Concat(Utility.emailApi, "/api/SendMailSMTP/InsertInfoSendMailSMTP");
                string data = JsonConvert.SerializeObject(input);
                string res = Common.callapi(url, "POST", data);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(res), logid + "SendMail: ");
                return res;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), logid + "SendMail: ");
                return ex.Message;
            }
        }
        public static string AddCustomer(AddCustomerReq req)
        {
            var url = PromotionConfig.Url + "/api/promotion/addCustomer";
            var data = JsonConvert.SerializeObject(req);
            return IHttpRequest.callapi_CR(url, "POST", data, "application/json; charset=utf-8");
        }
        public static List<CodeModel> GetCodeFPTplay(int ID)
        {
            using (IDbConnection connect = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var sp = StoreProcedure.Schema + StoreProcedure.OS6_FPTVoucher_ReferralProgram_GetCodeFPTplay;
                var data = new List<CodeModel>();
                data = connect.Query<CodeModel>(sp, new { ID }, commandType: CommandType.StoredProcedure).ToList();
                return data;
            }
        }
        public static List<HifptModel> sendnotifyHifpt(Referal_model model, List<CodeModel> lstFPTplayCode, int quantityRF)
        {
            var rs = new List<HifptModel>();
            //rs.Add(sendconfirm(model));
            rs.Add(sendsuccess(model, quantityRF));
            //rs.Add(sendsuccess_2(model, lstFPTplayCode));
            return rs;
        }
        private static HifptModel sendconfirm(Referal_model model)
        {
            var rs = new HifptModel();
            var url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;
            var modelapi = new
            {
                contractIdGT = model.ObjIDGT,
                data = new
                {
                    eventType = "referral_code_confirm",
                    nameGT = model.ReceiveFullname,
                    nameDK = model.Fullname,
                }
            };
            string dataapi = JsonConvert.SerializeObject(modelapi);
            string result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
            var jsonapi = JObject.Parse(result);
            if (jsonapi != null)
            {
                rs = jsonapi.ToObject<HifptModel>();
            }
            return rs;
        }

        public static string GetPackageTypeInvited(long objIDInvited)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<string>("PowerInside.dbo.OSU6_ReferralProgram_HiFPT", new
                {
                    ActionName = "GetPackageTypeInvited",
                    ObjID = objIDInvited,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            }
        }

        private static HifptModel sendsuccess(Referal_model model, int quantityRF)
        {
            var rs = new HifptModel();
            string url = string.Empty;
            string result = string.Empty;
            string packageTypeInvited = GetPackageTypeInvited(model.ObjIDDK);
            
            if (model.ChannelRegister.Equals(GTBB))
            {
                url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;
                var modelapi = new
                {
                    contractIdGT = model.ObjIDGT,
                    contractIdDK = model.ObjIDDK,
                    data = new
                    {
                        eventType = "referral_code_success",
                        nameDK = model.Fullname,
                        successTimes = quantityRF,
                        inviteCode = model.InviteCode,
                        contractDK = model.ContractDK,
                        contractType = packageTypeInvited,
                        deploymentTime = model.FirstAccess.ToString("yyyy/MM/dd HH:mm:ss"),
                        nameGT = model.ReceiveFullname,
                        promotionalMoneyDK = model.Reward,
                        promotionalMoneyGT = model.RewardGT,
                        promotionalGoldGT = model.FoxGold,
                        promotionalGoldDK = packageTypeInvited.ToUpper() == "COMBO" ? 10000 : 5000
                    }
                };
                L.Mes(Level.INFO, JsonConvert.SerializeObject(modelapi), "ProcessActiveVoucher - send hi FPT req");
                string dataapi = JsonConvert.SerializeObject(modelapi);
                result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
            }
            if (model.ChannelRegister.Equals(DKH))
            {
                url = HelperConfigs.url_hifpt + "/hi-customer-local/inside/push-notify";
                var modelapi = new
                {
                    contractNo = model.ContractGT,
                    type = "successfully_activated_registration",
                    data = new {
                      registrationContractNo = model.ContractDK,
                      registrationName = model.Fullname,
                      activeTime = model.FirstAccess.ToString("yyyy/MM/dd HH:mm:ss"),
                      money= model.RewardGT,
                      gold=model.FoxGold,
                      successTimes = quantityRF,
                      contractType = packageTypeInvited,
                    }
                };
                L.Mes(Level.INFO, JsonConvert.SerializeObject(modelapi), "ProcessActiveVoucher - send hi FPT req");
                string dataapi = JsonConvert.SerializeObject(modelapi);
                result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
            }
            
            var jsonapi = JObject.Parse(result);
            if (jsonapi != null)
            {
                rs = jsonapi.ToObject<HifptModel>();
            }
            return rs;
        }
        private static HifptModel sendsuccess_2(Referal_model model, List<CodeModel> lstFPTplayCode)
        {
            //var code = store.GetCodeFPTplay(model.ID);
            var rs = new HifptModel();
            if (lstFPTplayCode.Count() > 0)
            {
                var url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;
                var modelapi = new
                {
                    contractIdGT = model.ObjIDGT,
                    contractIdDK = model.ObjIDDK,

                    data = new
                    {
                        eventType = "referral_code_success_2",
                        numberOfCode = lstFPTplayCode.Count(),
                        codeString = string.Join(",", lstFPTplayCode.Select(a => a.Code))
                    }
                };
                string dataapi = JsonConvert.SerializeObject(modelapi);
                string result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
                var jsonapi = JObject.Parse(result);
                if (jsonapi != null)
                {
                    rs = jsonapi.ToObject<HifptModel>();
                }
            }
            return rs;
        }

        public static List<SmsResult> SendSms(Referal_model ref_model, List<CodeModel> lstCodeFPtplay)
        {
            var rs_api = new List<SmsResult>();
            var model = new SmsResult();
            try { model = Login(SmsConfig.username,SmsConfig.password); }
            catch { }
            if (model != null)
            {
                if (!string.IsNullOrEmpty(ref_model.Location_Phone))
                {
                var rs_sendsms = new SmsResult();
                string template_msg = string.Empty;
                if (ref_model.ChannelRegister.Equals(DKH))
                    template_msg = "Chuc mung Quy khach da Dang ky ho thanh cong 1 hop dong dich vu FPT Telecom. Truy cap app Hi FPT de biet them thong tin chi tiet. Xin cam on!";
                if (ref_model.ChannelRegister.Equals(GTBB))
                    template_msg = "Chuc mung QK da gioi thieu thanh cong 1 hop dong dich vu FPT Telecom. Truy cap http://fpt.vn/member hoac app Hi FPT de biet them thong tin chi tiet. Xin cam on!";
                if (string.IsNullOrEmpty(template_msg))
                    return null;
                var sms_md = new Smsdata();
                sms_md.Message = template_msg;
                sms_md.PhoneNumber = ref_model.Location_Phone;

                // AesScript model sms
                var param_api = AesCrypt.GetAesCrypt(JsonConvert.SerializeObject(sms_md), SmsConfig.password);
                // Convert aesScript to base64string
                string base64string = System.Convert.ToBase64String(param_api);
                // call api send sms
                var url = HelperConfigs.url_apisms + SmsConfig.action_Sendsms;
                var model_api = new { data = base64string };
                string sendsms_status = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8", model.Detail.AccessToken);
                //string rs = JsonConvert.SerializeObject(loginResult);
                var jsonapi = JObject.Parse(sendsms_status);
                if (jsonapi != null)
                {
                    rs_sendsms = jsonapi.ToObject<SmsResult>();
                    rs_api.Add(rs_sendsms);
                }
                //Helper.WriteLog("IPTV\\OS6_ReferalProgram\\", "log.txt", string.Format("Thoi gian chay: {0}. ", DateTime.Now) + ";" + sms_md.Message + ";" + sms_md.PhoneNumber + ";" + rs_sendsms.Message, 60, 30);
                if (lstCodeFPtplay.Count > 0)
                {
                    rs_api.AddRange(SendSmsCodeFPTPlay(lstCodeFPtplay, ref_model.Location_Phone));
                    }
                }

                if (!string.IsNullOrEmpty(ref_model.PhoneDK))
                {
                    var rs_sendsms = new SmsResult();
                    //int goldAdd = !string.IsNullOrEmpty(ref_model.Internet) && !string.IsNullOrEmpty(ref_model.IPTV) ? 10000 : 5000;
                    int goldAdd = 30000;
                    string template_msg = "Hop dong "+ref_model.ContractDK+" cua QK da trien khai thanh cong. QK nhan "+ goldAdd + " FGold, truy cap app HiFPT hoac https://khachhangthanthiet.fpt.vn de xem chi tiet.";
                    var sms_md = new Smsdata();
                    sms_md.Message = template_msg;
                    sms_md.PhoneNumber = ref_model.PhoneDK;

                    // AesScript model sms
                    var param_api = AesCrypt.GetAesCrypt(JsonConvert.SerializeObject(sms_md), SmsConfig.password);
                    // Convert aesScript to base64string
                    string base64string = System.Convert.ToBase64String(param_api);
                    // call api send sms
                    var url = HelperConfigs.url_apisms + SmsConfig.action_Sendsms;
                    var model_api = new { data = base64string };
                    string sendsms_status = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8", model.Detail.AccessToken);
                    //string rs = JsonConvert.SerializeObject(loginResult);
                    var jsonapi = JObject.Parse(sendsms_status);
                    if (jsonapi != null)
                    {
                        rs_sendsms = jsonapi.ToObject<SmsResult>();
                        rs_api.Add(rs_sendsms);
                    }
                    //Helper.WriteLog("IPTV\\OS6_ReferalProgram\\", "log.txt", string.Format("Thoi gian chay: {0}. ", DateTime.Now) + ";" + sms_md.Message + ";" + sms_md.PhoneNumber + ";" + rs_sendsms.Message, 60, 30);
                    if (lstCodeFPtplay.Count > 0)
                    {
                        rs_api.AddRange(SendSmsCodeFPTPlay(lstCodeFPtplay, ref_model.PhoneDK));
                    }
                }

            }
            return rs_api;
        }

        public static List<SmsResult> SendSmsMKTp6(Referal_model ref_model)
        {
            var rs_api = new List<SmsResult>();
            var model = new SmsResult();
            try { model = Login(SmsMktP6.username, SmsMktP6.password); }
            catch { }
            if (model != null)
            {
                var rs_sendsms = new SmsResult();
                string template_msg = SmsMktP6.content_sms;                
                var sms_md = new Smsdata();
                sms_md.Message = template_msg;
                sms_md.PhoneNumber = ref_model.Location_Phone;

                // AesScript model sms
                var param_api = AesCrypt.GetAesCrypt(JsonConvert.SerializeObject(sms_md), SmsMktP6.password);
                // Convert aesScript to base64string
                string base64string = System.Convert.ToBase64String(param_api);
                // call api send sms
                var url = HelperConfigs.url_apisms + SmsConfig.action_Sendsms;
                var model_api = new { data = base64string };
                string sendsms_status = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8", model.Detail.AccessToken);
                //string rs = JsonConvert.SerializeObject(loginResult);
                var jsonapi = JObject.Parse(sendsms_status);
                if (jsonapi != null)
                {
                    rs_sendsms = jsonapi.ToObject<SmsResult>();
                    rs_api.Add(rs_sendsms);
                }
            }
            return rs_api;
        }
        public static SmsResult Login(string userName, string passWord)
        {
            var model = new SmsResult();
            var url = HelperConfigs.url_apisms + SmsConfig.action_Login;
            var model_api = new { UserName = userName, Password = passWord };
            string loginResult = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8");
            //string rs = JsonConvert.SerializeObject(loginResult);
            var jsonapi = JObject.Parse(loginResult);
            if (jsonapi != null)
            {
                model = jsonapi.ToObject<SmsResult>();
            }
            return model;
        }
        public static List<SmsResult> SendSmsCodeFPTPlay(List<CodeModel> lstCodeFPtplay, string phone)
        {
            //var code = store.GetCodeFPTplay(model.ID);
            var model = new List<SmsResult>();
            var modellogin = new SmsResult();
            try { modellogin = Login(SmsConfig.username,SmsConfig.password); }
            catch { }
            if (modellogin != null)
            {
                if (lstCodeFPtplay.Count() > 0)
                {
                    foreach (var item in lstCodeFPtplay)
                    {
                        string template_msg = "Cam on Quy khach da gioi thieu them khach hang cho FPT Telecom. Quy khach duoc tang ma FPT Play " + item.Code + ". Vui long kich hoat truoc ngay " + item.ExpiredDate.ToString("dd-MM-yyyy");
                        var rs_sendsms = new SmsResult();

                        var sms_md = new Smsdata();
                        sms_md.Message = template_msg;
                        sms_md.PhoneNumber = phone;

                        // AesScript model sms
                        var param_api = AesCrypt.GetAesCrypt(JsonConvert.SerializeObject(sms_md), SmsConfig.password);
                        // Convert aesScript to base64string
                        string base64string = System.Convert.ToBase64String(param_api);
                        // call api send sms
                        var url = HelperConfigs.url_apisms + SmsConfig.action_Sendsms;
                        var model_api = new { data = base64string };
                        string sendsms_status = IHttpRequest.callapi_smsworld(url, ApiMethod.Post, JsonConvert.SerializeObject(model_api), "application/json; charset=utf-8", modellogin.Detail.AccessToken);
                        //string rs = JsonConvert.SerializeObject(loginResult);
                        var jsonapi = JObject.Parse(sendsms_status);
                        if (jsonapi != null)
                        {
                            rs_sendsms = jsonapi.ToObject<SmsResult>();
                            model.Add(rs_sendsms);
                        }
                        //Helper.WriteLog("IPTV\\OS6_ReferalProgram\\", "log.txt", string.Format("Thoi gian chay: {0}. ", DateTime.Now) + ";" + sms_md.Message + ";" + sms_md.PhoneNumber + ";" + rs_sendsms.Message, 60, 30);
                    }
                }
            }
            return model;
        }
        public static void RealRedeem()
        {
            using (IDbConnection connect = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var sp = StoreProcedure.Schema + StoreProcedure.OS6_ReferralProgram_RealRedeem;
                connect.Execute(sp, commandType: CommandType.StoredProcedure);
            }
        }
        public static void MaskSentmail(int ID)
        {
            using (IDbConnection connect = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var sp = StoreProcedure.Schema + StoreProcedure.OS6_ReferralProgram_MarkSentMail;
                connect.Execute(sp, new { ID }, commandType: CommandType.StoredProcedure);
            }
        }

        public static int GetLocation(string contract)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<int>(string.Concat(StoreProcedure.Schema,StoreProcedure.OS6_FPTVoucher_GetInforByContract), new
                {
                    ActionName = "GetLocationID",
                    contract = contract
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static bool CheckPersonalContract(string contract)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int res = connection.Query<int>(string.Concat(StoreProcedure.Schema, StoreProcedure.OS6_FPTVoucher_GetInforByContract), new
                {
                    ActionName = "CheckPersonalContract",
                    contract = contract
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return (res == 1);
            }
        }
        private static long ConvertToTimestamp(DateTime value)
        {
            long epoch = (value.Ticks - 621355968000000000) / 10000000;
            return epoch;
        }

        public static string getBaseTokenLoyalty()
        {
            string res = string.Empty;
            if (_auth != null)
            {
                long timeExpire = Convert.ToInt32(_auth.expires_in) + Convert.ToInt32(_auth.iat);
                DateTime datetimeNow = DateTime.Now.AddMinutes(30);
                long timeNow = ConvertToTimestamp(datetimeNow);
                if (timeExpire > timeNow)
                    return _auth.access_token;
            }
            try
            {
                var uri = Utility.loyaltyapi + "/auth/oauth/token";

                var keyValues = new List<KeyValuePair<string, string>>();
                keyValues.Add(new KeyValuePair<string, string>("grant_type", "client_credentials"));

                var content = new FormUrlEncodedContent(keyValues);

                L.Mes(Level.INFO, "getBaseTokenLoyalty" + JsonConvert.SerializeObject(new { uri = uri, keyValues = keyValues }));

                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic",Utility.Token_key_GTBB_loy.ToString());                   
                    using (var response = httpClient.PostAsync(uri, content).Result)
                    {
                        response.EnsureSuccessStatusCode();                        
                        string r = response.Content.ReadAsStringAsync().Result;
                        L.Mes(Level.INFO, r, "getBaseTokenLoyalty");
                        JToken jToken = JToken.Parse(r);
                        if (jToken["access_token"] != null)
                        res = jToken["access_token"].ToString();

                        _auth = JsonConvert.DeserializeObject<AuthorizationInfoLoy>(r);
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "getBaseTokenLoyalty");
                return null;
            }
            return res;
        }

        public static string callapi_loy(string logId, string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            try
            {
                string token = getBaseTokenLoyalty();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(token), "ProcessActiveVoucher - callapi_loy token: ");   
      
                HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
                wr.Headers.Add("Authorization", "Bearer " + token);                
                wr.Method = _method;
                if (_method.ToUpper().Equals("POST"))
                {
                    //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                    wr.ContentType = contentType;
                    // Set the data to send.
                    using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                    {
                        streamWriter.Write(_data);
                    }
                }
                var httpResponse = (HttpWebResponse)wr.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    var rs = streamReader.ReadToEnd();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(rs), "ProcessActiveVoucher - callapi_loy rs: ");   
                    return rs;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "ProcessActiveVoucher - callapi_loy error: ");  
                return "";
            }
        }

        public static string RandomString()
        {
            Random random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(chars, 16)
              .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public static int CountContractReferral(long objidGT)
        {

            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<int>("PowerInside.dbo.OSU6_ReferralProgram_HiFPT", new
                {
                    ActionName = "GetCountSuccessInvite",
                    ObjID = objidGT
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static int CountContractReferral(SqlConnection connection, SqlTransaction transaction, long objidGT)
        {
            return connection.Query<int>("PowerInside.dbo.OSU6_ReferralProgram_HiFPT", new
            {
                ActionName = "GetCountSuccessInvite",
                ObjID = objidGT
            }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static ModelMKT6 RollGift(long objID, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(objID), "ProcessActiveVoucher - RollGift req: ");
            ModelMKT6 res = new ModelMKT6();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();

                    var pars = new { ActionName = "GetGift", ObjID = objID};
                    var data = connection.Query<GiftModel>("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", pars,
                         commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                    Random rnd = new Random();
                    if (data.Count > 0)
                    {
                        var gift = data[rnd.Next(0, data.Count)];

                        using (var transaction = connection.BeginTransaction())
                        {
                            if (gift == null)
                            {
                                return null;
                            }

                            connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MarketingGift",
                                new { @ActionName = "UpdateObjID", @GiftCode = gift.GiftCode, @ObjID = objID },
                                transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            transaction.Commit();

                            res.Image = gift.Image;
                            res.Name = gift.Name;
                            res.objid = objID;
                            res.TypeGift = gift.TypeGift;
                            res.GiftCode = gift.GiftCode;
                            res.ValueVoucher = gift.ValueVoucher;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "ProcessActiveVoucher - RollGift for " + objID.ToString() + " Error ");
                return null;
            }

            return res;
        }
        public static bool RollGiftMKT7(long objIDGT, long objIDDK, string logId, string channel)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(objIDGT), "ProcessActiveVoucher - RollGift req: ");
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();

                    var pars = new { ActionName = "GetGift", ObjIDGT = objIDGT };
                    var data = connection.Query<GiftModel>("PowerInside.dbo.OS6_FPTVoucher_MKTGift", pars,
                         commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                    Random rnd = new Random();
                    if (data.Count > 0)
                    {
                        var gift = data[rnd.Next(0, data.Count)];

                        using (var transaction = connection.BeginTransaction())
                        {
                            if (gift == null)
                            {
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(objIDGT), "dataPrizeCode:{No data} ");
                                return false;
                            }

                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                   JsonConvert.SerializeObject(gift), "dataPrizeCode: ");                         

                            if (gift.TypeGift.Equals(0))
                            {
                                int isAddPrepaid = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                                {
                                    ActionName = "InsertToPrepaid",
                                    GiftCode = gift.GiftCode,
                                    ObjID = objIDGT,
                                    ValueVoucher = gift.ValueVoucher
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            }

                            connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MKTGift", new 
                            {
                                ActionName = "UpdateInfoData",
                                Channel = channel,
                                ObjIDDK = objIDDK,
                                ObjIDGT = objIDGT,
                                GiftCode = gift.GiftCode
                            },
                            transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

                            var reqHiFPT = new
                            {
                                contractIdGT = objIDGT,
                                data = new
                                {
                                    eventType = "referral_gift_promotion",
                                    giftUrl = gift.Image,
                                    giftType = gift.TypeGift,
                                    giftCode = gift.GiftCode,
                                    giftName = gift.Name,
                                    giftValue = Math.Ceiling(gift.ValueVoucher * 1.1),
                                }
                            };

                            transaction.Commit();

                            string dataapi = JsonConvert.SerializeObject(reqHiFPT);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(dataapi), " req noti hifpt dataapi ");
                            string url = url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;
                            string result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(result), " dataPrizeCode ToHiFPT: ");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "ProcessActiveVoucher - RollGift for " + objIDGT.ToString() + " Error ");
                return false;
            }

            return true;
        }

        public static bool RollGiftMKT8(long objIDGT, long objIDDK, string logId, string channel)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(objIDGT), "ProcessActiveVoucher - RollGift req: ");
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();

                    var pars = new { ActionName = "GetGift_MKT8", ObjIDGT = objIDGT };
                    var data = connection.Query<GiftModel>("PowerInside.dbo.OS6_FPTVoucher_MKTGift", pars,
                         commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                    Random rnd = new Random();
                    if (data.Count > 0)
                    {
                        var gift = data[rnd.Next(0, data.Count)];

                        using (var transaction = connection.BeginTransaction())
                        {
                            if (gift == null)
                            {
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(objIDGT), "dataPrizeCode:{No data} ");
                                return false;
                            }

                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                   JsonConvert.SerializeObject(gift), "dataPrizeCode: ");

                            if (gift.TypeGift.Equals(0))
                            {
                                int isAddPrepaid = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                                {
                                    ActionName = "InsertToPrepaid",
                                    GiftCode = gift.GiftCode,
                                    ObjID = objIDGT,
                                    ValueVoucher = gift.ValueVoucher
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            }

                            connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MKTGift", new
                            {
                                ActionName = "UpdateInfoData",
                                Channel = channel,
                                ObjIDDK = objIDDK,
                                ObjIDGT = objIDGT,
                                GiftCode = gift.GiftCode
                            },
                            transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

                            var reqHiFPT = new
                            {
                                contractIdGT = objIDGT,
                                data = new
                                {
                                    eventType = "referral_gift_promotion",
                                    giftUrl = gift.Image,
                                    giftType = gift.TypeGift,
                                    giftCode = gift.GiftCode,
                                    giftName = gift.Name,
                                    giftValue = Math.Ceiling(gift.ValueVoucher * 1.1),
                                }
                            };

                            transaction.Commit();

                            string dataapi = JsonConvert.SerializeObject(reqHiFPT);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(dataapi), " req noti hifpt dataapi ");
                            string url = url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;
                            string result = IHttpRequest.callapi_hifpt(url, "POST", dataapi, "application/json; charset=utf-8");
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(result), " dataPrizeCode ToHiFPT: ");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "ProcessActiveVoucher - RollGift for " + objIDGT.ToString() + " Error ");
                return false;
            }

            return true;
        }

        public static void MKT6_sendNoti2()
        {
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    var pars = new { ActionName = "GetDataNoActive" };
                    var data = connection.Query<ModelMKT6Noti2>("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", pars,
                         commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", "", DateTime.Now) + " " +
                            JsonConvert.SerializeObject(data), " MKT6_sendNoti2 data ");
                    if (data.Count() == 0) return;
                    foreach (var item in data)
                    {
                        var logid = Guid.NewGuid();
                        using (var transaction = connection.BeginTransaction())
                        {
                            int isUpdate = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                            {
                                ActionName = "UpdateStatus",
                                GiftCode = item.GiftCode,
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(isUpdate), " MKT6_sendNoti2 isUpdate ");

                            if (item.GiftType.Equals(0) && isUpdate>0)
                            {
                                int isAddPrepaid = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_MarketingGift", new
                                {
                                    ActionName = "InsertToPrepaid",
                                    GiftCode = item.GiftCode,
                                    ObjID = item.ObjID,
                                    ValueVoucher = item.GiftValue
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(isAddPrepaid), " MKT6_sendNoti2 isAddPrepaid ");
                            }
                            transaction.Commit();                            
                            var reqNoti2 = new
                            {
                                contractIdGT=item.ObjID,
                                data= new
                                {
                                    eventType="referral_gift_promotion_confirm",
                                    giftType= item.GiftType,
                                    giftName= item.GiftName,
                                    giftValue = item.GiftValueWithVAT
                                }
                            };
                            string datareq = JsonConvert.SerializeObject(reqNoti2);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                                    datareq, " MKT6_sendNoti2 isAddPrepaid ");
                            string url = url = HelperConfigs.url_hifpt + NotifyConfig.action_notify;
                            string result = IHttpRequest.callapi_hifpt(url, "POST", datareq, "application/json; charset=utf-8");
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), " MKT6_sendNoti2 res API hifpt ");
                            return;
                        }
                    }
                    
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", "", DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " MKT6_sendNoti2 Error ");
                throw;
            }
        }


        #region mktpha9
        public static string GetValueByKeyConfig(string str)
        {
            using (var conn = new SqlConnection(SqlHelper.ConnRead()))
            {
                return conn.Query<string>(StoreProcedure.OS6_FPTVoucher_VoucherConfigCURD, new
                {
                    action = "GetValueByKey",
                    key = str
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static string GetLocationCodeByID(long objID)
        {
            using (var conn = new SqlConnection(SqlHelper.ConnRead()))
            {
                return conn.Query<string>(StoreProcedure.OS6_FPTVoucher_VoucherConfigCURD, new
                {
                    action = "GetLocationCodeByID",
                    ObjID = objID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static string GetContracTypeInvite(string contract, long objID)
        {
            using (var conn = new SqlConnection(SqlHelper.ConnRead()))
            {
                int combo = conn.Query<int>(StoreProcedure.OS6_FPTVoucher_MKT9, new
                {
                    action = "CheckContractTypeCombo",
                    objID = objID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                if (combo == 1) return "COMBO";
            }

            using (var client = new HttpClient())
            {
                var url = Utility.camera_api;
                client.BaseAddress = new Uri(url);
                var data = new StringContent(JsonConvert.SerializeObject(new { contract }), Encoding.UTF8, "application/json");
                var response = client.PostAsync("/API/Info/CheckUsingCamera", data).Result.Content.ReadAsAsync<CheckUsingCameraResponse>();
                var result = response.Result;
                if (result.StatusCode == 1)
                {
                    if (result.Data.IsUsingCamOnly) return "CAMERA";
                }
            }

            return "NET";
        }

        public static string GetPhoneByObjID(long objID)
        {
            using (var conn = new SqlConnection(SqlHelper.ConnRead()))
            {
                return conn.Query<string>(StoreProcedure.OS6_FPTVoucher_MKT9, new
                {
                    action = "GetPhoneByObjID",
                    objID = objID
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static string GetPackageTypeInviteByObjid(long objid)
        {
            using (var conn = new SqlConnection(SqlHelper.ConnRead()))
            {
                return conn.Query<string>(StoreProcedure.OS6_FPTVoucher_MKT9, new
                {
                    action = "GetPackageTypeInviteByObjid",
                    objID = objid
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        #endregion
    }
}