using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using Voucher.APIHelper;

namespace RedeemInviteCode.Service
{
    public class ProcessRFModel
    {
    }
    public class Referal_model
    {
        public int ID { get; set; }
        public long ObjIDGT { get; set; }
        public string ContractGT { get; set; }
        public string GenenalCodeLoyInvite { get; set; }
        public string ReceiveFullname { get; set; }
        public string ReceiveEmail { get; set; }
        public long ObjIDDK { get; set; }
        public string ContractDK { get; set; }
        public string Fullname { get; set; }
        public string Location_Phone { get; set; }
        public string InviteCode { get; set; }
        public string Internet { get; set; }
        public string IPTV { get; set; }
        public string Contract { get; set; }
        public DateTime FirstAccess { get; set; }
        public decimal Reward { get; set; }
        public decimal RewardGT { get; set; }

        public long? GeneralCodeID { get; set; }
        public DateTime? RedeemDate { get; set; }
        public int FoxGold { get; set; }
        public string ChannelRegister { get; set; }
        public string PhoneDK { get; set; }
        public string MailDK { get; set; }
        public string FullNameDK { get; set; }
    }
    public class HelperConfigs
    {
        public static readonly string EmailFrom = Utility.OS6_ReferalProgram_EmailFrom;
        public static readonly string url_hifpt = Utility.apiHiFptcustomer;
        public static readonly string url_apisms = Utility.apiSmsworld;
        public static readonly string url_CR = ConfigurationManager.AppSettings["APICR"];
        public static readonly string url_HR = ConfigurationManager.AppSettings["APIHR"];
    }

    public class MailConfig
    {
        public static string
            subject_mail = "[FPT Telecom] Thông báo Giới thiệu khách hàng thành công",
            subject_mail_spring = @"Mã số dự thưởng chương trình 'Khai xuân như ý - Phú quý cả năm'",
            url_sendmail = Utility.systemapi_fpt_vn,
            url_action = "/api/SystemBulkMailApi/SendMailAttachment";

        public static string
            content_mail_spring_lottery = @"<p>Kinh chào anh/chị {0},</p><p>Xin chân thành cảm ơn Quý khách đã tin tưởng và đăng ký sử dụng dịch vụ của FPT Telecom. Nhằm tri ân điều này, FPT Telecom xin gửi tặng Quý khách mã dự thưởng {1} để tham gia chương trình 'Khai Xuân như ý – Phú quý cả năm'.</p><p>Chương trình 'Khai Xuân như ý – Phú quý cả năm” là một món quà ý nghĩa nhân dịp đầu năm mới mà FPT Telecom dành tặng cho các khách hàng với các giải thưởng rất hấp dẫn như 1 xe SH 150i bản ABS, 4 chiếc iPhone 12 Pro. Thông tin chi tiết, Quý khách vui lòng xem thêm tại: fpt.vn/event/khai-xuan-phu-quy'.
Rất mong Quý khách tiếp tục ủng hộ FPT Telecom, và giới thiệu thêm cho bạn bè, người thân sử dụng dịch vụ Internet, Truyền hình của FPT Telecom để được giảm giá cước đến 200.000 và tham gia thêm chương trình 'Rủ bạn thêm vui – Ưu đãi ngất ngây'.</p><p>Trân trọng cảm ơn, FPT Telecom.</p><p>Thư do hệ thống tự động gửi, làm ơn không đáp lại!'</p>";

    }
    public class ApiMethod
    {
        public const string
            Post = "Post",
            Get = "Get";
    }
    public class NotifyConfig
    {
        public const string

            action_notify = "/hi-customer-local/referral/send-notify-v3",
            action_notify_lotery = "/hi-customer-local/inside/push-notify";
    }
    public class SmsConfig
    {
        public const string action_Login = "/api/Login",
                            action_Sendsms = "/api/v3/send",
                            username = "gtbb",
                            password = "0ef63d0e8b7d5f8db9124319d61fc4b0";
        public static string content_sms_spring_lottery = @"FPT chuc mung Quy khach da nhan {0} ma du thuong tu chuong trinh Rinh Van Qua Tet. Chi tiet xem tai: https://fpt.vn/sale/ma-du-thuong. Xin cam on";
    }
    public class SmsMKTConfig
    {
        public const string username = "mktpromotion",
                            password = "c1962ad2cc5a17c3db4b00caecadfe54";
        public static string content_sms_spring_lottery = @"FPT chuc mung Quy khach nhan ma du thuong {0} tu chuong trinh Vui Ket Loc Xuan . Chi tiet xem tai: https://fpt.vn/event/vui-ket-loc-xuan. Xin cam on";
    }
    public class SmsMktP6
    {
        public const string username = "advocatorsell",
                            password = "902h302ad001fd0200ed8a5j18bba9dc";
        public static string content_sms= @"FPT Telecom cam on Quy khach da tham gia Gioi thieu ban be. Xin gui tang Quy khach THEM 01 QUA TANG DAC BIET. Mo Hi FPT de nhan qua ngay http://onelink.to/hifpt";
    }
    public class PromotionConfig
    {
        public static readonly string Url = Utility.apiPromotion;
    }
    public class StoreProcedure
    {
        public const string
            Schema = "PowerInside.dbo.",
            OS6_ReferralProgram_RealRedeem = "OS6_ReferralProgram_RealRedeem",
            OS6_ReferralProgram_GetListMail = "OS6_ReferralProgram_GetListMail",
            OS6_ReferralProgram_MarkSentMail = "OS6_ReferralProgram_MarkSentMail",
            OS6_Foxpro_ReferralProgram_SendNotificationActive = "OS6_Foxpro_ReferralProgram_SendNotificationActive",
            OS6_FPTVoucher_ReferralProgram_GetCodeFPTplay = "OS6_FPTVoucher_ReferralProgram_GetCodeFPTplay",
            OS6_FPTVoucher_GetInforByContract = "OS6_FPTVoucher_GetInforByContract",
            OS6_FPTVoucher_VoucherConfigCURD = "PowerInside.dbo.OS6_FPTVoucher_VoucherConfigCURD",
            OS6_FPTVoucher_MKT9 = "PowerInside.dbo.OS6_FPTVoucher_MKT9";
    }
    public class AddCustomerReq
    {
        public string customerKey { get; set; }
        public string customerName { get; set; }
        public string customerPhone { get; set; }
        public string registerDate { get; set; }
        public int sourceId { get; set; }
        public int roleId { get; set; }
        public int LocationID { get; set; }
    }
    public class CodeModel
    {
        public string Code { get; set; }
        public DateTime ExpiredDate { get; set; }
    }
    public class HifptModel
    {
        public int statusCode { get; set; }
        public string message { get; set; }
        public object data { get; set; }
    }
    public class SmsResult
    {
        public int ID { get; set; }
        public string Message { get; set; }
        public DataSms_Login Detail { get; set; }
    }
    public class DataSms_Login
    {
        public string Name { get; set; }
        public int Quota { get; set; }
        public string Email { get; set; }
        public string AccessToken { get; set; }
    }
    public class Smsdata
    {
        public string PhoneNumber { get; set; }
        public string Message { get; set; }

    }

    public class GiftModel
    {
        public string GiftCode { get; set; }
        public string Name { get; set; }
        public int TypeGift { get; set; }
        public int ValueVoucher { get; set; }
        public string Image { get; set; }
    }

    public class ModelMKT6 {
        public long objid { get; set; }
        public string Image { get; set; }
        public string GiftCode { get; set; }
        public string Name { get; set; }
        public int TypeGift { set; get; }
        public decimal ValueVoucher { get; set; }
    }
    public class ModelMKT6Noti2
    {
        public string GiftCode { get; set; }
        public int ObjID { get; set; }       
        public int GiftType { get; set; }
        public string GiftName { get; set; }
        public decimal GiftValue { set; get; }
        public decimal GiftValueWithVAT { set; get; }
    }
    public class UpdateStatusInput 
    {
        public string GiftCode { get; set; }
    }
    public class MKTp6Type
    {
        public int TypeGift { get; set; }
        public decimal ValueVoucher { get; set; }
        public int ObjID { get; set; }
    }

    public class CameraModel
    {
        public int statusCode { get; set; }
        public string error { get; set; }
        public string data { get; set; }
    }
}