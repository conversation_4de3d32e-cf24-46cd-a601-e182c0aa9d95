using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Net;

namespace RedeemInviteCode.Service
{
    public static class Helper
    {
        public static string HttpRequest(string _urlApi = "", string _urlAction = "", string _method = "GET", string _data = null, string _contentType = "application/json", string encoded = null)
        {
            using (var client = new HttpClient())
            {
                HttpResponseMessage response;
                if (!string.IsNullOrEmpty(encoded))
                    client.DefaultRequestHeaders.Add("Authorization", encoded);

                if (_method.ToUpper() == "GET")
                {
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    client.BaseAddress = new Uri(_urlApi);
                    response = client.GetAsync(_urlAction).Result;
                }
                else
                {
                    if (_urlApi.Substring(_urlApi.Length - 1, 1) != "/" && _urlAction.Substring(0, 1) != "/")
                        _urlApi += "/";
                    else
                        if (_urlApi.Substring(_urlApi.Length - 1, 1) == "/" && _urlAction.Substring(0, 1) == "/")
                            _urlApi = _urlApi.Substring(0, _urlApi.Length - 1);

                    _urlApi += _urlAction;
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(_contentType));
                    response = client.PostAsync(_urlApi, new StringContent(_data, Encoding.UTF8, _contentType)).Result;
                }

                if (response.IsSuccessStatusCode)
                {
                    return response.Content.ReadAsStringAsync().Result;
                }
                else
                {
                    return string.Format("{0} ({1})", (int)response.StatusCode, response.ReasonPhrase);
                }
            }
        }
        public static string HttpRequest2(string _url, string _method = "GET", string _data = null)
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                wr.ContentType = "application/json";
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            // Get the response.
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }


        /// <summary>
        /// Không sử dụng, chuyển sang WriteLogTrace, WriteLogError
        /// </summary>
        /// <param name="subFolder">dir</param>
        /// <param name="fileName">KSD</param>
        /// <param name="strWrite">message</param>
        /// <param name="days">KSD</param>
        /// <param name="line">KSD</param>

        /*
        public static void WriteLog(string subFolder, string fileName, string strWrite, int days = 90, int line = 0)
        {
            try
            {
                string folderName = AppDomain.CurrentDomain.BaseDirectory + "LogFile\\" + subFolder;
                if (!System.IO.Directory.Exists(folderName))
                {
                    System.IO.Directory.CreateDirectory(folderName);
                }

                #region Kiểm tra file và xóa nếu thỏa đk
                DirectoryInfo d = new DirectoryInfo(folderName);
                FileInfo[] Files = d.GetFiles("*.txt");
                foreach (FileInfo file in Files)
                {
                    DateTime date = File.GetCreationTime(file.FullName);
                    if (date.AddDays(days) < DateTime.Now && file.Name != fileName)
                    {
                        File.Delete(file.FullName);
                    }
                }

                string filePath = System.IO.Path.Combine(folderName, fileName);
                int lineFile = System.IO.File.Exists(filePath) == true ? File.ReadLines(filePath).Count() : 0;
                if (line > 0 && lineFile >= line)
                {
                    string newFileName = String.Format("{0}log.txt", DateTime.Now.ToString("ddMMyyyy"));
                    string newFilePath = System.IO.Path.Combine(folderName, newFileName);
                    File.Copy(filePath, newFilePath);
                    File.SetCreationTime(newFilePath, DateTime.Now);
                    File.Delete(filePath);
                }
                #endregion

                if (System.IO.File.Exists(filePath))
                {
                    using (System.IO.StreamWriter file = new System.IO.StreamWriter(filePath, true))
                    {
                        file.WriteLine(strWrite);
                    }
                }
                else
                {
                    using (System.IO.StreamWriter file = new System.IO.StreamWriter(filePath))
                    {
                        file.WriteLine(strWrite);
                    }
                }
            }
            catch { }
        }
        */

        #region Dapper
        public static IEnumerable<dynamic> Query(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.Query(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static IEnumerable<T> Query<T>(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.Query<T>(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static dynamic QueryFirstOrDefault(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.QueryFirstOrDefault(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static T QueryFirstOrDefault<T>(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.QueryFirstOrDefault<T>(sql, param, commandType: commandType, commandTimeout: 300);
            }
        }

        public static int Execute(string connectionString, string sql, object param = null, CommandType commandType = CommandType.StoredProcedure)
        {
            using (var connection = new SqlConnection(connectionString))
            {
                return connection.Execute(sql, param, commandType: commandType);
            }
        }
        #endregion

        public static class Converter
        {
            public static T FromRow<T>(DataRow row, string columName)
            {
                if (row.Table.Columns.Contains(columName))
                    return To<T>(row[columName]);
                else
                    return To<T>(null);
            }

            public static T FromRow<T>(DataRow row, string columName, T defaultValue)
            {
                if (row.Table.Columns.Contains(columName))
                    return To<T>(row[columName]);
                else
                    return defaultValue;
            }

            public static T To<T>(object obj)
            {
                try
                {
                    Type conversionType = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
                    return (T)System.Convert.ChangeType(obj, conversionType);
                }
                catch
                {
                    return default(T);
                }
            }

            public static T To<T>(object obj, T defaultValue)
            {
                try
                {
                    Type conversionType = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);
                    return (T)System.Convert.ChangeType(obj, conversionType);
                }
                catch
                {
                    return defaultValue;
                }
            }
        }


    }
}