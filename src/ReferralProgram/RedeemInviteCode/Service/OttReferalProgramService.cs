using Dapper;
using Newtonsoft.Json;
using RedeemInviteCode.Contant;
using RedeemInviteCode.Model;
using RedeemVoucher.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Xml.Linq;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;

namespace RedeemInviteCode.Service
{
    public class OttReferalProgramService
    {
        private const string OS6_FPTVoucher_OTTReferralProgram = "PowerInside.dbo.OS6_FPTVoucher_OTTReferralProgram";
        public static Boolean checkOTTCode(string vouchercode)
        {
            try
            {
                string url = Utility.apiOtt + "/api/v1/isc/referral/";
                OTTReferalProgram ott = callapi_CheckOTT(string.Concat(url , vouchercode));
                L.Mes(Level.ERROR, JsonConvert.SerializeObject(ott), "OTT - checkOTTCode");
                if (ott.status == 1)
                {
                    return (ott.data.valid == 1);
                }
                return false;
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "OTT - checkOTTCode");
                return false;
            }
        }
        public static OTTReferalProgram callapi_CheckOTT(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                OTTReferalProgram ott = new OTTReferalProgram { data = null, message = "Fail", status = 0 };
                ott = JsonConvert.DeserializeObject<OTTReferalProgram>(rs);
                return ott;
            }
        }

        public static bool RedeemReferalOTTCode(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, string keylog)
        {
            try
            {
                // kiểm tra nhập nhiều hơn 1 mã hoặc không có trong giỏ
                L.Mes(Level.REQUEST, keylog + " "+ JsonConvert.SerializeObject(input), "RedeemReferalOTTCode");
                if (input.Count() != 1) return false;
                // getInfo Prepaid of InviteCode (vì mã RP có nhiều Prepaid nên phải chọn chính xác mã voucher quy đổi nào chính xác)
                // item1 :  IDPaidTimeType
                // item2 :  IsPrepaidNET
                // Item3 :  IsPrepaidTV
                // item4 :  Base ID NET
                // item5 :  Base ID TV
                var PrepaidInfo = connection.Query<Tuple<int, int, int, int, int>>(OS6_FPTVoucher_OTTReferralProgram, new
                {
                    actionName = "GetPrepaidOfInviteCode",
                    OrderCode = input[0].OrderCode,
                    Code = input[0].VoucherCode
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                L.Mes(Level.REQUEST, JsonConvert.SerializeObject(PrepaidInfo), keylog + " " + "RedeemReferalOTTCode PrepaidInfo:");
                //get Info promotionEvent invite and Invited
                List<InfoRP> InviteInfo = new List<InfoRP>();

                InviteInfo = connection.Query<InfoRP>(OS6_FPTVoucher_OTTReferralProgram, new
                {
                    //voucherCode = input[0].VoucherCode,
                    actionName = "GetPromotionEventOTT",
                    PaidTimeType = PrepaidInfo.Item1
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

                if (InviteInfo.Count() == 0)
                {
                    L.Mes(Level.INFOPE, "No promotion event", keylog + " " + "RedeemReferalOTTCode");
                    return false;
                }

                // update History_temp
                Boolean StsUpdateHistoryTemp = UpdateHistoryTemp(connection, transaction, input);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StsUpdateHistoryTemp = StsUpdateHistoryTemp }), keylog + " " + "RedeemReferalOTTCode");
                if (!StsUpdateHistoryTemp) return false;


                // insert data to table generalcode
                int? generalCodeID = InsertGeneralCodeOTT(connection, transaction, input, InviteInfo, PrepaidInfo);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(generalCodeID), keylog + " " + "RedeemReferalOTTCode InsertGC code res ID: ");
                bool isSuccess = InsertSuccessCodeOTT(connection, transaction, input[0].VoucherCode, InviteInfo[0].EventCode, input[0].Objectinvited);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(new { StatusAddGeneralCode = generalCodeID > 0 }), "add general code - RedeemReferalOTTCode");
                if (generalCodeID == null || generalCodeID == 0) return false;

                // update Discount
                if (StsUpdateHistoryTemp && generalCodeID > 0)
                {
                    var PEInfo = InviteInfo[0];
                    UpdateDiscount(
                    input[0].Objectinvited,  // objID khách hàng
                    PEInfo.NetPromotionID, //XđồngY tháng NET + Tháng
                    PEInfo.IPTVPromotionID, //XđồngY tháng TV + Tháng
                    PEInfo.MoneyPromotionNETID, //Giảm Tiền Trực Tiếp NET
                    PEInfo.MoneyPromotionTVID, //Giảm Tiền Trực Tiếp TV
                    PEInfo.EventCode, // Code
                    input[0].ObjecInvite, false); //Object Invite -TypeVC:True  
                    
                    L.Mes(Level.INFO, "Call Voucher Bill");
                    // add voucher bill invited
                    UpdateOanhVK(input[0].Objectinvited, input[0].OrderCode,PEInfo.EventCode);
                    L.Mes(Level.INFO, "RedeemReferalOTTCode");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, ex.Message, "RedeemReferalOTTCode Error");
                return false;
            }
        }
        private static bool InsertSuccessCodeOTT(SqlConnection connection, SqlTransaction transaction, string invitecode, string voucher, int objidinvited)
        {
            int RowAffected = connection.Execute(OS6_FPTVoucher_OTTReferralProgram, new
            {
                actionName = "InsertSuccessOTTRF",
                InviteCode = invitecode,
                ObjID = objidinvited,
                Code = voucher
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

            // check records update to tbale History_temp
            return (RowAffected>0);
        }

        private static int GetPromotionNetByContainerID(int ContainerID, int AddMonth, int IsEvenMonth = 0)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPromotionNetByContainerID",
                    new { ContainerID = ContainerID, AddMonth = AddMonth, IsEvenMonth = IsEvenMonth },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        private static MonthPromotion GetMonthBase(int obj)
        {
            MonthPromotion res;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.QueryFirstOrDefault<MonthPromotion>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetMonthPromotion",
                    new { ObjID = obj },
                    commandType: CommandType.StoredProcedure);
            }
            return res;
        }
        private static void AddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null)
        {
            L.Mes(Level.INFO, "AddCustomerDiscountV2:" + JsonConvert.SerializeObject(new
            {
                ObjID = ObjID,
                PromotionNetID = PromotionNetID,
                IPTVPromotionID = IPTVPromotionID,
                MoneyPromotionNETID = MoneyPromotionNETID,
                MoneyPromotionTVID = MoneyPromotionTVID,
                VoucherCode = VoucherCode,
                SalesID = SalesID,
                IsEvenMonth = IsEvenMonth,
                GeneralCodeID = GeneralCodeID,
                PrivateCodeID = PrivateCodeID
            }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }
        private static void UpdateOanhVK(int ObjID, string OrderCode, string EventCode)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(new { ObjID, OrderCode }));
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                var p = new DynamicParameters();
                p.Add("@ObjID", ObjID);
                p.Add("@OrderCode", OrderCode);
                p.Add("@EventCode", EventCode);
                connection.Execute(ConstantAPI.spOanhVK, p, commandType: CommandType.StoredProcedure);
            }
        }
        private static void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }
        private static Boolean UpdateHistoryTemp(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input)
        {
            int RowAffected = connection.Query<int>(ConstantAPI.StoreName_HistoryTemp, new
            {
                ActionName = "UpdateHistory",
                XML = CreateXMLRedeem(input),
                RowAffected = 0
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            // check records update to tbale History_temp
            return (RowAffected == input.Count());
        }
        private static XElement CreateXMLRedeem(List<ItemRequest> input, string GeneralCodeInvite = "", string GeneralCodeInvited = "")
        {
            var xmlString = new XElement("N",
            from item in input
            select new XElement("I",
                           new XElement("RPinvited", item.VoucherCode), // Mã RP Người DK
                           new XElement("Vinvite", GeneralCodeInvite),  // Mã RP Người Giới Thiệu
                           new XElement("Vinvited", GeneralCodeInvited),
                           new XElement("Or", item.OrderCode),
                           new XElement("Oinvited", item.Objectinvited),  // object của người được giới thiệu
                           new XElement("Oinvite", item.ObjecInvite)  // object của người giới thiệu
                       ));
            return xmlString;
        }

        public static int? InsertGeneralCodeOTT(SqlConnection connection, SqlTransaction transaction, List<ItemRequest> input, List<InfoRP> InviteInfo, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            try
            {
                var GeneralCodeIDs = connection.Query<int>(OS6_FPTVoucher_OTTReferralProgram, new
                {
                    actionName = "InsertGeneralCodeXMLV2",
                    XML = CreateXMLUpdateGeneralCodeOTT(input, InviteInfo, PrepaidInfo)
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                return GeneralCodeIDs.FirstOrDefault();
            }
            catch (Exception ex)
            {
                return 0;
            }

        }

        private static XElement CreateXMLUpdateGeneralCodeOTT(List<ItemRequest> input, List<InfoRP> promotionEvent, Tuple<int, int, int, int, int> PrepaidInfo)
        {
            int PrepaidTimeTV;
            if (PrepaidInfo.Item3 > 0) PrepaidTimeTV = 1; else if (PrepaidInfo.Item3 < 0) PrepaidTimeTV = -1; else PrepaidTimeTV = 0;

            var xmlString = new XElement("N",
                                    new XElement("I",
                                        new XElement("C", promotionEvent[0].EventCode),  // nguoi được giới thiệu
                                        new XElement("P", promotionEvent[0].ID),
                                        new XElement("Or", input[0].OrderCode),
                                        new XElement("L", 0),
                                        new XElement("D", 0),
                                        new XElement("S", 0),
                                        new XElement("O", input[0].Objectinvited),
                                        new XElement("Ac", 111),
                                        new XElement("BNET", PrepaidInfo.Item4),
                                        new XElement("BTV", PrepaidInfo.Item5),
                                        new XElement("IsPrepaidTV", PrepaidTimeTV)
                                        ));

            return xmlString;
        }
    }
}