using Dapper;
using Newtonsoft.Json;
using RedeemInviteCode.Model.LoyaltyModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;

namespace RedeemInviteCode.Service
{
    public class LoyaltyProcessService
    {
        private const string OS6_FPTVoucher_LoyaltyPromotionEvent = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent";
        public static void ProcessLoyaltyVoucher()
        {
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), ReferalProgramMyFPT.RamdoneString());
            try
            {
                List<VoucherInforModel> listData = getListVoucherDone();
                <PERSON><PERSON>(Level.INFO, JsonConvert.SerializeObject(listData), keylog + " ProcessLoyaltyVoucher");
                foreach (var data in listData)
                {
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(data), keylog + " Start To change");
                    bool changLoyalty = ChangeStatusLoyalty(data.Location_Phone, data.Voucher,keylog);
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(changLoyalty), keylog + " ChangeStatusLoyalty ");
                    if (changLoyalty)
                    {
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {
                            connection.Open();
                            using (var transaction = connection.BeginTransaction())
                            {
                                int isUpdate = connection.Execute(OS6_FPTVoucher_LoyaltyPromotionEvent, new
                                {
                                    ActionName = "ChangeStatusLoyalty",
                                    privateCodeID = data.ID,
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                L.Mes(Level.INFO, JsonConvert.SerializeObject(isUpdate), keylog + " ChangeStatusLoyalty ");
                                if (isUpdate == 1)
                                {
                                    transaction.Commit();
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, keylog + " ProcessLoyaltyVoucher");
            }
        }
        private static List<VoucherInforModel> getListVoucherDone()
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<VoucherInforModel>(OS6_FPTVoucher_LoyaltyPromotionEvent, new
                {
                    ActionName = "GetListBillDone"
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }
        }

        private static AuthorizationInfor getAuthorInfor(LoginInfor login, string keylog)
        {
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                string authInfo = login.username + ":" + login.password;
                authInfo = Convert.ToBase64String(Encoding.Default.GetBytes(authInfo));

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(WebAPIHelper.loyaltyapi + "/loyalty-services/oauth/token?grant_type=client_credentials");
                request.Method = "POST";
                request.Accept = "application/json; charset=utf-8";

                request.Headers["Authorization"] = "Basic " + authInfo;

                var response = (HttpWebResponse)request.GetResponse();

                string strResponse = "";
                using (var sr = new StreamReader(response.GetResponseStream()))
                {
                    strResponse = sr.ReadToEnd();
                }
                L.Mes(Level.INFO, strResponse, keylog + "getAuthorInfor");
                res = JsonConvert.DeserializeObject<AuthorizationInfor>(strResponse);
                //return strResponse;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, ex.Message, keylog + "getAuthorInfor");
                return null;
            }
            return res;
        }
        public static bool ChangeStatusLoyalty(string phone, string voucherCode,string keylog)
        {
            LoyaltyRedeemOutput lro = new LoyaltyRedeemOutput();
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = getAuthorInfor(login,keylog);
                var request = new
                {
                    mobileHiFpt = phone,
                    voucherCode = voucherCode,
                    voucherPaymentStatus = "SUCCESS"
                };

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/loyalty-services/api/integration-evoucher/redeem-device", data).Result;
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(response), keylog + "ChangeStatusLoyalty response " + voucherCode);
                    //iStatusCode = (int)response.StatusCode;
                    string result = response.Content.ReadAsStringAsync().Result;

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), " ChangeStatusLoyalty APIres " + voucherCode);
                    lro = JsonConvert.DeserializeObject<LoyaltyRedeemOutput>(result);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message,keylog + " ChangeStatusLoyalty");
                return false;
            }

            if (lro.statusCode.ToUpper().Equals("SUCCESS"))
            {
                return true;
            }

            return false;
        }
    }
}