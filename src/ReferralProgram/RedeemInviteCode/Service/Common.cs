using RedeemInviteCode.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;

namespace RedeemInviteCode.Service
{
    public class Common
    {
        public static string callapi(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8",List<HeaderAPI> headers = null)
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            if (headers != null)
            {
                foreach(var header in headers){
                    wr.Headers.Add(header.key, header.value);
                }                
            }
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };

                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }
    }
}