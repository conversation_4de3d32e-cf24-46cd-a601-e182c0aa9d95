using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Model.CameraModel
{
    public class CameraSucceedModel
    {
        public int temRedeemID { set; get; }
        public int CamSucceedID { set; get; }
        public int RegID { set; get; }
        public string RegCode { set; get; }
        public string NameGT { set; get; }
        public string Email { set; get; }
        public int IDGT { set; get; }
        public string ContractGT { set; get; }
        public string NameDK { set; get; }
        public int IDDK { set; get; }
        public string ContractDK { set; get; }
        public string InviteCode { set; get; }
        public decimal ValueDiscountAmountGT { set; get; }
        public decimal ValueDiscountAmountDK { set; get; }
        public int FoxGold { set; get; }
        public string Register { set; get; }
    }

    public class InforCameraModel
    {
        public int statuscode { set; get; }
        public string error { set; get; }
        public List<CameraData> data { set; get; }
    }

    public class EventCodeEF
    {
        public int ID { set; get; }
        public int InviteType { set; get; }
        public int ServiceCode { set; get; }
        public string Name { set; get; }
        public string EventCode { set; get; }
    }

    public class HistoryCamInfo
    {
        public int ServiceCodeCamera { set; get; }
        public int EFcodeID { set; get; }
        public int CamQuantity { set; get; }
    }
    public class CameraData 
    {
        public int supid { set; get; }
        public string activedate { set; get; }
        public int servicecode { set; get; }
        public int regid { set; get; }
        public int channel { set; get; }
    }
}