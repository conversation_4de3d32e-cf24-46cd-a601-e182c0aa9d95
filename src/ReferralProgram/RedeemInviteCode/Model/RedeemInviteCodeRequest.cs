using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.ComponentModel.DataAnnotations;

namespace RedeemInviteCode.Model
{
    public class RedeemInviteCodeRequest
    {
        public int ObjectID { get; set; }
        public string ContractGT { get; set; }
        public string  OrderCode { get; set; }
        public List<ItemCodeNode> VoucherCodes { get; set; }
    }

    public class ItemCodeNode {
        public string Code { get; set; }
        public int Type { get; set; }
    }

    public class checkCodeOutput
    {
        public string VoucherCode { set; get; }
        public string OrderCode { set; get; }
    }
}