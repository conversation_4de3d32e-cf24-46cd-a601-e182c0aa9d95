using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Model
{
    public class AuthorizationInfor
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string refresh_token { set; get; }
        public string accessTokenExpiresOn { set; get; }
        public string refreshTokenExpiresOn { set; get; }
    }

    public class AuthorizationInfoLoy
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string scope { set; get; }
        public string iat { set; get; }
        public string expires_in { set; get; }
        public string jti { set; get; }
    }
}