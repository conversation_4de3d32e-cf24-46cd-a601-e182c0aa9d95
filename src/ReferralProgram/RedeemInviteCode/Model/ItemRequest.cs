using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemVoucher.Models
{
    public class ItemRequest
    {
        public int PromotionEventID { get; set; }
        public int ObjecInvite { get; set; }

        public int Objectinvited { get; set; }
        public string OrderCode { get; set; }
        public string VoucherCode { get; set; }

        public int FreeMonthNet { get; set; }
    }
    public class PEDiscountModel
    {
        public int ID { get; set; }
        public string EventCode { get; set; }
        public int MoneyPromotionNETID { get; set; }
        public int MoneyPromotionTVID { get; set; }
        // SubsProm
        public int NetPromotionID { get; set; }
        public int IPTVPromotionID { get; set; }
        public int NetInterConnPromotionID { get; set; }
        public int IPTVInterConnPromotionID { get; set; }
        public int GiftPromotionID { get; set; }
        // PromotionNet
        public int PromotionNetID { get; set; }
        public int ContainerID { get; set; }
    }
    public class MonthPromotionModel
    {
        //public string Contract { get; set; }
        //public string Description { get; set; }
        //public DateTime? NgayApDungKM { get; set; }
        public string MonthFrom { get; set; }
        public string MonthTo { get; set; }
        public DateTime? ActiveDate { get; set; }
        public int EventMonth { get; set; }
        public int GetAddMonth()
        {
            try
            {
                DateTime? finalMonth;
                DateTime monthFrom, monthTo;
                if (DateTime.TryParse(MonthFrom, out monthFrom))
                {
                    finalMonth = monthFrom;
                    if (DateTime.TryParse(MonthTo, out monthTo))
                    {
                        if (monthTo > finalMonth)
                            finalMonth = monthTo;
                    }
                    if (ActiveDate.HasValue && finalMonth.HasValue)
                    {
                        finalMonth = finalMonth.Value.AddMonths(1);
                        return ((finalMonth.Value.Year - ActiveDate.Value.Year) * 12) + finalMonth.Value.Month - ActiveDate.Value.Month;
                    }
                }
            }
            catch { }
            return 0;
        }
    }
    public class In4VoucherTHmodel
    {
        public int amountTv { get; set; }
        public int locationId { get; set; }
        public int addBy { get; set; }
    }
    public class ServicePlatform : Services
    {
        public List<SubServiceTypesModel> SubServiceTypes { set; get; }
    }
    public class Services
    {
        public int ServiceID { set; get; }

    }
    public class SubServiceTypesModel
    {
        public int SubServiceTypeID { set; get; }
        public List<SubServiceModel> SubServices { set; get; }
    }
    public class SubServiceModel
    {
        public int SubServiceID { set; get; }
        public int PrePaid { set; get; }
        public int DeployTypeID { set; get; }
        public int ServiceCode { set; get; }
        public int Qty { set; get; }
    }
}