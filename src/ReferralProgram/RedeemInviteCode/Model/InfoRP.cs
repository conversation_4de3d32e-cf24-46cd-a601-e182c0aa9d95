using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Model
{
    public class InfoRP
    {
        public int ID { get; set; }
        public string EventCode { get; set; }
        public int MoneyPromotionNETID { get; set; }
        public int MoneyPromotionTVID { get; set; }
        public int NetPromotionID { get; set; }
        public int IPTVPromotionID { get; set; }
        public int NetInterConnPromotionID { get; set; }
        public int IPTVInterConnPromotionID { get; set; }
        public int GiftPromotionID { get; set; }

        public int PromotionNetID { get; set; }
        public int ContainerID { get; set; }
    }

    public class InfoRFCAM
    {
        public int ID { set; get; }
        public string EventCode { set; get; }
        public int QuotaGeneralCode { set; get; }
        public int InviteType { set; get; }
        public int ID1 { set; get; }
    }

    public class EventCamera
    {
        public string EventCodeCamIn { set; get; }
        public string EventCodeCamOut { set; get; }
    }
}
