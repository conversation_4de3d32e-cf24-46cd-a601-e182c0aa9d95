using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Model.LoyaltyModel
{
    public class VipVpnModel
    {
        public string Code { set; get; }
        public string Account { set; get; }
    }
    public class VipVPNCheckStatusModel
    {
        public bool success { set; get; }
        public List<DataVPNStatusModel> data { set; get; }
        public string message { set; get; }
    }
    public class DataVPNStatusModel 
    {
        public string activeDate { set; get; }
        public string expiresDate { set; get; }
        public string username { set; get; }
        public int trialDate { set; get; }
    }
    
}