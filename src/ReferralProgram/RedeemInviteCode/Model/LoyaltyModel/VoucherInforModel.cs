using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using Voucher.APIHelper;

namespace RedeemInviteCode.Model.LoyaltyModel
{
    public class VoucherInforModel
    {
        public string Location_Phone { set; get; }
        public string Voucher { set; get; }
        public int ID { set; get; }
    }
    public class AuthorizationInfor
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string scope { set; get; }
        public string iat { set; get; }
        public string jti { set; get; }
    }
    public class LoginInfor
    {
        public string username { set; get; }
        public string password { set; get; }
    }
    public class Login
    {
        public static string userName { get { return Utility.loyaltyapi_username; } }
        public static string passWord { get { return Utility.loyaltyapi_password; } }
    }
}