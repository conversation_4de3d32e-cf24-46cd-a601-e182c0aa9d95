using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Model.FriendSellModel
{
    public class FriendSellAddVoucherInput
    {
        public string ContractDK { set; get; }
        public string ContractSell { set; get; }
    }

    
    public class FriendSellHistory
    {
        public string contractDK { set; get; }
        public string FullName { set; get; }
        public string LocalType { set; get; }
        public string CreateDate { set; get; }
        public string FirstAccess { set; get; }
        public string ValueDiscount { set; get; }
    }
    public class FriendSellSendHiFPTModel
    {
        public string contractNo { set; get; }
        public string type { set; get; }        
    }
    public class FriendSellConfirm : FriendSellSendHiFPTModel
    {
        public FriendSellNotiConfirmModel data { set; get; }
    }

    public class FriendSellSuccess : FriendSellSendHiFPTModel
    {
        public FriendSellNotiSuccessModel data { set; get; }
    }

    public class FriendSellNotiOutputModel
    {
        public int statusCode { set; get; }
        public string message { set; get; }
        public string data { set; get; }
    }
    public class FriendSellNotiConfirmModel
    {
        public string registrationContractNo { set; get; }
        public string registrationName { set; get; }
    }


    public class FriendSellNotiSuccessModel
    {
        public string registrationContractNo { set; get; }
        public string registrationDate { set; get; }
        public string activeTime { set; get; }
        public string money { set; get; }
    }
}