using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Model
{
    public class ModelRequestNotify
    {
        public int contractIdGT { get; set; }
        public string titleVi { get; set; }
        public string titleEn { get; set; }
        public string messageEn { get; set; }
        public string messageVi { get; set; }
        public int MyProperty { get; set; }
        public data data { get; set; }
    }

    public class ModelSendNotificationReferralMyFPT
    {
        public string employeeCode { get; set; }
        public string codeRefer { get; set; }
        public string nameContract { get; set; }
        public string subjectEmail { get; set; }
        public string timeActive { get; set; }
        public string moneyRefer { get; set; }
        public decimal Referrer { get; set; }
        public string BookingId { get; set; }
        public string contract_owner { get; set; }
        public string BookingStatus { get; set; }
        public string attendPrizeCode { get; set; }
    }

    public class ModelSendNotifActiveReferralMyFPT
    {
        public string StaffIDInvite { get; set; }
        public decimal incentivesReceivedEmp { get; set; }
        public string incentivesReceivedCus { get; set; }
        public string InviteCode { get; set; }
        public string Description { get; set; }
        public string Contract { get; set; }
        public string FullName { get; set; }
        public string FirstAccess { get; set; }

    }
    public class data
    {
        public string eventType { get; set; }

    }
}