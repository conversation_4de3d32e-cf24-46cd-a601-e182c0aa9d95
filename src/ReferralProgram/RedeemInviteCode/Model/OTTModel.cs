using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Model
{
    public class OTTReferalProgram
    {
        public validCode data { set; get; }
        public string message { set; get; }
        public int status { set; get; }
    }

    public class OTTUserOutput
    {
        public OTTUserInfo data { set; get; }
        public string message { set; get; }
        public int status { set; get; }
    }
    public class OTTUserInfo
    {
        public string fullname { set; get; }
    }
    public class validCode
    {
        public string referral_code { set; get; }
        public int valid { set; get; }
    }

    public class SendRequestOTTOutput
    {
        public string message { set; get; }
        public int status { set; get; }
    }

    public class DataActiveOTT 
    {
        public int IDSuccessInvice { set; get; }
        public int GeneralCodeID { set; get; }
        public DateTime RedeemDate { set; get; }
        public int ID { set; get; }
        public int ObjIDDK { set; get; }
        public string ContractDK { set; get; }
        public string FullnameDK { set; get; }
        public string FullnameGT { set; get; }
        public string OttClient { set; get; }
        public string FirstAccess { set; get; }
        public string Date { set; get; }
        public string Internet { set; get; }
        public string IPTV { set; get; }
        public string Location_Phone { set; get; }
        public decimal RewardDK { set; get; }
        public string RewardGT { set; get; }
        public string PhoneGT { set; get; }
        public string EmailGT { set; get; }
        public string InviteCode { set; get; }
    }
    public class CallbackReferralOTT
    {
        public string user_phone { set; get; }
        public string campaign_id { set; get; }
        public string transaction_id { set; get; }
        public string additional_info { set; get; }
        public string lucky_number { set; get; }
        public string user_fullname { set; get; }
    }
}