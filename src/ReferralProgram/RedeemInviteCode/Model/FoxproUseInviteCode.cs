using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Model
{
    public class FoxproUseInviteCode
    {
        public int ID { set; get; }
        public int generalCodeID { set; get; }
        public int res { set; get; }
        public string StaffIDInvite { set; get; }
        public string InviteCode { set; get; }
        public string Description { set; get; }
        public int ObjID { set; get; }
        public string FullName { set; get; }
        public string Contract { set; get; }
        public string FirstAccess { set; get; }   
        public decimal RealMoneyAmount { set; get; }
        public string EventCode { set; get; }
        public int MoneyPromotionNETID { set; get; }
        public string NET { set; get; }
        public int MoneyPromotionTVID { set; get; }
        public string TV { set; get; }
    }
}