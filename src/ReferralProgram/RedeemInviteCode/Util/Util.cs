
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;

namespace RedeemInviteCode
{
    public static class Util
    {
        public static List<Dictionary<string, object>> ConvertToList(this DataSet ds)
        {
            List<Dictionary<string, object>> lRows = new List<Dictionary<string, object>>();
            DataTable dtObject = ds.Tables[0];
            if (ds.Tables.Count > 0)
            {
                Dictionary<string, object> dicRow = null;

                foreach (DataRow dr in dtObject.Rows)
                {
                    dicRow = new Dictionary<string, object>();
                    foreach (DataColumn col in dtObject.Columns)
                    {
                        dicRow.Add(col.ColumnName, dr[col]);
                    }
                    lRows.Add(dicRow);
                }
            }
            return lRows;
        }


        
    }
}