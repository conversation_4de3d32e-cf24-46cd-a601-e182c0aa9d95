using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace RedeemInviteCode.Contant
{
    public class ConstantAPI
    {
        public const string StoreName_HistoryTemp = "PowerInside.dbo.OS6_FPTVoucher_HistotyTempInfo";
        public const string StoreName_InviteCode = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";
        public const string StoreName_InviteSuccess = "PowerInside.dbo.OS6_ReferralProgram_SucceedInvite";
        public const string StoreName_GeneralCode = "PowerInside.dbo.OS6_FPTVoucher_GeneralCode";
        public const string StoreName_MBS = "PowerInside.dbo.OS6_FPTVoucher_MBSAPIVoucher";
        public const string spOanhVK = "PowerInside.dbo.OS6_FPTVoucher_AddVoucherBill";
        public const string StoreName_Private = "PowerInside.dbo.OS6_FPTVoucher_Private";
        public const string StoreName_MKTgeneralCode = "PowerInside.dbo.OS6_FPTVoucher_GeneralCodeMKT";
        public const string OS6_ReferalProgram_CAM = "PowerInside.dbo.OS6_ReferalProgram_CAM";
        public const string OS6_FPTVoucher_HistotyTempInfo = "PowerInside.dbo.OS6_FPTVoucher_HistotyTempInfo";
        public const string OS6_ReferralProgram_InviteCode = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";
        public const string OS6_FPTVoucher_MBSAPIVoucher = "PowerInside.dbo.OS6_FPTVoucher_MBSAPIVoucher";
        public const string OS6_FPTVoucher_OTTReferralProgram = "PowerInside.dbo.OS6_FPTVoucher_OTTReferralProgram";
        public const string OS6_FPTVoucher_RegisterWebChannel = "PowerInside.dbo.OS6_FPTVoucher_RegisterWebChannel";
        public const string OS6_FPTVoucher_PromotionEvent = "PowerInside.dbo.OS6_FPTVoucher_PromotionEvent";
        public const string OS6_FPTVoucher_Private = "PowerInside.dbo.OS6_FPTVoucher_Private";
    }
}