using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Web;
using System.Web.Caching;
using System.Web.Http;
using Voucher.APIHelper;
using System.Web.Http.Cors;

namespace JwtBearerAuth.Controllers
{
    [Route("API/Sys/{action}")]
    public class LoginController : ApiController
    {
        List<UserRoleModel> Users = new List<UserRoleModel>
        {
            new UserRoleModel { Username = "Admin", Password = "V19ZXCx0zFXaGYIi6KVW", Roles = new List<string> { AuthorizeRole.Admin }  },
            new UserRoleModel { Username = "SuperUser", Password = "1u6fFs8RMMJ6FYFiuLKn", Roles = new List<string> { AuthorizeRole.SuperUser }  },
            new UserRoleModel { Username = "AdminRF", Password = "3IjV0rYWqEVH63gYufEO", Roles = new List<string> { AuthorizeRole.AdminRF }  },
            new UserRoleModel { Username = "Loyalty", Password = "1aWWgDyQ541FMTl1t533LO", Roles = new List<string> { AuthorizeRole.Loyalty }  },
            new UserRoleModel { Username = "Foxpay", Password = "ehoq3hrTTagRakjclEDN13t24", Roles = new List<string> { AuthorizeRole.Foxpay }  },
            new UserRoleModel { Username = "Hifpt", Password = "eikchlprEmnARakjclEDN1ht45", Roles = new List<string> { AuthorizeRole.Hifpt }  },
            new UserRoleModel { Username = "ToolQuay", Password = "cjFSdIf6LxGaDjmIE2MrTag", Roles = new List<string> { AuthorizeRole.ToolQuay }  },
            new UserRoleModel { Username = "Mobinet", Password = "E6JuquJVHiTf21BDAayUSdI", Roles = new List<string> { AuthorizeRole.Mobinet }  },
            new UserRoleModel { Username = "Telesale", Password = "ho9T7uaTVoB9zimVPS9YiTf", Roles = new List<string> { AuthorizeRole.Telesale }  },
            new UserRoleModel { Username = "Dkol", Password = "pfBz9cxIYrDvHmwLaNZY", Roles = new List<string> { AuthorizeRole.Dkol }  },
            new UserRoleModel { Username = "Hifg", Password = "oppqp914lso3902f120e4", Roles = new List<string> { AuthorizeRole.Hifg }  },
            new UserRoleModel { Username = "Frtvoucher", Password = "IO3LU6nXQK6gkjU2J3tBhf", Roles = new List<string> { AuthorizeRole.Frtvoucher } },
            new UserRoleModel { Username = "Fpt", Password = "tkxVDIN6sLUQJturg7ef", Roles = new List<string> { AuthorizeRole.Fpt } },
            new UserRoleModel { Username = "ReportGTBB", Password = "1aWWHGy767KKMMjf833LO", Roles = new List<string> { AuthorizeRole.ReportGTBB } },//
            new UserRoleModel { Username = "IssueInvoice", Password = "vj4vJp3QaRiwvjT6dSPP", Roles = new List<string> { AuthorizeRole.IssueInvoice } },
            new UserRoleModel { Username = "ReportGTBB", Password = "1aWWHGy767KKMMjf833LO", Roles = new List<string> { AuthorizeRole.ReportGTBB } },
            new UserRoleModel { Username = "RegisterWeb", Password = "Ps29hNBBJjEZIBuWXjZE", Roles = new List<string> { AuthorizeRole.RegisterWeb } },
            new UserRoleModel { Username = "SaleClub", Password = "dpHkUZXCXRu5qWpptisV", Roles = new List<string> { AuthorizeRole.SaleClub } },
            new UserRoleModel { Username = "FPTPlay", Password = "XOGE2wv7f13SX0VU738o", Roles = new List<string> { AuthorizeRole.FPTPlay } },
            new UserRoleModel { Username = "PTC", Password = "FJ8T5er6g28QW1YZ547p", Roles = new List<string> { AuthorizeRole.PTC } },
            new UserRoleModel { Username = "SM", Password = "88Tn9I7wlQnAvDgakN1f", Roles = new List<string> { AuthorizeRole.SM } },
            new UserRoleModel { Username = "fptvn", Password = "m29LpQRxJ4EKcdvZT1Nh5aWG8oF", Roles = new List<string> { AuthorizeRole.fptvn } },
            new UserRoleModel { Username = "qlcs", Password = "k7PxRjHYn3MDvbWQ9ZtL2eUF5oA", Roles = new List<string> { AuthorizeRole.qlcs } },
            new UserRoleModel { Username = "TLS", Password = "k7PxRjHYn3MDvbWQ9ZtL2eUF5oA", Roles = new List<string> { AuthorizeRole.TLS } },
        };

        [EnableCors(origins: "*", headers: "*", methods: "*")]
        [HttpPost]
        public ResponseModel Login(UserModel login)
        {
            var res = new ResponseModel();
            if (login.Username.IndexOf("$") > -1 || login.Username.IndexOf("#") > -1 || login.Username.IndexOf("&") > -1
                || login.Password.IndexOf("$") > -1 || login.Password.IndexOf("#") > -1 || login.Password.IndexOf("&") > -1)
            {
                res.error = "Login failed";
                res.result = -1;
                return res;
            }
            string key = string.Format("Login-Login-{0}", getClientIp());
            Cache cache = HttpRuntime.Cache;
            if(HttpRuntime.Cache[key] != null)
            {
                int cachedkey = (int)cache.Get(key);
                if (cachedkey >= 5)
                {
                    res.data = null;
                    res.result = -1;
                    res.error = "Login sai quá nhiều lần! thử lại sau 60s";
                    return res;
                }
            }

            try
            {
                if (login != null)
                {
                    var user = Users.FirstOrDefault(c => c.Username == login.Username && c.Password == login.Password);
                    if (user != null)
                    {
                        res.data = new { token = CreateToken(user.Username, user.Roles), expires = DateTime.Now.AddMinutes(120) };
                        res.result = 1;
                    }
                    else
                    {                       
                        if (HttpRuntime.Cache[key] == null)
                        {
                            int i = 0;
                            cache.Add(key,
                            i,
                            null,
                            DateTime.Now.AddMinutes(1),
                            Cache.NoSlidingExpiration,
                            CacheItemPriority.Low,
                            null);
                        }
                        else
                        {
                            int cachedVAlue = (int)cache.Get(key);
                            cachedVAlue++;
                            HttpRuntime.Cache.Remove(key);
                            cache.Add(key,
                            cachedVAlue,
                            null,
                            DateTime.Now.AddMinutes(1),
                            Cache.NoSlidingExpiration,
                            CacheItemPriority.Low,
                            null);
                        }
                        
                        

                        res.error = "Login failed";
                        res.result = -1;
                    }
                }
            }
            catch (Exception ex)
            {
                res.error = ex.Message;
                res.result = -1;
            }

            return res;
        }
        private string getClientIp()
        {
            //string userRequest = System.Web.HttpContext.Current.Request.UserHostAddress;
            string ip = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            if (string.IsNullOrEmpty(ip))
            {
                ip = System.Web.HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
            }
            return ip;
        }

        [Authorize(Roles = AuthorizeRole.Admin)]
        [HttpGet]
        public string Test()
        {
            ClaimsIdentity claimsIdentity;
            var httpContext = HttpContext.Current;
            claimsIdentity = httpContext.User.Identity as ClaimsIdentity;

            if (claimsIdentity.HasClaim(c => c.Type == ClaimTypes.Name))
            {
                return claimsIdentity.Name;
            }

            return "";
        }

        private string CreateToken(string username, List<string> roles)
        {
            //Set issued at date
            DateTime notBefore = DateTime.Now;
            //set the time when it expires
            DateTime expires = DateTime.Now.AddMinutes(120);

            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(WebAPIHelper.Jwt_Key));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            //create a identity and add claims to the user which we want to log in
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, username)
            };
            foreach (string role in roles)
                claims.Add(new Claim(ClaimTypes.Role, role));

            //Create the jwt (JSON Web Token)
            var token = new JwtSecurityToken(
                WebAPIHelper.Jwt_Issuer,
                WebAPIHelper.Jwt_Issuer,
                claims,
                notBefore,
                expires,
                credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public class UserModel
        {
            public string Username { get; set; }
            public string Password { internal get; set; }
        }

        public class UserRoleModel : UserModel
        {
            public List<string> Roles { get; set; }
        }
    }
}
