<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.AspNet.Cors" version="5.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net45" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.7.1" targetFramework="net45" />
  <package id="Microsoft.IdentityModel.Logging" version="6.7.1" targetFramework="net45" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.7.1" targetFramework="net45" />
  <package id="Newtonsoft.Json" version="10.0.1" targetFramework="net45" />
  <package id="System.IdentityModel.Tokens.Jwt" version="6.7.1" targetFramework="net45" />
</packages>