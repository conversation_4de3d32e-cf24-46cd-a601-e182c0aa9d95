using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CSAPI.Models
{
    public class PE
    {
        public int ID { get; set; }
        public string Name { get; set; }
        public string EventCode { get; set; }
        public int QuotaGeneralCode { get; set; }
        public int QuotaPrivateCode { get; set; }
        public int InviteType { get; set; }
        public int ContainerID { get; set; }
        public int PromotionNetID { get; set; }
        public int PromotionNetAmount { get; set; }
        public string PromotionNetDiscount { get; set; }

        public bool HasQuota { get; set; }

        //////////////////////////////// Giam tien truc tiep NET
        public int ID1 { get; set; }
        public String Discount1 { get; set; }
        public int Value1 { get; set; }
        public int ServiceCode1 { get; set; }

        ////////////////////////////// <PERSON><PERSON>yen Mai Tra Truoc NET
        public int ID2 { get; set; }
        public int PrepaidTime { get; set; }
        public String Discount2 { get; set; }
        public int Value2 { get; set; }
        public int DisMonth2 { get; set; }
        public int DisMonth2L2 { get; set; }
        public int DisMonth2L3 { get; set; }
        public int DisMonth2L4 { get; set; }
        public int DisRatio2L2 { get; set; }
        public int DisRatio2L3 { get; set; }
        public int DisRatio2L4 { get; set; }
        public int ServiceCode2 { get; set; }

        /////////////////////////// Khuyen Mai IPTV
        public int ID3 { get; set; }
        public String Discount3 { get; set; }
        public int Value3 { get; set; }
        public int DisMonth3 { get; set; }
        public int DisMonth3L2 { get; set; }
        public int ServiceCode3 { get; set; }

        //////////////////////// Khuyen Mai Hoa Mang Net
        public int ID4 { get; set; }
        public String Discount4 { get; set; }
        public int Value4 { get; set; }
        public int ServiceCode4 { get; set; }

        //////////////////////// Khuyen Mai Hoa Mang IPTV
        public int ID5 { get; set; }
        public String Discount5 { get; set; }
        public int Value5 { get; set; }
        public int ServiceCode5 { get; set; }

        //////////////////////// Khuyen Mai Qua tang
        public int ID6 { get; set; }
        public string Discount6 { get; set; }
        public string Model { get; set; }

        ////////////////////// Khuyen Mai Giam tien truc tiep TV
        public int ID7 { get; set; }
        public String Discount7 { get; set; }
        public int Value7 { get; set; }
        public int ServiceCode7 { get; set; }
    }
}