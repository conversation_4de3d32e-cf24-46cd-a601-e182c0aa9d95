using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CSAPI.Models
{
    public class EvoucherOutput
    {
        public string Contract { get; set; }
  

        public List<Item> Item { get; set; }
        
    }

    public class Item {
        public int FeeNET { get; set; }
        public string VoucherCode { get; set; }
        public string Description { get; set; }

        [JsonIgnore]
        public int ContainerID { get; set; }
        [JsonIgnore]
        public int PromotionNetID { get; set; }

        // Giảm Tiền TT NET
        [JsonIgnore]
        public int DiscountNETID { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DiscountNET { get; set; }

        // Giảm Tiền TT TV
        [JsonIgnore]
        public int DiscountTVID { get; set; }
        
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DiscountTV { get; set; }

        // Tặng Tháng NET
        [JsonIgnore]
        public int FreeMonthNETID { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string FreeMonthNET { get; set; }

        // Tặng tháng TV
        [JsonIgnore]
        public int FreeMonthTVID { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string FreeMonthTV { get; set; }

        // X đồng Y tháng NET
        [JsonIgnore]
        public int DiscountByMonthNETID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DiscountByMonthNET { get; set; }
        public int DiscountFreeMonth { get; set; }

        // X đồng Y tháng TV
        [JsonIgnore]
        public int DiscountByMonthTVID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DiscountByMonthTV { get; set; }

        // Hòa Mạng NET
        [JsonIgnore]
        public int DiscountNewInterconnNETID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DiscountNewInterconnNET { get; set; }

        // Hòa Mạng NET
        [JsonIgnore]
        public int DiscountNewInterconnTVID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DiscountNewInterconnTV { get; set; }

        // Hòa Mạng NET
        [JsonIgnore]
        public int GiftID { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Gift { get; set; }
        public int Quota { get; set; } 
    }
}