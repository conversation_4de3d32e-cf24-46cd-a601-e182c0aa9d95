using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CSAPI.Models
{
    public class MonthPromotion
    {
        public string MonthFrom { get; set; }
        public string MonthTo { get; set; }
        public DateTime? ActiveDate { get; set; }
        public int EventMonth { get; set; }
        public int GetAddMonth()
        {
            try
            {
                DateTime? finalMonth;
                DateTime monthFrom, monthTo;
                if (DateTime.TryParse(MonthFrom, out monthFrom))
                {
                    finalMonth = monthFrom;
                    if (DateTime.TryParse(MonthTo, out monthTo))
                    {
                        if (monthTo > finalMonth)
                            finalMonth = monthTo;
                    }
                    if (ActiveDate.HasValue && finalMonth.HasValue)
                    {
                        finalMonth = finalMonth.Value.AddMonths(1);
                        return ((finalMonth.Value.Year - ActiveDate.Value.Year) * 12) + finalMonth.Value.Month - ActiveDate.Value.Month;
                    }
                }
            }
            catch { }
            return 0;
        }
    }
}