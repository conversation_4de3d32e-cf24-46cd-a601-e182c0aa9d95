using CSAPI.Constant;
using CSAPI.Models;
using Dapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web.Http;
using Voucher.APIHelper;
using Voucher.APIHelper.Func;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;

namespace CSAPI.Controllers
{
    [Route("API/CS-EVoucher/{action}")]

    public class CSAPIController : ApiController
    {
        private Func temp = null;
        public CSAPIController()
        {
            this.temp = new Func();
        }

        [HttpPost]
        public ResponseModel<bool> CSReedemmVoucher(RedeemInput input)
        {
            try
            {
                bool StatusRedeem = false;

                L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "CSReedemmVoucher");
                if (!CheckValidObj(input.ObjID)) return new ResponseModel<bool>() { data = false, error = Message.ObjNotValid.Value, result = 0 };

                if (!CheckValidActive(input.ObjID)) return new ResponseModel<bool>() { data = false, error = Message.RedeemNotValid.Value, result = 0 };

                // Status Active Base Obj
                BaseSatus baseStatus = CheckBaseStatus(input.ObjID);

                if (baseStatus.BranchCode > 90) return new ResponseModel<bool>() { data = false, error = Message.VCFTIER.Value, result = 0 };

                PE PETemp = GetPEByEventCode(input.VoucherCode);
                L.Mes(Level.INFOPE, JsonConvert.SerializeObject(new { StatusActive = baseStatus, PE = PETemp }));

                if (PETemp == null) return new ResponseModel<bool>() { data = false, error = Message.EventCodeNotFound.Value, result = 0 };

                if (baseStatus.Status == 1)
                {
                    int generalCodeID = Redeem(input, PETemp);
                    Boolean stsRedeem = generalCodeID > 0;
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(new { STATUSREDEEM = stsRedeem }));
                    if (!stsRedeem)
                        return new ResponseModel<bool>() { data = false, error = Message.RedeemFalse.Value, result = 0 };
                    L.Mes(Level.INFO, "Call Store Update VuPA");
                    if (!string.IsNullOrEmpty(input.VoucherCode))
                    {
                        if (PETemp.ID2 > 0)
                        {
                            UpdateDiscount(
                                input.ObjID,  // objID khách hàng
                                PETemp.ID2, //XđồngY tháng NET + Tháng
                                PETemp.ID3, //XđồngY tháng TV + Tháng
                                PETemp.ID4, //Giảm Tiền Trực Tiếp NET
                                PETemp.ID5, //Giảm Tiền Trực Tiếp TV
                                PETemp.EventCode, // Code
                                0, true //Sales -TypeVC:True
                            );
                        }
                        else
                        {
                            // tang thang
                            int isEvenMonth = 0;
                            if (PETemp.ContainerID > 0)
                            {
                                // lay clkm nen
                                var monthPromotion = GetMonthBase(input.ObjID);
                                if (monthPromotion != null)
                                {
                                    // thang chan le
                                    isEvenMonth = monthPromotion.EventMonth;
                                    // lay promotion net theo thang
                                    int promotionNetID = GetPromotionNetByContainerID(PETemp.ContainerID, monthPromotion.GetAddMonth(), isEvenMonth);
                                    if (promotionNetID > 0)
                                        PETemp.PromotionNetID = promotionNetID;
                                }
                            }
                            AddCustomerDiscountV2(
                                input.ObjID,
                                PETemp.PromotionNetID,
                                PETemp.ID3,
                                PETemp.ID4,
                                PETemp.ID5,
                                PETemp.EventCode,
                                0,
                                isEvenMonth,
                                generalCodeID
                            );
                        }
                    }
                    L.Mes(Level.INFO, "Update Success");
                    StatusRedeem = true;
                }

                if (baseStatus.Status == 2 && PETemp.ID1 == 0
                    && (PETemp.ID2 > 0 || PETemp.PromotionNetID > 0)
                    && PETemp.ID3 == 0 && PETemp.ID4 == 0 && PETemp.ID5 == 0 && PETemp.ID6 == 0 && PETemp.ID7 == 0)
                {
                    int generalCodeID = Redeem(input, PETemp);
                    Boolean stsRedeem = generalCodeID > 0;
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(new { STATUSREDEEM = stsRedeem }));
                    if (!stsRedeem) return new ResponseModel<bool>() { data = false, error = Message.RedeemFalse.Value, result = 0 };
                    L.Mes(Level.INFO, "Call Store Update VuPA");
                    if (!string.IsNullOrEmpty(input.VoucherCode))
                    {
                        if (PETemp.ID2 > 0)
                        {
                            UpdateDiscount(
                                input.ObjID,  // objID khách hàng
                                PETemp.ID2, //XđồngY tháng NET + Tháng
                                PETemp.ID3, //XđồngY tháng TV + Tháng
                                PETemp.ID4, //Giảm Tiền Trực Tiếp NET
                                PETemp.ID5, //Giảm Tiền Trực Tiếp TV
                                PETemp.EventCode, // Code
                                0, true //Sales -TypeVC:True
                            );
                        }
                        else
                        {
                            // tang thang
                            int isEvenMonth = 0;
                            if (PETemp.ContainerID > 0)
                            {
                                // lay clkm nen
                                var monthPromotion = GetMonthBase(input.ObjID);
                                if (monthPromotion != null)
                                {
                                    // thang chan le
                                    isEvenMonth = monthPromotion.EventMonth;
                                    // lay promotion net theo thang
                                    int promotionNetID = GetPromotionNetByContainerID(PETemp.ContainerID, monthPromotion.GetAddMonth(), isEvenMonth);
                                    if (promotionNetID > 0)
                                        PETemp.PromotionNetID = promotionNetID;
                                }
                            }
                            AddCustomerDiscountV2(
                                input.ObjID,
                                PETemp.PromotionNetID,
                                PETemp.ID3,
                                PETemp.ID4,
                                PETemp.ID5,
                                PETemp.EventCode,
                                0,
                                isEvenMonth,
                                generalCodeID
                            );
                        }
                    }
                    L.Mes(Level.INFO, "Update Success");
                    StatusRedeem = true;
                }
                L.Mes(Level.ENDREQUEST, "Success", "CSReedemmVoucher");
                return new ResponseModel<bool>() { data = StatusRedeem, error = null, result = 1 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, "CS-EVoucher/CSReedemmVoucher: " + ex.ToString());
                return new ResponseModel<bool>() { data = false, error = ex.ToString(), result = 0 };
            }
        }

        [HttpPost]
        public ResponseModel<EvoucherOutput> CSListEVoucher(EvoucherInput input)
        {
            try
            {
                L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "CSListEVoucher");
                // check valid obj
                EvoucherOutput response = new EvoucherOutput();
                List<Item> iresponse = new List<Item>();
                if (!CheckValidObj(input.ObjID)) return new ResponseModel<EvoucherOutput>() { data = response, error = Message.ObjNotValid.Value, result = 0 };
                // check Obj valiable time
                if (!CheckValidActive(input.ObjID)) return new ResponseModel<EvoucherOutput>() { data = response, error = Message.RedeemNotValid.Value, result = 0 };

                // get PE
                var listPE = GetListPE(2, input.LocalType, input.ApplyTimeTypeID);
                L.Mes(Level.INFOPE, JsonConvert.SerializeObject(listPE));
                //List<PE> lst = new List<PE>();
                //check quotar
                listPE.AsParallel().ForAll(item =>
                {
                    /*
                    bool isTrue = CheckQuotarVoucher(input.ObjID, item.ID);
                    if (isTrue)
                    {
                        lst.Add(item);
                    }
                    */
                    item.HasQuota = CheckQuotarVoucher(input.ObjID, item.ID);
                });
                listPE = listPE.Where(c => c.HasQuota).ToList();
                //L.Mes(Level.INFOPE, JsonConvert.SerializeObject(lst));
                L.Mes(Level.INFOPE, JsonConvert.SerializeObject(listPE));
                // Status Active Base Obj
                BaseSatus baseStatus = CheckBaseStatus(input.ObjID);
                if (baseStatus.BranchCode > 90) return new ResponseModel<EvoucherOutput>() { data = response, error = Message.VCFTIER.Value, result = 0 };

                //for (int i = 0; i < lst.Count(); i++)
                foreach (PE item in listPE)
                {
                    //PE item = lst[i];
                    int netFee = 0;
                    if (item.ID2 > 0)
                        netFee = GetNetFee(input.ObjID, item.ID2, (item.Value1 + item.Value2));
                    else
                    {
                        int promotionNetAmount = (int)Math.Round(item.PromotionNetAmount * 0.11, 0) * 10;
                        netFee = GetNetFeeV2(input.ObjID, promotionNetAmount, item.Value1 + promotionNetAmount);
                    }
                    iresponse.Add(new Item()
                    {
                        Quota = item.QuotaGeneralCode - temp.GetQuotaUsed(item.ID),
                        Description = item.Name,
                        VoucherCode = item.EventCode,
                        FeeNET = netFee,

                        ContainerID = item.ContainerID,
                        PromotionNetID = item.PromotionNetID,

                        DiscountNETID = item.ID1,
                        DiscountNET = item.Discount1,

                        DiscountTVID = item.ID7,
                        DiscountTV = item.Discount7,

                        DiscountByMonthNETID = item.ID2,
                        DiscountByMonthNET = (item.ID2 > 0 ? item.Discount2 : item.PromotionNetDiscount),
                        DiscountFreeMonth = item.DisMonth2L2,

                        DiscountByMonthTVID = item.ID3,
                        DiscountByMonthTV = item.Discount3,

                        DiscountNewInterconnNETID = item.ID4,
                        DiscountNewInterconnNET = item.Discount4,

                        DiscountNewInterconnTVID = item.ID5,
                        DiscountNewInterconnTV = item.Discount5,

                        GiftID = item.ID6,
                        Gift = item.Discount6
                    });
                }

                L.Mes(Level.INFOPE, JsonConvert.SerializeObject(iresponse));
                iresponse = iresponse.Where(x => x.FeeNET > 0 && x.Quota > 0).ToList();

                if (baseStatus.Status == 1)
                {
                    var FreeMonthBase = GetMonthBase(input.ObjID);
                    // trường hợp trả sau : FreeMonthBase
                    // trường MonthTo, MonthFrom  : không thặng tháng
                    if (FreeMonthBase != null)
                    {
                        int addMonth = FreeMonthBase.GetAddMonth() + 1;
                        L.Mes(Level.INFOPE, "FreeMonthBase:" + JsonConvert.SerializeObject(new { FreeMonthBase = FreeMonthBase, addMonth = addMonth }));
                        iresponse = iresponse.Where(x => (x.DiscountFreeMonth == 0 || (x.DiscountFreeMonth > 0 && x.DiscountFreeMonth == addMonth))).ToList();
                        L.Mes(Level.INFOPE, JsonConvert.SerializeObject(new { CaseUser = 1, PEFilter = "FreeMonth", value = iresponse }));
                    }
                    else
                        iresponse = iresponse.Where(x => (x.DiscountFreeMonth == 0 && x.ContainerID == 0)).ToList();
                }

                if (baseStatus.Status == 2)
                {
                    L.Mes(Level.INFOPE, JsonConvert.SerializeObject(new { CaseUser = 2, PEFilter = "", Value = iresponse }));
                    iresponse = iresponse.Where(x => (
                        x.DiscountNETID == 0 &&
                        x.DiscountTVID == 0 &&
                        x.FreeMonthNETID == 0 &&
                        x.FreeMonthTVID == 0 &&
                        (x.DiscountByMonthNETID > 0 || x.PromotionNetID > 0) &&
                        (x.DiscountFreeMonth == 0 && x.ContainerID == 0) &&
                        x.DiscountByMonthTVID == 0 &&
                        x.DiscountNewInterconnNETID == 0 &&
                        x.DiscountNewInterconnTVID == 0 &&
                        x.GiftID == 0 && x.FeeNET > 0
                        )).ToList();
                }

                if (iresponse.Count() == 0) return new ResponseModel<EvoucherOutput>() { data = response, error = Message.VoucherNotFound.Value, result = 0 };

                // add net free + Contract
                response.Item = iresponse;
                response.Contract = temp.GetContract(input.ObjID);

                L.Mes(Level.ENDREQUEST, "CSListEVoucher");
                return new ResponseModel<EvoucherOutput>() { data = response, result = 1, error = null };
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, "CS-EVoucher/CSListEVoucher: " + ex.ToString());
                return new ResponseModel<EvoucherOutput>() { data = default(EvoucherOutput), error = ex.ToString(), result = 0 };
            }
        }

        [HttpPost]
        public ResponseModels<ApplyTimeType> GetApplyTimeType()
        {
            var res = new ResponseModels<ApplyTimeType>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res.data = connection.Query<ApplyTimeType>(API.OS6_FPTVoucher_CSAPI, new
                {
                    ActionName = ActionName.GetApplyTimeType,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).ToList();
                res.result = res.data.Count;
            }
            return res;

        }

        private BaseSatus CheckBaseStatus(int obj)
        {
            BaseSatus status;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                status = connection.QueryFirstOrDefault<BaseSatus>(API.OS6_FPTVoucher_CheckVoucherCS, new
                {
                    @ObjID = obj
                }, commandType: CommandType.StoredProcedure);
            }
            return status;
        }

        #region Private Method

        private MonthPromotion GetMonthBase(int obj)
        {
            MonthPromotion res;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.QueryFirstOrDefault<MonthPromotion>(API.OS6_FPTVoucher_GetMonthPromotion, new
                {
                    ObjID = obj
                }, commandType: CommandType.StoredProcedure);
            }
            return res;
        }

        private int GetPromotionNetByContainerID(int ContainerID, int AddMonth, int IsEvenMonth = 0)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(
                    "PowerInside.dbo.OS6_FPTVoucher_GetPromotionNetByContainerID",
                    new { ContainerID = ContainerID, AddMonth = AddMonth, IsEvenMonth = IsEvenMonth },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private int GetNetFee(int ObjID, int NetByMonth, int DisOffA)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(API.OS6_FPTVoucher_NETFEE, new
                {
                    ObjID = ObjID,
                    NetByMonth = NetByMonth,
                    DisOffA = DisOffA
                }, commandType: CommandType.StoredProcedure);
            }
        }

        private int GetNetFeeV2(int ObjID, int DisAmount, int DisOffA)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.QueryFirstOrDefault<int>(
                    "PowerInside.dbo.OS6_FPTVoucher_NetFeeV2",
                    new { ObjID = ObjID, DisAmount = DisAmount, DisOffA = DisOffA },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private PE GetPEByEventCode(string EventCode)
        {
            PE res;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.QueryFirstOrDefault<PE>(API.OS6_FPTVoucher_CSAPI, new
                {
                    ActionName = "GetPromotionEventByEventCode",
                    EventCode = EventCode,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure);
            }
            return res;
        }

        private void UpdateDiscount(int ObjID, int PNET, int PTV, int MNET, int MTV, string Voucher, int SalesManID, Boolean chanelType)
        {
            if (chanelType)
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
            else
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(
                        "PowerInside.dbo.OS6_ReferralProgram_AddCustomerDiscount",
                        new
                        {
                            ObjID = ObjID,
                            PromotionIDNet = PNET,
                            PromotionIDTV = PTV,
                            MoneyPromotionIDNet = MNET,
                            MoneyPromotionIDTV = MTV,
                            VoucherCode = Voucher,
                            AddBy = SalesManID
                        },
                        commandType: CommandType.StoredProcedure
                    );
                }
            }
        }

        private void AddCustomerDiscountV2(int ObjID, int PromotionNetID, int IPTVPromotionID, int MoneyPromotionNETID, int MoneyPromotionTVID,
            string VoucherCode, int SalesID, int IsEvenMonth, int? GeneralCodeID = null, int? PrivateCodeID = null, int? IPTVID = null)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Execute(
                    "PowerInside.dbo.OS6_FPTVoucher_AddCustomerDiscountV2",
                    new
                    {
                        ObjID = ObjID,
                        PromotionIDNet = PromotionNetID,
                        PromotionIDTV = IPTVPromotionID,
                        MoneyPromotionIDNet = MoneyPromotionNETID,
                        MoneyPromotionIDTV = MoneyPromotionTVID,
                        VoucherCode = VoucherCode,
                        AddBy = SalesID,
                        GeneralCodeID = GeneralCodeID,
                        PrivateCodeID = PrivateCodeID,
                        IsEvenMonth = IsEvenMonth,
                        IPTVID = IPTVID
                    },
                    commandType: CommandType.StoredProcedure
                );
            }
        }

        private bool CheckValidObj(int Obj)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                int res = connection.Query<int>(API.OS6_FPTVoucher_CSAPI, new
                {
                    ActionName = ActionName.ValidateObjID,
                    ObjID = Obj,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return (res > 0);
            }
        }

        private List<PE> GetListPE(int Owner, int LocalType, int ApplyTimeType)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<PE>(API.OS6_FPTVoucher_CSAPI, new
                {
                    ActionName = ActionName.GETPE,
                    IDOwner = Owner,
                    Localtype = LocalType,
                    ApplyTimeTypeID = ApplyTimeType,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure).DistinctBy(x => x.ID).ToList();
            }
        }
        private bool CheckQuotarVoucher(int objid, int promotionEventID)
        {
            int res = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                res = connection.QueryFirstOrDefault<int>(API.OS6_FPTVoucher_CSAPI, new
                {
                    ActionName = ActionName.CheckQoutar,
                    ObjID = objid,
                    PromotionEventID = promotionEventID,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure);
            }
            return (res == 1);
        }

        private int Redeem(RedeemInput input, PE pe)
        {
            L.Mes(Level.ERROR, JsonConvert.SerializeObject(new { input, pe }));

            int generalCodeID = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        generalCodeID = connection.QueryFirstOrDefault<int>(API.OS6_FPTVoucher_CSAPI, new
                        {
                            ActionName = ActionName.Redeem,
                            EventCode = input.VoucherCode,
                            PromotionEventID = pe.ID,
                            LocationID = 0,
                            DepartmentID = 0,
                            SaleID = 0,
                            ObjID = input.ObjID,
                            OrderCode = 0,
                            ActiveChannel = 3,
                            IdVoucherPlaning = input.IdVoucherPlaning,
                            RowAffected = 0,
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        L.Mes(Level.ERROR, ex.ToString());
                    }
                }
            }
            return generalCodeID;
        }

        // wip
        private bool CheckValidActive(int obj)
        {
            int tempValid;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                tempValid = connection.QueryFirstOrDefault<int>(API.OS6_FPTVoucher_CSAPI, new
                {
                    ActionName = ActionName.CheckValidActive,
                    ObjID = obj,
                    RowAffected = 0
                }, commandType: CommandType.StoredProcedure);
            }
            return (tempValid == 0);
        }
        #endregion

    }
}
