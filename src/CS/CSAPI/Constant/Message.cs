using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CSAPI.Constant
{
    public class Message
    {
        private Message(string value) { Value = value; }

        public string Value { get; set; }

        public static Message ObjNotValid { get { return new Message("HD Không Hợp Lệ"); } }
        public static Message VoucherNotFound { get { return new Message("Không Tìm Thấy Danh Sách Voucher"); } }
        public static Message RedeemNotValid { get { return new Message("Hợp Đồng Đang Hưởng Voucher, Không Kích Hoạt Được Voucher Mới"); } }
        public static Message EventCodeNotFound { get { return new Message("Mã Voucher Không Hợp Lệ"); } }
        public static Message RedeemFalse { get { return new Message("Redeem Thất Bại. Vui Lòng Liên Hệ Trung Tâm ISC"); } }
        public static Message ContractNotValid { get { return new Message("Trạng Thái Hợp Đồng <PERSON>hông Th<PERSON> Mã Voucher"); } }

        public static Message VCFTIER { get { return new Message("Voucher Không Áp Dụng Các HD FTI"); } }
    }
}