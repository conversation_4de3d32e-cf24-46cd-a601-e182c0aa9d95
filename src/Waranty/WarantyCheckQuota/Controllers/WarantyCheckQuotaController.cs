using Dapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using WarantyCheckQuota.Models;

namespace WarantyCheckQuota.Controllers
{
    public class WarantyCheckQuotaController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_FPTVoucher_WarantyCheckQuota";
        [Route("API/Waranty/CheckQuota")]
        public ResponseModel post(CheckQuotaParam input)
        {
            var error = "";
            var _reponse = new { status_check = false };
            try
            {
                
                if (input.listVouchers.Count()>0)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        connection.Open();
                        int isok = 0;
                        foreach (var item in input.listVouchers)
                        {
                            var rs = connection.ExecuteScalar<int>(sp, new { ObjID = input.ObjID, Voucher = item.Voucher, Quantity = item.Quantity }, commandType: CommandType.StoredProcedure);
                            if (rs > 0)
                                isok += 1;
                        }
                        if(input.listVouchers.Count() == isok)
                            _reponse = new { status_check = true }; 
                    }
                }
                else
                {
                    error = "Wrong input";
                }
            }
            catch (Exception e) { error = e.Message; }
            L.Mes(Level.INFO, JsonConvert.SerializeObject(input));
            return new ResponseModel() { data = _reponse, error = error, result = 1 };
        }
    }
}
