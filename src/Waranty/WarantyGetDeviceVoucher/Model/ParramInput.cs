using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace WarantyGetDeviceVoucher.Model
{
    public class ParramInput
    {
        public int ObjID { set; get; }
        public int OrderCode { set; get; }
        public List<VoucherUse> VoucherUse { set; get; }
    }
    public class VoucherUse
    {
        public int VoucherID { set; get; }
        public int QuotaUse { set; get; }
    }

    public class DeviceVoucherOutputMBN
    {       
        public int VoucherID { set; get; }
        public int CodeID { set; get; }
        public string Description { set; get; }
        public int Quota { set; get; }
        public decimal Money { set; get; }
    }
    public class DeviceVoucherOutput
    {
        public int DeviceID { set; get; }
        public string DeviceName { set; get; }
        public string EventCode { set; get; }
        public int Quota { set; get; }
        public decimal DeviceDiscountAmount { set; get; }
    }
    public class MailModelInput
    {
        public string FromEmail { set; get; }
        public string Recipients { set; get; }
        public string CarbonCopys { set; get; }
        public string BlindCarbonCopys { set; get; }
        public string Subject { set; get; }
        public string Body { set; get; }
        public string AttachFile { set; get; }
        public string AttachUrl { set; get; }
    }
}