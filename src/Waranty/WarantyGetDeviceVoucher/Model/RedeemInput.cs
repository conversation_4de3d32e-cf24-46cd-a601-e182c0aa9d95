using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using Voucher.APIHelper;

namespace WarantyGetDeviceVoucher.Model
{
    public class RedeemInput
    {
        public string voucherCode { get; set; }
        public int objId { get; set; }

        public int type { set; get; }
    }
    public class RedeemTLSInput
    {
        public int orderCode { get; set; }
        public string voucherCode { get; set; }
        public int objId { get; set; }

        public int type { set; get; }
    }

    public class RedeemTLSDeviceVcInput
    {
        public string orderCode { get; set; }
        public string voucherCode { get; set; }
        public int objId { get; set; }

        public int quantity { set; get; }
        public int typeRedeem { set; get; }
    }

    public class RedeemInputMBN
    {
        public string voucherCode { get; set; }
        public int orderCode { get; set; }
        public int objId { get; set; }
        public int orderType { get; set; }
    }
    public class ResponseRedeem
    {
        public int res { set; get; }
    }
    public class LoginInfor
    {
        public string username { set; get; }
        public string password { set; get; }
    }
    public class Login
    {
        public static string userName { get { return Utility.loyaltyapi_username; } }
        public static string passWord { get { return Utility.loyaltyapi_password; } }
    }

    public class AuthorizationInfor
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string scope { set; get; }
        public string iat { set; get; }
        public string jti { set; get; }
    }

    public class SendMessageHiFPT
    {
        public string contractNo { get; set; }
        public string type { get; set; }
        public string phone { get; set; }
        public voucher data { get; set; }
    }

    public class SendMessageLoyalty
    {
        public string voucherCode { get; set; }
        public string mobileHiFpt { get; set; }
    }
    public class voucher
    {
        public string voucherCode { get; set; }
    }

    public class ContactInfor
    {
        public string Phone { get; set; }
        public string Contract { get; set; }
    }
    public class SendMessageHiFPTOutput
    {
        public int statusCode { get; set; }
        public string message { get; set; }
        public object data { get; set; }
    }
    public class CheckStatusInput
    {
        public int ObjID { get; set; }
        public int OrderCode { get; set; }
        public int OrderType { get; set; }
    }
    public class CheckStatusOutput
    {
        public int statusCode { get; set; }
        public StatusCode data { get; set; }
    }
    public class StatusCode
    {
        public int status { get; set; }
    }
    public class DeviceLoyaltyMBN
    {
        public int ID { get; set; }
        public string Code { get; set; }
        public int ObjID { get; set; }
        public int OrderCode { get; set; }
        public int OrderType { get; set; }
    }
}