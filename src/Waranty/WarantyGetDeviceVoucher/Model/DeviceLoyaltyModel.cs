using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace WarantyGetDeviceVoucher.Model
{
    public class DeviceLoyaltyModel
    {
        public string code { set; get; }
        public decimal DevicePromotionLoyalty { set; get; }
        public List<DevicePrice> Devices { set; get; }
    }
    public class Device
    {
        public int DeviceID { set; get; }
        public string Description { set; get; }
        public int Status { set; get; }
        [JsonIgnore]
        public int CustomerStatus {set;get;}
        [JsonIgnore]
        public int Deployment { set; get; }
        [JsonIgnore]
        public int PromotionStatus { set; get; }
        [JsonIgnore]
        public int ReturnStatus { set; get; }
    }
    public class DevicePrice : Device
    {
        public decimal Price { set; get; }
    }
    public class DeviceStatus 
    {
        public int DeviceID { set; get; }
        public int Status { set; get; }
    }

    public class DeviceLoyaltyMBNModel
    {
        public string code { set; get; }
        public decimal DevicePromotionLoyalty { set; get; }
        public List<DevicePrice> Devices { set; get; }
    }
    public class ParapioraOutput
    {
        public List<Parapiora> data {set;get;}
    }
    public class Parapiora
    {
        public int CodeID { set; get; }
        public long Price { set; get; }
        public int ServiceCode { set; get; }
        public int CustomerStatus { set; get; }
        public int PriceID { set; get; }
        public string PriceName { set; get; }
        public string EquipmentName { set; get; }
        public int ReturnStatus { set; get; }
        public int PromotionStatus { set; get; }
        public int NumMax { set; get; }
        public int EFStatus { set; get; }
        public int Combo { set; get; }
        public int Deployment { set; get; }
        public long PriceNotVAT { set; get; }
        public string Description { set; get; }
        public string Name { set; get; }
        
    }

    public class DeviceInfor
    {
        public int CodeID { set; get; }
        public long Price { set; get; }
        public string Name { set; get; }
        public int Status { set; get; }
    }

    public class ParapioraInput
    {
        public int LocationID { set;get; }
        public string CodeID { set; get; }
    }
}