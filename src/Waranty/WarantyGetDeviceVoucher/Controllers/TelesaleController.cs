using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using WarantyGetDeviceVoucher.Model;
using WarantyGetDeviceVoucher.Service;
using Dapper;
using System.Threading.Tasks;

namespace WarantyGetDeviceVoucher.Controllers
{
    public class TelesaleController : ApiController
    {
        [Route("API/WarantyLoyalty/TLS/GetDeviceVoucherLoyalty")]
        public ResponseModel<DeviceLoyaltyModel> GetDeviceVoucherLoyalty(string voucherCode, string objID)
        {
            var LogId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(voucherCode + " " + objID), " TLS GetDeviceVoucherLoyalty req ");
            
            var error = "";
            int _result = 0;
            var _reponse = new DeviceLoyaltyModel();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    _reponse = connection.Query<DeviceLoyaltyModel>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                        new
                        {
                            actionName = "GetInforDeviceLoyaltyPE",
                            voucherCode = voucherCode,
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(_reponse), " TLS GetDeviceVoucherLoyalty _reponse ");
                    if (_reponse == null)
                    {
                        return new ResponseModel<DeviceLoyaltyModel>() { data = _reponse, error = "Voucher không hợp lệ!", result = _result };
                    }
                    //lấy location id
                    int locationID = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                        new
                        {
                            actionName = "GetLocationIDByOBJ",
                            objid = objID,
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();
                    // lấy danh sách thiết bị
                    List<Device> Devices = connection.Query<Device>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS", new
                    {
                        actionName = "GetDeviceLoyaltyPE",
                        voucherCode = voucherCode,
                    }, commandType: CommandType.StoredProcedure).ToList();

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(Devices), " TLS GetDeviceVoucherLoyalty Devices ");
                    if (Devices.Count == 0)
                    {
                        return new ResponseModel<DeviceLoyaltyModel>() { data = null, error = "Voucher không hợp lệ!", result = 0 };
                    }

                    string codeids = string.Join(",", Devices.Select(c => c.DeviceID));
                    var req = new ParapioraInput
                    {
                        LocationID = locationID,
                        CodeID = codeids
                    };
                    List<DeviceInfor> lstDevice = DeviceLoyaltyService.GetEFcodeWithPrice(req, Devices, LogId.ToString());
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(Devices), " TLS GetDeviceVoucherLoyalty lstDevice ");
                    if (lstDevice.Count == 0)
                    {
                        error = "Không có thiết bị phù hợp";
                    }
                    _reponse.Devices = lstDevice.Select(c => new DevicePrice { DeviceID = c.CodeID, Description = c.Name, Price = c.Price, Status = c.Status }).GroupBy(c => c.DeviceID).Select(g => g.First()).ToList();
                    _result = 1;
                }
            }
            catch (Exception e)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(e.Message), " TLS GetDeviceVoucherLoyalty Error ");
                error = e.Message;
            }
            return new ResponseModel<DeviceLoyaltyModel>() { data = _reponse, error = error, result = _result };
        }

        [HttpPost]
        [Route("API/WarantyLoyalty/TLS/RedeemVoucherDeviceLoyalty")]
        public async Task<ResponseModel> RedeemVoucherDeviceLoyalty(RedeemTLSInput input)
        {
            var error = "";
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(input), " TLS RedeemVoucherDeviceLoyalty req ");
            int _result = 0;
            DeviceVoucherTLS dvt = new DeviceVoucherTLS { response = false, message = "Thất bại" };
            try
            {
                if (input.type == 1)
                {
                    #region Active voucher
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        connection.Open();
                        using (var transaction = connection.BeginTransaction())
                        {
                            int res = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                            new
                            {
                                actionName = "RedeemDeviceLoyalty",
                                objid = input.objId,
                                voucherCode = input.voucherCode,
                                orderCode = input.orderCode
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(res), " TLS RedeemVoucherDeviceLoyalty res ");
                            if (res > 0)
                            {
                                transaction.Commit();
                                dvt.response = true;
                                dvt.message = "Tạm ứng voucher thành công";
                                _result = 1;
                            }                            
                        }
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(dvt), " TLS RedeemVoucherDeviceLoyalty dvt ");                     
                    }
                    #endregion
                }
                else if (input.type == 2)
                {
                    #region rollbackVoucher
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        bool isRedeem = DeviceLoyaltyService.CheckRedeemVoucher(input.voucherCode, input.objId);
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(isRedeem), " TLS RedeemVoucherDeviceLoyalty isRedeem ");
                        if (!isRedeem)
                        {
                            return new ResponseModel() { data = 0, error = "Chưa Redeem mã", result = _result };
                        }
                        connection.Open();
                        using (var transaction = connection.BeginTransaction())
                        {
                            int res = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                            new
                            {
                                actionName = "RollBackVC",
                                objid = input.objId,
                                voucherCode = input.voucherCode,
                                orderCode = input.orderCode
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(res), " TLS RedeemVoucherDeviceLoyalty rb ");   
                            if (res > 0)
                            {
                                transaction.Commit();
                                dvt.response = true;
                                dvt.message = "Hoàn trả voucher thành công";
                                _result = 1;
                            } 
                        }
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(dvt), " TLS RedeemVoucherDeviceLoyalty dvt ");  

                        _result = 1;
                    }
                    #endregion
                }
                else if (input.type == 3)
                {
                    #region xác nhận sử dụng voucher
                    bool isRedeem = DeviceLoyaltyService.CheckRedeemVoucher(input.voucherCode, input.objId);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(isRedeem), " TLS RedeemVoucherDeviceLoyalty isRedeem ");
                    if (!isRedeem)
                    {
                        return new ResponseModel() { data = 0, error = "Chưa Redeem mã", result = _result };
                    }
                    var data = new ContactInfor();
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {                        
                        connection.Open();
                        using (var transaction = connection.BeginTransaction())
                        {
                            data = connection.Query<ContactInfor>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                            new
                            {
                                actionName = "RedeemVCGetInfor",
                                objid = input.objId,
                                voucherCode = input.voucherCode,
                                orderCode = input.orderCode
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(data), " TLS RedeemVoucherDeviceLoyalty data ");
                            bool bSend = await Common.ChangeStatusLoyalty(data.Phone, input.voucherCode, logId.ToString());
                            if (bSend == true)
                            {
                                transaction.Commit();
                                dvt.response = true;
                                dvt.message = "Sử dụng voucher thành công";
                                _result = 1;
                            }
                        }   
                    }
                    #endregion
                }
            }
            catch (Exception e)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(e.Message), " TLS RedeemVoucherDeviceLoyalty Error ");
                error = e.Message;
            }
            return new ResponseModel() { data = dvt, error = error, result = _result };
        }

        [Route("API/Waranty/TLS/GetDeviceVoucher")]
        public ResponseModel GetDeviceVoucher(long ObjID)
        {
            var error = "";
            int _result = 0;
            var LogId = Guid.NewGuid();
            var _reponse = new List<DeviceVoucherOutput>();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ObjID), " TLS GetDeviceVoucher req ");
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    _reponse = connection.Query<DeviceVoucherOutput>("PowerInside.dbo.OS6_FPTVoucher_WarantyGetDeviceVoucher", new { ObjID = ObjID },
                        commandType: CommandType.StoredProcedure).ToList();
                    
                    _result = 1;
                }
                // kiểm tra khách hàng thuộc tập thay thế
                bool isCusReplaceDevice = DeviceCSService.CheckObj(ObjID);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(isCusReplaceDevice), " TLS GetDeviceVoucher isCusReplaceDevice ");
                if (isCusReplaceDevice)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                    {
                        connection.Open();
                        var listVoucher = connection.Query<DeviceVoucherOutput>("PowerInside.dbo.OS6_FPTVoucher_SpecialWarantyGetDeviceVoucher", 
                            new { ObjID = ObjID }, commandType: CommandType.StoredProcedure).ToList();
                        _reponse.AddRange(listVoucher);
                    }
                }
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(_reponse), " TLS GetDeviceVoucher _reponse ");
                
                //_reponse = _reponse.Where(x => x.Quota > 0).ToList();
            }
            catch (Exception e) 
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(e.Message), " TLS GetDeviceVoucher Error ");
                error = e.Message; 
            }
            return new ResponseModel() { data = _reponse, error = error, result = _result };
        }

        [HttpPost]
        [Route("API/Waranty/TLS/RedeemDeviceVoucher")]
        public ResponseModel<DeviceVoucherTLS> RedeemDeviceVoucher(RedeemTLSDeviceVcInput input)
        {
            var LogId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(input), " TLS RedeemDeviceVoucher req ");
            var result = new DeviceVoucherTLS { response = false, message = "" };
            int kq = 0;
            try
            {

                bool checkRedeem = DeviceCSService.CheckRedeemVoucherDevice(input.voucherCode, input.objId, input.orderCode);

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(checkRedeem), " TLS RedeemDeviceVoucher checkRedeem ");

                if (checkRedeem && input.typeRedeem == 1)//input.typeRedeem =1 active mới, input.typeRedeem=0 thu hồi
                {
                    return new ResponseModel<DeviceVoucherTLS>
                    {
                        data = new DeviceVoucherTLS { response = false, message = "Phiếu đã được active voucher" },
                        error = "Thao tác không thành công",
                        result = -1
                    };
                }
                else if (checkRedeem && input.typeRedeem == 0)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        connection.Open();
                        int res = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                        new
                        {
                            actionName = "RollbackDeviceVoucher",
                            objid = input.objId, 
                            orderCodeGC = input.orderCode,
                            voucherCode = input.voucherCode,
                            Quantity = input.quantity
                        },
                        commandType: CommandType.StoredProcedure);
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(res), " TLS RedeemDeviceVoucher RB res ");
                        if (res > 0)
                        {
                            result.response = true;
                            result.message = "Thu hồi voucher thành công";
                            kq = 1;
                        }
                        else
                        {
                            result.response = false;
                            result.message = "Thu hồi voucher không thành công";
                        }
                        return new ResponseModel<DeviceVoucherTLS> { data=result,error=null,result=kq};
                    }
                }
                else if (!checkRedeem && input.typeRedeem == 1)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        connection.Open();
                        int res = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                        new
                        {
                            actionName = "redeemDeviceVoucher",
                            objid = input.objId,
                            orderCodeGC = input.orderCode,
                            voucherCode = input.voucherCode,
                            Quantity = input.quantity
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();

                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(res), " TLS RedeemDeviceVoucher AC res ");
                        if (res > 0)
                        {
                            result.response = true;
                            result.message = "Active voucher thành công";
                            kq = 1;
                        }
                        else
                        {
                            result.response = false;
                            result.message = "Active voucher không thành công";
                        }
                        return new ResponseModel<DeviceVoucherTLS> { data = result, error = null, result = kq };
                    }
                }
                else return new ResponseModel<DeviceVoucherTLS> { data = result, error = "Không thành công", result = kq };
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " TLS RedeemDeviceVoucher Error ");
                return new ResponseModel<DeviceVoucherTLS>
                {
                    data = new DeviceVoucherTLS { response = false, message = "Thao tác không thành công" },
                    error = ex.Message,
                    result = -1
                };
            }
        }
    }

}