using Dapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using WarantyGetDeviceVoucher.Model;
using WarantyGetDeviceVoucher.Service;

namespace WarantyGetDeviceVoucher.Controllers
{
    public class WarantyGetDeviceVoucherController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_FPTVoucher_WarantyGetDeviceVoucher";
        const string OS6_FPTVoucher_SpecialWarantyGetDeviceVoucher = "PowerInside.dbo.OS6_FPTVoucher_SpecialWarantyGetDeviceVoucher";
        [Route("API/Waranty/GetDeviceVoucher")]
        public ResponseModel get(long ObjID)
        {
            var error = "";
            int _result = 0;
            var _reponse = new List<DeviceVoucherOutput>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    _reponse = connection.Query<DeviceVoucherOutput>(sp, new { ObjID = ObjID }, commandType: CommandType.StoredProcedure).ToList();
                    _result = 1;
                }
                // kiểm tra khách hàng thuộc tập thay thế
                bool isCusReplaceDevice = DeviceCSService.CheckObj(ObjID);
                if (isCusReplaceDevice)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                    {
                        connection.Open();
                        var listVoucher = connection.Query<DeviceVoucherOutput>(OS6_FPTVoucher_SpecialWarantyGetDeviceVoucher, new { ObjID = ObjID }, commandType: CommandType.StoredProcedure).ToList();
                        _reponse.AddRange(listVoucher);
                    }                    
                }

                L.Mes(Level.INFO, JsonConvert.SerializeObject(_reponse), " GetDeviceVoucher: ");
                //_reponse = _reponse.Where(x => x.Quota > 0).ToList();
            }
            catch (Exception e) { error = e.Message; }
            return new ResponseModel() { data = _reponse, error = error, result = _result };
        }


        [Route("API/Waranty/GetDeviceVoucherMBN")]
        public ResponseModel GetDeviceVoucherMBN(long ObjID)
        {
            var error = "";
            int _result = 0;
            var _reponse = new List<DeviceVoucherOutputMBN>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))   
                {
                    connection.Open();
                    _reponse = connection.Query<DeviceVoucherOutputMBN>("PowerInside.dbo.OS6_FPTVoucher_WarantyGetDeviceVoucherMBN", new { ObjID = ObjID }, commandType: CommandType.StoredProcedure).ToList();
                    _result = 1;
                }
                // kiểm tra khách hàng thuộc tập thay thế
                bool isCusReplaceDevice = DeviceCSService.CheckObj(ObjID);
                if (isCusReplaceDevice)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                    {
                        connection.Open();
                        var listVoucher = connection.Query<DeviceVoucherOutputMBN>("PowerInside.dbo.OS6_FPTVoucher_SpecialWarantyGetDeviceVoucherMBN", new { ObjID = ObjID }, commandType: CommandType.StoredProcedure).ToList();
                        _reponse.AddRange(listVoucher);
                    }
                }
                L.Mes(Level.INFO, JsonConvert.SerializeObject(_reponse), " GetDeviceVoucher: ");
            }
            catch (Exception e) { error = e.Message; }
            return new ResponseModel() { data = _reponse, error = error, result = _result };
        }

        [Route("API/Waranty/GetMonitorVoucherCS")]
        public ResponseModel GetMonitorVoucherCS(long ObjID)
        {
            var error = "";
            int _result = 0;
            var _reponse = new List<MonitorVoucherCS>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    _reponse = connection.Query<MonitorVoucherCS>("PowerInside.dbo.OS6_FPTVoucher_WarantyInforVoucherDeviceCS", new { ObjID = ObjID }, commandType: CommandType.StoredProcedure).ToList();
                    _result = 1;
                }
                // kiểm tra khách hàng thuộc tập thay thế
                bool isCusReplaceDevice = DeviceCSService.CheckObj(ObjID);
                if (isCusReplaceDevice)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                    {
                        connection.Open();
                        var listVoucher = connection.Query<MonitorVoucherCS>("PowerInside.dbo.OS6_FPTVoucher_SpecialWarantyInforVoucherDeviceCS", new { ObjID = ObjID }, commandType: CommandType.StoredProcedure).ToList();
                        _reponse.AddRange(listVoucher);
                    }
                }
                L.Mes(Level.INFO, JsonConvert.SerializeObject(_reponse), " GetDeviceVoucher: ");
            }
            catch (Exception e) { error = e.Message; }
            return new ResponseModel() { data = _reponse, error = error, result = _result };
        }

        [HttpPost]
        [Route("API/Waranty/InsertNewCode")]
        public ResponseModel<VoucherUseOutput> InsertNewCode(ParramInput input)
        {
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(input), "InsertNewCode");
            var res = new VoucherUseOutput();
            var error = "";
            int isExist = 1;
            if (input.VoucherUse.Count == 0)
            {
                res.ID = 0;
                res.Description = "Chưa nhập dữ liệu Voucher Use";
                return new ResponseModel<VoucherUseOutput>() { data = res, error = error };
            }
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    isExist = connection.QueryFirstOrDefault<int>("PowerInside.dbo.OS6_FPTVoucher_WarantyAddGeneralCodeFromOrderID",
                        new { actionName = "CheckOrderCode", orderCode = input.OrderCode }, commandType: CommandType.StoredProcedure);
                }
                int resp = 0;
                if (isExist == 0)
                {
                    foreach (var item in input.VoucherUse)
                    {
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {
                            connection.Open();
                            resp = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_WarantyAddGeneralCodeFromOrderID",
                                new
                                {
                                    actionName = "InsertNewCode",
                                    Quota = item.QuotaUse,
                                    promotionEventID = item.VoucherID,
                                    objID = input.ObjID,
                                    orderCode = input.OrderCode
                                }, commandType: CommandType.StoredProcedure);
                        }
                    }
                }
                if (isExist == 1)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        // huy
                        // KhanhHC2 2020-06-09 khong goi action nay nhieu lan, khi tao lai nhieu voucher, voucher vua tao se bi huy
                        resp = connection.Execute(
                            "PowerInside.dbo.OS6_FPTVoucher_WarantyAddGeneralCodeFromOrderID",
                            new
                            {
                                actionName = "OrderCodeIsExists",
                                objID = input.ObjID,
                                orderCode = input.OrderCode
                            },
                            commandType: CommandType.StoredProcedure
                        );

                        if (resp > 0)
                        {
                            // tao lai
                            foreach (var item in input.VoucherUse)
                            {
                                if (item.VoucherID > 0 && item.QuotaUse > 0)
                                {
                                    int rs = connection.Execute(
                                        "PowerInside.dbo.OS6_FPTVoucher_WarantyAddGeneralCodeFromOrderID",
                                        new
                                        {
                                            actionName = "InsertNewCode",
                                            Quota = item.QuotaUse,
                                            promotionEventID = item.VoucherID,
                                            objID = input.ObjID,
                                            orderCode = input.OrderCode
                                        },
                                        commandType: CommandType.StoredProcedure
                                    );
                                    L.Mes(Level.REQUEST, JsonConvert.SerializeObject(item) + "|" + rs, "InsertNewCode");
                                }
                            }
                        }
                    }
                }
                if (resp > 0)
                {
                    res.ID = 1;
                    res.Description = "Thành công";
                }
                else
                {
                    res.ID = 0;
                    res.Description = "Thất bại";
                }
            }
            catch (Exception e)
            {
                res.ID = 0;
                res.Description = e.Message;
            }
            return new ResponseModel<VoucherUseOutput>() { data = res, error = error };
        }
    }
}
