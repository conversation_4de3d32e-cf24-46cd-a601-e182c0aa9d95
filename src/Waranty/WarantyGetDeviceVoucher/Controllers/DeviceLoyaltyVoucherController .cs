using Dapper;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using WarantyGetDeviceVoucher.Model;
using System.IO;
using System.Configuration;
using WarantyGetDeviceVoucher.Service;

namespace WarantyGetDeviceVoucher.Controllers
{
    public class DeviceLoyaltyVoucherController : ApiController
    {
        //const string sp = "PowerInside.dbo.OS6_FPTVoucher_WarantyGetDeviceVoucher";
        [Route("API/WarantyLoyalty/GetDeviceVoucherLoyalty")]
        public ResponseModel<DeviceLoyaltyModel> GetDeviceVoucherLoyalty(string voucherCode, string objID)
        {
            var LogId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(voucherCode), "GetDeviceVoucherLoyalty Voucher ");
            var error = "";
            int _result = 0;
            var _reponse = new DeviceLoyaltyModel();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    _reponse = connection.Query<DeviceLoyaltyModel>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                        new
                        {
                            ActionName = "GetInforDeviceLoyaltyPE",
                            code = voucherCode,
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (_reponse == null)
                    {
                        return new ResponseModel<DeviceLoyaltyModel>() { data = _reponse, error = "Voucher không hợp lệ!", result = _result };
                    }
                    //lấy location id
                    int locationID = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                        new
                        {
                            ActionName = "GetLocationIDByOBJ",
                            objId = objID,
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();
                    // lấy danh sách thiết bị
                    List<Device> Devices = connection.Query<Device>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent", new
                    {
                        ActionName = "GetDeviceLoyaltyPE",
                        code = voucherCode,
                    }, commandType: CommandType.StoredProcedure).ToList();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(Devices), "GetDeviceVoucherLoyalty Devices ");
                    if (Devices.Count == 0)
                    {
                        return new ResponseModel<DeviceLoyaltyModel>() { data = null, error = "Voucher không hợp lệ!", result = 0 };
                    }

                    string codeids = string.Join(",", Devices.Select(c => c.DeviceID));
                    var req = new ParapioraInput
                    {
                        LocationID = locationID,
                        CodeID = codeids
                    };
                    List<DeviceInfor> lstDevice = DeviceLoyaltyService.GetEFcodeWithPrice(req, Devices,LogId.ToString());
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(lstDevice), "GetDeviceVoucherLoyalty lstDevice ");
                    if (lstDevice.Count == 0)
                    {
                        error = "Không có thiết bị phù hợp";
                    }
                    _reponse.Devices = lstDevice.Select(c => new DevicePrice { DeviceID = c.CodeID, Description = c.Name, Price = c.Price, Status = c.Status }).ToList();
                    _result = 1;
                }
            }
            catch (Exception e) 
            {
                L.Mes(Level.ERROR, e.Message, "GetDeviceVoucherLoyalty");
                error = e.Message; 
            }
            return new ResponseModel<DeviceLoyaltyModel>() { data = _reponse, error = error, result = _result };
        }

        [HttpPost]
        [Route("API/WarantyLoyalty/RedeemVoucherDeviceLoyalty")]
        public async Task<ResponseModel> RedeemVoucherDeviceLoyalty(RedeemInput input)
        {
            var LogId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(input), "RedeemVoucherDeviceLoyalty req ");
            var error = "";
            int _result = 0;
            var _reponse = new ResponseRedeem();
            try
            {
                if (input.type == 1)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        connection.Open();
                        using (var transaction = connection.BeginTransaction())
                        {
                            _reponse.res = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                            new
                            {
                                actionName = "RedeemDeviceLoyalty",
                                ObjID = input.objId,
                                VoucherCode = input.voucherCode
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                            transaction.Commit();
                        }
                        L.Mes(Level.INFO, "Redeem Voucher respose: " + _reponse.res, "RedeemVoucherDeviceLoyalty");
                        _result = 1;
                    }
                }
                else if (input.type == 2)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        bool isRedeem = CheckRedeemVoucher(input.voucherCode, input.objId);
                        L.Mes(Level.INFO, "isRedeem: " + isRedeem.ToString(), "RedeemVoucherDeviceLoyalty");
                        if (!isRedeem)
                        {
                            return new ResponseModel() { data = 0, error = "Chưa Redeem mã", result = _result };
                        }
                        connection.Open();
                        using (var transaction = connection.BeginTransaction())
                        {
                            _reponse.res = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                            new
                            {
                                actionName = "RollBackVC",
                                ObjID = input.objId,
                                VoucherCode = input.voucherCode
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                            transaction.Commit();
                        }
                        L.Mes(Level.INFO, "RollBank RedeemVoucherDeviceLoyalty response: " + _reponse.res, "RedeemVoucherDeviceLoyalty");
                        _result = 1;
                    }
                }
                else if (input.type == 3)
                {
                    var data = new ContactInfor();
                    bool isRedeem = CheckRedeemVoucher(input.voucherCode, input.objId);
                    if (!isRedeem)
                    {
                        return new ResponseModel() { data = 0, error = "Chưa Redeem mã", result = _result };
                    }
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        connection.Open();
                        using (var transaction = connection.BeginTransaction())
                        {
                            data = connection.Query<ContactInfor>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                            new
                            {
                                actionName = "UpdateStatusLoyalty",
                                ObjID = input.objId,
                                VoucherCode = input.voucherCode,
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                            L.Mes(Level.INFO, "Start to ChangeStatusLoyalty to phone " + data.Phone + " và " + data.Contract, "ChangeStatusLoyalty");
                            bool bSend = await ChangeStatusLoyalty(data.Phone, input.voucherCode, LogId.ToString());
                            if (bSend == true)
                            {
                                transaction.Commit();
                                _reponse.res = 1;
                                _result = 1;
                            }
                        }
                    }

                }
            }
            catch (Exception e)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(e.Message), "RedeemVoucherDeviceLoyalty Error ");
                error = e.Message;
            }
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(_reponse), "RedeemVoucherDeviceLoyalty res ");
            return new ResponseModel() { data = _reponse, error = error, result = _result };
        }

        [HttpPost]
        [Route("API/WarantyLoyalty/TestAPIloy")]
        public async Task<bool> TestAPIloy(string voucher, string phone)
        {
            var logid = Guid.NewGuid();
            try
            {
                bool isSend = await ChangeStatusLoyalty(phone, voucher, logid.ToString());
                L.Mes(Level.ERROR, isSend.ToString(), "TestAPIloy");
                return isSend;
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "TestAPIloy");
                return false;
            }
            
        }

        public string HashMD5(string s)
        {
            using (var provider = System.Security.Cryptography.MD5.Create())
            {
                StringBuilder builder = new StringBuilder();

                foreach (byte b in provider.ComputeHash(Encoding.UTF8.GetBytes(s)))
                    builder.Append(b.ToString("x2").ToLower());

                return builder.ToString();
            }
        }
        private async Task<Boolean> SendNotiHiFPT(string phone, string contract, string voucherCode)
        {
            string clientKey = "inside";
            string secretKey = "072020ewrerfghauiobnhgfh";
            string authorization = HashMD5(clientKey + "::" + secretKey + DateTime.Now.ToString("yyyy-dd-MM"));
            SendMessageHiFPTOutput result = new SendMessageHiFPTOutput();
            try
            {
                var request = new SendMessageHiFPT()
                {
                    contractNo = contract,
                    type = "use_voucher_success",
                    phone = phone,
                    data = new voucher() { voucherCode = voucherCode }
                };

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(Utility.hi_fpt_api);
                    client.DefaultRequestHeaders.Add("Authorization", authorization);
                    //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = await client.PostAsync("/hi-customer-local/loyalty/inside-send-notify", data);
                    string r = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, r, "SendNotiHiFPT");
                    result = JsonConvert.DeserializeObject<SendMessageHiFPTOutput>(r);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "SendNotiHiFPT");
                return false;
            }

            if (result.statusCode == 0)
            {
                return true;
            }

            return false;
        }
        private bool CheckRedeemVoucher(string voucherCode, int objId)
        {
            bool isRedeem = false;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                string checkRedeem = connection.Query<string>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                new
                {
                    actionName = "CheckRedeemVC",
                    ObjID = objId,
                    VoucherCode = voucherCode
                },
                commandType: CommandType.StoredProcedure).FirstOrDefault();
                if (string.IsNullOrEmpty(checkRedeem))
                {
                    return isRedeem;
                }
                else
                {
                    isRedeem = true;
                }
            }
            return isRedeem;
        }
        private async Task<Boolean> ChangeStatusLoyalty(string phone, string voucherCode, string LogId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(phone), "ChangeStatusLoyalty req ");
            LoyaltyRedeemOutput lro = new LoyaltyRedeemOutput();
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = getAuthorInfor(login);
                var request = new SendMessageLoyalty()
                {
                    mobileHiFpt = phone,
                    voucherCode = voucherCode
                };

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = await client.PostAsync("/loyalty-services/api/integration-evoucher/redeem-device", data);
                    string result = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), " ChangeStatusLoyalty APIres " + voucherCode);
                    lro = JsonConvert.DeserializeObject<LoyaltyRedeemOutput>(result);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " ChangeStatusLoyalty APIres Error " + voucherCode);
                return false;
            }

            if (lro.statusCode.ToUpper().Equals("SUCCESS"))
            {
                return true;
            }

            return false;
        }

        private AuthorizationInfor getAuthorInfor(LoginInfor login)
        {
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                string authInfo = login.username + ":" + login.password;
                authInfo = Convert.ToBase64String(Encoding.Default.GetBytes(authInfo));

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(WebAPIHelper.loyaltyapi + "/loyalty-services/oauth/token?grant_type=client_credentials");
                request.Method = "POST";
                request.Accept = "application/json; charset=utf-8";

                request.Headers["Authorization"] = "Basic " + authInfo;

                var response = (HttpWebResponse)request.GetResponse();

                string strResponse = "";
                using (var sr = new StreamReader(response.GetResponseStream()))
                {
                    strResponse = sr.ReadToEnd();
                }
                L.Mes(Level.INFO, strResponse, "getAuthorInfor");
                res = JsonConvert.DeserializeObject<AuthorizationInfor>(strResponse);
                //return strResponse;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, ex.Message, "getAuthorInfor");
                return null;
            }
            return res;
        }

        [Route("API/WarantyLoyalty/GetDeviceVoucherLoyaltyMBN")]
        public ResponseModel<DeviceLoyaltyMBNModel> GetDeviceVoucherLoyaltyMBN(string voucherCode,int objID = 0)
        {
            L.Mes(Level.REQUEST, JsonConvert.SerializeObject(voucherCode), "GetDeviceVoucherLoyaltyMBN");
            string keylog = string.Concat(DateTime.Now.ToString("yyyyMMddHHmmsstt"), DeviceLoyaltyService.RandomString(16));
            var error = "";
            int _result = 0;
            var _reponse = new DeviceLoyaltyMBNModel();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    _reponse = connection.Query<DeviceLoyaltyMBNModel>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                        new
                        {
                            ActionName = "GetInforDeviceLoyaltyPE",
                            code = voucherCode,
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (_reponse == null)
                    {
                        return new ResponseModel<DeviceLoyaltyMBNModel>() { data = _reponse, error = "Voucher không hợp lệ!", result = _result };
                    }
                    //lấy location id
                    int locationID = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                        new
                        {
                            ActionName = "GetLocationIDByOBJ",
                            objId = objID,
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();
                    // lấy danh sách thiết bị
                    List<Device> Devices = connection.Query<Device>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent", new
                    {
                        ActionName = "GetDeviceLoyaltyPE",
                        code = voucherCode,
                    }, commandType: CommandType.StoredProcedure).ToList();
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(Devices), keylog + "GetDeviceVoucherLoyaltyMBN DeviceList: ");
                    if (Devices.Count == 0)
                    {                        
                        return new ResponseModel<DeviceLoyaltyMBNModel>() { data = null, error = "Voucher không hợp lệ!", result = 0 };
                    }

                    string codeids = string.Join(",",Devices.Select(c => c.DeviceID));
                    var req = new ParapioraInput
                    {
                        LocationID = locationID,
                        CodeID = codeids
                    };
                    List<DeviceInfor> lstDevice = DeviceLoyaltyService.GetEFcodeWithPrice(req, Devices, keylog);
                    if (lstDevice.Count == 0)
                    {
                        error = "Không có thiết bị phù hợp";
                    }
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(lstDevice), keylog + "GetDeviceVoucherLoyaltyMBN DevicePrice: ");

                    _reponse.Devices = lstDevice.Select(c => new DevicePrice { DeviceID = c.CodeID, Description = c.Name, Price = c.Price, Status = c.Status }).ToList();
                    _result = 1;
                }
            }
            catch (Exception e) { error = e.Message; }
            return new ResponseModel<DeviceLoyaltyMBNModel>() { data = _reponse, error = error, result = _result };
        }

        [HttpPost]
        [Route("API/WarantyLoyalty/RedeemVoucherDeviceLoyaltyMBN")]
        public async Task<ResponseModel> RedeemVoucherDeviceLoyaltyMBN(RedeemInputMBN input)
        {
            var LogId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now)+" " + JsonConvert.SerializeObject(input), " RedeemVoucherDeviceLoyaltyMBN ");
            var error = "";
            int _result = 0;
            var _reponse = new ResponseRedeem();
            string phone = string.Empty;
            try
            {
                L.Mes(Level.INFO,string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now)+" "+ "Start to check Status Support", "RedeemVoucherDeviceLoyaltyMBN");
                bool StatusSupport = await CheckStatusSupport(input.objId, input.orderCode, input.orderType);
                L.Mes(Level.INFO,string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now)+ " check Status Support: " + StatusSupport.ToString(), "RedeemVoucherDeviceLoyaltyMBN");
                if (StatusSupport)
                {
                    _reponse.res = 0;
                    return new ResponseModel() { data = _reponse, error = "Phiếu đã được hoàn ứng", result = _result };
                }
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        _reponse.res = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                        new
                        {
                            actionName = "RedeemDeviceLoyaltyMBN",
                            ObjID = input.objId,
                            VoucherCode = input.voucherCode,
                            orderCode = input.orderCode,
                            orderType = input.orderType
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO,string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now)+" "+ _reponse.res.ToString(), "RedeemVoucherDeviceLoyaltyMBN");
                        if (_reponse.res == 1)
                        {
                            _result = 1;
                            transaction.Commit();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " + e.Message, "RedeemVoucherDeviceLoyaltyMBN Error ");
                error = e.Message;
            }
            return new ResponseModel() { data = _reponse, error = error, result = _result };
        }
        /*
        [Authorize(Roles = AuthorizeRole.SuperUser)]
        [Route("API/WarantyLoyalty/ProcessRedeemDeviceLoyaltyMBN")]
        [HttpPost]
        public async Task<ResponseModel> ProcessRedeemDeviceLoyaltyMBN()
        {
            var LogId = Guid.NewGuid();
            int _result = 0;
            var data = new List<DeviceLoyaltyMBN>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    data = connection.Query<DeviceLoyaltyMBN>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                    new
                    {
                        actionName = "GetListDeviceLoyaltyMBN"
                    },
                    commandType: CommandType.StoredProcedure).ToList();
                }
                foreach (var item in data)
                {
                    L.Mes(Level.INFO,string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " "+ JsonConvert.SerializeObject(item), "ProcessRedeemDeviceLoyaltyMBN");
                    bool StatusSupport = await CheckStatusSupport(item.ObjID, item.OrderCode, item.OrderType);
                    if (StatusSupport)
                    {
                        var contactInfor = new ContactInfor();
                        using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                        {
                            contactInfor = connection.Query<ContactInfor>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                            new
                            {
                                actionName = "GetPhoneByObjID",
                                ObjID = item.ObjID
                            },
                            commandType: CommandType.StoredProcedure).FirstOrDefault();
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " + JsonConvert.SerializeObject(contactInfor), "ProcessRedeemDeviceLoyaltyMBN contactInfor ");
                        }
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {
                            connection.Open();
                            using (var transaction = connection.BeginTransaction())
                            {
                                int res = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                                new
                                {
                                    actionName = "updateDeviceLoyaltyMBN",
                                    idprivateCode = item.ID
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " res= " + res.ToString(), "ProcessRedeemDeviceLoyaltyMBN");
                                if (res > 0)
                                {
                                    L.Mes(Level.INFO, "Start to change Status Loyalty phone " + contactInfor.Phone, "ProcessRedeemDeviceLoyaltyMBN");
                                    bool isChange = await ChangeStatusLoyalty(contactInfor.Phone, item.Code,LogId.ToString());
                                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " change Status Loyalty: " + isChange.ToString(), "ProcessRedeemDeviceLoyaltyMBN");
                                    
                                    transaction.Commit();
                                    _result++;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "ProcessRedeemDeviceLoyaltyMBN");
                return new ResponseModel() { data = false, error = ex.Message, result = _result };
            }
            return new ResponseModel() { data = true, error = "", result = _result };
        }*/
        private async Task<Boolean> CheckStatusSupport(int objid, int ordercode, int type)//kiểm tra trạng thái thi công vật tư
        {
            var res = new CheckStatusOutput();
            try
            {
                var request = new CheckStatusInput()
                {
                    ObjID = objid,
                    OrderCode = ordercode,
                    OrderType = type
                };

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(Utility.parapiora_fpt_api);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = await client.PostAsync("/api/v1/RPMaintaince/check-status-support ", data);
                    string r = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO,r, "CheckStatusSupport");
                    res = JsonConvert.DeserializeObject<CheckStatusOutput>(r);
                }

                if (res.data.status == 1 && res.statusCode == 200)
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message, "CheckStatusSupport");
                return false;
            }

            return false;
        }

        /*
        [Route("API/LoyaltyOTT/UpdateStatusOTTCode")]
        [HttpPost]
        public async Task<ResponseModel<bool>> UpdateStatusOTTCode(LoyaltyOTTModel input)
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(input), " UpdateStatusOTTCode req ");
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        // kiểm tra voucher tồn tại
                        int isExist = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                        new
                        {
                            ActionName = "checkExistvoucher",
                            transCode = input.transCode,
                            code = input.code,
                            EventCode = input.eventCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(isExist), " UpdateStatusOTTCode isExist ");
                        if (isExist.Equals(0))
                        {
                            return new ResponseModel<bool> { data = false, error = "Voucher không tồn tại", result = 0 };
                        }
                        
                        int isUpdate = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                        new
                        {
                            ActionName = "UpdateStatusVoucherFPTplay",
                            transCode = input.transCode,
                            code = input.code,
                            EventCode = input.eventCode,
                            clientOTT=input.clientOTT
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(isUpdate), " UpdateStatusOTTCode isUpdate ");

                        if (isUpdate > 0)
                        {
                            transaction.Commit();
                        }
                        else 
                        {
                            return new ResponseModel<bool> { data = false, error = "Active voucher không thành công", result = 0 };
                        }
                    }     
                }
                bool bSend = await ChangeStatusLoyalty("", input.code, logId.ToString());
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(bSend), " UpdateStatusOTTCode ChangeStatusLoyalty ");
                if (bSend)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        connection.Open();
                        using (var transaction = connection.BeginTransaction())
                        {
                            int isChangeStatus = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                            new
                            {
                                ActionName = "ChangeStatusFptplay",
                                transCode = input.transCode,
                                code = input.code,
                                EventCode = input.eventCode
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(isChangeStatus), " UpdateStatusOTTCode isChangeStatus ");
                            if (isChangeStatus > 0)
                            {                                
                                transaction.Commit();
                                return new ResponseModel<bool> { data = true, error =null, result = 1 };
                            }
                            else
                            {
                                //gửi mail thông báo trường hợp lỗi
                                var req = new MailModelInput
                                {
                                    FromEmail = "<EMAIL>",
                                    Recipients = "<EMAIL> ",
                                    CarbonCopys = "",
                                    BlindCarbonCopys = "",
                                    Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                                    Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công (status = 5) FPT play", voucher = input.code, date = DateTime.Now.ToString() }),
                                    AttachFile = "",
                                    AttachUrl = ""
                                };
                                Common.SendMail(req);
                                return new ResponseModel<bool> { data = true, error = null, result = 1 };
                            }
                        }                        
                    }
                }
                else
                {
                    //gửi mail thông báo trường hợp lỗi
                    var req = new MailModelInput
                    {
                        FromEmail = "<EMAIL>",
                        Recipients = "<EMAIL> ",
                        CarbonCopys = "",
                        BlindCarbonCopys = "",
                        Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                        Body = JsonConvert.SerializeObject(new {content="Cập nhật trạng thái Loyalty không thành công FPT play", voucher=input.code, date=DateTime.Now.ToString()}),
                        AttachFile = "",
                        AttachUrl = ""
                    };
                    Common.SendMail(req);
                }                
                return new ResponseModel<bool> { data = false, error = "Thất bại", result = 0 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(ex.Message), " UpdateStatusOTTCode Error ");
                return new ResponseModel<bool> { data = false, error = ex.Message, result = 0 };
            }
        }
        */
    }
}
