using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using Dapper;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using WarantyGetDeviceVoucher.Model;

namespace WarantyGetDeviceVoucher.Service
{
    public class DeviceLoyaltyService
    {
        public static List<DeviceInfor> GetEFcodeWithPrice(ParapioraInput input, List<Device> devices,string keylog)
        {
            List<DeviceInfor> res = new List<DeviceInfor>();
            string url = string.Concat(Utility.parapiora_fpt_api.ToString(),"/api/MobiSale/Get_EFCodePriceList_MobiSale");
            //string url = "http://parapiora.fpt.vn/api/MobiSale/Get_EFCodePriceList_MobiSale";
            string sJsonData = JsonConvert.SerializeObject(input);
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(sJsonData), "GetDeviceVoucherLoyalty GetEFcodeWithPrice req ");
            var httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
            httpWebRequest.ContentType = "application/json";
            httpWebRequest.Method = "POST";
            try
            {
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(sJsonData);
                }
                var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    ParapioraOutput result = new ParapioraOutput { data= new List<Parapiora>()};
                    result = JsonConvert.DeserializeObject<ParapioraOutput>(streamReader.ReadToEnd());
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), "GetDeviceVoucherLoyalty GetEFcodeWithPrice res ");
                    foreach (var item in devices)
                    {
                        //string niemyet = convertToUnSign3(c.PriceName);
                        //List<DeviceInfor> lstpara = result.data.Where(c => c.Deployment == item.Deployment && c.EFStatus == item.Status 
                        //                                    && c.CustomerStatus == item.CustomerStatus && c.CodeID == item.DeviceID
                        //                                    && item.PromotionStatus == c.PromotionStatus && item.ReturnStatus == c.ReturnStatus)
                        //                                    .Select(a => new DeviceInfor { CodeID = a.CodeID, Price = a.Price, Name = a.EquipmentName,Status=item.Status }).ToList();
                        var lstData = result.data.Where(c => c.Deployment == item.Deployment && c.EFStatus == item.Status
                                                            && c.CustomerStatus == item.CustomerStatus && c.CodeID == item.DeviceID
                                                            && item.PromotionStatus == c.PromotionStatus && item.ReturnStatus == c.ReturnStatus).ToList();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(lstData), "GetDeviceVoucherLoyalty GetEFcodeWithPrice lstData ");
                        List<DeviceInfor> lstdevice = lstData.Select(a => new DeviceInfor { CodeID = a.CodeID, Price = a.Price, Name = a.EquipmentName, Status = item.Status }).ToList();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(lstdevice), "GetDeviceVoucherLoyalty GetEFcodeWithPrice lstData ");
                        res.AddRange(lstdevice);
                    }                    
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), "GetDeviceVoucherLoyalty GetEFcodeWithPrice Error ");
                return null;
            }
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", keylog, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(res), "GetDeviceVoucherLoyalty GetEFcodeWithPrice: ");
            return res;
        }

        public static bool CheckRedeemVoucher(string voucherCode, int objId)
        {
            bool isRedeem = false;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                string checkRedeem = connection.Query<string>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                new
                {
                    actionName = "CheckRedeemVoucherLoy",
                    ObjID = objId,
                    VoucherCode = voucherCode
                },
                commandType: CommandType.StoredProcedure).FirstOrDefault();
                if (string.IsNullOrEmpty(checkRedeem))
                {
                    return isRedeem;
                }
                else
                {
                    isRedeem = true;
                }
            }
            return isRedeem;
        }

        

        public static string RandomString(int length)
        {
            Random random = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            string res = new string(Enumerable.Repeat(chars, length)
              .Select(s => s[random.Next(s.Length)]).ToArray());
            return res;
        }

        public static string convertToUnSign3(string s)
        {
            Regex regex = new Regex("\\p{IsCombiningDiacriticalMarks}+");
            string temp = s.Normalize(NormalizationForm.FormD);
            string res = regex.Replace(temp, String.Empty).Replace('\u0111', 'd').Replace('\u0110', 'D').ToLower().Trim().Replace(" ","");
            return res;
        } 
    }
}