using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using WarantyGetDeviceVoucher.Model;

namespace WarantyGetDeviceVoucher.Service
{
    public class Common
    {
        public static string callapi_hifpt(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8")
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            var headers = wr.Headers;
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }
        public static async Task<Boolean> ChangeStatusLoyalty(string phone, string voucherCode, string LogId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(phone), "ChangeStatusLoyalty req ");
            LoyaltyRedeemOutput lro = new LoyaltyRedeemOutput();
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = getAuthorInfor(login);
                var request = new SendMessageLoyalty()
                {
                    mobileHiFpt = phone,
                    voucherCode = voucherCode
                };

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = await client.PostAsync("/loyalty-services/api/integration-evoucher/redeem-device", data);
                    string result = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), " ChangeStatusLoyalty APIres " + voucherCode);
                    lro = JsonConvert.DeserializeObject<LoyaltyRedeemOutput>(result);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " ChangeStatusLoyalty APIres Error " + voucherCode);
                return false;
            }

            if (lro.statusCode.ToUpper().Equals("SUCCESS"))
            {
                return true;
            }

            return false;
        }
        private static AuthorizationInfor getAuthorInfor(LoginInfor login)
        {
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                string authInfo = login.username + ":" + login.password;
                authInfo = Convert.ToBase64String(Encoding.Default.GetBytes(authInfo));

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(WebAPIHelper.loyaltyapi + "/loyalty-services/oauth/token?grant_type=client_credentials");
                request.Method = "POST";
                request.Accept = "application/json; charset=utf-8";

                request.Headers["Authorization"] = "Basic " + authInfo;

                var response = (HttpWebResponse)request.GetResponse();

                string strResponse = "";
                using (var sr = new StreamReader(response.GetResponseStream()))
                {
                    strResponse = sr.ReadToEnd();
                }
                L.Mes(Level.INFO, strResponse, "getAuthorInfor");
                res = JsonConvert.DeserializeObject<AuthorizationInfor>(strResponse);
                //return strResponse;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, ex.Message, "getAuthorInfor");
                return null;
            }
            return res;
        }
        public static void SendMail(MailModelInput input)
        {
            var logid = Guid.NewGuid();
            L.Mes(Level.INFO, JsonConvert.SerializeObject(input), logid + "SendMail: ");
            try
            {
                string url = string.Concat(Utility.emailApi, "/api/SendMailSMTP/InsertInfoSendMailSMTP");
                string data = JsonConvert.SerializeObject(input);
                string res = callapi_hifpt(url,"POST",data);
                L.Mes(Level.INFO, JsonConvert.SerializeObject(res), logid + "SendMail: ");
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), logid + "SendMail: ");
            }
        }
    }
}