using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using Voucher.APIHelper;
using WarantyGetDeviceVoucher.Model;
using Dapper;

namespace WarantyGetDeviceVoucher.Service
{
    public class DeviceCSService
    {
        public static bool CheckObj(long objid)
        {
            try
            {
                DeviceCSModel dcsm = new DeviceCSModel { Status = "", Data = new List<dataCustomer>()};
                string url = Utility.manapi_fpt_vn + "/APICall.svc/API_CheckProgramEquipmentReplace?ObjID=" + objid;
                string res = Common.callapi_hifpt(url, "GET");
                dcsm = JsonConvert.DeserializeObject<DeviceCSModel>(res);
                if (dcsm.Status.ToLower().Equals("success"))
                {
                    if (dcsm.Data.Count.Equals(0))
                    {
                        return false;
                    }
                    if (dcsm.Data.Count > 0)
                    {
                        if (dcsm.Data[0].Result > 0 && dcsm.Data[0].TypeRM == 1)
                        {
                            return true;
                        }                        
                    }
                }
            }
            catch (Exception ex)
            {
                return false;                 
            }
            return false;   
        }

        public static bool CheckRedeemVoucherDevice(string voucherCode, int objid, string orderCode)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int checkRedeem = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_Loyalty_DeviceTLS",
                new
                {
                    actionName = "CheckRedeemVCDevice",
                    objid = objid,
                    orderCodeGC = orderCode,
                    voucherCode = voucherCode
                },
                commandType: CommandType.StoredProcedure).FirstOrDefault();
                return (checkRedeem > 0);
            }
        }
    }
}