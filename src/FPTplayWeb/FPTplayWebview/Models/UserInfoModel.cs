using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace FPTplayWebview.Models
{
    public class UserInfoModel
    {
        public string message { set; get; }
        public int status { set; get; }
        public UserInfo data { set; get; }
    }

    public class HistoryOTT
    {
        public string FullName { set; get; }
        public string FirstAccess { set; get; }
        public string ReceivedAmountDK { set; get; }
        public string ReceivedAmountGT { set; get; }
        public string ReceivedCodeGT { set; get; }
    }
    public class OTTReferalProgram
    {
        public validCode data { set; get; }
        public string message { set; get; }
        public int status { set; get; }
    }
    public class validCode
    {
        public string referral_code { set; get; }
        public int valid { set; get; }
    }
    public class UserInfo
    {
        public string email { set; get; }
        public string fullname { set; get; }
        public string phone { set; get; }
        public string user_id { set; get; }
    }
    public class HistoryInputModel : HomeModelInput
    {
        public string fromDate { set; get; }
        public string toDate { set; get; }
        public string receivedCodeGT { set; get; }
    }
    public class HomeModelInput
    {
        public string token { set; get; }
    }
    public class HomeModelOutput
    {
        public string inviteCode { set; get; }
        public string fullName { set; get; }
        public int historyCount { set; get; }
        public List<Banner> banners { set; get; }
    }
    public class Banner
    {
        public string group { set; get; }
        public int position { set; get; }
        public string linkPath { set; get; }
    }
}