using FPTplayWebview.Contants;
using FPTplayWebview.Models;
using FPTplayWebview.Services;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using System.Web.Http.Cors;
using Voucher.APIHelper.Func.Constant;

namespace FPTplayWebview.Controllers
{
    [Route("API/WebviewFPTplay/{action}")]
    [EnableCors(origins: "*", headers: "*", methods: "*")]
    public class WebviewController : ApiController
    {
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.AdminRF)]
        public ResponseModel<HomeModelOutput> Index(HomeModelInput input)
        {            
            var logId = Guid.NewGuid();
            CommonServices.WriteToLog(input, "Call index api ", logId);
            var res = new ResponseModel<HomeModelOutput> { data=null, error="đã xảy ra lỗi",result=0};
            var homeData = new HomeModelOutput();
            if (string.IsNullOrEmpty(input.token))
            {
                return res;
            }

            var userId = AesService.DecryptString(input.token);
            if (string.IsNullOrEmpty(userId))
            {
                return res;
            }

            homeData.banners = new List<Banner>();
            try
            {
                bool validUserId = UserInforServices.checkOTTCode(userId,logId);
                if (!validUserId)
                {
                    res.data = null;
                    res.result = 0;
                    res.error = "User không tồn tại";
                }
                int checkUser = UserInforServices.CheckExistUser(userId);
                if (checkUser.Equals(1))
                {
                    var data = UserInforServices.GetInforExist(userId);
                    homeData.inviteCode = data.Item1;
                    homeData.fullName = data.Item2;
                }
                else
                {
                    var dataUser = UserInforServices.GetInforClient(userId, logId);
                    homeData.fullName = dataUser.fullname;
                    homeData.inviteCode = UserInforServices.UpdateInviteCode(userId, dataUser);
                }
                homeData.historyCount = UserInforServices.CountHistory(userId);
                homeData.banners = BannerServices.GetBanners();
                res.data = homeData;
                res.result = 1;
                res.error = "";
            }
            catch (Exception ex)
            {
                CommonServices.WriteToLog(ex.Message, "Call index api Error ", logId);
                res.data = null;
                res.result = -1;
                res.error = ex.Message;
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.AdminRF)]
        public ResponseModels<HistoryOTT> HistoryOTT(HistoryInputModel input)
        {

            var logId = Guid.NewGuid();
            CommonServices.WriteToLog(input, "Call HistoryOTT api ", logId);
            var res = new ResponseModels<HistoryOTT> { data = null, error = "đã xảy ra lỗi", result = 0 };
            if (string.IsNullOrEmpty(input.token))
            {
                return res;
            }

            var userId = AesService.DecryptString(input.token);
            if (string.IsNullOrEmpty(userId))
            {
                return res;
            }

            try
            {
                var parrams = new
                {
                    actionName = "GetHistory",
                    userId = userId,
                    fromDate = input.fromDate,
                    toDate = input.toDate,
                    receivedCodeGT = input.receivedCodeGT
                };
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    List<HistoryOTT> lst = new List<HistoryOTT>();
                    lst = connection.Query<HistoryOTT>(ContantStore.OS6_FPTVoucher_FPTplayWebview, parrams, commandType: CommandType.StoredProcedure).ToList();
                    res.data = lst;
                    res.error = "";
                    res.result =1;
                }
            }
            catch (Exception ex)
            {
                CommonServices.WriteToLog(ex.Message, "Call HistoryOTT api Error ", logId);
                res.data = null;
                res.result = -1;
                res.error = ex.Message;
            }
            return res;
        }

        [HttpGet]
        [Authorize(Roles = AuthorizeRole.AdminRF)]
        [Route("API/WebviewFPTplay/policies")]
        public async Task<ResponseModel<List<Policy>>> GetPolicies()
        {
            var logId = Guid.NewGuid();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    var result = (await connection.QueryAsync<Policy>(ContantStore.OS6_FPTVoucher_FPTplayWebview, new
                    {
                        ActionName = "GetPolicies"
                    }, commandType: CommandType.StoredProcedure)).ToList();
                    CommonServices.WriteToLog(result, "Policies: ", logId);

                    var response = new ResponseModel<List<Policy>>
                    {
                        data = result,
                        result = 1
                    };

                    return response;
                }
            }
            catch (Exception e)
            {
                CommonServices.WriteToLog(e.Message, "Get policies error: ", logId);
                return new ResponseModel<List<Policy>>()
                {
                    data = null,
                    result = -1,
                    error = e.Message
                };
            }
        }
        
        [HttpGet]
        [Authorize(Roles = AuthorizeRole.AdminRF)]
        [Route("API/WebviewFPTplay/faqs")]
        public async Task<ResponseModel<List<Faq>>> GetFaqs()
        {
            var logId = Guid.NewGuid();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    var result = (await connection.QueryAsync<Faq>(ContantStore.OS6_FPTVoucher_FPTplayWebview, new
                    {
                        ActionName = "GetFaqs"
                    }, commandType: CommandType.StoredProcedure)).ToList();
                    CommonServices.WriteToLog(result, "FAQs: ", logId);

                    var response = new ResponseModel<List<Faq>>
                    {
                        data = result,
                        result = 1
                    };

                    return response;
                }
            }
            catch (Exception e)
            {
                CommonServices.WriteToLog(e.Message, "Get faqs error: ", logId);
                return new ResponseModel<List<Faq>>()
                {
                    data = null,
                    result = -1,
                    error = e.Message
                };
            }
        }
    }
}
