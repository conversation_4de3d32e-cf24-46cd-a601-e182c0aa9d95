using FPTplayWebview.Models;
using FPTplayWebview.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using Voucher.APIHelper;

namespace FPTplayWebview.Controllers
{
    [Route("API/FPTPlay/{action}")]
    public class FPTPlayController : ApiController
    {
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.FPTPlay)]
        public ResponseModel<List<InviteHistoryResponse>> GetInviteHistory(InviteHistoryRequest request)
        {
            var logId = Guid.NewGuid();
            var response = new ResponseModel<List<InviteHistoryResponse>>();
           
            try
            {
                bool validUserId = UserInforServices.checkOTTCode(request.ID, logId);
                if (!validUserId)
                {
                    response.data = null;
                    response.result = 0;
                    response.error = "User không tồn tại";

                    return response;
                }

                response.data = FPTPlayServices.GetInviteHistory(request.ID);
                response.result = 1;

                return response;
            } catch (Exception e)
            {
                response.result = -1;
                response.error = e.Message;
                return response;
            }
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.FPTPlay)]
        public ResponseModel<InviteInfoResponse> GetInviteInfo(InviteHistoryRequest request)
        {
            var logId = Guid.NewGuid();
            var response = new ResponseModel<InviteInfoResponse>();

            try
            {
                bool validUserId = UserInforServices.checkOTTCode(request.ID, logId);
                if (!validUserId)
                {
                    response.data = null;
                    response.result = 0;
                    response.error = "User không tồn tại";

                    return response;
                }
                int checkUser = UserInforServices.CheckExistUser(request.ID);
                if (!checkUser.Equals(1))
                {
                    var dataUser = UserInforServices.GetInforClient(request.ID, logId);
                    UserInforServices.UpdateInviteCode(request.ID, dataUser);
                }

                response.data = FPTPlayServices.GetInviteInfo(request.ID);
                response.result = 1;
                return response;
            }
            catch (Exception e)
            {
                response.result = -1;
                response.error = e.Message;
                return response;
            }
        }
    }
}