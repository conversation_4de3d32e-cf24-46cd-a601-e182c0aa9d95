using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using FPTplayWebview.Contants;
using System.Threading.Tasks;
using FPTplayWebview.Models;
using FPTplayWebview.Services;
using Dapper;
using System.Data.SqlClient;
using Voucher.APIHelper;
using System.Data;

namespace FPTplayWebview.Services
{
    public class BannerServices
    {
        public static List<Banner> GetBanners()
        {
            var parrams = new
            {
                actionName = "GetFplayBanners"
            };
            var data = new List<Banner>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                data = connection.Query<Banner>(ContantStore.OS6_FPTVoucher_FPTplayWebview, parrams, commandType: CommandType.StoredProcedure).ToList();
            }
            return data;
        }
    }
}