using FPTplayWebview.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using Voucher.APIHelper.Log4net;
using Dapper;
using FPTplayWebview.Contants;
using System.Threading.Tasks;
using System.Configuration;
using Voucher.APIHelper;
using System.Data.SqlClient;
using System.Data;

namespace FPTplayWebview.Services
{
    public class UserInforServices
    {
        public static int CheckExistUser(string userId)
        {
            var parrams = new
            {
                actionName= "validUserExist",
                userId= userId
            };
            return CommonServices.QueryIntScalarStore(ContantStore.OS6_FPTVoucher_FPTplayWebview, parrams);
        }
        public static int CountHistory(string userId)
        {
            var parrams = new
            {
                actionName = "CountHistory",
                userId = userId
            };
            int count = CommonServices.QueryIntScalarStore(ContantStore.OS6_FPTVoucher_FPTplayWebview, parrams);
            return count;
        }
        public static Tuple<string, string> GetInforExist(string userId)
        {
            Tuple<string, string> tplInfor = new Tuple<string, string> ("","");
            var parrams = new
            {
                actionName = "GetInviteCode",
                userId = userId
            };
            //tplInfor.Item1 --> MGT
            //tplInfor.Item2 --> fullname
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                tplInfor = connection.Query<Tuple<string, string>>(ContantStore.OS6_FPTVoucher_FPTplayWebview, parrams, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return tplInfor;
        }
        public static bool checkOTTCode(string userid,Guid logId)
        {
            try
            {
                string uri = Utility.apiOtt;
                var headers = new List<HeaderAPI>();
                headers.Add(new HeaderAPI { key = "X-Fid-Key", value = "4885eabca4f8fda6955b4de6da6f13c1" });
                string resAPI = CommonServices.callapi(uri + "/api/v1/isc/referral/" + userid, "GET", null, "application/json; charset=utf-8", headers);

                CommonServices.WriteToLog(resAPI, "checkOTTCode resAPI ", logId);
                OTTReferalProgram ott = new OTTReferalProgram();
                ott = JsonConvert.DeserializeObject<OTTReferalProgram>(resAPI);
                if (ott.status == 1)
                {
                    return (ott.data.valid == 1);
                }
                return false;
            }
            catch (Exception ex)
            {
                CommonServices.WriteToLog(ex.Message, "checkOTTCode error ", logId);
                return false;
            }
        }

        public static string UpdateInviteCode(string userId,UserInfo user)
        {
            var parrams = new
            {
                actionName = "UpdateInviteCode",
                userId = userId,
                userName= user.fullname,
                userMail = user.email,
                userPhone = user.phone,
            };
            string inviteCode = string.Empty;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                inviteCode = connection.Query<string>(ContantStore.OS6_FPTVoucher_FPTplayWebview, parrams, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return inviteCode;
        }

        public static UserInfo GetInforClient(string clientID,Guid logId)
        {
            var ott = new UserInfoModel();
            ott.data = new UserInfo();
            try
            {
                var url = Utility.apiOtt + "/api/v1/isc/user/" + clientID;
                HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(url);
                var headers = wr.Headers;
                headers.Add("X-Fid-Key", "4885eabca4f8fda6955b4de6da6f13c1");
                wr.Method = "GET";
                // timeout
                wr.Timeout = 10000;
                var httpResponse = (HttpWebResponse)wr.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    var rs = streamReader.ReadToEnd();
                    CommonServices.WriteToLog(rs, "GetInforClient res API ", logId);
                    ott = JsonConvert.DeserializeObject<UserInfoModel>(rs);
                    if (ott.status == 1)
                    {                        
                        return ott.data;
                    }
                    else return null;
                }
            }
            catch (Exception e)
            {
                CommonServices.WriteToLog(e.Message, "GetInforClient Error ", logId);
                return null;
            }
        }
    }
}