using FPTplayWebview.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;
using System.Data;

namespace FPTplayWebview.Services
{
    public class CommonServices
    {
        public static void WriteToLog(object input, string mes, Guid logId)
        {
            LoggerKafka.InsertLog(string.Format("{1} : {0}", JsonConvert.SerializeObject(input), mes));
        }
        public static int ExecScalarStore(string storeName, object parrams)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                return connection.Execute(storeName, parrams, commandType: CommandType.StoredProcedure);
            }
        }
        public static int QueryIntScalarStore(string storeName, object parrams)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<int>(storeName, parrams, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static string QueryStringScalarStore(string storeName, object parrams)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<string>(storeName, parrams, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static string callapi(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8", List<HeaderAPI> headers = null)
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    wr.Headers.Add(header.key, header.value);
                }
            }
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };

                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }
    }
}