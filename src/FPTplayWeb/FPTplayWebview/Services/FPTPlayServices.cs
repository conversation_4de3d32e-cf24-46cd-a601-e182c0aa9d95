using FPTplayWebview.Contants;
using FPTplayWebview.Models;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using Voucher.APIHelper;
using Dapper;

namespace FPTplayWebview.Services
{
    public static class FPTPlayServices
    {
        public static List<InviteHistoryResponse> GetInviteHistory(string userId)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                var result = connection.Query<InviteHistoryResponse>(ContantStore.OSU6_ReferralProgram_FPTPlay_Extend, new
                {
                    Action = "GetInviteHistory",
                    UserId = userId,
                }, commandType: CommandType.StoredProcedure).ToList();

                return result;
            }
        }

        public static InviteInfoResponse GetInviteInfo(string userId)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                var result = connection.Query<InviteInfoResponse>(ContantStore.OSU6_ReferralProgram_FPTPlay_Extend, new
                {
                    Action = "GetInviteInfo",
                    UserId = userId,
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                return result;
            }
        }
    }
}