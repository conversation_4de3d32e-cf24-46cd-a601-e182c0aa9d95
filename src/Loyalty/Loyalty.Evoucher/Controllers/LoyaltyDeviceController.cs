using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using Loyalty.Evoucher.Models;
using Loyalty.Evoucher.Services;
using Voucher.APIHelper.ShareModel;
using Newtonsoft.Json;
using Voucher.APIHelper.Util;

namespace Loyalty.Evoucher.Controllers
{
    
    public class LoyaltyDeviceController : ApiController
    {        
        private const string OS6_FPTVoucher_LoyaltyDevicePromotionEvent = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyDevicePromotionEvent";

        #region Tool bảo hành
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.ToolQuay)]
        [Route("API/Loyalty-Device/ToolQuay/GetDeviceVoucherLoyalty")]
        public ResponseModel<DeviceLoyaltyModel> GetDeviceVoucherLoyalty(DeviceVoucherModelInput input)
        {
            var LogId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "GetDeviceVoucherLoyalty req", LogId);
            return LoyaltyDeviceServices.GetInforVoucher(input, LogId);
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.ToolQuay)]
        [Route("API/Loyalty-Device/ToolQuay/RedeemVoucherDeviceLoyalty")]
        public ResponseModel<bool> RedeemVoucherDeviceLoyalty(RedeemToolInput input)
        {
            var LogId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "", LogId);
            var response  = new ResponseModel<bool> { data = false, result = 0, error = "Voucher không hợp lệ" };
            try
            {
                int res = 0;
                if (input.type == 1)
                {
                    res = LoyaltyDeviceServices.ExcuteStoreActionToolBH(input, "HaftRedeemDeviceLoyalty", LogId, "ToolQuay");
                    if (res > 0)
                    {
                        response.data = true;
                        response.error = "";
                        response.result = 1;
                    }
                    return response;
                }

                if (input.type == 2 || input.type==3)
                {
                    bool isRedeem = LoyaltyDeviceServices.CheckRedeemVoucher(input.voucherCode, input.objId);
                    LoyaltyCommonServices.WriteToLog(isRedeem, "CheckRedeemVoucher res", LogId);

                    if (!isRedeem)
                    {
                        response.data = false;
                        response.error = "Chưa redeem mã";
                        response.result = 1;
                        return response;
                    }
                    string action = "";
                    if (input.type == 2)
                    {
                        action = "RollBackVC";
                        res = LoyaltyDeviceServices.ExcuteStoreActionToolBH(input, action, LogId, "");
                        LoyaltyCommonServices.WriteToLog(res, "ExcuteStoreAction res", LogId);
                        if (res > 0)
                        {
                            response.data = true;
                            response.error = "";
                            response.result = 1;
                        }
                        return response;
                    }
                    else if (input.type == 3)
                    {
                        InforUseVoucher iuv = new InforUseVoucher();
                        var MailContent = new MailModelInput
                        {
                            FromEmail = "<EMAIL>",
                            Recipients = "<EMAIL> ",
                            Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                            Body = null,
                            CarbonCopys = "",
                            BlindCarbonCopys = "",
                            AttachFile = "",
                            AttachUrl = ""
                        };
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {

                            connection.Open();
                            using (var transaction = connection.BeginTransaction())
                            {
                                iuv = LoyaltyDeviceServices.UpdateLoyaltyVoucherToolBH(connection,transaction,input, LogId);
                                int cal = LoyaltyDeviceServices.CalcAccumulatePointWaranty(connection, transaction, input.objId, input.billNumber);
                                int billTotalDebt = LoyaltyDeviceServices.getBillTotalDebt(input.billNumber);
                                LoyaltyCommonServices.WriteToLog(billTotalDebt, input.billNumber + " - billTotalDebt: ", LogId);
                                if (iuv != null && cal >0)
                                {
                                    transaction.Commit();
                                    var request = new SendLoyalty()
                                    {
                                        mobileHiFpt = iuv.Phone,
                                        contractNo = iuv.Contract,
                                        billNumber = input.billNumber,
                                        redeemDate = iuv.RedeemDate,
                                        transCode = iuv.TransCode,
                                        voucherCode = input.voucherCode,
                                        billTotalDebt = billTotalDebt
                                    };
                                    LoyaltyCommonServices.WriteToLog(request, "start to updateLoy req ", LogId);
                                    bool bSend = CallbackCommonServices.NewChangeStatusLoyaltyV2(request, iuv.PrivateCodeID, LogId.ToString());
                                    LoyaltyCommonServices.WriteToLog(request, "NewChangeStatusLoyalty res ", LogId);
                                    if (!bSend)
                                    {
                                        MailContent.Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công Change Status Loy = false", voucher = input.voucherCode, date = DateTime.Now.ToString() });
                                        try
                                        {
                                            Email.SendMail(MailContent);
                                        }
                                        catch (Exception ex)
                                        { LoyaltyCommonServices.WriteToLog(ex.Message, " RedeemVoucherDeviceLoyalty SendMail error ", LogId); }
                                    }
                                    //else
                                    //{
                                    //    transaction.Commit();
                                    //}

                                    //# domain
                                    //#172.20.18.252 cssapi-dev.fpt.net 
                                    //#172.20.18.252 cssapi-stag.fpt.net 
                                    //#172.20.17.32 cssapi.fpt.net 
                                    var apiUrl = Utility.cssapi_fpt_net + "/billing/v1/PrintBill/CreateBillingEInvoice?BillNumber=" + input.billNumber;

                                    using (var httpClient = new HttpClient())
                                    {
                                        try
                                        {
                                            // Gửi yêu cầu HTTP GET
                                            var responseGenbill = httpClient.GetAsync(apiUrl).Result;

                                            // Kiểm tra xem yêu cầu có thành công không
                                            responseGenbill.EnsureSuccessStatusCode();

                                            // Đọc nội dung từ phản hồi
                                            string content = responseGenbill.Content.ReadAsStringAsync().Result;
                                        }
                                        catch (HttpRequestException ex)
                                        {
                                            LoyaltyCommonServices.WriteToLog(ex.Message, " responseGenbill error "+ input.billNumber, LogId);
                                        }
                                    }

                                    response.data = true;
                                    response.error = "";
                                    response.result = 1;
                                    return response;
                                }
                                else
                                {
                                    return response;
                                }
                            }
                        }
                    }                    
                }
            }
            catch (Exception e)
            {
                LoyaltyCommonServices.WriteToLog(e.Message, "", LogId);
                return new ResponseModel<bool>() { data = false, error = e.Message, result = -1 };
            }

            return new ResponseModel<bool>() { data = false, error = "Không xác định", result = 0 };
        }
        #endregion

        #region MBN
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Mobinet)]
        [Route("API/Loyalty-Device/Mobinet/GetDeviceVoucherLoyaltyMobinet")]
        public ResponseModel<DeviceLoyaltyModel> GetDeviceVoucherLoyaltyMobinet(DeviceVoucherModelInput input)
        {
            var LogId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "GetDeviceVoucherLoyaltyMobinet req", LogId);
            return LoyaltyDeviceServices.GetInforVoucher(input, LogId);
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Mobinet)]
        [Route("API/Loyalty-Device/Mobinet/RedeemVoucherDeviceLoyaltyMobinet")]
        public ResponseModel<bool> RedeemVoucherDeviceLoyaltyMobinet(RedeemInputMBN input)
        {
            var LogId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "RedeemVoucherDeviceLoyaltyMobinet req ", LogId);
            var reponse = new ResponseModel<bool> { data = false, error = "", result = 0 };
            var checkStatusInput = new CheckStatusInput
            {
                ObjID = input.objId, OrderType=input.orderType,OrderCode=input.orderCode
            };
            bool StatusSupport = LoyaltyDeviceServices.CheckStatusSupport(checkStatusInput, LogId);
            LoyaltyCommonServices.WriteToLog(StatusSupport, "RedeemVoucherDeviceLoyaltyMobinet StatusSupport ", LogId);

            bool validValue = LoyaltyDeviceServices.CheckRealValueVoucher(input.realUse);
            if (!validValue)
            {
                reponse.error = "Giá trị voucher không hợp lệ";
                return reponse;
            }
            int res = 0;
            if (StatusSupport)
            {
                reponse.error = "Phiếu đã được hoàn ứng";
                return reponse;
            }
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        res = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_LoyaltyDevicePromotionEvent",
                        new
                        {
                            actionName = "RedeemMBN",
                            objId = input.objId,
                            code = input.voucherCode,
                            orderCode = input.orderCode,
                            orderType = input.orderType,
                            description="MBN",
                            xml = LoyaltyDeviceServices.CreateXMLServiceCode(input.realUse,LogId)
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        LoyaltyCommonServices.WriteToLog(res, "RedeemVoucherDeviceLoyaltyMobinet res ", LogId);
                        if (res > 0)
                        {
                            reponse.result = 1;
                            reponse.data = true;
                            transaction.Commit();
                        }
                        else
                        {
                            reponse.error = "voucher không hợp lệ";
                        }
                    }
                }
            }
            catch (Exception e)
            {
                LoyaltyCommonServices.WriteToLog(e.Message, "RedeemVoucherDeviceLoyaltyMobinet error ", LogId);
                reponse.result = -1;
                reponse.data = false;
                reponse.error = e.Message;
            }
            return reponse;
        }

        #endregion

        #region TLS
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Telesale)]
        [Route("API/Loyalty-Device/TLS/GetDeviceVoucherLoyaltyTeleSale")]
        public ResponseModel<DeviceLoyaltyModel> GetDeviceVoucherLoyaltyTeleSale(DeviceVoucherModelInput input)
        {
            var LogId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "GetDeviceVoucherLoyaltyTeleSale req ", LogId);
            return LoyaltyDeviceServices.GetInforVoucher(input, LogId);
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Telesale)]
        [Route("API/Loyalty-Device/TLS/RedeemVoucherDeviceLoyaltyTeleSale")]
        public ResponseModel<bool> RedeemVoucherDeviceLoyaltyTeleSale(RedeemTLSInput input)
        {
            var LogId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "RedeemVoucherDeviceLoyaltyTeleSale req ", LogId);
            var reponse = new ResponseModel<bool> { data = false, error = "thông tin không hợp lệ", result = 0 };
            try
            {
                bool validValue = LoyaltyDeviceServices.CheckRealValueVoucher(input.realUse);
                if (!validValue)
                {
                    reponse.error = "Giá trị voucher không hợp lệ";
                    return reponse;
                }
                if (input.type == 1)
                {
                    #region Active voucher
                    int res = LoyaltyDeviceServices.KeepLoyaltyVoucherTLS(input, LogId);
                    if (res > 0)
                    {
                        reponse.data = true;
                        reponse.error = "";
                        reponse.result = 1;
                    }
                    return reponse;
                    #endregion
                }
                else if (input.type == 2)
                {
                    #region rollbackVoucher
                    bool isRedeem = LoyaltyDeviceServices.CheckRedeemVoucher(input.voucherCode, input.objId);

                    LoyaltyCommonServices.WriteToLog(isRedeem, "RedeemVoucherDeviceLoyaltyTeleSale CheckRedeemVoucher ", LogId);
                    if (!isRedeem)
                    {
                        reponse.error = "Chưa Redeem mã";
                        return reponse;
                    }
                    int res = LoyaltyDeviceServices.ExcuteStoreActionTLS(input, "RollBackVcTLS", LogId);
                    if (res > 0)
                    {
                        reponse.data = true;
                        reponse.error = "";
                        reponse.result = 1;
                    }
                    return reponse;
                    #endregion
                }
                #region type = 3
                /*
                else if (input.type == 3)
                {
                    #region xác nhận sử dụng voucher
                    bool isRedeem = LoyaltyDeviceServices.CheckRedeemVoucher(input.voucherCode, input.objId);
                    LoyaltyCommonServices.WriteToLog(isRedeem, "RedeemVoucherDeviceLoyaltyTeleSale CheckRedeemVoucher ", LogId);

                    if (!isRedeem)
                    {
                        reponse.error = "Chưa Redeem mã";
                        return reponse;                        
                    }
                    var iuv = new InforUseVoucher();
                    iuv = LoyaltyDeviceServices.UpdateLoyaltyVoucherTLS(input, LogId);
                    if (iuv != null)
                    {
                        var MailContent = new MailModelInput
                        {
                            FromEmail = "<EMAIL>",
                            Recipients = "<EMAIL> ",
                            Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                            Body = null,
                            CarbonCopys = "",
                            BlindCarbonCopys = "",
                            AttachFile = "",
                            AttachUrl = ""
                        };
                        var request = new SendLoyalty()
                        {
                            mobileHiFpt = iuv.Phone,
                            redeemDate = iuv.RedeemDate,
                            transCode = iuv.TransCode,
                            voucherCode = input.voucherCode
                        };
                        LoyaltyCommonServices.WriteToLog(request, "start to updateLoy req ", LogId);
                        bool bSend = CallbackCommonServices.NewChangeStatusLoyaltyV2(request, iuv.PrivateCodeID, LogId.ToString());
                        LoyaltyCommonServices.WriteToLog(bSend, "NewChangeStatusLoyalty res ", LogId);
                        if (!bSend)
                        {
                            MailContent.Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công Change Status Loy = false", voucher = input.voucherCode, date = DateTime.Now.ToString() });
                            try
                            {
                                Email.SendMail(MailContent);
                            }
                            catch (Exception ex)
                            { LoyaltyCommonServices.WriteToLog(ex.Message, " RedeemVoucherDeviceLoyalty SendMail error ", LogId); }
                        }
                        reponse.data = true;
                        reponse.error = "";
                        reponse.result = 1;                        
                    }
                    return reponse;
                    
                    #endregion
                }
                */
                #endregion
            }
            catch (Exception e)
            {
                LoyaltyCommonServices.WriteToLog(e.Message, "", LogId);
            }
            return new ResponseModel<bool>() { data = false, error = "Không xác định", result = 0 };
        }
        #endregion
    }
}
