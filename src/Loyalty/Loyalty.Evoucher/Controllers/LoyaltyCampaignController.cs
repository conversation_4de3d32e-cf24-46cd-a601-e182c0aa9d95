using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Voucher.APIHelper.Util;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Data;
using Loyalty.Evoucher.Models;
using Loyalty.Evoucher.Services;
using Loyalty.Evoucher.Contants;
using Voucher.APIHelper.ShareModel;
using System.Configuration;
using System.Threading.Tasks;
using System.Text;
using Loyalty.Models.LoyaltyCampaign;
using Loyalty.Services.LoyaltyCampaign;
using APIMBS.Models.SalePlatform;
using System.Net.Http.Headers;
using APIMBS.Models;

namespace Loyalty.Evoucher.Controllers
{
    [Route("api/loyalty/campaign/points/{action}")]
    public class LoyaltyCampaignController : ApiController
    {
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SaleClub)]
        public ResponseModel<bool> apply(Apply_Action input)
        {
            var result = new ResponseModel<bool>() { data=false,result=0,error="Fail"};
            StringBuilder sb = new StringBuilder();
            var logid = new Guid();

            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("input", input));

            try
            {
                if (input.OrderStatus != 1 || (input.CustomerInfor?.CusTypeID ?? 102) != 102) return result;

                var rs = LoyaltyCampaignServices.order_apply_points(input, sb, logid);
                if (string.IsNullOrEmpty(rs))
                {
                    result.data = true;
                    result.result = 1;
                    result.error = "Success";
                }
                else
                {
                    result.data = false;
                    result.result = 0;
                    result.error = rs;
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("apply err", ex));
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "campaign/point/apply", logid.ToString());
            }
            return result;
        }


        [HttpPost]
        public string saleclub_sunmit_action(SalePolicyRedeemEVC input)
        {
            StringBuilder sb = new StringBuilder();
            Guid guid = Guid.NewGuid();
            string rs = "";
            try
            {
                rs = LoyaltyCampaignServices.saleClub_submit_action_loyalty(sb, guid, input);
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("saleclub_sunmit_action err", ex));
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "campaign/point/saleclub_sunmit_action", guid.ToString());
            }
            return rs;
        }

        [HttpPost]
        public int retry_sunmit_action(SalePolicyRedeemEVC input)
        {
            return LoyaltyCampaignServices.retry_submit_action_loy();
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.SaleClub_qlcs)]
        public ResponseModel<bool> CancelTSD(CancelTSD input)
        {
            return LoyaltyCampaignServices.cancel_tsd(input);
        }

        [HttpPost]
        public ResponseModel<bool> receiptpayment(ReceiptPayment input)
        {
            return LoyaltyCampaignServices.ReceiptPaymentProcess(input);
        }
        
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.qlcs)]
        public ResponseModel<bool> FGoldCskhDynamic(PolicyAproveFGold input)
        {
            return LoyaltyCampaignServices.PolicyAproveFGoldCSKH(input);
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        public ResponseModel<bool> report(NotedGold input)
        {
            var rs = new ResponseModel<bool> { data = true, result = 1, error = ""};
            var sb = new StringBuilder();
            try
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("input", input));
                var excu = LoyaltyCampaignServices.NoteFGold(input);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("excu", excu));
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Exception", e.Message));
                rs.data = false;
                rs.error = "Lỗi.";
                rs.result = -1;
            }
            finally {
                LoggerKafka.WriteLogKafka(sb, "points/report", "");
            }
            return rs;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.qlcs)]
        public ResponseModel<EstimateGoldOut> EstimatedGold(EstimateGold input)
        {
            return LoyaltyCampaignServices.EstimatedGold(input);
        }
    }
}
