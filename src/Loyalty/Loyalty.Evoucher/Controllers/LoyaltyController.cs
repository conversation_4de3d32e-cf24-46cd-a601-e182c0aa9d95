using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Voucher.APIHelper.Util;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Data;
using Loyalty.Evoucher.Models;
using Loyalty.Evoucher.Services;
using Loyalty.Evoucher.Contants;
using Voucher.APIHelper.ShareModel;
using System.Configuration;
using System.Threading.Tasks;
using System.Text;

namespace Loyalty.Evoucher.Controllers
{
    public class LoyaltyController : ApiController
    {

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Frtvoucher)]
        [Route("api/Loyalty-FRT/ChangeStatusEventCode")]
        public ResponseModel<bool> ChangeStatusEventCode(EventCodeModel input)
        {
            int res = 0;
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "FRT ChangeStatusEventCode: ");
            var MailContent = new MailModelInput
            {
                FromEmail = "<EMAIL>",
                Recipients = "<EMAIL>;<EMAIL>;<EMAIL>",
                Subject = "Hệ thống FRT EVC đã sẵn sàng voucher",
                Body = null,
                CarbonCopys = "",
                BlindCarbonCopys = "",
                AttachFile = "",
                AttachUrl = ""
            };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        res = connection.Execute(Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT, new
                        {
                            actionName = "ChangeStatusLoyaltyFRT",
                            eventCode = input.eVoucher,
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(res), "FRT ChangeStatusEventCode res: ");
                        var VoucherInfor = connection.Query<EventFRTinfo>(
                            Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT,
                            new
                            {
                                actionName = "GetInforVoucherFRT",
                                eventCode = input.eVoucher
                            }, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        if (VoucherInfor != null)
                        {
                            MailContent.Body = "Chương trình ưu đãi "+VoucherInfor.EventCode+" - "+VoucherInfor.Name+" đã sẵn sáng liên kết tại FRT EVC với số lượng "+VoucherInfor.QuotaPrivateCode.ToString();
                        }
                        if (res > 0)
                        {
                            transaction.Commit();
                            Email.SendMail(MailContent);
                            return new ResponseModel<bool> { data = true, result = 1, error = null };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ResponseModel<bool> { data = false, result = 0, error = ex.Message };
            }
            return new ResponseModel<bool> { data = false, result = 0, error = "cập nhật không thành công" };
        }

        [Route("api/Loyalty-FRT/CreateEventCodeFRT")]
        public ResponseModel<bool> CreateEventCodeFRT(FRTCreateVoucherInputModel input)
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "FRT CreateEventCodeFRT: ");
            try
            {
                string data = JsonConvert.SerializeObject(input);
                string url = Utility.loyalty_FRTurl.ToString();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    url, "FRT CreateEventCodeFRT url: ");
                string endpoint = Utility.loyalty_FRT_create_endpoint.ToString();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    "Start to call API " + url + endpoint, "FRT CreateEventCodeFRT url: ");
                string resapi = LoyaltyFRTService.CallAPI(url, endpoint, data);

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(resapi), "CreateEventCodeFRT resapi: ");

                if (string.IsNullOrEmpty(resapi))
                {
                    return new ResponseModel<bool> { data = false, error = "Đã xảy ra lỗi", result = 0 };
                }
                FRTCreateVoucherOutputModel cvom = new FRTCreateVoucherOutputModel();
                cvom = JsonConvert.DeserializeObject<FRTCreateVoucherOutputModel>(resapi);
                if (cvom.status)
                {
                    return new ResponseModel<bool> { data = cvom.status, result = 1, error = null };
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(ex.Message), "CreateEventCodeFRT Error: ");
                return new ResponseModel<bool> { data = false, result = 0, error = ex.Message };
            }
            return new ResponseModel<bool> { data = false, result = 0, error = "cập nhật không thành công" };
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Frtvoucher)]
        [Route("api/Loyalty-FRT/RedeemVoucherFRT")]
        public ResponseModel<bool> RedeemVoucherFRT(FRTRedemModel input)
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(input), "FRT RedeemVoucherFRT res: ");
            var MailContent = new MailModelInput
            {
                FromEmail = "<EMAIL>",
                Recipients = "<EMAIL> ",
                Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                Body = null,
                CarbonCopys = "",
                BlindCarbonCopys = "",
                AttachFile = "",
                AttachUrl = ""
            };
            if(string.IsNullOrEmpty(input.saleOrder) || string.IsNullOrEmpty(input.eVoucher) || input.price_real <= 0)
            {
                return new ResponseModel<bool> {data=false, result=0, error="request không hợp lệ" };
            }
            try
            {
                RedeemFRTinfo rif = new RedeemFRTinfo();
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        int vcStatus = connection.Query<int>(Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT, new
                        {
                            actionName = "checkStatusVoucherFRT",
                            code = input.eVoucher,
                        },transaction:transaction,commandTimeout:null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(vcStatus), "FRT RedeemVoucherFRT vcStatus: ");
                        if (!vcStatus.Equals(2))
                        {
                            return new ResponseModel<bool> { data = false, result = 0, error = "Voucher không hợp lệ" };
                        }

                        int outDate = connection.Query<int>(Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT, new
                        {
                            actionName = "checkExpriteVoucher",
                            code = input.eVoucher,
                            redeemDate = input.redeemDate,
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        if (outDate == 0)
                        {
                            return new ResponseModel<bool> { data = false, result = 0, error = "Voucher không hợp lệ" };
                        }
                        rif = connection.Query<RedeemFRTinfo>(Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT, new
                        {
                            actionName = "RedeemVoucherFRT",
                            code = input.eVoucher,
                            saleOrder = input.saleOrder,
                            description = input.saleOrder + ";" + input.shopCode + ";" + input.shopName,
                            value=input.price_real,
                            redeemDate = input.redeemDate
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(rif), "FRT RedeemVoucherFRT rif: ");
                        if (rif != null)
                        {
                            transaction.Commit();
                        }
                    }
                }
                var request = new SendLoyalty()
                {
                    mobileHiFpt = rif.Phone,
                    redeemDate = input.redeemDate,
                    shopCode = input.shopCode,
                    saleOrder = input.saleOrder,
                    shopName = input.shopName,
                    transCode = rif.TransCode,
                    voucherActualUse = input.price_real,
                    voucherCode = input.eVoucher
                };
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(request), "start to changeStatus RedeemVoucherFRT req: ");
                int idPrivateCode = CallbackCommonServices.GetPrivateCodeID(input.eVoucher);
                bool bSend = CallbackCommonServices.NewChangeStatusLoyaltyV2(request, idPrivateCode, logId.ToString());
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(bSend), "FRT RedeemVoucherFRT ChangeStatusLoyalty bSend: ");
                if (bSend)
                {
                    try
                    {
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {
                            connection.Open();
                            using (var transaction = connection.BeginTransaction())
                            {
                                int isChangeLoyalty = connection.Execute(Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT, new
                                {
                                    actionName = "ChangeStatusLoyVoucher",
                                    code = input.eVoucher,
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(isChangeLoyalty), "FRT RedeemVoucherFRT isChangeLoyalty: ");
                                if (isChangeLoyalty > 0)
                                {
                                    transaction.Commit();
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MailContent.Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC thành công Change Status Loy = true, lỗi update status=5 FRT, Lỗi: " +ex.Message, voucher = input.eVoucher, date = DateTime.Now.ToString() });
                        Email.SendMail(MailContent);
                    }                    
                }
                else
                {
                    MailContent.Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công Change Status Loy = false FRT", voucher = input.eVoucher, date = DateTime.Now.ToString() });
                    Email.SendMail(MailContent);
                }
                return new ResponseModel<bool> { data = true, result = 1, error = null };
            }
            catch (Exception ex)
            {
                MailContent.Body = JsonConvert.SerializeObject(new { content = "RedeemVoucherFRT Lỗi: "+ex.Message, voucher = input.eVoucher, date = DateTime.Now.ToString() });
                Email.SendMail(MailContent);
                return new ResponseModel<bool> { data = false, result = 0, error = ex.Message };
            }
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty/BookVoucher")]
        public ResponseModel<BookVoucherOutput> BookVoucher(BookVoucherModel input)
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "BookVoucher req: ");
            
            if (string.IsNullOrEmpty(input.transCode))
            {
                return new ResponseModel<BookVoucherOutput> { data = null, result = -1, error = "transCode không được để trống" };
            }
            try
            {
                var parram = new
                {
                    eventCode = input.eVoucher
                };
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        var evcHiFPT = new string[] { LoyaltyVoucherType.DEVICEHIFPT.ToString(), LoyaltyVoucherType.INSURANCE.ToString() };
                        TypeVoucher type = connection.Query<TypeVoucher>(Contant.OS6_FPTVoucher_LoyaltyVoucherType,
                            parram, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(type), "BookVoucher type: ");
                        if (type.ID == 0)
                        {
                            return new ResponseModel<BookVoucherOutput> { data = null, error = "Promotion Event không hoạt động", result = 0 };
                        }

                        if (type.QuotaPrivateCode <= type.Exchange)
                        {
                            return new ResponseModel<BookVoucherOutput> { data = null, error = "Quota đã hết", result = 0 };
                        }

                        if (type.VoucherTypeName.Equals(LoyaltyVoucherType.FPTPLAY.ToString()))
                        {
                            return FPTplayService.BookVoucherFPTplay(connection, transaction, input, type, logId.ToString());
                        }   
                        
                        if (type.VoucherTypeName.Equals(LoyaltyVoucherType.VPN.ToString()))
                        {
                            return LoyaltyVPNService.BookVoucherVPN(connection, transaction, input, type, logId.ToString());
                        }

                        if (type.VoucherTypeName.Equals(LoyaltyVoucherType.MOBILECARD.ToString()))
                        {
                            return MobileCardService.BookVoucherMobileCard(connection, transaction, input, logId.ToString());
                        }

                        if (type.VoucherTypeName.Equals(LoyaltyVoucherType.FRT.ToString()))
                        {
                            return LoyaltyFRTService.BookVoucherFRT(connection, transaction, input, type, logId.ToString());
                        }

                        if (type.VoucherTypeName.Equals(LoyaltyVoucherType.AIRCONCLEAN.ToString()))
                        {
                            return LoyaltyAIRCONCLEANServices.BookVoucherAIRCONCLEANS(connection, transaction, input, type, logId.ToString());
                        }

                        if (type.VoucherTypeName.Equals(LoyaltyVoucherType.ULTRAFAST.ToString()))
                        {
                            return LoyaltyServices.UpdateInactive(connection, transaction, type.EventCode, input.phone, type.ExpiredDay, type.VoucherTypeName,input.transCode,logId.ToString(),logId);
                        }

                        var pars = new
                        {
                            eVoucher = input.eVoucher,
                            transCode = input.transCode,
                            phone = input.phone,
                            point = input.point
                        };

                        var temp = connection.Query<BookVoucherOutput>(
                            Contant.OS6_FPTVoucher_Loyalty_VoucherBooking, pars,
                            commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();

                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(temp), "BookVoucher temp: ");

                        if (temp != null)
                        {
                            transaction.Commit();
                            return new ResponseModel<BookVoucherOutput> { data=temp,error=null, result=1};
                        }
                        return new ResponseModel<BookVoucherOutput> { data = null, error = "đã xảy ra lỗi", result = 0 };
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "BookVoucher Error: ");
                return new ResponseModel<BookVoucherOutput> { data = null, error = ex.Message, result = 0 };
            }
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty/RedeemVoucher")]
        public ResponseModel<RedeemOutput> RedeemVoucher(RedeemInput input)
        {
            var logId = Guid.NewGuid();
            try
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "Loyalty RedeemVoucher req: ");
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        int VoucherType = connection.Query<int>(Contant.OS6_FPTVoucher_PromotionEventLoyaltyFilm,
                            new { ActionName = "CheckVoucherFilm", objID = input.objId, voucherCode = input.voucherCode }
                        , transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(VoucherType), "Loyalty RedeemVoucher VoucherType: ");
                        #region delivery voucher to iptv
                        if (VoucherType.Equals(6))
                        {
                            int res = 0;
                            res = connection.Execute(Contant.OS6_FPTVoucher_PromotionEventLoyaltyFilm,
                            new { ActionName = "AddVoucherFilm", objID = input.objId, voucherCode = input.voucherCode }
                                , transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(res), "RedeemVoucher - Loyalty, Add voucher Film Status: ");

                            if (res > 0)
                            {
                                transaction.Commit();
                                return new ResponseModel<RedeemOutput> { data = new RedeemOutput { 
                                    status = StatusRedeem.SUCCESS, 
                                    statusDescription = LoyaltyDescription.REDEEMSUCCESS 
                                }, error = null, result = 1 };
                            }

                            return new ResponseModel<RedeemOutput>
                            {
                                data = new RedeemOutput
                                {
                                    status = StatusRedeem.FALSE,
                                    statusDescription = LoyaltyDescription.REDEEMERROR
                                },
                                error = null,
                                result = 0
                            };
                        }
                        #endregion

                        if (VoucherType.Equals(11))
                        {
                            return LoyaltyVoucherPaymentService.RedeemVoucherPayment(connection, transaction, input.voucherCode, input.billnumber, input.objId, logId.ToString());
                        }

                        var pars = new
                        {
                            actionName = "CheckCodeUse",
                            objID = input.objId,
                            VoucherCode = input.voucherCode
                        };

                        dynamic temp = connection.Query(
                            "PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                            pars
                        , transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(temp), "Loyalty RedeemVoucher temp: ");

                        if (temp != null && temp.res == 1)
                        {
                            #region Voucher không phải phim lẻ
                            if (VoucherType != 6)
                            {
                                dynamic infor = connection.Query(
                                    "PowerInside.dbo.OS6_FPTVoucher_Loyalty_PrivateCode_Redeem",
                                    new
                                    {
                                        actionName = "GetPhoneByObjID",
                                        ObjID = input.objId
                                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure
                                ).FirstOrDefault();
                                transaction.Commit();

                                bool bSend = LoyaltyVoucherPaymentService.ChangeStatusLoyalty(infor.Phone, input.voucherCode, logId.ToString());
                                if (bSend == true)
                                {
                                    transaction.Commit();
                                    return new ResponseModel<RedeemOutput>
                                    {
                                        data = new RedeemOutput
                                        {
                                            status = StatusRedeem.SUCCESS,
                                            statusDescription = LoyaltyDescription.REDEEMSUCCESS,
                                            infor = new PaymentUseVoucher()
                                        },
                                        error = "",
                                        result = 1
                                    };
                                }
                            }
                            #endregion
                        }
                        else
                            if (temp != null && temp.res == 2)
                        {
                            return new ResponseModel<RedeemOutput>
                            {
                                data = new RedeemOutput
                                {
                                    status = StatusRedeem.FALSE,
                                    statusDescription = string.Format("Hợp đồng {0} đã được sử dụng Evoucher {1}", temp.Contract, temp.Name),
                                    infor = new PaymentUseVoucher()
                                },
                                error = "",
                                result = 1
                            };
                        }
                        else
                            return new ResponseModel<RedeemOutput>
                            {
                                data = new RedeemOutput
                                {
                                    status = StatusRedeem.FALSE,
                                    statusDescription = LoyaltyDescription.REDEEMERROR,
                                    infor = new PaymentUseVoucher()
                                },
                                error = "",
                                result = 1
                            };
                        return new ResponseModel<RedeemOutput>
                        {
                            data = new RedeemOutput
                            {
                                status = StatusRedeem.FALSE,
                                statusDescription = LoyaltyDescription.REDEEMERROR,
                                infor = new PaymentUseVoucher()
                            },
                            error = LoyaltyDescription.REDEEMERROR,
                            result = 0
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                        JsonConvert.SerializeObject(ex.Message), "Loyalty RedeemVoucher error: ");
                return new ResponseModel<RedeemOutput>
                {
                    data = new RedeemOutput
                    {
                        status = StatusRedeem.FALSE,
                        statusDescription = LoyaltyDescription.REDEEMERROR,
                        infor = new PaymentUseVoucher()
                    },
                    error = ex.Message,
                    result = 1
                };
            }
        }

        [HttpGet]
        //[Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty/GetListEvoucherLoyalty")]
        public ResponseModels<LoyaltyEVoucherOutput> GetListEvoucherLoyalty(int status)
        {
            List<LoyaltyEVoucherOutput> res = new List<LoyaltyEVoucherOutput>();
            int result = 1;
            GetListEnum statusVoucher = (GetListEnum)status;
            string _statusVoucher = statusVoucher.ToString();
            string error = string.Empty;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    res = connection.Query<LoyaltyEVoucherOutput>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent", new
                    {
                        ActionName = "GetListVoucher",
                        StatusVoucher = _statusVoucher,
                    }, commandType: CommandType.StoredProcedure).ToList();
                }
            }
            catch (Exception ex)
            {
                error = ex.Message;
                result = 0;
            }
            return new ResponseModels<LoyaltyEVoucherOutput> { data = res, error = error, result = result };
        }

        [HttpGet]
        [Route("api/Loyalty/GetInforBoxByContracts")]
        public ResponseModels<LoyaltyBoxModel> GetInforBoxByContract(string contracts) // input nhiều hợp đông cách nhau bằng dấu ,
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(contracts), "Loyalty CheckBoxByContracts contracts: ");
            int result = 1;
            string error = string.Empty;
            try
            {
                List<LoyaltyBoxModel> data = LoyaltyBoxService.GetBoxType(contracts);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                           JsonConvert.SerializeObject(data), "Loyalty GetInforBoxByContract data: ");
                if (data != null && data.Count > 0)
                    return new ResponseModels<LoyaltyBoxModel> { data = data, error = error, result = result };
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "Loyalty GetInforBoxByContract Error: ");
                error = ex.Message;
                result = 0;
            }
            return new ResponseModels<LoyaltyBoxModel> { data = null, error = error, result = result };
        }

        [HttpGet]
        [Route("api/Loyalty/CheckBoxByContracts")]
        public ResponseModel<bool> CheckBoxByContracts(string contracts) // input nhiều hợp đông cách nhau bằng dấu ,
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(contracts), "Loyalty CheckBoxByContracts contracts: ");
            bool res = false;
            try
            {
                List<LoyaltyBoxModel> data = LoyaltyBoxService.GetBoxType(contracts);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                           JsonConvert.SerializeObject(data), "Loyalty CheckBoxByContracts data: ");
                if (data != null && data.Count > 0)
                {
                    foreach (var item in data)
                    {
                        if (item.BoxType == 1 || item.BoxType == 2)
                        {
                            res = true;
                            break;
                        }
                    }
                    return new ResponseModel<bool> { data = res, error = "", result = 1 };
                }
                return new ResponseModel<bool> { data = res, error = "không tim thấy", result = 0 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "Loyalty CheckBoxByContracts Error: ");
                return new ResponseModel<bool> { data = false, error = ex.Message, result = 0 };
            }
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty/LoyaltyRollBackVoucher")]
        public ResponseModel<OutputMessage> LoyaltyRollBackVoucher(TransCodeLoy input)
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "LoyaltyRollBackVoucher: ");
            OutputMessage om = new OutputMessage();
            string dataHash = LoyaltyCommonServices.HashSha1(input.eVoucher + input.transCode);
            if (dataHash.ToLower() != input.checkSum)
            {
                return new ResponseModel<OutputMessage> { data = null, result = 0, error = "Dữ liệu không hợp lệ" };
            }
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        om = connection.Query<OutputMessage>(Contant.OS6_FPTVoucher_Loyalty, new
                        {
                            actionName = "RollbackVoucherBook",
                            eventCode = input.eVoucher,
                            transCode = input.transCode,
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(om), "LoyaltyRollBackVoucher res: ");
                        if (om != null)
                        {
                            transaction.Commit();
                            return new ResponseModel<OutputMessage> { data = om, result = 1, error = null };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new ResponseModel<OutputMessage> { data = null, result = 0, error = ex.Message };
            }
            return new ResponseModel<OutputMessage> { data = null, result = 0, error = "cập nhật không thành công" };
        }        

        [HttpPost]
        //[Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty/CheckDataVPN")]
        public async Task<bool> CheckDataVPN()
        {
            Task checkVPN = VipVPNService.TaskCheckdata();
            Task activeLoy = VipVPNService.TaskActiveLoy();
            await checkVPN;
            await activeLoy;
            return true;
        }

        [HttpPost]
        [Route("api/Loyalty/RedeemVoucherAIRCONCLEANS")]
        public VoucherRedeemOutput RedeemVoucherAIRCONCLEANS(VoucherRedeemInput input)
        {
            var logId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("input", input));
            try
            {
                var CheckVoucher = new CheckVoucher();
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        CheckVoucher = connection.Query<CheckVoucher>(Contant.OSU6_FPTVoucher_AIRCONCLEANS_Servies, new
                        {
                            actionName = "checkVoucher",
                            Code = input.voucherCode,
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("CheckVoucher", CheckVoucher));

                        if (CheckVoucher == null)
                        {
                            return new VoucherRedeemOutput { Result = 0, message = "Không tìm thấy Voucher"};
                        }

                        if(DateTime.Now.Date > CheckVoucher.ExpiredDate.Date)
                        {
                            return new VoucherRedeemOutput { Result = 0, message = "Voucher hết hạn" };
                        }

                        int redem = connection.Execute(Contant.OSU6_FPTVoucher_AIRCONCLEANS_Servies, new
                        {
                            actionName = "RedeemVoucher",
                            PrivateCodeID = CheckVoucher.ID
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("redem", redem));

                        if (redem > 0)
                        {
                            transaction.Commit();
                        }
                    }
                }
                var request = new SendLoyalty()
                {
                    mobileHiFpt = input.mobileHiFpt,
                    redeemDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    billNumber = input.billNumber,
                    billTotalDebt = input.billTotalDebt,
                    transCode = CheckVoucher.TransCode,
                    contractNo = input.contractNo,
                    voucherCode = input.voucherCode
                };
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("request", request));

                bool bSend = CallbackCommonServices.NewChangeStatusLoyaltyV2(request, CheckVoucher.ID, logId.ToString());
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("bSend", bSend));

                if (bSend)
                {
                    try
                    {
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {
                            connection.Open();
                            using (var transaction = connection.BeginTransaction())
                            {
                                int isChangeLoyalty = connection.Execute(Contant.OSU6_FPTVoucher_AIRCONCLEANS_Servies, new
                                {
                                    actionName = "ChangeStatusLoyVoucher",
                                    PrivateCodeID = CheckVoucher.ID,
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("isChangeLoyalty", isChangeLoyalty));

                                if (isChangeLoyalty > 0)
                                {
                                    transaction.Commit();
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        return new VoucherRedeemOutput { Result = 0, message = ex.Message };
                    }
                }
                else
                {
                    return new VoucherRedeemOutput { Result = 0, message = "Redeem voucher thất bại" };
                }
                return new VoucherRedeemOutput { Result = 1, message = "Thành công" };
            }
            catch (Exception ex)
            {
                return new VoucherRedeemOutput { Result = 0, message = ex.Message };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "RedeemVoucherAIRCONCLEANS", logId.ToString());
            }
        }

    }
}