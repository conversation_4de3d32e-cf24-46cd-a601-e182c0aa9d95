using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Voucher.APIHelper.Util;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Data;
using Loyalty.Evoucher.Models;
using Loyalty.Evoucher.Services;
using Loyalty.Evoucher.Contants;
using Voucher.APIHelper.ShareModel;
using System.Configuration;
using System.Threading.Tasks;
using System.Text;
using Loyalty.Models.LoyaltyCampaign;
using Loyalty.Services.LoyaltyCampaign;
using APIMBS.Models.SalePlatform;
using System.Net.Http.Headers;
using APIMBS.Models;

namespace Loyalty.Evoucher.Controllers
{
    [Route("api/services/loyalty/voucher/{action}")]
    public class LoyaltyServicesController : ApiController
    {
        [HttpGet]
        [Authorize(Roles = AuthorizeRole.LoyaltyService_Check)]
        public ResponseModel<bool> check(string voucherCode, string voucherType)
        {
            return LoyaltyServices.Check(voucherCode, voucherType);
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.LoyaltyService_Redeem)]
        public ResponseModel<bool> redeem(RedeemLoyaltyInput input)
        {
            return LoyaltyServices.Redeem(input);
        }
    }
}
