using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using Loyalty.Evoucher.Models;
using Loyalty.Evoucher.Services;
using Voucher.APIHelper.ShareModel;
using Newtonsoft.Json;
using Voucher.APIHelper.Util;
using System.Threading.Tasks;
using Loyalty.Evoucher.Contants;
using Voucher.APIHelper.Log4net;
using System.Web;
using System.Web.Http.Results;
using System.Text;
using Loyalty.Services.LoyaltyCampaign;

namespace Loyalty.Evoucher.Controllers
{

    public class LoyaltyPaymentController : ApiController
    {
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("API/Loyalty-payment/CheckContractVoucherPayment")]
        public CheckContractVoucherPayment_Out CheckContractVoucherPayment(PaymentLoyaltyModel input)
        {
            var LogId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("CheckContractVoucherPayment input", input));
            var contracts_out = new List<lst_contract>();
            try
            {
                contracts_out = LoyaltyPaymentServices.check_contract_v3(input.contracts, sb);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("CheckContractVoucherPayment contracts_out", contracts_out));

                return new CheckContractVoucherPayment_Out { data = true, error = "", result = 1, contracts = contracts_out };
                }
            catch (Exception ex)
                {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("CheckContractVoucherPayment ex", ex));
                return new CheckContractVoucherPayment_Out { data = false, error = ex.Message, result = -1, contracts = new List<lst_contract>() };
                }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "CheckContractVoucherPayment", LogId.ToString());
            }            
        }
        /*
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("API/Loyalty-payment/ContractListPayment")]
        public ResponseModels<string> ContractListPayment(PaymentLoyaltyModel input)
        {
            var LogId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "ContractListPayment req ", LogId);
            var lst = new List<string>();
            int result = 0;
            string error = string.Empty;
            try
            {
                string contracts = string.Join(",", input.contracts);
                lst = LoyaltyPaymentServices.CheckContract(contracts, LogId);
                if (lst.Count > 0 && lst != null)
                {
                    result = 1;
                }
                else
                {
                    error = "danh sách không đủ điều kiện";
                }
                return new ResponseModels<string> { data = lst, error = error, result = result };
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, " ContractListPayment error ", LogId);
                return new ResponseModels<string> { data = null, error = ex.Message, result = -1 };
            } 
        }
        */
        
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("API/Loyalty-payment/ContractListPayment")]// kiem tra hd co no cuoc hay khong
        public ResponseModels<EstimatedPayment> ContractListPayment(PaymentLoyaltyModelv2 input)
        {
            var LogId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("input", input));
            
            var lstdataa = new List<datePayment>();
            var res = new ResponseModels<EstimatedPayment>{data= new List<EstimatedPayment>(), error="", result=0};
            try
            {
                //string contracts = string.Join(",", input.contracts);
                lstdataa = LoyaltyPaymentServices.CheckContractv2(input, sb);

                foreach (var item in lstdataa)
                {
                    List<EstimatedPayment> dataBillPrepaid = LoyaltyPaymentServices.GetDataPrepaidBilling(item, input.voucherType, sb);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("dataBillPrepaid", dataBillPrepaid));
                    if (dataBillPrepaid != null && dataBillPrepaid.Count > 0)
                    {
                        res.data.AddRange(dataBillPrepaid);
                    }
                    else
                {
                        var checkHidden = LoyaltyPaymentServices.checkConfigInvoicePrepaid(input.voucherType);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("checkHidden " + input.voucherType, checkHidden));

                        var all_evc_type = LoyaltyPaymentServices.get_all_evc_type_payment();
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("all_evc_type ", all_evc_type));

                        ContractBillingModel dataBill = LoyaltyPaymentServices.GetDataContractBilling(item, sb);
                        if(checkHidden == 1 || all_evc_type.Where(x=>x.evc_type.ToUpper() == input.voucherType.ToUpper() && x.check_estimate == 0).Any() == true)
                    {
                            EstimatedPayment ep = new EstimatedPayment { contract = item.contract.Replace(" ", ""), estimatedPayment = 0, paymentType = 1, serviceType = null, isHidden = checkHidden };
                            res.data.Add(ep);
                        }
                        else if (dataBill != null)
                        {
                            if (dataBill.InternetBilling > 0)
                            {
                                EstimatedPayment ep = new EstimatedPayment { contract = item.contract.Replace(" ", ""), estimatedPayment = (int)dataBill.InternetBilling, paymentType = 1, serviceType = "NET", isHidden = checkHidden };
                                res.data.Add(ep);
                            }
                            if (dataBill.IPTVBilling > 0)
                            {
                                EstimatedPayment ep = new EstimatedPayment { contract = item.contract.Replace(" ", ""), estimatedPayment = (int)dataBill.IPTVBilling, paymentType = 1, serviceType = "TV", isHidden = checkHidden };
                                res.data.Add(ep);
                            }
                            if (dataBill.CameraBilling > 0)
                            {
                                EstimatedPayment ep = new EstimatedPayment { contract = item.contract.Replace(" ", ""), estimatedPayment = (int)dataBill.CameraBilling, paymentType = 1, serviceType = "CMR", isHidden = checkHidden };
                                res.data.Add(ep);
                            }
                        }
                    }
                }

                res.result = 1;
                res.error = "";

                if(res.data.Count == 0)
                {
                    res.result = 0;
                    res.error = "danh sách không đủ điều kiện";
                }

                return res;
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("ContractListPayment error", ex));
                return new ResponseModels<EstimatedPayment> { data = null, error = ex.Message, result = -1 };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "ContractListPayment", LogId.ToString());
            }
        }
        
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]        
        [Route("API/Loyalty-payment/RedeemPayment")]
        public ResponseModel<bool> RedeemPayment(RedeemPaymentModel input)
        {
            var LogId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "RedeemPayment req ", LogId);
            var response = new ResponseModel<bool> { data = false, error = "voucher không hợp lệ", result = 0 };
            try
            {
                int objid = LoyaltyPaymentServices.GetObjid(input.contract);

                #region nếu có cước tạm tính thỏa đk -> call redeem serviceCode
                var evc_type = LoyaltyPaymentServices.get_evc_type(input.voucherCode);
                var contractPayments = new List<datePayment>();
                contractPayments.Add(new datePayment() { contract = input.contract, toDate = DateTime.Now.ToString("yyyy-MM-dd") });

                var input2 = new PaymentLoyaltyModelv2()
                {
                    voucherType = evc_type,
                    contractPayments = contractPayments
                };
                var contract_list_layments = ContractListPayment(input2);
                LoyaltyCommonServices.WriteToLog(contractPayments, "contractPayments", LogId);
                LoyaltyCommonServices.WriteToLog(contract_list_layments, "contract_list_layments", LogId);
                if(contract_list_layments.result == 1 && contract_list_layments.data.Count > 0)
                {
                    var item = contract_list_layments.data.Where(x => x.contract.ToUpper() == input.contract.ToUpper()
                                                                && x.isHidden == 0 
                                                                && x.paymentType == 2 
                                                                && !string.IsNullOrEmpty(x.bill_receipt)).FirstOrDefault();
                    if (item != null)
                    {
                        var input_hi = new ObjRedeemHi()
                        {
                            phone = input.phone,
                            bill_receipt = item.bill_receipt,
                            amount = 0,
                            evc_code = input.voucherCode,
                            obj_id = objid
                        };
                        return LoyaltyPaymentServices.hi_redeem_evc_loy(input_hi, "RedeemPayment");
                    }
                }
                #endregion

                
                bool isPendingVoucher = LoyaltyPaymentServices.CheckObjPendingVoucher(objid);
                LoyaltyCommonServices.WriteToLog(isPendingVoucher, "RedeemPayment isPendingVoucher ", LogId);
                if (isPendingVoucher)
                {
                    response.data = false;
                    response.error = "Khách hàng đang có voucher hẹ giờ";
                    response.result = 0;
                    return response;
                }
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        int res = connection.Execute(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment,
                        new
                        {
                            actionName = "ActiveVoucherPending",
                            contract = input.contract,
                            voucherCode = input.voucherCode,
                            typePayment=input.paymentType
                        }, transaction: transaction, commandType: CommandType.StoredProcedure);
                        LoyaltyCommonServices.WriteToLog(res, "RedeemPayment res ", LogId);

                        if (res == 2)
                        {
                            transaction.Commit();
                            response.data = true;
                            response.result = 1;
                            response.error = string.Empty;
                        }
                    }
                }
                return response; 
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, " RedeemPayment error ", LogId);
                return new ResponseModel<bool> { data = false, error = ex.Message, result = -1 };
            }            
        }

        [HttpPost]
        [Route("API/Loyalty-payment/ProcessLoyaltyPayment")]
        public async Task<bool> ProcessLoyaltyPayment()
        {
            Task success = LoyaltyPaymentServices.UpdateVoucherSuccessPayment();
            Task rollback = LoyaltyPaymentServices.UpdateVoucherRBPayment();
            Task MBNdevice = LoyaltyDeviceServices.MBNdeviceVoucher();

            Task RollbackVoucherByReceipts = LoyaltyPaymentServices.RollbackVoucherByReceipts();
            Task private_exprired = LoyaltyPaymentServices.update_privateCode_expired();
            Task cancelActionLoyTask = LoyaltyPaymentServices.CancelActionLoy();
            Task processRetryMarkExpiredVouchers = LoyaltyCampaignServices.ProcessRetryMarkExpiredVouchers();


            await Task.WhenAll(success, rollback, MBNdevice, RollbackVoucherByReceipts, private_exprired, cancelActionLoyTask, processRetryMarkExpiredVouchers);

            return true;
        }

        [HttpPost]
        //[Authorize(Roles = AuthorizeRole.SuperUser)]
        [Route("API/WarantyLoyalty/ProcessRedeemDeviceLoyaltyMBN")]
        public async Task<bool> ProcesscallbackLoy()
        {
            Task rcLoyatylPayment = CallbackCommonServices.processcallbackLoyatylPayment();
            Task activeDevicesTLS = LoyaltyDeviceServices.processActiveVoucherDevices();
            Task recareBillAddGold = LoyaltyPaymentServices.ProcessRecareBillAddPoint();

            await Task.WhenAll(rcLoyatylPayment, activeDevicesTLS, recareBillAddGold);

            return true;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty-payment/CheckPaymentUse")]
        public ResponseModel<int> CheckPaymentUse(TransCodeLoy input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, " GetInforVoucherPaymentUse req: ", logId);
            int res = 0;
            string dataHash = LoyaltyCommonServices.HashSha1(input.eVoucher + input.transCode);
            if (dataHash.ToLower() != input.checkSum)
            {
                return new ResponseModel<int> { data = res, result = 0, error = "Dữ liệu không hợp lệ" };
            }
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    res = connection.Query<int>(Contant.OS6_FPTVoucher_Loyalty, new
                    {
                        actionName = "CheckPaymentUse",
                        eventCode = input.eVoucher,
                        transCode = input.transCode,
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    LoyaltyCommonServices.WriteToLog(res, " GetInforVoucherPaymentUse res: ", logId);
                    if (res > 0)
                    {
                        return new ResponseModel<int> { data = res, result = 1, error = null };
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, " GetInforVoucherPaymentUse Error: ", logId);
                return new ResponseModel<int> { data = 0, result = -1, error = ex.Message };
            }
            return new ResponseModel<int> { data = res, result = 0, error = "không thành công" };
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Admin)]
        [Route("api/Loyalty-payment/GetInforVoucherByBill")]
        public ResponseModel<string> GetInforVoucherByBill(PaymentBillVoucherName input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, " GetInforVoucherByBill req: ", logId);            
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    string res = connection.Query<string>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment, new
                    {
                        actionName = "GetInforVoucherByBill",
                        @contract = input.contract,
                        @billNum = input.billNumber,
                    }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    LoyaltyCommonServices.WriteToLog(res, " GetInforVoucherByBill res: ", logId);
                    if (!string.IsNullOrEmpty(res))
                    {
                        return new ResponseModel<string> { data = res, result = 1, error = null };
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, " GetInforVoucherByBill Error: ", logId);
                return new ResponseModel<string> { data = "", result = -1, error = ex.Message };
            }
            return new ResponseModel<string> { data = "", result = 0, error = "không thìm thấy" };
        }


        [Route("API/LoyaltyOTT/UpdateStatusOTTCode")]
        [HttpPost]
        public ResponseModel<bool> UpdateStatusOTTCode(LoyaltyOTTModel input)
        {
            var logId = Guid.NewGuid();
            int idPrivateCode = 0;
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(input), " UpdateStatusOTTCode req ");
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        // kiểm tra voucher tồn tại
                        int isExist = connection.Query<int>("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                        new
                        {
                            ActionName = "checkExistvoucher",
                            transCode = input.transCode,
                            code = input.code,
                            EventCode = input.eventCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(isExist), " UpdateStatusOTTCode isExist ");
                        if (isExist.Equals(0))
                        {
                            return new ResponseModel<bool> { data = false, error = "Voucher không tồn tại", result = 0 };
                        }

                        int isUpdate = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                        new
                        {
                            ActionName = "UpdateStatusVoucherFPTplay",
                            transCode = input.transCode,
                            code = input.code,
                            EventCode = input.eventCode,
                            clientOTT = input.clientOTT
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);

                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(isUpdate), " UpdateStatusOTTCode isUpdate ");

                        if (isUpdate > 0)
                        {
                            transaction.Commit();
                        }
                        else
                        {
                            return new ResponseModel<bool> { data = false, error = "Active voucher không thành công", result = 0 };
                        }
                    }
                }
                var request = new SendLoyalty()
                {
                    mobileHiFpt = "",
                    redeemDate = "",
                    transCode = input.transCode,
                    voucherCode = input.code
                };
                idPrivateCode = CallbackCommonServices.GetPrivateCodeID(input.code);
                bool bSend = CallbackCommonServices.NewChangeStatusLoyaltyV2(request, idPrivateCode, logId.ToString());
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(bSend), " UpdateStatusOTTCode ChangeStatusLoyalty ");
                
                if (bSend)
                {
                    using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                    {
                        connection.Open();
                        using (var transaction = connection.BeginTransaction())
                        {
                            int isChangeStatus = connection.Execute("PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent",
                            new
                            {
                                ActionName = "ChangeStatusFptplay",
                                transCode = input.transCode,
                                code = input.code,
                                EventCode = input.eventCode
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(isChangeStatus), " UpdateStatusOTTCode isChangeStatus ");
                            if (isChangeStatus > 0)
                            {
                                transaction.Commit();
                                return new ResponseModel<bool> { data = true, error = null, result = 1 };
                            }
                            else
                            {
                                //gửi mail thông báo trường hợp lỗi
                                var req = new MailModelInput
                                {
                                    FromEmail = "<EMAIL>",
                                    Recipients = "<EMAIL> ",
                                    CarbonCopys = "",
                                    BlindCarbonCopys = "",
                                    Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                                    Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công (status = 5) FPT play", voucher = input.code, date = DateTime.Now.ToString() }),
                                    AttachFile = "",
                                    AttachUrl = ""
                                };
                                Email.SendMail(req);
                                return new ResponseModel<bool> { data = true, error = null, result = 1 };
                            }
                        }
                    }
                }
                else
                {
                    //gửi mail thông báo trường hợp lỗi
                    var req = new MailModelInput
                    {
                        FromEmail = "<EMAIL>",
                        Recipients = "<EMAIL> ",
                        CarbonCopys = "",
                        BlindCarbonCopys = "",
                        Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                        Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái Loyalty không thành công FPT play", voucher = input.code, date = DateTime.Now.ToString() }),
                        AttachFile = "",
                        AttachUrl = ""
                    };
                    Email.SendMail(req);
                }
                return new ResponseModel<bool> { data = false, error = "Thất bại", result = 0 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " + JsonConvert.SerializeObject(ex.Message), " UpdateStatusOTTCode Error ");
                return new ResponseModel<bool> { data = false, error = ex.Message, result = 0 };
            }
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty/TokenLoy")]
        public AuthorizationInfor TokenLoy()
        {
            LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
            AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, "");
            return aut;
        }
        
        #region payment HiFPT module
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Hifpt)]
        [Route("api/Loyalty-payment/HiFPT/GetListBill")]
        public ResponseModel<PaymentBillModelOutput> GetListBill(PaymentBillModel input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "GetListBill req ", logId);
            var res = new ResponseModel<PaymentBillModelOutput> { data = new PaymentBillModelOutput(), result=0, error="không tìm thấy" };
            
            res.data.billing = new List<Billing>();
            res.data.receipt = new List<Receipt>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    foreach (string bill in input.billing)
                    {
                        Billing bil = new Billing();
                        bil = connection.Query<Billing>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment, new
                        {
                            actionName = "GetBillings",
                            @billNum = bill,
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        if (bil != null)
                        {
                            bil.amount = LoyaltyPaymentServices.RoundTotalMoney(Convert.ToInt32(bil.amount), 10);
                            res.data.billing.Add(bil);
                        }
                    }
                    LoyaltyCommonServices.WriteToLog(res.data.billing, " GetListBill billing: ", logId);
                    foreach (string receipt in input.receipt)
                    {
                        //receipt là số khoản thu (không phải billNumber)
                        Receipt rec = new Receipt();
                        rec = connection.Query<Receipt>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment, new
                        {
                            actionName = "GetReceipt",
                            billNum = receipt,
                        }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        
                        if (rec != null)
                        {
                            rec.amount = LoyaltyPaymentServices.RoundTotalMoney(Convert.ToInt32(rec.amount), 10);
                            res.data.receipt.Add(rec);
                        }
                            
                    }
                    LoyaltyCommonServices.WriteToLog(res.data.receipt, " GetListBill receipt: ", logId);
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, " GetListBill Error: ", logId);
                return new ResponseModel<PaymentBillModelOutput> { data = null, result = -1, error = ex.Message };
            }
            if (res.data != null)
            {
                res.result = 1;
                res.error = "";
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Hifpt)]
        [Route("api/Loyalty-payment/HiFPT/GetListVoucher")]
        public ResponseModels<VoucherInforLoyalty> GetListVoucher(VoucherBill input)
        {
            int config_get_list = 0;
            if (input.debug > 0)
            {
                config_get_list = input.debug == 1 ? 1 : 0;
            }
            else
            {
                config_get_list = LoyaltyPaymentServices.get_config_value("get_evc_changed") > 0 ? 1 : 0;
            }

            if(config_get_list == 1)
            {
                return LoyaltyPaymentServices.get_evc_changed(input, "hi-GetListVoucher");
            }
            else
            {
                return LoyaltyPaymentServices.get_evc_changed_v2(input, "hi_GetListVoucher_v2");
            }
            
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Hifpt)]
        //[Utils.RateLimit(Seconds = 10)]
        [Route("API/Loyalty-payment/HiFPT/RedeemVoucherPaymentLoyalty")]
        public ResponseModel<bool> RedeemVoucherPaymentLoyalty(ObjRedeemHi input)
        {
            return LoyaltyPaymentServices.hi_redeem_evc_loy(input, "RedeemVoucherPaymentLoyalty");

        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Hifpt)]
        [Route("API/Loyalty-payment/HiFPT/GetHistoryPaymentVoucher")]
        public ResponseModel<PaymentBillModelOutput> GetHistoryPaymentVoucher(PaymentBillModel input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "GetHistoryPaymentVoucher req ", logId);
            var res = new ResponseModel<PaymentBillModelOutput> { data = new PaymentBillModelOutput(), result = 0, error = "Không tìm thấy" };
            res.data.billing = new List<Billing>();
            res.data.receipt = new List<Receipt>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    foreach (var item in input.billing)
                    {
                        Billing b = new Billing();
                        b = connection.Query<Billing>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment,
                        new
                        {
                            actionName = "GetBillingSuccess",
                            billNum = item,
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        if (b != null)
                        {
                            b.amount = LoyaltyPaymentServices.RoundTotalMoney(Convert.ToInt32(b.amount), 10);
                            res.data.billing.Add(b);
                        }
                        
                    }
                    LoyaltyCommonServices.WriteToLog(res.data.billing, "GetHistoryPaymentVoucher res.data.billing ", logId);
                    foreach (var item in input.receipt)
                    {
                        Receipt b = new Receipt();
                        b = connection.Query<Receipt>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment,
                        new
                        {
                            actionName = "GetReceiptSuccess",
                            billNum = item,
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        if (b != null)
                        {
                            b.amount = LoyaltyPaymentServices.RoundTotalMoney(Convert.ToInt32(b.amount), 10);
                            res.data.receipt.Add(b);
                        }
                    }
                    LoyaltyCommonServices.WriteToLog(res.data.receipt, "GetHistoryPaymentVoucher res.data.receipt ", logId);
                    if (res.data.receipt.Count > 0 || res.data.billing.Count > 0)
                    {
                        res.error = "";
                        res.result = 1;
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, " GetHistoryPaymentVoucher error ", logId);
                return new ResponseModel<PaymentBillModelOutput> { data = null, error = ex.Message, result = -1 };
            }
            return res;
        }
        #endregion

        #region payment FPT module
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Fpt)]
        [Route("api/Loyalty-payment/fpt-payment/GetListBill")]
        public ResponseModel<PaymentBillModelOutput> GetListBillFpt(PaymentBillModel input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "GetListBill req ", logId);
            var res = new ResponseModel<PaymentBillModelOutput> { data = new PaymentBillModelOutput(), result = 0, error = "không tìm thấy" };

            res.data.billing = new List<Billing>();
            res.data.receipt = new List<Receipt>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    List<Billing> billings = LoyaltyPaymentServices.GetBills(connection, input.billing);
                    LoyaltyCommonServices.WriteToLog(billings, " GetListBill billing: ", logId);

                    res.data.billing.AddRange(billings);

                    List<Receipt> receipts = LoyaltyPaymentServices.GetReceipts(connection, input.receipt);
                    LoyaltyCommonServices.WriteToLog(receipts, " GetListBill receipt: ", logId);

                    res.data.receipt.AddRange(receipts);
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, " GetListBill Error: ", logId);
                return new ResponseModel<PaymentBillModelOutput> { data = null, result = -1, error = ex.Message };
            }
            if (res.data != null)
            {
                res.result = 1;
                res.error = "";
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Fpt)]
        [Route("api/Loyalty-payment/fpt-payment/GetListVoucher")]
        public ResponseModels<VoucherInforLoyalty> GetListVoucherFpt(VoucherBill input)
        {
            int config_get_list = 0;
            if (input.debug > 0)
            {
                config_get_list = input.debug == 1 ? 1 : 0;
            }
            else
            {
                config_get_list = LoyaltyPaymentServices.get_config_value("get_evc_changed") > 0 ? 1 : 0;
            }

            if (config_get_list == 1)
            {
                return LoyaltyPaymentServices.get_evc_changed(input, "hi_GetListVoucherFpt");
            }
            else
            {
                return LoyaltyPaymentServices.get_evc_changed_v2(input, "hi_GetListVoucherFpt_v2");
            }
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Fpt)]
        [Utils.RateLimit(Seconds = 10)]
        [Route("API/Loyalty-payment/fpt-payment/RedeemVoucherPaymentLoyalty")]
        public ResponseModel<bool> RedeemVoucherPaymentLoyaltyFpt(ObjRedeemHi input)
        {
            return LoyaltyPaymentServices.hi_redeem_evc_loy(input, "RedeemVoucherPaymentLoyaltyFpt");

        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Fpt)]
        [Route("API/Loyalty-payment/fpt-payment/GetHistoryPaymentVoucher")]
        public ResponseModel<PaymentBillModelOutput> GetHistoryPaymentVoucherFpt(PaymentBillModel input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "GetHistoryPaymentVoucher req ", logId);
            var res = new ResponseModel<PaymentBillModelOutput> { data = new PaymentBillModelOutput(), result = 0, error = "Không tìm thấy" };
            res.data.billing = new List<Billing>();
            res.data.receipt = new List<Receipt>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    List<Billing> successBills = LoyaltyPaymentServices.GetSuccessBills(connection, input.billing);
                    LoyaltyCommonServices.WriteToLog(successBills, "GetHistoryPaymentVoucher res.data.billing ", logId);

                    res.data.billing.AddRange(successBills);

                    List<Receipt> successReceipts = LoyaltyPaymentServices.GetSuccessReceipts(connection, input.receipt);
                    LoyaltyCommonServices.WriteToLog(successReceipts, "GetHistoryPaymentVoucher res.data.receipt ", logId);

                    res.data.receipt.AddRange(successReceipts);

                    if (res.data.receipt.Count > 0 || res.data.billing.Count > 0)
                    {
                        res.error = "";
                        res.result = 1;
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, " GetHistoryPaymentVoucher error ", logId);
                return new ResponseModel<PaymentBillModelOutput> { data = null, error = ex.Message, result = -1 };
            }
            return res;
        }
        #endregion
    }
}
