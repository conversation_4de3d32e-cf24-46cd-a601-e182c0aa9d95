using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Voucher.APIHelper.Util;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Data;
using Loyalty.Evoucher.Models;
using Loyalty.Evoucher.Services;
using Loyalty.Evoucher.Contants;
using Voucher.APIHelper.ShareModel;
using System.Configuration;
using System.Threading.Tasks;
namespace Loyalty.Evoucher.Controllers
{
    public class LoyaltyHiFptController : ApiController
    {
        /*
        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Hifg)]
        [Route("api/Loyalty-HiFPT/RedeemVoucherCode")]
        public ResponseModel<bool> RedeemVoucherCode(UseVoucherModel input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "RedeemVoucherCode req  ", logId);
            
            var res = new ResponseModel<bool> { result = 0, data = false, error = "Voucher không hợp lệ" };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        string voucherType = HifptService.GetVoucherType(connection,transaction,input.voucherCode);
                        LoyaltyCommonServices.WriteToLog(voucherType, "RedeemVoucherCode voucherType  ", logId);
                        int effect = 0;
                        if(string.IsNullOrEmpty(voucherType))
                        {
                            return res;
                        }
                        if (voucherType.Equals(VoucherType.DEVICEHIFPT.ToString()))
                        {
                            effect = HifptService.RedeemVoucherDevice(connection,transaction,input);
                            LoyaltyCommonServices.WriteToLog(effect, "RedeemVoucherCode effect DEVICEHIFPT  ", logId);
                            bool isPendingVoucher = LoyaltyPaymentServices.UpdatePendingDSC(input.voucherCode,input.objId, logId);
                            LoyaltyCommonServices.WriteToLog(isPendingVoucher, "RedeemVoucherCode isPendingVoucher  ", logId);
                        }
                        if (voucherType.Equals(VoucherType.AIRCONCLEAN.ToString()) || voucherType.Equals(VoucherType.INSURANCE.ToString()))
                        {
                            effect = HifptService.RedeemVoucherNoDevice(connection, transaction, input, "RedeemVoucherNoDivice");
                            LoyaltyCommonServices.WriteToLog(effect, "RedeemVoucherCode effect No DEVICEHIFPT  ", logId);
                            var reqDSC = new SendLoyalty
                            {
                                mobileHiFpt = input.phone,
                                voucherCode = input.voucherCode
                            };
                            LoyaltyCommonServices.WriteToLog(reqDSC, "RedeemVoucherCode effect No DEVICEHIFPT UpdateSS req SendLoy ", logId);
                            int idPrivateCode =HifptService.GetPrivateCodeID(input.voucherCode);
                            bool isSendLoy = CallbackCommonServices.NewChangeStatusLoyaltyV2(reqDSC, idPrivateCode, logId.ToString());
                            if (isSendLoy)
                            {
                                effect = HifptService.RedeemVoucherNoDevice(connection, transaction, input, "UpdateStatusVoucherNoDivice");
                            }
                        }

                        if(effect >0)
                        {
                            transaction.Commit();
                            res.result = 1;
                            res.data = true;
                            res.error = "";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "RedeemVoucherCode error  ", logId);
                res.error=ex.Message;
                res.result=-1;
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Hifg)]
        [Route("api/Loyalty-HiFPT/CancelVoucher")]
        public ResponseModel<bool> CancelVoucher(UseVoucherModel input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "RedeemVoucherCode req  ", logId);
            var res = new ResponseModel<bool> { result = 0, data = false, error = "Voucher không hợp lệ" };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        string voucherType = HifptService.GetVoucherType(connection, transaction, input.voucherCode);
                        LoyaltyCommonServices.WriteToLog(voucherType, "RedeemVoucherCode voucherType  ", logId);
                        if (!voucherType.Equals(VoucherType.DEVICEHIFPT.ToString()))
                        {
                            return res;
                        }
                        var reqDSC = new SendLoyalty
                        {
                            mobileHiFpt = "",
                            voucherCode = input.voucherCode,
                            voucherPaymentStatus = "FAIL"
                        };
                        LoyaltyCommonServices.WriteToLog(reqDSC, "CancelVoucher  DEVICEHIFPT UpdateRb req SendLoy ", logId);
                        int idPrivateCode = HifptService.GetPrivateCodeID(input.voucherCode);
                        bool isSendLoy = CallbackCommonServices.NewChangeStatusLoyaltyV2(reqDSC, idPrivateCode, logId.ToString());

                        int updateCancel = connection.Execute(Contant.OS6_FPTVoucher_LoyaltyHiFPT, new
                        {
                            actionName = "CancelVoucherDivice",
                            voucherCode = input.voucherCode
                        }, commandType: CommandType.StoredProcedure);
                        LoyaltyCommonServices.WriteToLog(updateCancel, "CancelVoucher  updateCancel ", logId);
                        if (isSendLoy && updateCancel > 0)
                        {
                            transaction.Commit();
                            res.data = true;
                            res.result = 1;
                            res.error = "";
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "CancelVoucher  Error: ", logId);
                res.error = ex.Message;
                res.result = -1;
            }
            return res;
        }
        */

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty-HiFPT/ApplyVoucher")]
        public ResponseModel<bool> ApplyVoucher(VoucherHiFPTApply input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "ApplyVoucher req  ", logId);
            var res = new ResponseModel<bool> { data = false, error = "Voucher không hợp lệ", result = 0 };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        int save = connection.Execute(Contant.OS6_FPTVoucher_LoyaltyHiFPT,new {
                            actionName = "applyVoucher",
                            voucherCode = input.voucherCode ,
                            trans = input.transCode,
                            phone = input.phone,
                            orderCode = input.orderCode,
                            valueVoucher = input.valueVoucher
                        },commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);
                        if (save.Equals(3))
                        {
                            transaction.Commit();
                            res.data = true;
                            res.result = 1;
                            res.error = "";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "ApplyVoucher Error  ", logId);
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Hifg)]
        [Route("api/Loyalty-HiFPT/RedeemVoucherCode")]
        public ResponseModel<bool> RedeemVoucherCode(VoucherHiFPTRedeem input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "RedeemVoucherCode req  ", logId);
            var res = new ResponseModel<bool> { data = false, error = "Voucher không hợp lệ", result = 0 };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        int save = connection.Execute(Contant.OS6_FPTVoucher_LoyaltyHiFPT, new
                        {
                            actionName = "RedeemVoucherDivice",
                            voucherCode = input.voucherCode,
                            orderCode = input.orderCode,
                            phone = input.phone,
                            objid = input.objId
                        }, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);
                        LoyaltyCommonServices.WriteToLog(save, "RedeemVoucherCode save  ", logId);
                        if (save.Equals(2))
                        {
                            transaction.Commit();
                            res.data = true;
                            res.result = 1;
                            res.error = "";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "RedeemVoucherCode Error  ", logId);
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            return res;
        }

        [HttpPost]
        [Authorize(Roles = AuthorizeRole.Loyalty)]
        [Route("api/Loyalty-HiFPT/CancelVoucherCode")]
        public ResponseModel<bool> CancelVoucherCode(VoucherHiFPTApply input)
        {
            var logId = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog(input, "CancelVoucherCode req  ", logId);
            var res = new ResponseModel<bool> { data = false, error = "Voucher không hợp lệ", result = 0 };
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        int save = connection.Execute(Contant.OS6_FPTVoucher_LoyaltyHiFPT, new
                        {
                            actionName = "cancelVoucher",
                            voucherCode = input.voucherCode,
                            trans = input.transCode,
                            phone = input.phone
                        }, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);
                        LoyaltyCommonServices.WriteToLog(save, "CancelVoucherCode save  ", logId);
                        if (save.Equals(3))
                        {
                            transaction.Commit();
                            res.data = true;
                            res.result = 1;
                            res.error = "";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "CancelVoucherCode Error  ", logId);
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            return res;
        }
    }
}
