using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Contants
{
    public class Contant
    {
        public const string OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT";
        public const string OS6_FPTVoucher_LoyaltyPromotionEvent = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent";
        public const string OS6_FPTVoucher_LoyaltyPromotionEvent_MobileCard = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent_MobileCard";
        public const string OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN";
        public const string OS6_FPTVoucher_Loyalty_VoucherBooking = "PowerInside.dbo.OS6_FPTVoucher_Loyalty_VoucherBooking";
        public const string OS6_FPTVoucher_PromotionEventLoyaltyFilm = "PowerInside.dbo.OS6_FPTVoucher_PromotionEventLoyaltyFilm";
        public const string OS6_FPTVoucher_Loyalty = "PowerInside.dbo.OS6_FPTVoucher_Loyalty";
        public const string OS6_FPTVoucher_LoyaltyVoucherType = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyVoucherType";
        public const string OS6_FPTVoucher_LoyaltyHiFPT = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyHiFPT";
        public const string OSU6_FPTVoucher_AIRCONCLEANS_Servies = "PowerInside.dbo.OSU6_FPTVoucher_AIRCONCLEANS_Servies";
    }
    public class ACTION_OS6_FPTVoucher_LoyaltyPromotionEvent
    {
        public const string CheckConditionBookVCInvoiceLoyalty = "CheckConditionBookVCInvoiceLoyalty";
        public const string RedeemVoucherPayment = "RedeemVoucherPayment";
        public const string UpdateTimeQuotar = "UpdateTimeQuotar";
        public const string GetBillByContract = "GetBillByContract";
        public const string CheckBillUseVoucherPayment = "CheckBillUseVoucherPayment";
        public const string UpdateStatusUseVoucherPayment = "UpdateStatusUseVoucherPayment";
        public const string RollBackVoucherPayment = "RollBackVoucherPayment";
        public const string GetListVoucher = "GetListVoucher";
        public const string CheckContractVoucherPayment = "CheckContractVoucherPayment";
        public const string GetTypeVoucher = "GetTypeVoucher";
        public const string AddVoucherFPTplay = "AddVoucherFPTplay";
        public const string checkQuotarFPTplay = "checkQuotarFPTplay";
    }
    public enum LoyaltyVoucherType
    {
        VPN, VINMART, SAMSUNG, PAYTV, MOBILECARD, INVOICEPAYMENT, INTERNET, INSURANCE, IHOME, GOLDEN, GIFT, FRT, FPTPLAY, FOXY, FILM, DEVICEHIFPT, DEVICE, CLOUDCAMERA, AIRCONCLEAN, ULTRAFAST
    }
}