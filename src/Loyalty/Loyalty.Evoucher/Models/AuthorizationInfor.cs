using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using Voucher.APIHelper;

namespace Loyalty.Evoucher.Models
{
    public class LoginInfor
    {
        public string username { set; get; }
        public string password { set; get; }
    }

    public class AuthorizationInfor
    {
        public string access_token { set; get; }
        public string token_type { set; get; }
        public string scope { set; get; }
        public string iat { set; get; }
        public string expires_in { set; get; }
        public string jti { set; get; }
    }
    public class LoyaltyRedeemOutput
    {
        public string statusCode { set; get; }
        public string message { set; get; }
    }
    public class Login
    {
        public static string userName { get { return Utility.loyaltyapi_username; } }
        public static string passWord { get { return Utility.loyaltyapi_password; } }
    }
    public class SendMessageLoyalty
    {
        public string voucherCode { get; set; }
        public string mobileHiFpt { get; set; }
        public string voucherPaymentStatus { set; get; }
    }

    public class SendLoyalty
    {
        public string mobileHiFpt { get; set; }
        public string redeemDate { get; set; }
        public int? shopCode { set; get; }
        public string shopName { set; get; }
        public string transCode { set; get; }
        public string saleOrder { set; get; }
        public int? voucherActualUse { set; get; }
        public string voucherCode { set; get; }
        public string billContent { set; get; }
        public string billFromDate { set; get; }
        public string billNumber { set; get; }
        public int? billRemain { set; get; }
        public string billToDate { set; get; }
        public int? billTotalDebt { set; get; }
        public string voucherPaymentStatus { set; get; }
        public string contractNo { set; get; }
    }
    public class DataRecall
    {
        public string request { set; get; }
        public int idPrivateCode { set; get; }
    }
}