using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class LoyaltyEVoucherOutput
    {
        public string eVoucher { get; set; }

        public string description { get; set; }
        public string note { get; set; }
        public string officeBranch { get; set; }
        public string voucherType { get; set; }

        public string status { get; set; }

        public int? discountIPTV { get; set; }
        public int? discountNET { get; set; }
        public int? devicePromotionLoyalty { get; set; }
        public int? voucherValue { get; set; }
    }
    public enum GetListEnum
    {
        All, ACTIVE, DEACTIVE, EXPIRED
    }
}