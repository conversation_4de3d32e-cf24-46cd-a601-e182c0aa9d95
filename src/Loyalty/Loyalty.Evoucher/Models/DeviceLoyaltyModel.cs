using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class DeviceLoyaltyModel
    {
        [JsonIgnore]
        public int privateCodeID { set; get; }
        public string code { set; get; }
        public decimal DevicePromotionLoyalty { set; get; }
        public List<DevicePrice> Devices { set; get; }
    }
    public class Device
    {
        public int DeviceID { set; get; }
        public string Description { set; get; }
        public int Status { set; get; }
        [JsonIgnore]
        public int CustomerStatus { set; get; }
        [JsonIgnore]
        public int Deployment { set; get; }
        [JsonIgnore]
        public int PromotionStatus { set; get; }
        [JsonIgnore]
        public int ReturnStatus { set; get; }
    }
    public class DevicePrice : Device
    {
        public decimal Price { set; get; }
    }
    public class RedeemInputMBN
    {
        public string voucherCode { get; set; }
        public int orderCode { get; set; }
        public int objId { get; set; }
        public int orderType { get; set; }
        public List<RealUseVoucher> realUse { set; get; }
    }

    public class ObjVoucherMBN 
    {
        public int ID { set; get; }
        public string Code { set; get; }
        public string Phone { set; get; }
        public string RedeemDate { set; get; }
        public int OrderCode { set; get; }
        public int OrderType { set; get; }
        public int ObjID { set; get; }
        public string TransCode { set; get; }
        public string Contract { set; get; }
        public string BillNumber { set; get; }
    }

    public class RedeemTLSInput
    {
        public int orderCode { get; set; }
        public string voucherCode { get; set; }
        public int objId { get; set; }
        public int type { set; get; }
        public List<RealUseVoucher> realUse { set; get; }
    }
    public class RealUseVoucher
    {
        public int amount { set; get; }
        public int serviceCode { set; get; }
    }

    public class DeviceVoucherModelInput
    {
        public string voucherCode { get; set; }
        public int objId { get; set; }
        
    }
    public class RedeemToolInput : DeviceVoucherModelInput
    {
        public string billNumber { set; get; }
        public int type { set; get; }
    }
    public class ParapioraInput
    {
        public int LocationID { set; get; }
        public int CustomerStatus { set; get; }
        public int ReturnStatus { set; get; }
        public string CodeID { set; get; }
    }
    public class DeviceInfor
    {
        public int CodeID { set; get; }
        public long Price { set; get; }
        public string Name { set; get; }
        public int Status { set; get; }
    }

    public class ParapioraOutput
    {
        public List<Parapiora> data { set; get; }
    }
    public class Parapiora
    {
        public int CodeID { set; get; }
        public long Price { set; get; }
        public int ServiceCode { set; get; }
        public int CustomerStatus { set; get; }
        public int PriceID { set; get; }
        public string PriceName { set; get; }
        public string EquipmentName { set; get; }
        public int ReturnStatus { set; get; }
        public int PromotionStatus { set; get; }
        public int NumMax { set; get; }
        public int EFStatus { set; get; }
        public int Combo { set; get; }
        public int Deployment { set; get; }
        public long PriceNotVAT { set; get; }
        public string Description { set; get; }
        public string Name { set; get; }

    }

    public class InforUseVoucher
    {
        public int PrivateCodeID { set; get; }
        public string RedeemDate { set; get; }
        public string TransCode { set; get; }
        public string Phone { get; set; }
        public string BillNumber { set; get; }
        public string Contract { get; set; }
        public string VoucherCode { set; get; }
        public int ObjID { set; get; }
    }

    public class CheckStatusInput
    {
        public int ObjID { get; set; }
        public int OrderCode { get; set; }
        public int OrderType { get; set; }
    }

    public class CheckStatusOutput
    {
        public int statusCode { get; set; }
        public StatusCode data { get; set; }
    }
    public class StatusCode
    {
        public int status { get; set; }
    }

    public class DataJobActiveDevicesTLS
    {
        public string VoucherCode { set; get; }
        public int ObjID { set; get; }
    }
}