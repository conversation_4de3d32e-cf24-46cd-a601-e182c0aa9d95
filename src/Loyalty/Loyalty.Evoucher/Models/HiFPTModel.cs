using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class HiFPTModel
    {
    }
    public class BookHiFPTVoucherOP
    {
        public int statusCode { set; get; }
        public string message { set; get; }
        public BookHiFPTVoucherModel data { set; get; }
    }
    public class BookHiFPTVoucherModel
    {
        public string evoucher { set; get; }
        public string exp_date { set; get; }
        public string issuance_time { set; get; }
    }
    public class UseVoucherModel
    {
        public int objId { set; get; }
        public string voucherCode { set; get; }
        public string orderCode { set; get; }
        public string phone { set; get; }
        public int serviceCode { set; get; }
        public int valueVoucher { set; get; }
    }
    public class VoucherHiFPTRedeem : VoucherHiFPTApply
    {
        public int objId { set; get; }
    }
    public class VoucherHiFPTApply
    {
        public string voucherCode { set; get; }
        public string orderCode { set; get; }
        public string phone { set; get; }
        public string transCode { set; get; }
        public int valueVoucher { set; get; }
    }
}