using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class PaymentLoyaltyModel
    {
        public List<string> contracts { set; get; }
    }

    public class PaymentLoyaltyModelv2 
    {
        public string voucherType { set; get; }
        public List<datePayment> contractPayments { set; get; }
    }
    public class EstimatedPayment:Contract
    {
        public int estimatedPayment { set; get; }
        public string serviceType { set; get; }
        public int paymentType { set; get; }
        public int isHidden { set; get; } = 0;
        public string bill_receipt { set; get; }
    }

    public class ContractBilling
    {
        public ContractBillingModel data { set; get; }
        public string error { set; get; }
        public int statusCode { set; get; }
    }
    public class ContractBillingModel
    {
        public string Contract { set; get; }
        public string BillDate { set; get; }
        public double InternetAmount { set; get; }
        public double InternetVATRate { set; get; }
        public double InternetVAT { set; get; }
        public double InternetBilling { set; get; }
        public double IPTVAmount { set; get; }
        public double IPTVVATRate { set; get; }
        public double IPTVVAT { set; get; }
        public double IPTVBilling { set; get; }
        public double CameraBilling { get; set; }
    }
    public class Contract
    {
        public string contract { set; get; }
    }
    public class datePayment:Contract
    {
        public string toDate{set;get;}
    }
    public class RedeemPaymentModel : Contract
    {
        public string voucherCode { set; get; }
        public int paymentType { set; get; }
        public string phone { set; get; }
    }

    public class DataRBJob
    {
        public int ID { set; get; }
        public int VoucherPrivateCodeID { set; get; }
        public string Code { set; get; }
        public string Phone { set; get; }
        public string BillNumber { set; get; }
    }
    public class DataSuccessJob : DataRBJob
    { 
        
        public int Total {set;get;}
        public int voucherActualUse {set;get;}
        public int billRemain {set;get;}
        public string Content {set;get;}
        public string FromDate {set;get;}
        public string ToDate {set;get;}
        public string RedeemDate { set; get; }
        public string Contract { set; get; }
        
    }

    public class PaymentBillVoucherName
    {
        public string billNumber { set; get; }
        public string contract { set; get; }
    }

    public class PaymentBillModel
    {
        public List<string> billing { set; get; }
        public List<string> receipt { set; get; } 
    }

    public class PaymentBillModelOutput
    {
        public List<Billing> billing { set; get; }
        public List<Receipt> receipt { set; get; }
    }
    public class Billing : EvcBill
    {
        public string bill_number { set; get; }
        
    }
    public class EvcBill
    {
        public string evc_code { set; get; }
        public decimal amount { set; get; }
    }
    public class Receipt : EvcBill
    {
        public string bill_receipt  { set; get; }
    }
    public class ObjRedeemHi : Receipt
    {
        public int obj_id { set; get; }
        public string phone { set; get; }
    }
    public class VoucherBill : PaymentBillVoucherName
    {
        public string phone { set; get; }
        public int debug { set; get; } = 0;
    }
    public class VoucherInforLoyalty : EvcBill
    {
        public string title { set; get; }
        public string expiry { set; get; }
        public string statusCode { set; get; }
    }
    public class GetBillDetailInput
    {
        public string BillNumber { set; get; }
        public int Source { set; get; }
    }
    public class GetBillDetailOutput
    {
        public string Message { set; get; }
        public int ResID { set; get; }
        public List<BillDetail> Data { set; get; }
    }
    public class BillDetail
    {
        public int ID { set; get; }
        public int ReceiptID { set; get; }
        public string BillNumber { set; get; }
        public int ServiceType { set; get; }
        public int Fee { set; get; }
        public string BranchCode { set; get; }
        public string DescVN { set; get; }
        public string DescEN { set; get; }
        public int SaleId { set; get; }
        public int FeeVoucher { set; get; }
        public int MonthUse { set; get; }
    }
    public class LoyaltyListVoucher
    {
        public int statusCode { set; get; }
        public bool success { set; get; }
        public string message { set; get; }
        public List<VoucherLoyaltyPayment> data { set; get; }
    }
    public class VoucherLoyaltyPayment
    {
        public string voucherName { set; get; }
        public string eventCode { set; get; }
        public string voucherCode { set; get; }
        public string expiredDate { set; get; }
    }

    public class HiFPTVoucherFromLoy
    {
        public string BillNumber { set; get; }
        public int ScheduleID { set; get; }
        public string Code { set; get; }
        public int ServiceCode { set; get; }
    }

    public class CheckContractVoucherPayment_Out
    {
        public bool data { set; get; }
        public int result { set; get; }
        public string error { set; get; }
        public List<lst_contract> contracts { set; get; }
    }

    public class cf_evc_type
    {
        public string evc_type { set; get; }
        public string date_type { set; get; }
        public int service_type { set; get; }
        public int month_use { set; get; }
    }

    public class lst_contract
    {
        public string voucherType { set; get; }
        public List<string> contracts { set; get; }
    }

    public class all_evc_type
    {
        public string evc_type { set; get; }
        public int check_estimate { set; get; }
    }
}