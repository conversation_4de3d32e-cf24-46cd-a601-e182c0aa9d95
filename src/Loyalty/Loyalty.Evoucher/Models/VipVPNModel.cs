using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class VipVPNModel
    {
        public string Code { set; get; }
        public string Account { set; get; }
    }
    public class ActiveVPN : VipVPNModel
    {
        public string activeDate { set; get; }
    }

    public class ActiveVPNModel : ActiveVPN
    {
        public string transCode { set; get; }
    }
    public class CreateAccountModel
    {
        public bool success { set; get; }
        public string message { set; get; }
        public DataAccountModel data { set; get; }
    }
    public class DataAccountModel
    {
        public string message { set; get; }
        public List<AccountModel> data { set; get; }
    }
    public class AccountModel
    {
        public string username { set; get; }
        public string password { set; get; }
        public string prefix { set; get; }
        public string trialDate { set; get; }
        public string expiresDate { set; get; }
    }
    public class VipVPNCheckStatusModel
    {
        public bool success { set; get; }
        public List<DataVPNStatusModel> data { set; get; }
        public string message { set; get; }
    }
    public class DataVPNStatusModel
    {
        public string activeDate { set; get; }
        public string expiresDate { set; get; }
        public string username { set; get; }
        public int trialDate { set; get; }
    }
}