using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class GetListExchanges
    {
        public int ObjID { get; set; }
        public string Contract { get; set; }
        public string Phone { get; set; }
        public string TransCode { get; set; }
        public List<string> VoucherTypeName { get; set; }
    }

    public class GetListExchangesOutput
    {
        public int VoucherCode { get; set; }
        public string VoucherTypeName { get;set; }
        public decimal VoucherAmount { get; set; }
        public DateTime ExpiredDate { get; set; }
    }

    public class VoucherLoy
    {
        public string OrderCode { get; set; }
        public string VoucherCode { get; set; }
        public decimal VoucherAmount { get; set; }
        public string VoucherTypeName { get; set; }
        public int TypeRedeem { get; set; }
    }

    public class RedeemLoyaltyInput
    {
        public int ObjID { get; set; }
        public string Contract { get; set; }
        public string Phone { get; set; }
        public string BillReceipt { get; set; }
        public string BillNumber { get; set; }
        public string TransCode { get; set; }
        public string Description { get; set; }
        public List<VoucherLoy> Vouchers { get; set; }
    }

    public class VoucherInfoInput
    {
        public int ObjID { get; set;}
        public string Contract { get; set; }
        public List<VoucherInfo> VoucherInfos { get; set; }
    }

    public class VoucherInfo
    {
        public int VoucherCode { get; set; }
        public string VoucherTypeName { get; set; }
        public decimal VoucherAmount { get; set; }
        public DateTime ExpiredDate { get; set; }

    }

    public class VoucherInfoOutput
    {
        public List<VoucherInfo> VoucherInfos { get; set; }
    }

    public class  PrivateCodeModel
    {
        public int Id { get; set; } 
        public string Code { get; set; } 
    }

    #region ultrafast
    public class UltrafastCampaignKey
    {
        public string CampaignInforKey { get; set; }
        public int MonthValue { get; set; }
    }

    public class UpdateInactiveModel
    {
        public int UseMonth { get; set; }
        public string PhoneNumber { get; set; }
        public string PhoneReceiveCode { get; set; }
        public string CampaignInfo { get; set; }
        public string ExpiredDate { get; set; }
        public string CodeString { get; set; }
    }

    #endregion

}