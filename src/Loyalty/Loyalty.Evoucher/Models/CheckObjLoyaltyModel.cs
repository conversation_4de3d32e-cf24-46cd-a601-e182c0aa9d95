using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class CheckObjLoyaltyModel
    {
        public List<ObjLoyaltyInfo> data { set; get; }
        public int statusCode { set; get; }
        public bool success { set; get; }
    }
    public class ObjLoyaltyInfo
    {
        public int coinsNearExpire { set; get; }
        public string coinsNearExpireDate { set; get; }
        public string dob { set; get; }
        public string fId { set; get; }
        public string fullName { set; get; }
        public string joinLoyaltyDate { set; get; }
        public long loyaltyCoins { set; get; }
        public long loyaltyPoints { set; get; }
        public string loyaltyRank { set; get; }
        public string loyaltySeniority { set; get; }
        public int loyaltyStatus { set; get; }
        public string loyaltyStatusName { set; get; }
        public string potential { set; get; }
        public int potentialFlag { set; get; }
        public int seniorityInMonths { set; get; }
    }
    public class HeaderAPI
    {
        public string key { set; get; }
        public string value { set; get; }
    }
}