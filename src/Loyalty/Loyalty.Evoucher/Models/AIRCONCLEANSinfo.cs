using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class AIRCONCLEANSinfo
    {
        public int PromotionEventID { set; get; }
        public int VoucherValue { set; get; }
        public string VoucherType { set; get; }
        public int ExpiredDay { set; get; }
        public string TransCode { set; get; }
    }

    public class BookVoucherHifptInput
    {
        public string merchant_id { get; set; }
        public string mobile { get; set; }
        public int coins { get; set; }
        public int voucher_value { get; set; }
        public int number_of_days { get; set; }
        public string execution_time { get; set; }
        public string signature { get; set; }
    }
    public class PromotionData
    {
        public string promotion_code { get; set; }
        public string promotion_end_usable { get; set; }
    }

    public class ApiResponse
    {
        public int statusCode { get; set; }
        public string message { get; set; }
        public PromotionData data { get; set; }
    }
    public class VoucherRedeemInput
    {
        public string mobileHiFpt { get; set; }
        public string voucherCode { get; set; }
        public string billNumber { get; set; }
        public int billTotalDebt { get; set; }
        public DateTime redeemDate { get; set; }
        public string contractNo { get; set; }
        public string signature { get; set; }
    }
    public class VoucherRedeemOutput
    {
        public string message { get; set;}  
        public int Result { get; set;}  

    }

    public class CheckVoucher
    {
        public int ID { get; set; }
        public int Status { get; set; }
        public string TransCode { get; set; }
        public DateTime ExpiredDate { get; set; }
    }
}