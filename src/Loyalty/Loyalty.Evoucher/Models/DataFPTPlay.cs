using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class DataFPTPlay
    {
    }
    public class BookVoucherFPTplay
    {
        public string voucher_type { get; set; }
        public int expired_date { get; set; }

        [JsonProperty("event")]
        public string event_Code { get; set; }
        public string trans_id { get; set; }
        public string signature { get; set; }
    }
    public class DataVoucherFPTplay
    {
        public string code { get; set; }
        public string expired_date { get; set; }
    }
    public class BookVoucherFPTplayOut
    {
        public string error_code { get; set; }
        public string message { get; set; }
        public DataVoucherFPTplay data { get; set; }
    }
}