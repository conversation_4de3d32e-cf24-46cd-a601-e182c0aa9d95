using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class FRTModel
    {
    }
    public class EventFRTinfo
    {
        public int ID { set; get; }
        public string Name { set; get; }
        public string EventCode { set; get; }
        public int QuotaPrivateCode { set; get; }
        public int countBook { set; get; }
        public int Point { set; get; }
        public int ExpiredDay { set; get; }
        public string Branch { set; get; }
    }

    public class FRTGetVoucherInputModel
    {
        public string codeLoaiVoucher { set;get; }
        public int limitDay { set; get; }
        public string numberPhone { set; get; }
        public int point { set; get; }
        public string supplier { set; get; }
    }
    public class FRTCreateVoucherInputModel
    {
        public string code { set; get; }
        public string name { set; get; }
        public int limitDate { set; get; }
        public int quota { set; get; }
        public int point { set; get; }
        public string supplier { set; get; }
        public int priceVoucher { set; get; }
        public string description { set; get; }
    }
    public class FRTCreateVoucherOutputModel
    {
        public bool status { set; get; }
        public string error_code { set; get; }
        public string messages { set; get; }
        public int statusCode { set; get; }
    }
    public class FRTGetVoucherOutputModel
    {
        public string code { set; get; }
        public string expiredDate { set; get; }
        public string messages { set; get; }
        public int statusCode { set; get; }
    }
}