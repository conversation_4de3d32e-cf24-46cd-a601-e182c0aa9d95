using Loyalty.Evoucher.Contants;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Loyalty.Evoucher.Models
{
    public class EventCodeModel
    {
        public string eVoucher { set; get; }
    }
    public class TransCodeLoy : EventCodeModel
    {
        public string transCode { set; get; }
        public string checkSum { set; get; }
    }

    public class OutputMessage
    {
        public int statusCode { set; get; }
        public string message { set; get; }
    }
    public class PaymentInfor
    {
        public string BillNumber { set; get; }
        public int Total { set; get; }
        public int TotalPay { set; get; }
        public string Content { set; get; }
        public int VoucherUse { set; get; }
        public int TotalDebt { set; get; }
        public string FromDate { set; get; }
        public string ToDate { set; get; }
    }
    public class FRTRedemModel : EventCodeModel
    {
        public string saleOrder { set; get; }
        public string redeemDate { set; get; }
        public int price_real { set; get; }
        public int shopCode { set; get; }
        public string shopName { set; get; }
        public string phone { set; get; }
    }
    public class RedeemFRTinfo 
    {        
        public string RedeemDate { get; set; }
        public string TransCode { get; set; }
        public string Phone { get; set; }
    }
    public class BookVoucherModel : EventCodeModel
    {
        public string transCode { get; set; }
        public string phone { get; set; }
        public int point { set; get; }
    }
    public class TypeVoucher
    {
        public int VoucherType { get; set; }
        public string VoucherTypeName { get; set; }
        public int ID { get; set; }
        public int ExpiredDay { get; set; }
        public string EventCode { get; set; }
        public int QuotaPrivateCode { get; set; }
        public int Exchange { get; set; }
    }
    public class BookVoucherOutput
    {
        public string eVoucher { get; set; }
        public string SerialNumber { get; set; }
        
        public string voucherType { get; set; }
        [JsonConverter(typeof(DateTimeFormatOutput))]
        public DateTime? validTo { get; set; }
    }

    public class RedeemInput
    {
        public string voucherCode { get; set; }

        public int objId { get; set; }
        public string billnumber { get; set; }
    }
    public class RedeemOutput
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public StatusRedeem status { get; set; }


        public string statusDescription { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public PaymentUseVoucher infor { set; get; }
    }
    public class LoyaltyDescription
    {
        public const string ACTIVE = "voucher trạng thái khả dụng";
        public const string DEACTIVE = "voucher đã bị ngừng kích hoạt";
        public const string EXPIRED = "voucher đã hết thời gian kích hoạt";
        public const string REDEEMERROR = "có lỗi trong quá trình redeem";
        public const string NOTFOUND = "thông tin mã Voucher không tồn tại";
        public const string REDEEMSUCCESS = "redeem Voucher thành công";
        public const string EXISTS = "HĐ đã được áp dụng eVoucher ";
    }
    public enum StatusRedeem
    {
        SUCCESS, FALSE
    }
    public class PaymentUseVoucher
    {
        public decimal RealValueUse { set; get; }
        public decimal DebtAmount { set; get; }
        public string billNumber { set; get; }
        public string voucher { set; get; }
    }
    public enum STATUSVOUCHER
    {
        SUCCESS, FAIL
    }
    public enum VoucherType
    {
        INTERNET = 1, PAYTV = 2,
        DEVICE = 3, VPN = 4, 
        FPTPLAY = 5, FILM = 6, 
        FOXY = 7, GIFT = 8, UNKNOW = -1,
        IHOME = 33, SAMSUNG = 9, INVOICEPAYMENT = 11, MOBILECARD = 34, FRT = 35, VINMART = 36, GOLDEN=37, AIRCONCLEAN=39, INSURANCE=40, DEVICEHIFPT=41
    }
}