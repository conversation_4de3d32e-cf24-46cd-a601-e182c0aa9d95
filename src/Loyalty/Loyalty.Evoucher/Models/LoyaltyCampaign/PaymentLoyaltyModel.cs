using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Loyalty.Models.LoyaltyCampaign
{
    public class Order_Apply_Point
    {
        
        public int OrderStatus { get; set; }
        public string Contract { get; set; }
        public string OrderCode { get; set; } 
        public string RegCode { get; set; } 
        public long OrderCreateDate { get; set; }
        public List<int> OrderService { get; set; }
    }

    public class OrderService
    {
        public int NET { get; set; } = 0;
        public int PAY { get; set; } = 0;
        public int CAM { get; set; } = 0;
    }

    public class request_log
    {
        public string Contract { get; set; }
        public int ObjID { get; set; }
        public string OrderCode { get; set; }
        public string ActionCode { get; set; }
        public string Phone { get; set; }
        public string RequestId { get; set; }
        public string ActionDate { get; set; }
        public bool IsSuccess { get; set; }
        public string Request { get; set; }
    }

    public class Order_Apply_Point_Voucher {         
        public string eventCode { get; set; }
        public string voucherCode { get; set; }
        public int privateCodeId { get; set; }
        public string voucherType { get; set; }
        public int voucherValue { get; set; }
    }
    public class event_prepaid_config
    {
        public string action_code { get; set; }
        public int prepaid { get; set; }
        public int month_used { get; set; }
        public string event_code { get; set; }
        public int event_id { get; set; }
    }

    public class Apply_Action
    {
        public double OrderStatus { get; set; }
        public string Contract { get; set; }
        public int ObjID { get; set; }
        public string OrderCode { get; set; }
        public string RegCode { get; set; }
        public List<Service> Services { get; set; }
        public List<Product> Products { get; set; }
        public CustomerInfor CustomerInfor { get; set; }
    }

    public class Service
    {
        public int ServiceID { get; set; }
        public List<SubServiceType> SubServiceTypes { get; set; }
    }

    public class SubServiceType
    {
        public int SubServiceTypeID { get; set; }
        public List<SubService> SubServices { get; set; }
    }

    public class SubService
    {
        public int SubServiceID { get; set; }
        public int ServiceCode { get; set; }
        public float PrePaid { get; set; }
        public float MonthUsed { get; set; }
        public decimal Total { get; set; }
        public decimal Total_VAT { get; set; }
    }

    public class Product
    {
        public int ServiceID { get; set; }
        public List<SubServiceType> SubServiceTypes { get; set; }
    }

    public class CancelActionModel
    {
        public int idRQ { get; set; }
        public string contract { get; set; }
        public string actionCode { get; set; }
        public string mobile { get; set; }
        public string requestId { get; set; }
    }
    public class CustomerInfor
    {
        public int BranchCode { get; set; }
        public int LocationID { get; set; }
        public int DistrictID { get; set; }
        public int WardID { get; set; }
        public int BuildingID { get; set; }
        public int CusTypeID { get; set; }
        public int CusTypeL2ID { get; set; }
        public int ObjectTypeID { get; set; }
        public string CustomerRank { get; set; }
        public int LoyaltyStatus { get; set; }
        public string ContractGT { get; set; }
    }

    public class CancelTSD{
        public string contract { get; set; }
        public int ObjID { get; set; }
        public string OrderCode { get; set; }
    }

    public class GetDataCancelTSD{
        public int idRQ { get; set; }
        public string contract { get; set; }
        public string actionCode { get; set; }
        public string mobile { get; set; }
        public string requestId { get; set; }
        public int privateCodeId { get; set; }
        public string privateCode { get; set; }
        public string cancelType { get; set; }
    }

    public class ReceiptPayment
    {
        public string contract { get; set; }
        public string receipt_number { get; set; }
    }

    public class ReceiptPayment_GetData
    {
        public string Mesage { get; set; }
        public ReceiptPayment_Evc Data { get; set; }
    }

    public class ReceiptPayment_Evc 
    {
        public string Contract { get; set; }
        public int ObjId { get; set; }
        public string Phone { get; set; }
        public string ActionCode { get; set; }
        public string EventCode { get; set; }
        public string PrivateCode { get; set; }
        public int PrivateCodeId { get; set; }
        public string VoucherType { get; set; }
        public int Discount { get; set; }
        public int Prepaid { get; set; }
    }

    public class PointToBill_Get
    {
        public string Contract { get; set; }
        public int ObjID { get; set; }
        public string Phone { get; set; }
        public string OrderCode { get; set; }
        public string ActionCode { get; set; }
        public int Prepaid { get; set; }
    }

    #region retry update hết hạn    
    public class VouchersToBeExpired
    {
        public int PrivateCodeId { get; set; }
        public string PrivateCode { get; set; }
    }
    #endregion

    #region FGold dynamic value
    public class PolicyAproveFGold
    {
        public string Contract { get; set; }
        public int ObjId { get; set; }
        public string OrderCode { get; set; }
        public List<EstimateGoldService> Services { get; set; }
        public int GeneralCodeId { get; set; }
    }
    public class CskhPolicyToFGold
    {
        public string Mesage { get; set; }
        public Order_Apply_Point_Voucher evc { get; set; }
    }
    public class NotedGold
    {
        public string RequestId { get; set; }
        public int FGold { get; set; }
        public DateTime SuccessDate { get; set; }
        public int Status { get; set; }
        public string Description { get; set; }
    }
    public class EstimateGold
    {
        public int LocationId { get; set; }
        public int BranchCode { get; set; }
        public List<EstimateGoldService> Services { get; set; }
        public int GeneralCodeId { get; set; } = 0;
    }
    public class EstimateGoldService
    {
        public int ServiceId {  get; set; }
        public int SubServiceTypeId {  get; set; }
        public int SubServiceId {  get; set; }
        public float Prepaid {  get; set; }
        public float MonthUsed {  get; set; }
    } 

    public class EstimateGoldOut
    {
        public int ServiceId { get; set; }
        public int SubServiceTypeId { get; set; }
        public int SubServiceId { get; set; }
        public float Prepaid { get; set; }
        public float MonthUsed { get; set; }
        public int Point { get; set; }
        public string ActionCode { get; set; }
    }

    public class ActionCode_MonthUse
    {
        public string ActionCode { get; set; }
        public int MonthUse { get; set; }
    }
    #endregion
}