using Loyalty.Evoucher.Contants;
using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;
using System.Data;
using System.Configuration;
using System.Net.Http;
using System.Net;
using System.Text;
using System.IO;
using System.Net.Http.Headers;
using System.Globalization;

namespace Loyalty.Evoucher.Services
{
    public class LoyaltyVPNService
    {
        public static ResponseModel<BookVoucherOutput> BookVoucherVPN(SqlConnection connection, SqlTransaction transaction, BookVoucherModel input, TypeVoucher typeData, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "BookVoucherVPN req: ");

            try
            {
                //kiểm tra quotar voucher 
                var quotar = connection.Query<int>(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN,
                    new
                    {
                        ActionName = "checkQuotarVPN",
                        EventCode = input.eVoucher
                    }, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();


                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(quotar), "BookVoucherVPN quotar: ");

                if (quotar.Equals(0))
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "hết quotar", result = 0 };
                }
                string url = Utility.UrlVPN.ToString();
                string endpointCreate = Utility.vpn_endpoint_create.ToString();
                string fileKey = @"~/Keys/VPNkeys/public.key";
                Guid log = Guid.Parse(logId);
                string responseAPI = CallAPIVPN(url, endpointCreate, typeData.ExpiredDay.ToString(), fileKey, log);
                if (string.IsNullOrEmpty(responseAPI))
                {
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject("no responseAPI"), "BookVoucherVPN : ");

                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Lấy voucher thất bại", result = 0 };
                }
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(responseAPI), "BookVoucherVPN res api: ");

                CreateAccountModel bookVoucherVPN = new CreateAccountModel { data = new DataAccountModel { message = null, data = new List<AccountModel>() }, success = false, message = null };
                bookVoucherVPN = JsonConvert.DeserializeObject<CreateAccountModel>(responseAPI);
                if (bookVoucherVPN == null)
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Lấy voucher thất bại", result = 0 };
                }
                string code = string.Concat("U:", bookVoucherVPN.data.data[0].username, "_P:", bookVoucherVPN.data.data[0].password);
                var pars = new
                {
                    ActionName = "AddVoucherVPN",
                    code = code,
                    usernamevpn = bookVoucherVPN.data.data[0].username,
                    EventCode = typeData.EventCode,
                    transCode = input.transCode,
                    expiredDate = bookVoucherVPN.data.data[0].expiresDate
                };
                int isAddPrivate = connection.Execute(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN,
                    pars, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(isAddPrivate), "BookVoucherVPN isAddPrivate: ");
                if (isAddPrivate > 0)
                {
                    transaction.Commit();
                    DateTime convertedDate = DateTime.SpecifyKind(DateTime.Parse(bookVoucherVPN.data.data[0].expiresDate), DateTimeKind.Utc);
                    //var bookVoucherOutput = new BookVoucherOutput { eVoucher = code, validTo = Convert.ToDateTime(bookVoucherVPN.data.data[0].expiresDate), voucherType = VoucherType.VPN };
                    var bookVoucherOutput = new BookVoucherOutput { eVoucher = code, validTo = Convert.ToDateTime(convertedDate), voucherType = typeData.VoucherTypeName };
                    return new ResponseModel<BookVoucherOutput> { data = bookVoucherOutput, error = null, result = 1 };
                }
                return new ResponseModel<BookVoucherOutput> { data = null, error = "Đã xảy ra lỗi", result = 0 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(ex.Message), "BookVoucherVPN Error: ");
                return new ResponseModel<BookVoucherOutput> { data = null, error = ex.Message, result = 0 };
            }
        }
        private static string CallAPIVPN(string _url, string endpoint, string trialDate, string pathFile, Guid logid)
        {
            try
            {
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                    //Address = new Uri("http://isc-proxy.hcm.fpt.vn:80")
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                var multiForm = new MultipartFormDataContent();
                multiForm.Add(new StringContent(trialDate), "trialDate");
                FileStream fs = File.OpenRead(System.Web.Hosting.HostingEnvironment.MapPath(pathFile));
                multiForm.Add(new StreamContent(fs), "publicKey", Path.GetFileName(System.Web.Hosting.HostingEnvironment.MapPath(pathFile)));
                LoyaltyCommonServices.WriteToLog(multiForm, "CallAPIVPN multiForm ", logid);
                LoyaltyCommonServices.WriteToLog(proxy, "CallAPIVPN proxy ", logid);
                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(20);
                    LoyaltyCommonServices.WriteToLog(_url + endpoint, "CallAPIVPN _url+endpoint ", logid);
                    client.DefaultRequestHeaders.Add("token", Utility.vpn_token.ToString());

                    var response = client.PostAsync(_url + endpoint, multiForm).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    return r;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), "CallAPIVPN Error: ");
                return null;
            }
        }
    }
}