using Loyalty.Evoucher.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;

namespace Loyalty.Evoucher.Services
{
    public class LoyaltyBoxService
    {
        private const string OS6_FPTTV_EVC_GetBoxType = "PowerTV.dbo.OS6_FPTTV_EVC_GetBoxType";
        public static List<LoyaltyBoxModel> GetBoxType(string contracts)
        {
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    var res = connection.Query<LoyaltyBoxModel>(OS6_FPTTV_EVC_GetBoxType, new
                    {
                        Contract = contracts
                    }, commandType: CommandType.StoredProcedure).ToList();
                    return res;
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }
    }
}