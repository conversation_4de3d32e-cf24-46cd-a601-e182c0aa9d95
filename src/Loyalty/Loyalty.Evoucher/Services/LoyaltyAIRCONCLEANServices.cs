using Loyalty.Evoucher.Contants;
using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;
using System.Data;
using System.Configuration;
using System.Net.Http;
using System.Net;
using System.Text;
using System.Security.Cryptography;

namespace Loyalty.Evoucher.Services
{
    public class LoyaltyAIRCONCLEANServices
    {
        public static ResponseModel<BookVoucherOutput> BookVoucherAIRCONCLEANS(SqlConnection connection, SqlTransaction transaction, BookVoucherModel input, TypeVoucher typeData, string logId)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("----------");
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS input" , input));
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS typeData", typeData));

            if (input.point <= 0)
            {
                return new ResponseModel<BookVoucherOutput> { data = null, result = -1, error = "point kh�ng d�ng d?nh d?ng" };
            }
            try
            {
                // ki?m tra quotar voucher
                var VoucherInfor = connection.Query<AIRCONCLEANSinfo>(
                    Contant.OSU6_FPTVoucher_AIRCONCLEANS_Servies,
                    new
                    {
                        actionName = "GetInfor",
                        eventCode = typeData.EventCode
                    }, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();

                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS VoucherInfor", VoucherInfor));

                if (VoucherInfor == null)
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "S? lu?ng quota d� h?t", result = 0 };
                }

                string url = GetConfig("hifpt-staging", connection, transaction);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS url", url));

                string SECRET_KEY = GetConfig("SECRET_KEY_BOOKVOUCHER_HIFPT", connection, transaction);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS SECRET_KEY", SECRET_KEY));

                string s = "FCONNECT_VSML" + input.phone + input.point.ToString() + VoucherInfor.VoucherValue.ToString() + SECRET_KEY;
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS s", s));

                string signature = GetHash(s);

                var req = new BookVoucherHifptInput
                {
                    merchant_id = "FCONNECT_VSML",
                    mobile = input.phone,
                    coins = input.point,
                    voucher_value = VoucherInfor.VoucherValue,
                    number_of_days = VoucherInfor.ExpiredDay,
                    execution_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    signature = signature
                };
                string data = JsonConvert.SerializeObject(req);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS req", req));

                string responseAPI = CallAPI(url, "/hi-ecom-promotion-api/v1/public/evc/get-promotion-code", data);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS responseAPI", JsonConvert.DeserializeObject<ApiResponse>(responseAPI)));
                if (string.IsNullOrEmpty(responseAPI))
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "L?y voucher th?t b?i", result = 0 };
                }

                var BookVoucherVSML = new ApiResponse();
                BookVoucherVSML = JsonConvert.DeserializeObject<ApiResponse>(responseAPI);
                if (BookVoucherVSML.statusCode != 0)
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = BookVoucherVSML.message, result = 0 };
                }

                var pars = new
                {
                    ActionName = "AddPrivateCode",
                    Code = BookVoucherVSML.data.promotion_code,
                    PromotionEventID = VoucherInfor.PromotionEventID,
                    ExpiredDate = BookVoucherVSML.data.promotion_end_usable,
                    TransCode = input.transCode,
                    Phone = input.phone,
                    point = input.point,
                    Signature = signature

                };
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS pars", pars));

                int isAddPrivate = connection.Execute(
                    Contant.OSU6_FPTVoucher_AIRCONCLEANS_Servies,
                    pars, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);

                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS isAddPrivate", isAddPrivate));
                if (isAddPrivate > 0)
                {
                    transaction.Commit();
                    var bookVoucherOutput = new BookVoucherOutput { eVoucher = BookVoucherVSML.data.promotion_code, validTo = Convert.ToDateTime(BookVoucherVSML.data.promotion_end_usable), voucherType = typeData.VoucherTypeName };
                    return new ResponseModel<BookVoucherOutput> { data = bookVoucherOutput, error = null, result = 1 };
                }
                return new ResponseModel<BookVoucherOutput> { data = null, error = "�� x?y ra l?i", result = 0 };
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("BookVoucherAIRCONCLEANS erorr", ex));
                return new ResponseModel<BookVoucherOutput> { data = null, error = ex.Message, result = 0 };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "BookVoucherAIRCONCLEANS", logId);
            }
        }
        public static string CallAPI(string _url, string endpoint, string _data)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(_url+endpoint), "CallAPI req: ");
            L.Mes(Level.INFO, _data, "CallAPI data: ");
            try
            {
                var proxy = new WebProxy
                {
                    //Address = new Uri("http://isc-proxy.hcm.fpt.vn:80")
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(_url);
                    //client.DefaultRequestHeaders.Add("Authorization", Utility.loyalty_FRT_authen.ToString());
                    var data = new System.Net.Http.StringContent(_data, Encoding.UTF8, "application/json");
                    var response = client.PostAsync(endpoint, data).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    return r;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), "CallAPI Error: ");
                return null;
            }
        }

        public static string GetConfig(string key, SqlConnection connecttion, SqlTransaction transaction)
        {
            return connecttion.Query<string>(Contant.OSU6_FPTVoucher_AIRCONCLEANS_Servies, new
            {
                ActionName = "GetConfig",
                Key = key
            }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static string GetHash(string input)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(input);
            SHA256Managed sha256 = new SHA256Managed();
            byte[] hash = sha256.ComputeHash(bytes);

            string signature = BitConverter.ToString(hash).Replace("-", "").ToLower();
            return signature;
        }
    }
}