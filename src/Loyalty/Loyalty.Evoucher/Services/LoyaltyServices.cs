using Loyalty.Evoucher.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using System.Text;
using System.EnterpriseServices.CompensatingResourceManager;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using NLog.LayoutRenderers;
using Newtonsoft.Json.Linq;
using Voucher.APIHelper.ShareModel;
using System.Security.Policy;

namespace Loyalty.Evoucher.Services
{
    public class LoyaltyServices
    {
        private const string OS6_FPTVoucher_LoyaltyService = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyService";
        private const string OS6_FPTVoucher_Fsalego_Services = "PowerInside.dbo.OS6_FPTVoucher_Fsalego_Services";
        public static ResponseModels<GetListExchangesOutput> GetListExchanges(GetListExchanges input)
        {
            var data = new List<GetListExchangesOutput>();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetListExchanges input", input));

            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    foreach (var vouchertype in input.VoucherTypeName)
                    {
                        var infor = connection.Query<GetListExchangesOutput>(OS6_FPTVoucher_LoyaltyService, new
                        {
                            ActionName = "GetListExchanges",
                            VoucherTypeName = vouchertype
                        }, commandType: CommandType.StoredProcedure).ToList();

                        if (infor != null)
                        {
                            data.AddRange(infor);
                        }
                    }
                }
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("data", data));


                return new ResponseModels<GetListExchangesOutput>() { result = 1, error = "", data = data };
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("e", e));
                return new ResponseModels<GetListExchangesOutput>() { result = -1, error = e.Message, data = new List<GetListExchangesOutput>() };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "GetListExchanges", "");
            }

        }

        public static ResponseModel<bool> Check(string VoucherCode, string VoucherType)
        {
            StringBuilder sb = new StringBuilder();
            var rs = new ResponseModel<bool>() { data = false, error = "Không tìm thấy.", result = 0 };
            try
            {
                using (var conn = new SqlConnection(Utility.ConnRead))
                {
                    var check = conn.Query<int>(OS6_FPTVoucher_Fsalego_Services, new
                    {
                        ActionName = "CheckVoucher",
                        PrivateCode = VoucherCode,
                        VoucherType = VoucherType
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("check", check));

                    if (check > 0)
                    {
                        rs.data = true;
                        rs.error = "";
                        rs.result = 1;

                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Exception", e));
                rs.error = "Lỗi hệ thống.";
                rs.result = -1;
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "CheckVoucher", "");
            }
            return rs;
        }


        public static ResponseModel<bool> Redeem(RedeemLoyaltyInput input)
        {
            Guid logid = Guid.NewGuid();
            var sb = new StringBuilder();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Redeem input", input));

            try
            {
                using (var conn = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    conn.Open();
                    using (var transaction = conn.BeginTransaction())
                    {
                        foreach (var item in input.Vouchers)
                        {
                            item.VoucherCode = item.VoucherCode.ToUpper();
                            if (new[] { "FSAFEGO", "ULTRAFAST" }.Contains(item.VoucherTypeName.ToUpper()))
                            {
                                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Start vc", item.VoucherCode));
                                var rd = RedeemFsafego(conn, transaction, input, item, sb, logid.ToString());
                                if (!rd)
                                {
                                    return new ResponseModel<bool>() { data = false, error = "Không thành công.", result = 0 };
                                }
                            }
                        }
                        transaction.Commit();
                    }
                }

                return new ResponseModel<bool>() { data = true, error = "", result = 1 };
            }
            catch (Exception e)
            {

                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("e", e));
                return new ResponseModel<bool>() { data = false, error = "Lỗi hệ thống.", result = -1 };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "Redeem", logid.ToString());
            }
        }
        public static bool RedeemFsafego(SqlConnection conn, SqlTransaction transaction, RedeemLoyaltyInput input, VoucherLoy voucher, StringBuilder sb, string logid)
        {
            var privateCode = conn.Query<PrivateCodeModel>(OS6_FPTVoucher_Fsalego_Services, new
            {
                ActionName = "Redeem",
                PrivateCode = voucher.VoucherCode,
                Phone = input.Phone,
                VoucherType = voucher.VoucherTypeName,
            }, transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("privateCode", privateCode));

            if (privateCode == null) return false;

            var req = new SendLoyalty
            {
                contractNo = input.Contract,
                mobileHiFpt = input.Phone,
                voucherCode = voucher.VoucherCode,
                voucherPaymentStatus = "SUCCESS"
            };

            bool isSendLoy = CallbackCommonServices.NewChangeStatusLoyaltyV2(req, privateCode.Id, logid);
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("isSendLoy", isSendLoy));
            return true;
        }

        #region UltraFast
        public static ResponseModel<BookVoucherOutput> UpdateInactive(SqlConnection conn, SqlTransaction trann, string eventCode, string phone, int dateExpired, string voucherType, string transCode, string logid, Guid glogid)
        {
            try
            {
                var expiredDate = DateTime.Now.AddDays(dateExpired).ToString("yyyy-MM-dd 23:59:59");
            var checkCallFSI = conn.Query<int>(OS6_FPTVoucher_Fsalego_Services, new
            {
                ActionName = "CheckCallFSI",
                EventCode = eventCode
            }, trann, commandType: CommandType.StoredProcedure).FirstOrDefault();
            LoggerKafka.WriteLogKafka(checkCallFSI, "UpdateInactive checkCallFSI", logid.ToString());
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", glogid, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(checkCallFSI), "UpdateInactive checkCallFSI: ");

                if (checkCallFSI == 0) return new ResponseModel<BookVoucherOutput> { data = null, error = "EventCode không thỏa mãn.", result = 0 };

            var keyInactive = conn.Query<UltrafastCampaignKey>(OS6_FPTVoucher_Fsalego_Services, new
            {
                ActionName = "GetKeyInactive",
                    EventCode = eventCode
            }, trann, commandType: CommandType.StoredProcedure).FirstOrDefault();
            LoggerKafka.WriteLogKafka(keyInactive, "UpdateInactive keyInactive", logid.ToString());
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", glogid, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(keyInactive), "UpdateInactive keyInactive: ");

                if (keyInactive == null) return new ResponseModel<BookVoucherOutput> { data = null, error = "Không tìm thấy key active.", result = 0 };

            var req = new UpdateInactiveModel
            {
                UseMonth = keyInactive.MonthValue,
                PhoneNumber = phone,
                PhoneReceiveCode = phone,
                CampaignInfo = keyInactive.CampaignInforKey,
                    ExpiredDate = expiredDate
            };
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", glogid, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(req), "UpdateInactive req api uf: ");
            var response = Utility.PostAsync(Utility.get_appsettings("fsi_domain") + Utility.get_appsettings("fsi_inactive_ep"), JsonConvert.SerializeObject(req));
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", glogid, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(response), "UpdateInactive res api uf: ");

            var res = JObject.Parse(response.Result.data);
                var bookVoucherOutput = new BookVoucherOutput { eVoucher = res["Data"]?["Codestring"].ToString() ?? "", validTo = DateTime.ParseExact(expiredDate, "yyyy-MM-dd HH:mm:ss", null), voucherType = voucherType };
                if (bookVoucherOutput.eVoucher != "")
                {
                    var ultrafast_insert = Ultrafast_Insert(conn, trann, eventCode, phone, transCode, bookVoucherOutput.eVoucher);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", glogid, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ultrafast_insert), "UpdateInactive insert: ");

                    trann.Commit();
                    return new ResponseModel<BookVoucherOutput> { data = bookVoucherOutput, error = null, result = 1 };
                }

            }
            catch (Exception e)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", glogid, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(e.Message), "UpdateInactive Exception: ");
                return new ResponseModel<BookVoucherOutput> { data = null, error = "Có lỗi.", result = 0 };
            }
            return new ResponseModel<BookVoucherOutput> { data = null, error = "Có lỗi.", result = 0 };
        }

        public static int Ultrafast_Insert(SqlConnection conn,SqlTransaction trann,string eventCode,string phone,string transCode,string privateCode)
        {
            return conn.Execute(OS6_FPTVoucher_Fsalego_Services, new
            {
                ActionName = "ultrafast_insert",
                EventCode = eventCode,
                PrivateCode = privateCode,
                Phone = phone,
                TransCode = transCode
            }, trann, commandType: CommandType.StoredProcedure);
        }


        #endregion
    }
}