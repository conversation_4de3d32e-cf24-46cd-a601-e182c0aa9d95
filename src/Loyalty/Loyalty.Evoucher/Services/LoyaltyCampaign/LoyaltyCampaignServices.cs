using Dapper;
using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Loyalty.Models.LoyaltyCampaign;
using Loyalty.Evoucher.Services;
using APIMBS.Models.SalePlatform;
using System.Security.Policy;
using static System.Net.WebRequestMethods;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices.WindowsRuntime;
using System.Diagnostics.Contracts;
using Voucher.APIHelper.ShareModel;
using Voucher.APIHelper.Util;


namespace Loyalty.Services.LoyaltyCampaign
{
    public class LoyaltyCampaignServices
    {
        public static string OS6_FPTVoucher_Campaign_FoxGold_NewCustomer = "PowerInside.dbo.OS6_FPTVoucher_Campaign_FoxGold_NewCustomer";
        public static string OSU6_FPTVoucher_Job_RetryCancelActionLoy = "PowerInside.dbo.OSU6_FPTVoucher_Job_RetryCancelActionLoy";
        public static string OSU6_FPTVoucher_CskhPolicyToFGold = "PowerInside.dbo.OSU6_FPTVoucher_CskhPolicyToFGold";
        public static string order_apply_points(Apply_Action input, StringBuilder sb, Guid logid)
        {
            try
            {
                using (var conn = new SqlConnection(Utility.ConnWrite))
                {
                    var action_code = check_prepaid_order(conn, input);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("action_code", action_code));

                    if(action_code == null) return "Không có campaign.";

                    var obj = get_obj_info(conn, input.Contract);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("obj", obj));

                    if (string.IsNullOrEmpty(obj.Item3)) return "Không tìm thấy SÐT.";

                    var exist_submit = check_exist_submit(conn, obj.Item2);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("exist_submit", exist_submit));
                    if (exist_submit > 0) return "ObjID đã tồn tại.";

                    var is_gold_day = check_gold_day(conn, input.OrderCode);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("is_gold_day", is_gold_day));
                    if (!is_gold_day) return "Campaign đang off.";

                    submit_action_code_v2(conn, obj, action_code.action_code, input.OrderCode, sb, logid,action_code.month_used);

                }
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("order_apply_points Exception", e));
            }
            return "";
        }

        //public static string order_apply_points_v2(Apply_Action input, StringBuilder sb, Guid logid)
        //{
        //    try
        //    {
        //        using (var conn = new SqlConnection(Utility.ConnWrite))
        //        {
        //            var action_code = check_prepaid_order(conn, input);
        //            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("action_code", action_code));

        //            if (action_code == null) return "Không có campaign";

        //            var obj = get_obj_info(conn, input.Contract);
        //            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("obj", obj));

        //            if (string.IsNullOrEmpty(obj.Item3)) return "Không tùm th?y SÐT";

        //            var exist_submit = check_exist_submit(conn, obj.Item2);
        //            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("exist_submit", exist_submit));
        //            //if (exist_submit > 0) return "exist_submit exists.";

        //            var is_gold_day = check_gold_day(conn, input.OrderCode);
        //            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("is_gold_day", is_gold_day));
        //            if (!is_gold_day) return "Không ph?i ngày vàng";

        //            submit_action_code_v2(conn, obj, action_code.action_code, input.OrderCode, sb, logid, 6);

               
        //        }
        //    }
        //    catch (Exception e)
        //    {
        //        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("order_apply_points Exception", e));
        //    }
        //    return "";
        //}

        public static event_prepaid_config check_prepaid_order(SqlConnection conn, Apply_Action input)
        {
            var event_prepaid = conn.Query<event_prepaid_config>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "get_promotion_event_novc",
                regCode = input.RegCode
            }, commandType: CommandType.StoredProcedure).ToList();

            var CheckLocalType = conn.Query<int>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "CheckLocalType",
            }, commandType: CommandType.StoredProcedure).ToList();

            var event_prepaid_check = new List<event_prepaid_config>();

            foreach (var service in input.Services.Where(x => x.ServiceID == 1))
        {
                foreach (var subtype in service.SubServiceTypes)
                {
                    foreach (var subservice in subtype.SubServices)
            {
                        if (event_prepaid != null && subservice != null)
                        {
                            var matchingEvents = event_prepaid.Where(x => x.prepaid == subservice.PrePaid && 
                                                                        x.month_used == subservice.MonthUsed);

                            if (CheckLocalType != null && CheckLocalType.Any())
                            {
                                matchingEvents = matchingEvents.Where(x => CheckLocalType.Contains(subservice.SubServiceID));
                            }

                            if (matchingEvents.Any())
                        {
                                event_prepaid_check.AddRange(matchingEvents);
                            }
                        }
                    }
                }
            }

            if (event_prepaid_check.Count == 0) return null;

            return event_prepaid_check.OrderByDescending(x => x.prepaid).FirstOrDefault();
        }

        public static bool submit_action_code(SqlConnection conn,Tuple<string,int,string> obj, string action_code, string orderCode,StringBuilder sb, Guid logid, int? monthUsed = null, Order_Apply_Point_Voucher evc = null)
        {
            try
            {
                var requestId = Guid.NewGuid().ToString();
                string formattedDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                var req = new
                {
                    objId = obj.Item2,
                    actionCode = action_code,
                    mobile = obj.Item3,
                    requestId = requestId,
                    actionDate = formattedDate,
                    contract = obj.Item1,
                    voucher = evc
                };
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code req", req));

                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, logid.ToString());

                string data = JsonConvert.SerializeObject(req);
                string endpoint = "/internal/api/request-action";
                List<HeaderAPI> headers = new List<HeaderAPI>();
                headers.Add(new HeaderAPI { key = "Authorization", value = "Bearer " + aut.access_token });
                string dataOutput = LoyaltyCommonServices.callapi(string.Concat(WebAPIHelper.loyaltyapi, endpoint), "POST", data, "application/json; charset=utf-8", headers);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code dataOutput", dataOutput));
                dynamic respose = JsonConvert.DeserializeObject<dynamic>(dataOutput);

                var log = apply_point_log(conn, obj.Item1, obj.Item2, orderCode, action_code, obj.Item3, requestId, formattedDate, respose.success == true ? 1 : 0, data, dataOutput, 1, monthUsed);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code log", log));

                if (respose.success == true)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code ex", ex.Message));
                return false;
            }
        }

        public static Tuple<string, int, string> get_obj_info(SqlConnection conn, string contract)
        {
            return conn.Query<Tuple<string, int, string>>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "get_obj_info",
                contract = contract,
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static bool check_gold_day(SqlConnection conn, string orderCode)
        {

            var rs = conn.Query<int>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "check_gold_day",
                orderCode = orderCode
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            return rs > 0;
        }

        public static bool valid_service(List<int> service)
        {
            foreach (int serviceId in service)
            {
                if (serviceId != 1 && serviceId != 7) { return false; }
            }
            return true;
        }

        public static string apply_point_log(SqlConnection conn, string Contract, int ObjID, string OrderCode, string ActionCode, string Phone, string RequestId, string ActionDate, int IsSuccess, string Request,string Response, int RQStatus,int? prepaid, Order_Apply_Point_Voucher evc = null)
        {
            try
            {
                conn.Execute(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                {
                    action_name = "insert_log",
                    @contract = Contract,
                    @objid = ObjID,
                    @orderCode=OrderCode,
                    @actionCode=ActionCode,
                    @phone=Phone,
                    @requestId=RequestId,
                    @actionDate=ActionDate,
                    @isSuccess=IsSuccess,
                    @rq= Request,
                    @Response= Response,
                    @prepaid= prepaid,
                    @privateCodeId= evc?.privateCodeId ?? 0,
                    @requestStatus = RQStatus
                }, commandType: CommandType.StoredProcedure);
            }
            catch (Exception e)
            {
                return "apply_point_log: " + e.Message;
            }
            return "apply_point_log: OK";
        }

        public static void update_voucher()
        {
            var logId = Guid.NewGuid();
            var list_data = get_data_update_expired();
            LoyaltyCommonServices.WriteToLog(list_data, "update_voucher list_data", logId);
            foreach (var item in list_data)
            {
                var checkClear = true;
                if (item.Item4 > 0)
                {
                    checkClear = LoyaltyPaymentServices.ClearVoucherOnlineRecept(item.Item3, item.Item4, logId);
                }

                if(!checkClear) continue;

                var req = new SendLoyalty
                {
                    voucherCode = item.Item2,
                    voucherPaymentStatus = "CANCEL"
                };
                LoyaltyCommonServices.WriteToLog(req, "UpdateRb req SendLoy ", logId);
                bool isSendLoy = CallbackCommonServices.NewChangeStatusLoyaltyV2(req, (int)item.Item1, logId.ToString());
                LoyaltyCommonServices.WriteToLog(isSendLoy, "isSendLoy ", logId);
                if (isSendLoy)
                {
                    var update_private = private_update_expired((int)item.Item1);
                    LoyaltyCommonServices.WriteToLog(update_private, "update_private ", logId);
                }
            }
        }

        public static List<Tuple<long, string,string,int>> get_data_update_expired()
        {
            using (var conn = new SqlConnection(Utility.ConnWrite))
            {
                try
                {
                    List<Tuple<long, string, string, int>> result = conn.Query<Tuple<long, string, string, int>>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                        {
                            action_name = "get_data_update_expired"
                        },commandType: CommandType.StoredProcedure).ToList();

                    return result;
                }
                catch (Exception ex)
                {
                    LoyaltyCommonServices.WriteToLog(ex.Message, "get_data_update_expired error", Guid.NewGuid());
                    return new List<Tuple<long, string, string, int>>();
                }
            }
        }


        public static int private_update_expired(int privateCodeId)
        {
            using (var conn = new SqlConnection(Utility.ConnWrite))
            {
                return conn.Execute(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                {
                    action_name = "private_update_expired",
                    privateCodeId = privateCodeId
                }, commandType: CommandType.StoredProcedure);
            }
        }

        public static string saleClub_submit_action_loyalty(StringBuilder sb, Guid logid, SalePolicyRedeemEVC input) 
        {
            try
            {
                using (var conn = new SqlConnection(Utility.ConnWrite))
                {
                    var exist_submit = check_exist_submit(conn, input.objId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("exist_submit", exist_submit));
                    if(exist_submit > 0) return "exist_submit exists.";

                    var promotionEventId = get_promotion_event(conn, input);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("promotionEventId", promotionEventId));

                    if (promotionEventId == null) return "promotionEventId not found.";

                    var event_privateCode = get_event_private(conn, input, promotionEventId.event_id);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("event_privateCode", event_privateCode));

                    if (event_privateCode == null) return "event_privateCode not found.";

                    var obj_info = get_obj_info_by_objid(conn, input.objId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("obj_info", obj_info));

                    if (obj_info == null) return "obj_info not found.";

                    int sync_evc = sync_evc_qlcs_foxgold(conn, input.objId, input.RegCode, event_privateCode.privateCodeId, event_privateCode.voucherType);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("sync_evc", sync_evc));

                    submit_action_code_v2(conn, obj_info, promotionEventId.action_code, input.OrderCode, sb, logid, promotionEventId.month_used, event_privateCode);
                }
                
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_loyalty ex", ex));
            }

            return sb.ToString();
        }

        private static event_prepaid_config get_promotion_event(SqlConnection conn, SalePolicyRedeemEVC input)
        {
            var event_prepaid = conn.Query<event_prepaid_config>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "get_promotion_event_saleclub",
                regCode = input.RegCode
            }, commandType: CommandType.StoredProcedure).ToList();

            var event_prepaid_check = new List<event_prepaid_config>();

            foreach (var service in input.Services.Where(x=>x.ServiceID==1))
            {
                foreach(var subtype in service.SubServiceTypes)
                {
                    foreach(var subservice in subtype.SubServices)
                    {
                        if(event_prepaid.Where(x=>x.prepaid == subservice.PrePaid && x.month_used == subservice.MonthUsed)?.Any() == true)
                        {
                            event_prepaid_check.AddRange(event_prepaid.Where(x => x.prepaid == subservice.PrePaid && x.month_used == subservice.MonthUsed));
                        }
                    }
                }
            }

            if (event_prepaid_check.Count == 0) return null;

            return event_prepaid_check.OrderByDescending(x=>x.prepaid).FirstOrDefault();
        }

        public static Tuple<string, int, string> get_obj_info_by_objid(SqlConnection conn, int objid)
        {
            return conn.Query<Tuple<string, int, string>>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "get_obj_info_by_objid",
                objid = objid,
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static Order_Apply_Point_Voucher get_event_private(SqlConnection conn, SalePolicyRedeemEVC input, int promotionEventId)
        {
            return conn.Query<Order_Apply_Point_Voucher>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "get_event_private",
                objid = input.objId,
                orderCode = input.RegCode,
                promotionEventId = promotionEventId
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static int check_exist_submit(SqlConnection conn, int objid)
        {
            return conn.Query<int>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "check_exist_submit",
                objid = objid,

            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static int sync_evc_qlcs_foxgold(SqlConnection conn, int objid, string orderCode, int privateCodeId, string voucherType)
        {
            var i = conn.Execute(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "sync_evc_qlcs_foxgold",
                objid = objid,
                orderCode = orderCode,
                privateCodeId = privateCodeId,
                voucherType = voucherType
            }, commandType: CommandType.StoredProcedure);
            return i;
        }

        public static void submit_action_code_v2(SqlConnection conn, Tuple<string, int, string> obj, string action_code, string orderCode, StringBuilder sb, Guid logid, int? monthUsed = null, Order_Apply_Point_Voucher evc = null)
        {
            var requestId = Guid.NewGuid().ToString();
            string formattedDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            var req = new
            {
                objId = obj.Item2,
                actionCode = action_code,
                mobile = obj.Item3,
                requestId = requestId,
                actionDate = formattedDate,
                contract = obj.Item1,
                voucher = evc
            };
            try
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code req", req));

                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, logid.ToString());

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var dataRQ = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(req), Encoding.UTF8, "application/json");

                    // Log the request before sending
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code request", dataRQ));

                    var response = client.PostAsync("/internal/api/request-action", dataRQ).Result;
                    response.EnsureSuccessStatusCode();

                    // Log the response after receiving
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code response", response));

                    string result = response.Content.ReadAsStringAsync().Result;
                    dynamic respose = JsonConvert.DeserializeObject<dynamic>(result);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code respose", respose));

                    var log = apply_point_log(conn, obj.Item1, obj.Item2, orderCode, action_code, obj.Item3, requestId, formattedDate, respose.success == true ? 1 : 0, JsonConvert.SerializeObject(req), result, 1, monthUsed, evc);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code log", log));
                }
            }
            catch (HttpRequestException httpEx)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code httpEx", httpEx));
                var log = apply_point_log(conn, obj.Item1, obj.Item2, orderCode, action_code, obj.Item3, requestId, formattedDate, 0, JsonConvert.SerializeObject(req), httpEx.Message, 0, monthUsed, evc);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code log", log));
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_action_code ex", ex.Message));
            }
        }

        public static int retry_submit_action_loy()
        {
            StringBuilder sb = new StringBuilder();
            var logid = Guid.NewGuid();
            try
            {
                using (var conn = new SqlConnection(Utility.ConnWrite))
                {
                    var data = conn.Query<Tuple<int, string>>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                    {
                        action_name = "get_data_retry_submit_action_loy"
                    }, commandType: CommandType.StoredProcedure).ToList();
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("retry_submit_action_loy data", data));

                    LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                    AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, logid.ToString());

                    foreach (var item in data) 
                    {
                    using (var client = new HttpClient())
                    {
                            client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                            var dataRQ = new System.Net.Http.StringContent(item.Item2, Encoding.UTF8, "application/json");

                            // Log the request before sending
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("retry_submit_action_loy request", dataRQ));

                            var response = client.PostAsync("/internal/api/request-action", dataRQ).Result;
                            response.EnsureSuccessStatusCode();

                            // Log the response after receiving
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("retry_submit_action_loy response", response));

                            string result = response.Content.ReadAsStringAsync().Result;
                            dynamic respose = JsonConvert.DeserializeObject<dynamic>(result);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("retry_submit_action_loy respose", respose));

                            var update = conn.Execute(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                            {
                                action_name = "update_retry_submit_action_loy",
                                logIdRQ = item.Item1,
                                isSuccess = respose.success == true ? 1 : 0
                            }, commandType: CommandType.StoredProcedure);
                            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("retry_submit_action_loy update", update));
                        }
                    }
                }
            }
            catch (HttpRequestException httpEx)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("retry_submit_action_loy httpEx", httpEx));
                return 502;
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("retry_submit_action_loy ex", ex));
                return -1;
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "retry_submit_action_loy", logid.ToString());
            }
            return 1;
        }

        #region thu hồi điểm khi hủy tsd
        public static int submit_cancel_action_loy(SqlConnection conn, GetDataCancelTSD model, StringBuilder sb, Guid logid)
        {
            var requestStatus = 0;
            var cancelActionSuccess = 0;
            dynamic respose = null; 
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, logid.ToString());
                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var _model = JsonConvert.SerializeObject(model);
                    var dataRQ = new System.Net.Http.StringContent(_model, Encoding.UTF8, "application/json");

                    // Log the request before sending
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_cancel_action_loy request", dataRQ));

                    var response = client.PostAsync("/internal/api/request-action/cancel", dataRQ).Result;
                    response.EnsureSuccessStatusCode();

                    // Log the response after receiving
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_cancel_action_loy response", response));

                    string result = response.Content.ReadAsStringAsync().Result;
                    respose = JsonConvert.DeserializeObject<dynamic>(result);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_cancel_action_loy respose", respose));

                    requestStatus = 1;
                    if (respose.success == true) cancelActionSuccess = 1;
                }
            }
            catch (HttpRequestException httpEx)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_cancel_action_loy httpEx", httpEx));
                requestStatus = 0;
                cancelActionSuccess = 0;
                return -1;
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("submit_cancel_action_loy ex", ex));
                return 0;
            }
            finally
            {
                UpdateCancelActionLog(conn, model, requestStatus, cancelActionSuccess, respose, sb);
            }
            return 1;
        }

        public static int UpdateCancelActionLog(SqlConnection conn, GetDataCancelTSD model, int requestStatus, int cancelActionSuccess, dynamic respose, StringBuilder sb)
        {
            try
            {
                var update = conn.Execute(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                {
                    action_name = "cancel_action_log",
                    logIdRQ = model.idRQ,
                    requestStatus = requestStatus,
                    cancelActionSuccess = cancelActionSuccess,
                    Response = JsonConvert.SerializeObject(respose)
                }, commandType: CommandType.StoredProcedure);

                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("UpdateCancelActionLog update", update));
                return update;
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("UpdateCancelActionLog ex", ex));
                return -1;
            }
        }

        public static int CancelActionLoy()
        {
            var logid = Guid.NewGuid();
            var sb = new StringBuilder();
            try
            {
                using (var conn = new SqlConnection(Utility.ConnWrite))
                {
                    var data = GetData_CancelAction(conn);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("cancel_action_loy data", data));
                    if (data == null || data.Count == 0) return 0;
                    foreach (var item in data)
                    {
                        submit_cancel_action_loy(conn, item, sb, logid);
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("cancel_action_loy ex", ex));
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "cancel_action_loy", logid.ToString());
            }
            return 1;
        }

        public static List<GetDataCancelTSD> GetData_CancelAction(SqlConnection conn)
        {
            return conn.Query<GetDataCancelTSD>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "GetList_ActionCode_Cancel"
            }, commandType: CommandType.StoredProcedure).ToList();
        }
        #endregion

        #region thu hồi điểm + clear voucher realtime khi hủy tsd
        public static ResponseModel<bool> cancel_tsd(CancelTSD input)
        {
            var output = new ResponseModel<bool>(){result = 1,data = true,error = ""};
            var logid = Guid.NewGuid();
            var sb = new StringBuilder();
            try
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("cancel_tsd input", input));
                if (string.IsNullOrEmpty(input.contract) || input.ObjID <= 0)
                {
                    output.error = "Input không hợp lệ";
                    output.result = 0;
                    return output;
                }
                using (var conn = new SqlConnection(Utility.ConnWrite))
                {
                    conn.Open();
                    var data = GetDataCancelTSD(conn, input.ObjID, input.OrderCode);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("cancel_tsd data", data));

                    if (data == null)
                    {
                        output.error = "Không tìm thấy dữ liệu";
                        output.result = 0;
                        return output;
                    }

                    submit_cancel_action_loy(conn, data, sb, logid);

                    if (data.privateCodeId > 0) UpdateExpiedById(data.privateCodeId, data.privateCode, sb, logid);
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("cancel_tsd ex", ex));
                return new ResponseModel<bool>(){
                    result = -1,
                    error = ex.Message
                };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "cancel_tsd", logid.ToString());
            }
            return output;
        }


        /// <summary>
        /// Cập nhật trạng thái voucher theo ID.
        /// item1: privateCodeId, item2: privateCode, item3: receipt_billNumber, item4: receptDetailId
        /// </summary>
        public static void UpdateExpiedById(int privateCodeId, string privateCode, StringBuilder sb, Guid logId)
        {
            var req = new SendLoyalty
            {
                voucherCode = privateCode,
                voucherPaymentStatus = "CANCEL"
            };
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("SendLoyalty req", req));
            bool isSendLoy = CallbackCommonServices.NewChangeStatusLoyaltyV2(req, privateCodeId, logId.ToString());
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("isSendLoy", isSendLoy));
            if (isSendLoy)
            {
                var update_private = private_update_expired(privateCodeId);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("update_private", update_private));
            }
        }

        public static GetDataCancelTSD GetDataCancelTSD(SqlConnection conn, int objid, string orderCode)
        {
            return conn.Query<GetDataCancelTSD>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name = "get_data_cancel_tsd",
                objid = objid,
                orderCode = orderCode
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        #endregion

        #region cskh sr recare
        public static ResponseModel<bool> ReceiptPaymentProcess(ReceiptPayment input)
        {
            StringBuilder sb = new StringBuilder();
            var logid = Guid.NewGuid();
            try
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("input", input));
                using (var connection = new SqlConnection(Utility.ConnWrite))
                {
                    var actionCode = ReceiptPaymentGetData(connection, input.contract, input.receipt_number,sb);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("actionCode", actionCode));
                    if (string.IsNullOrEmpty(actionCode))
                    {
                        return new ResponseModel<bool>() { result = 0, data = false, error = "Không tìm thấy actionCode." };
                    }

                    var obj = get_obj_info(connection, input.contract);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("obj", obj));

                    int prepaid = get_prepaid_by_receipt(connection, input.receipt_number);

                    submit_action_code_v2(connection, obj, actionCode, input.receipt_number, sb, logid, prepaid);
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("ReceiptPaymentProcess err", ex));
                return new ResponseModel<bool>() { result = -1, data = false, error = ex.Message };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "ReceiptPaymentProcess", logid.ToString());
            }
            return new ResponseModel<bool>() { result = 1, data = true, error = "Success" };
        }

        public static string ReceiptPaymentGetData(SqlConnection conn, string contract, string receipt_number, StringBuilder sb)
        {
            var rs = new ReceiptPayment_GetData();
            var parameters = new DynamicParameters();
            parameters.Add("@action_name", "GetActionCodeByReceipt", DbType.String, ParameterDirection.Input);
            parameters.Add("@receiptNumber", receipt_number, DbType.String, ParameterDirection.Input);

            return conn.QueryFirstOrDefault<string>(OSU6_FPTVoucher_CskhPolicyToFGold, parameters, commandType: CommandType.StoredProcedure);
        }

        public static List<PointToBill_Get> PointToBill_Get(SqlConnection conn)
        {
            var rs = new List<PointToBill_Get>();
            var parameters = new DynamicParameters();
            parameters.Add("@action_name", "PointToBill_Get", DbType.String, ParameterDirection.Input);
            rs = conn.Query<PointToBill_Get>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, parameters, commandType: CommandType.StoredProcedure).ToList();
            return rs;
        }

        public static void PointToBillProcess()
        {
            StringBuilder sb = new StringBuilder();
            var logid = Guid.NewGuid();
            try
            {
                using (var connection = new SqlConnection(Utility.ConnWrite))
                {
                    var data = PointToBill_Get(connection);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("PointToBill_Get", data));
                    if (data == null) return;

                    foreach (var item in data) 
                    {
                        //if (PointBillCheckExist(connection, item.ObjID)) continue;
                        var obj = new Tuple<string, int, string>(item.Contract, item.ObjID, item.Phone);
                        submit_action_code_v2(connection, obj,item.ActionCode,item.OrderCode,sb,logid,item.Prepaid,null);
                    }

                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("PointToBillProcess err", ex));
                return;
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "PointToBillProcess", logid.ToString());
            }
            return;
        }

        public static bool PointBillCheckExist(SqlConnection conn, int objid)
        {
            return conn.Query<int>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
            {
                action_name= "PointBillCheckExist",
                objid = objid
            },commandType:CommandType.StoredProcedure).FirstOrDefault() > 0;
        }
        #endregion

        #region retry update hết hạn
        public static void RetryMarkExpiredVouchers()
        {
            var logid = Guid.NewGuid();
            var sb = new StringBuilder();
            var data = new List<VouchersToBeExpired>();
            try
            {
                data = GetVouchersToBeExpired();
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("RetryMarkExpiredVouchers data", data));
                if (data == null || data.Count == 0) return;
                foreach (var voucher in data)
                {
                    UpdateExpiedById(voucher.PrivateCodeId, voucher.PrivateCode, sb, logid);
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("RetryMarkExpiredVouchers ex", ex));
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "RetryMarkExpiredVouchers", logid.ToString());
            }
        }

        public static List<VouchersToBeExpired> GetVouchersToBeExpired()
        {
            using (var conn = new SqlConnection(Utility.ConnRead))
            {
                return conn.Query<VouchersToBeExpired>(OSU6_FPTVoucher_Job_RetryCancelActionLoy, new
                {
                    ActionName = "GetVouchersToBeExpired"
                }, commandType: CommandType.StoredProcedure).ToList();
            }
        }

        public static async Task ProcessRetryMarkExpiredVouchers()
        {
            try
            {
                Task cancelActionTask = Task.Run(() =>
                {
                    RetryMarkExpiredVouchers();
                });

                await cancelActionTask;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex, "ProcessRetryMarkExpiredVouchers error", Guid.NewGuid());
            }
        }
        #endregion

        #region FGold dynamic value
        public static ResponseModel<bool> PolicyAproveFGoldCSKH(PolicyAproveFGold input)
        {
            StringBuilder sb = new StringBuilder();
            Guid logid = Guid.NewGuid();
            try
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("input", input));

                if (input == null)
                {
                    return new ResponseModel<bool> { data = false, error = "Dữ liệu đầu vào không hợp lệ.", result = 0 };
                }

                using (var conn = new SqlConnection(Utility.ConnWrite)) 
                {
                    var checkSuccess = CheckPolicyAproveFGoldCSKH(conn, input);
                    if (checkSuccess) 
                    {
                        return new ResponseModel<bool> { data = true, error = "Cộng điểm/đổi voucher thành công.", result = 1 };
                    }
                    // Lấy action code và month used
                    var actionCode_monthUse = PolicyAproveFGoldCSKH_GetActionCode(conn, input.Services, input.GeneralCodeId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("actionCode_monthUse", actionCode_monthUse));
                    
                    if (actionCode_monthUse == null)
                    {
                        return new ResponseModel<bool> { data = false, error = "Không tìm thấy actionCode.", result = 0 };
                    }

                    // Lấy thông tin CSKH Policy
                    var cskhPolicyToFGold = CskhPolicyToFGolds(conn, input, actionCode_monthUse.MonthUsed, sb);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("cskhPolicyToFGold", cskhPolicyToFGold));

                    if (cskhPolicyToFGold == null || string.IsNullOrEmpty(cskhPolicyToFGold.evc?.voucherCode))
                    {
                        cskhPolicyToFGold.evc = null;
                    }

                    if (cskhPolicyToFGold != null && !string.IsNullOrEmpty(cskhPolicyToFGold.Mesage))
                    {
                        return new ResponseModel<bool> { data = false, error = cskhPolicyToFGold.Mesage };
                    }

                    // Lấy thông tin obj
                    var obj = get_obj_info(conn, input.Contract);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("obj", obj));

                    submit_action_code_v2(conn, obj, actionCode_monthUse.ActionCode, input.OrderCode, sb, logid, (int)actionCode_monthUse.MonthUsed, cskhPolicyToFGold.evc);
                }

                return new ResponseModel<bool> { data = true, result = 1 };
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Exception", e.Message));
                return new ResponseModel<bool> { data = false, result = -1, error = "Lỗi hệ thống." };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "PolicyAproveFGoldCSKH", logid.ToString());
            }
        }

        private static bool CheckPolicyAproveFGoldCSKH(SqlConnection conn, PolicyAproveFGold input)
        {
            return conn.Query<int>(OSU6_FPTVoucher_CskhPolicyToFGold, new
            {
                action_name = "CheckPolicyAproveFGoldCSKH",
                contract = input.Contract,
                objId = input.ObjId,
                generalCodeId = input.GeneralCodeId
            }, commandType: CommandType.StoredProcedure).FirstOrDefault() == 1;
        }

        public static CskhPolicyToFGold CskhPolicyToFGolds(SqlConnection conn, PolicyAproveFGold input, float monthUsed, StringBuilder sb)
        {
            var rs = new CskhPolicyToFGold();

            if (input.GeneralCodeId <= 0)
            {
                return rs;
            }    

            try
            {
                var parameters = new DynamicParameters();
                parameters.Add("@action_name", "CskhPolicyToFGold", DbType.String, ParameterDirection.Input);
                parameters.Add("@contract", input.Contract, DbType.String, ParameterDirection.Input);
                parameters.Add("@objId", input.ObjId, DbType.Int32, ParameterDirection.Input);
                parameters.Add("@generalCodeId", input.GeneralCodeId, DbType.Int32, ParameterDirection.Input);
                parameters.Add("@monthUsed", monthUsed, DbType.Double, ParameterDirection.Input);
                parameters.Add("@mesageId", dbType: DbType.Int32, direction: ParameterDirection.Output);

                rs.evc = conn.QueryFirstOrDefault<Order_Apply_Point_Voucher>(OSU6_FPTVoucher_CskhPolicyToFGold, parameters, commandType: CommandType.StoredProcedure);
                int mesageId = parameters.Get<int>("@mesageId");
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("ReceiptPaymentGetData mesageId", mesageId));

                switch (mesageId)
                {
                    case 0: rs.Mesage = ""; break;
                    case 1: rs.Mesage = "Không thỏa kì hạn."; break;
                    case 2: rs.Mesage = "Không tìm thấy voucher."; break;
                    case 3: rs.Mesage = "Không tìm thấy Event."; break;
                    default: rs.Mesage = $"Lỗi không xác định (Mã: {mesageId})."; break;
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("CskhPolicyToFGolds Exception", ex));
                rs.Mesage = $"Lỗi truy vấn: {ex.Message}";
            }

            return rs;
        }

        public static EstimateGoldOut PolicyAproveFGoldCSKH_GetActionCode(SqlConnection conn, List<EstimateGoldService> services, int generalCodeId)
        {
            try
            {
                var result = conn.Query<EstimateGoldOut>(OSU6_FPTVoucher_CskhPolicyToFGold, new
                {
                    action_name = "PolicyAproveFGoldCSKH_GetActionCode",
                    services = Utility.Converter.ToXml(services),
                    generalCodeId = generalCodeId
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                return result;
            }
            catch (Exception)
            {
                return null;
            }
        }


        public static int NoteFGold(NotedGold input)
        {
            StringBuilder sb = new StringBuilder();
            Guid logid = Guid.NewGuid();
            try
            {
                if (input == null)
                {
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("NoteFGold", "Input is null"));
                    return 0;
                }

                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("NoteFGold input", input));

                using (var conn = new SqlConnection(Utility.ConnWrite))
                {
                    conn.Open();
                    var result = conn.Execute(OSU6_FPTVoucher_CskhPolicyToFGold, new
                    {
                        action_name = "NoteFGold",
                        fgold = input.FGold,
                        requestId = input.RequestId,
                        addGoldStatus = input.Status > 0 ? 1 : 0,
                        addGoldDate = input.SuccessDate,
                        addGoldDesc = input.Description,
                        addGold = input.FGold
                    }, commandType: CommandType.StoredProcedure);

                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("NoteFGold result", result));
                    return result;
                }
            }
            catch (SqlException sqlEx)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("NoteFGold SqlException", sqlEx));
                return -1;
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("NoteFGold Exception", ex));
                return -1;
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "NoteFGold", logid.ToString());
            }
        }
        
        private static int get_prepaid_by_receipt(SqlConnection connection, string receipt_number)
        {
            try 
            {
                if (string.IsNullOrEmpty(receipt_number))
                {
                    return 0;
                }
                
                return connection.Query<int>(OSU6_FPTVoucher_CskhPolicyToFGold, new
                {
                    action_name = "get_prepaid_by_receipt",
                    receiptNumber = receipt_number
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        public static ResponseModel<EstimateGoldOut> EstimatedGold(EstimateGold input)
        {
            var sb = new StringBuilder();
            var logid = Guid.NewGuid();
            try
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("EstimatedGold input", input));
                
                if (input == null || input.Services == null || !input.Services.Any())
                {
                    return new ResponseModel<EstimateGoldOut>() { 
                        data = null, 
                        result = 0, 
                        error = "Dữ liệu đầu vào không hợp lệ hoặc danh sách dịch vụ trống" 
                    };
                }

                using (var conn = new SqlConnection(Utility.ConnWrite))
                {
                    conn.Open();
                    
                    var actionCode_monthUse = PolicyAproveFGoldCSKH_GetActionCode(conn, input.Services, input.GeneralCodeId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("actionCode_monthUse", actionCode_monthUse));
                    
                    if (actionCode_monthUse == null) 
                    { 
                        return new ResponseModel<EstimateGoldOut>() { 
                            data = null, 
                            result = 0, 
                            error = "Không tìm thấy actionCode phù hợp" 
                        };
                    }
                    
                    actionCode_monthUse.Point = GetGoldFromDSC(
                        actionCode_monthUse.ActionCode, 
                        input.LocationId, 
                        input.BranchCode, 
                        "", 
                        sb,
                        logid
                    );
                    
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("actionCode_monthUse final", actionCode_monthUse));
                    
                    return new ResponseModel<EstimateGoldOut>() { 
                        data = actionCode_monthUse, 
                        result = 1,
                        error = "" 
                    };
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("EstimatedGold Exception", e));
                return new ResponseModel<EstimateGoldOut>() { 
                    data = null, 
                    result = -1, 
                    error = $"Lỗi hệ thống: {e.Message}" 
                };
            }
            finally
            {
                LoggerKafka.WriteLogKafka(sb, "EstimatedGold", logid.ToString());
            }
        }
        
        public static int GetGoldFromDSC(string actionCode, int locationId, int branchCode, string voucherType, StringBuilder sb, Guid logid = new Guid())
        {
            if (string.IsNullOrEmpty(actionCode))
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC", "actionCode is null or empty"));
                return 0;
            }
            
            try
            {
                LoginInforShared login = new LoginInforShared() { 
                    username = Utility.loyaltyapi_username, 
                    password = Utility.loyaltyapi_password 
                };
                
                AuthorizationInforShared aut = FuncShared.GetTokenv2(login, logid.ToString());
                
                if (aut == null || string.IsNullOrEmpty(aut.access_token))
                {
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC", "Failed to get authorization token"));
                    return 0;
                }
                
                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    client.Timeout = TimeSpan.FromSeconds(10); // Thiết lập timeout

                    var url = $"/internal/api/request-action/coins?actionCode={actionCode}&locationId={locationId}&branchCode={branchCode}&voucherType={voucherType}";
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC url", url));

                    var response = client.GetAsync(url).Result;
                    
                    if (!response.IsSuccessStatusCode)
                    {
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC error", $"Status code: {response.StatusCode}"));
                        return 0;
                    }
                    
                    string result = response.Content.ReadAsStringAsync().Result;
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC result", result));
                    
                    var respose = JsonConvert.DeserializeObject<GetGold>(result);
                    
                    if (respose == null || respose.Data == null)
                    {
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC", "Response data is null"));
                        return 0;
                    }
                    
                    return respose.Data.Coins;
                }
            }
            catch (HttpRequestException httpEx)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC HttpRequestException", httpEx));
                return 0;
            }
            catch (JsonException jsonEx)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC JsonException", jsonEx));
                return 0;
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetGoldFromDSC Exception", e));
                return 0;
            }
        }
        #endregion
    }
}