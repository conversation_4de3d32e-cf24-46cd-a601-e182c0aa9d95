using Loyalty.Evoucher.Contants;
using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;

namespace Loyalty.Evoucher.Services
{
    public class MobileCardService
    {
        public static ResponseModel<BookVoucherOutput> BookVoucherMobileCard(SqlConnection connection, SqlTransaction transaction, BookVoucherModel input, string logId)
        {
            try
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "BookVoucherMobileCard req: ");

                var pars = new
                {
                    ActionName = "BookVoucherMC",
                    code = input.eVoucher,
                    transCode = input.transCode
                };
                var temp = connection.Query<BookVoucherOutput>(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_MobileCard,
                    pars, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(temp), "BookVoucherMobileCard temp: ");

                if (temp != null)
                {
                    var pars1 = new
                    {
                        ActionName = "UpdateStatusMC",
                        code = temp.eVoucher
                    };
                    var updateVC = connection.Execute(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_MobileCard,
                    pars1, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);
                    transaction.Commit();
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(updateVC), "BookVoucherMobileCard updateVC: ");

                    return new ResponseModel<BookVoucherOutput> { data = temp, error = null, result = 1 };
                }
                else
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Đã xảy ra lỗi", result = 0 };    
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(ex.Message), "BookVoucherMobileCard Error: ");
                return new ResponseModel<BookVoucherOutput> { data = null, error = ex.Message, result = 0 };    
            }
        }
    }
}