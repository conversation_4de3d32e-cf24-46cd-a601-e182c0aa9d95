using Dapper;
using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;

namespace Loyalty.Evoucher.Services
{
    public class LoyaltyCommonServices
    {
        public static bool IsValidPhoneNumber(string phoneNumber)
        {
            phoneNumber = phoneNumber.Replace("+84", "0");
            return Regex.Match(phoneNumber, @"^(84|0[3|5|7|8|9])+([0-9]{8})$").Success;
        }
        public static string HashSha1(string input)
        {
            return string.Join("", (new SHA1Managed().ComputeHash(Encoding.UTF8.GetBytes(input))).Select(x => x.ToString("X2")).ToArray());
        }
        public static void WriteToLog(object input, string mes, Guid logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(input), mes);
        }
        public static bool NewChangeStatusLoyalty(SendLoyalty sl, string LogId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(sl), "ChangeStatusLoyalty req ");
            LoyaltyRedeemOutput lro = new LoyaltyRedeemOutput();
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, LogId);                

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(WebAPIHelper.loyaltyapi), " NewChangeStatusLoyalty urlAPI " + sl.voucherCode);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(sl), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/loyalty-services/api/integration-evoucher/redeem-device", data).Result;
                    string result = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), " NewChangeStatusLoyalty APIres " + sl.voucherCode);
                    lro = JsonConvert.DeserializeObject<LoyaltyRedeemOutput>(result);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " NewChangeStatusLoyalty APIres Error " + sl.voucherCode);
                return false;
            }

            if (lro.statusCode.ToUpper().Equals("SUCCESS"))
            {
                return true;
            }

            return false;
        }


        public static AuthorizationInfor getAuthorInforV2(LoginInfor login, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject("start to getAuthorInfor"));
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                string authInfo = login.username + ":" + login.password;
                authInfo = Convert.ToBase64String(Encoding.Default.GetBytes(authInfo));

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(WebAPIHelper.loyaltyapi + "/auth/oauth/token?grant_type=client_credentials");
                request.Method = "POST";
                request.Accept = "application/json; charset=utf-8";

                request.Headers["Authorization"] = "Basic " + authInfo;

                var response = (HttpWebResponse)request.GetResponse();

                string strResponse = "";
                using (var sr = new StreamReader(response.GetResponseStream()))
                {
                    strResponse = sr.ReadToEnd();
                }
                res = JsonConvert.DeserializeObject<AuthorizationInfor>(strResponse);
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " getAuthorInforV2 Error:");
                return null;
            }
            return res;
        }

        public static bool CheckObjLoyalty(int objid, Guid LogId)
        {
            LoyaltyCommonServices.WriteToLog(objid, "CheckObjLoyalty ", LogId);
            bool status = false;
            CheckObjLoyaltyModel CheckO = new CheckObjLoyaltyModel { data = new List<ObjLoyaltyInfo>() };
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, LogId.ToString());
                string url = WebAPIHelper.loyaltyapi + "/internal/api/contracts/" + objid.ToString() + "/customer/brief";
                List<HeaderAPI> headers = new List<HeaderAPI>();
                headers.Add(new HeaderAPI { key = "Authorization", value = "Bearer "+aut.access_token });
                string resdataAPI = callapi(url, "POST", null, "application/json; charset=utf-8",headers);
                LoyaltyCommonServices.WriteToLog(resdataAPI, "CheckObjLoyalty resdataAPI ", LogId);
                CheckO = JsonConvert.DeserializeObject<CheckObjLoyaltyModel>(resdataAPI);
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "CheckObjLoyalty error ", LogId);
                return false;
            }

            foreach (var item in CheckO.data)
            {
                if (item.loyaltyStatus.Equals(1))
                {
                    status = true;
                    break;
                }
            }

            return status;
        }
        public static string callapi(string _url, string _method = "GET", string _data = null, string contentType = "application/json; charset=utf-8", List<HeaderAPI> headers = null)
        {
            HttpWebRequest wr = (HttpWebRequest)WebRequest.Create(_url);
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    wr.Headers.Add(header.key, header.value);
                }
            }
            wr.Method = _method;
            if (_method.ToUpper().Equals("POST"))
            {
                //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };

                wr.ContentType = contentType;
                // Set the data to send.
                using (var streamWriter = new StreamWriter(wr.GetRequestStream()))
                {
                    streamWriter.Write(_data);
                }
            }
            var httpResponse = (HttpWebResponse)wr.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                var rs = streamReader.ReadToEnd();
                return rs;
            }
        }

        public static string GetConfig(string key)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<string>("PowerInside.dbo.OS6_LoyaltyLog", new
                {
                    ActionName = "GetConfig",
                    Key = key
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
    }
}