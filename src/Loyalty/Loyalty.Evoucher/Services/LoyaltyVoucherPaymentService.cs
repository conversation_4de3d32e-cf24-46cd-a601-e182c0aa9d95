using Loyalty.Evoucher.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Loyalty.Evoucher.Contants;
using System.Data;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Net;
using System.IO;


namespace Loyalty.Evoucher.Services
{
    public class LoyaltyVoucherPaymentService
    {
        public static ResponseModel<RedeemOutput> RedeemVoucherPayment(SqlConnection connection, SqlTransaction transaction, string voucherCode, string billNumber, int objId, string logId)
        {
            var request = new
            {
                voucherCode = voucherCode,
                billNumber = billNumber,
                objId = objId
            };
            try
            {
                PaymentUseVoucherOutput res = new PaymentUseVoucherOutput();                

                // kiểm tra bill đã dùng voucher payment                
                bool isOk = CheckBillUseVoucher(connection,transaction,billNumber,logId);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(res), "RedeemVoucherPayment res: ");
                if (!isOk)
                {
                    return new ResponseModel<RedeemOutput>
                    {
                        data = new RedeemOutput
                        {
                            status = StatusRedeem.FALSE,
                            statusDescription = LoyaltyDescription.REDEEMERROR
                        },
                        error = "Số hóa đơn không hợp lệ",
                        result = 0
                    };
                }

                res = connection.Query<PaymentUseVoucherOutput>(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent,
                    new { ActionName = ACTION_OS6_FPTVoucher_LoyaltyPromotionEvent.RedeemVoucherPayment, objId = objId, code = voucherCode, BillNumber = billNumber }
                    , transaction: transaction, commandType: CommandType.StoredProcedure
                ).FirstOrDefault();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(res), "RedeemVoucherPayment res: "); 
                
                if (res != null)
                {
                    int totalPaid = GetTotalPaidByBill(connection,transaction,billNumber,logId);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(totalPaid), "RedeemVoucherPayment totalPaid: "); 

                    if (totalPaid > 0)
                    {
                        try
                        {
                            using (var connection1600 = new SqlConnection(Conn1660()))
                            {
                                int UpdateData = connection1600.Execute("Internet.dbo.BillingUpdateVoucherPayment",
                                    new
                                    {
                                        BillNumber = billNumber,
                                        PaidTotalValue = totalPaid
                                    }, commandType: CommandType.StoredProcedure);
                                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(UpdateData), "RedeemVoucherPayment update 1660: ");
                            }                           
                        }
                        catch (Exception ex)
                        {
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "RedeemVoucherPayment update 1660 error: ");
                        }
                    }
                    transaction.Commit();
                    return new ResponseModel<RedeemOutput>
                    {
                        data = new RedeemOutput
                        {
                            status = StatusRedeem.SUCCESS,
                            statusDescription = LoyaltyDescription.REDEEMSUCCESS,
                            infor = new PaymentUseVoucher { billNumber = billNumber, voucher = voucherCode, RealValueUse = res.RealValueUse, DebtAmount = res.DebtAmount }
                        },
                        error = "Số hóa đơn không hợp lệ",
                        result = 0
                    };
                }
                
                return new ResponseModel<RedeemOutput>
                {
                    data = new RedeemOutput
                    {
                        status = StatusRedeem.FALSE,
                        statusDescription = LoyaltyDescription.REDEEMERROR,
                        infor = new PaymentUseVoucher()
                    },
                    error = "Đã có lỗi xảy ra",
                    result = 0
                };
                    
                
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "RedeemVoucherPayment error: ");
                return new ResponseModel<RedeemOutput>
                {
                    data = new RedeemOutput
                    {
                        status = StatusRedeem.FALSE,
                        statusDescription = LoyaltyDescription.REDEEMERROR,
                        infor = new PaymentUseVoucher()
                    },
                    error = "Số hóa đơn không hợp lệ",
                    result = 0
                };
            }
        }

        public static string Conn1660()
        {
            return System.Web.Configuration.WebConfigurationManager.ConnectionStrings["sqlConnection1660"] != null ?
                System.Web.Configuration.WebConfigurationManager.ConnectionStrings["sqlConnection1660"].ConnectionString : "";
        }

        private static int GetTotalPaidByBill(SqlConnection connection, SqlTransaction transaction,string billNumber,string logId)
        {
            try
            {
                return connection.Query<int>(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent,
                    new { ActionName = "GetTotalPayment", BillNumber = billNumber },
                    transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure
                ).FirstOrDefault();
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "RedeemVoucherPayment GetTotalPaidByBill error: ");
                return 0;
            }
        }
        private static bool CheckBillUseVoucher(SqlConnection connection, SqlTransaction transaction,string billNumber, string logId)
        {
            try
            {
                int res = 0;
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(billNumber), "RedeemVoucherPayment CheckBillUseVoucher req: ");

                res = connection.Query<int>(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent,
                    new { ActionName = ACTION_OS6_FPTVoucher_LoyaltyPromotionEvent.CheckBillUseVoucherPayment, BillNumber = billNumber },
                    transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure
                ).FirstOrDefault();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(res), "RedeemVoucherPayment CheckBillUseVoucher res: ");
                if (res == 0)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "RedeemVoucherPayment CheckBillUseVoucher error: ");
                return false;
            }
        }

        public static bool ChangeStatusLoyalty(string phone, string voucherCode, string LogId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(voucherCode), "ChangeStatusLoyalty req ");
            LoyaltyRedeemOutput lro = new LoyaltyRedeemOutput();
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = getAuthorInfor(login,LogId);
                var request = new SendMessageLoyalty()
                {
                    mobileHiFpt = phone,
                    voucherCode = voucherCode
                };

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/loyalty-services/api/integration-evoucher/redeem-device", data).Result;
                    string result = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), " ChangeStatusLoyalty APIres " + voucherCode);
                    lro = JsonConvert.DeserializeObject<LoyaltyRedeemOutput>(result);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " ChangeStatusLoyalty APIres Error " + voucherCode);
                return false;
            }

            if (lro.statusCode.ToUpper().Equals("SUCCESS"))
            {
                return true;
            }

            return false;
        }        

        private static AuthorizationInfor getAuthorInfor(LoginInfor login, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject("start to getAuthorInfor"));
            AuthorizationInfor res = new AuthorizationInfor();
            try
            {
                string authInfo = login.username + ":" + login.password;
                authInfo = Convert.ToBase64String(Encoding.Default.GetBytes(authInfo));

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(WebAPIHelper.loyaltyapi + "/loyalty-services/oauth/token?grant_type=client_credentials");
                request.Method = "POST";
                request.Accept = "application/json; charset=utf-8";

                request.Headers["Authorization"] = "Basic " + authInfo;

                var response = (HttpWebResponse)request.GetResponse();

                string strResponse = "";
                using (var sr = new StreamReader(response.GetResponseStream()))
                {
                    strResponse = sr.ReadToEnd();
                }
                res = JsonConvert.DeserializeObject<AuthorizationInfor>(strResponse);
                
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " getAuthorInfor Eror:");
                return null;
            }
            return res;
        }
    }
}