using Loyalty.Evoucher.Contants;
using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;
using System.Data;
using System.Configuration;
using System.Net.Http;
using System.Net;
using System.Text;

namespace Loyalty.Evoucher.Services
{
    public class LoyaltyFRTService
    {
        public static ResponseModel<BookVoucherOutput> BookVoucherFRT(SqlConnection connection, SqlTransaction transaction, BookVoucherModel input, TypeVoucher typeData, string logId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "BookVoucherFRT req: ");
            if (!LoyaltyCommonServices.IsValidPhoneNumber(input.phone))
            {
                return new ResponseModel<BookVoucherOutput> { data = null, result = -1, error = "phone không đúng định dạng" };
            }
            if (input.point<=0)
            {
                return new ResponseModel<BookVoucherOutput> { data = null, result = -1, error = "point không đúng định dạng" };
            }
            try
            {
                //kiểm tra quotar voucher 
                var VoucherInfor = connection.Query<EventFRTinfo>(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT,
                    new
                    {
                        actionName = "GetInforVoucherFRT",
                        eventCode = typeData.EventCode
                    }, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(VoucherInfor), "BookVoucherFRT VoucherInfor: ");

                if (VoucherInfor.QuotaPrivateCode <= VoucherInfor.countBook)
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Số lượng quota đã hết", result = 0 };
                }

                if (!VoucherInfor.Point.Equals(input.point))
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Điểm không đủ", result = 0 };
                }
                
                string url = Utility.loyalty_FRTurl.ToString();
                string endpointCreateBook = Utility.loyalty_FRT_book_endpoint.ToString();                 

                var reqFRT = new FRTGetVoucherInputModel
                {
                    codeLoaiVoucher=VoucherInfor.EventCode,
                    limitDay= VoucherInfor.ExpiredDay,
                    numberPhone=input.phone,
                    point= input.point,
                    supplier=VoucherInfor.Branch
                };
                string data = JsonConvert.SerializeObject(reqFRT);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(data), "BookVoucherFRT req api: ");

                string responseAPI = CallAPI(url, endpointCreateBook, data);
                if (string.IsNullOrEmpty(responseAPI))
                {
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject("no responseAPI"), "BookVoucherFRT : ");

                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Lấy voucher thất bại", result = 0 };
                }
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(responseAPI), "BookVoucherFRT req api: ");

                FRTGetVoucherOutputModel BookVoucherFRT = new FRTGetVoucherOutputModel();
                BookVoucherFRT = JsonConvert.DeserializeObject<FRTGetVoucherOutputModel>(responseAPI);
                if (BookVoucherFRT == null || BookVoucherFRT.statusCode!=200)
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Lấy voucher thất bại", result = 0 };
                }
                
                var pars = new
                {
                    actionName = "AddPrivateCodeFRT",
                    code = BookVoucherFRT.code,
                    promotionEventID = VoucherInfor.ID,
                    ExpiredDate = BookVoucherFRT.expiredDate,
                    transCode = input.transCode,
                    phone = input.phone,
                    point = input.point,

                };
                int isAddPrivate = connection.Execute(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent_CampaignFRT,
                    pars, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(isAddPrivate), "BookVoucherFRT isAddPrivate: ");
                if (isAddPrivate > 0)
                {
                    transaction.Commit();
                    var bookVoucherOutput = new BookVoucherOutput { eVoucher = BookVoucherFRT.code, validTo = Convert.ToDateTime(BookVoucherFRT.expiredDate), voucherType = typeData.VoucherTypeName };
                    return new ResponseModel<BookVoucherOutput> { data = bookVoucherOutput, error = null, result = 1 };
                }
                return new ResponseModel<BookVoucherOutput> { data = null, error = "Đã xảy ra lỗi", result = 0 };
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(ex.Message), "BookVoucherFRT Error: ");
                return new ResponseModel<BookVoucherOutput> { data = null, error = ex.Message, result = 0 };
            }
        }
        public static string CallAPI(string _url, string endpoint, string _data)
        {
            L.Mes(Level.INFO, JsonConvert.SerializeObject(_url + endpoint), "CallAPI req: ");
            L.Mes(Level.INFO, _data, "CallAPI data: ");
            try
            {
                var proxy = new WebProxy
                {
                    //Address = new Uri("http://isc-proxy.hcm.fpt.vn:80")
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(_url);
                    client.DefaultRequestHeaders.Add("Authorization", Utility.loyalty_FRT_authen.ToString());
                    var data = new System.Net.Http.StringContent(_data, Encoding.UTF8, "application/json");
                    var response = client.PostAsync(endpoint, data).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    return r;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), "CallAPI Error: ");
                return null;
            }
        }
    }
}