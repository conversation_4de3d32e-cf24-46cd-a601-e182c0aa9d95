using Loyalty.Evoucher.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using Loyalty.Evoucher.Contants;
using System.Net;
using System.Data;
using System.Configuration;
using System.Text;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using System.Net.Http;

namespace Loyalty.Evoucher.Services
{
    public class FPTplayService
    {
        public static ResponseModel<BookVoucherOutput> BookVoucherFPTplay(SqlConnection connection, SqlTransaction transaction, BookVoucherModel input, TypeVoucher typeData, string LogId)
        {
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "BookVoucherFPTplay req: ");
            try
            {
                //kiểm tra quotar voucher 
                int quotar = connection.Query<int>(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent,
                    new
                    {
                        ActionName = ACTION_OS6_FPTVoucher_LoyaltyPromotionEvent.checkQuotarFPTplay,
                        EventCode = typeData.EventCode
                    }, commandTimeout: null,transaction:transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();

                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(quotar), "BookVoucherFPTplay check quota fpt play ");

                if (quotar.Equals(0))
                {
                    return new ResponseModel<BookVoucherOutput> {data=null, error="hết quotar",result=0 };
                }
                WebHeaderCollection headers = new WebHeaderCollection();
                headers.Add("APP-ID", Utility.FptPlay_APP_ID.ToString());
                headers.Add("APP-KEY", Utility.FptPlay_APP_KEY.ToString());
                string key = Utility.FptPlay_KEY.ToString();
                string ulr = Utility.urlFPTplay.ToString();
                string dataHash = "voucher_type=giftcode&expired_date=" + typeData.ExpiredDay.ToString() + "&event=" + typeData.EventCode + "&trans_id=" + input.transCode;
                string signature = HmacSha256Digest(dataHash, key);
                if (string.IsNullOrEmpty(signature))
                {
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject("No signature"), "BookVoucherFPTplay: ");

                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Lấy voucher thất bại", result = 0 };
                }
                var bvf = new BookVoucherFPTplay
                {
                    voucher_type = "giftcode",
                    expired_date = typeData.ExpiredDay,
                    event_Code = typeData.EventCode,
                    trans_id = input.transCode,
                    signature = signature
                };
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(bvf), " BookVoucherFPTplay req api ");
                string data = JsonConvert.SerializeObject(bvf);
                string responseAPI = CallAPI_ProXyHMc(ulr, data, LogId);
                if (string.IsNullOrEmpty(responseAPI))
                {
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject("No responseAPI"), "BookVoucherFPTplay: ");

                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Lấy voucher thất bại", result = 0 };
                }
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(responseAPI), "BookVoucherFPTplay responseAPI ");

                var dataVoucher = new DataVoucherFPTplay();
                BookVoucherFPTplayOut bookVoucherFPTplay = new BookVoucherFPTplayOut { data = dataVoucher, error_code = null, message = null };
                bookVoucherFPTplay = JsonConvert.DeserializeObject<BookVoucherFPTplayOut>(responseAPI);
                if (bookVoucherFPTplay == null)
                {
                    return new ResponseModel<BookVoucherOutput> { data = null, error = "Lấy voucher thất bại", result = 0 };
                }
                var pars = new
                {
                    ActionName = ACTION_OS6_FPTVoucher_LoyaltyPromotionEvent.AddVoucherFPTplay,
                    code = bookVoucherFPTplay.data.code,
                    EventCode = typeData.EventCode,
                    expiredDate = bookVoucherFPTplay.data.expired_date,
                    transCode = input.transCode
                };

                int isAddPrivate = connection.Execute(
                    Contant.OS6_FPTVoucher_LoyaltyPromotionEvent,
                    pars, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);
                
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(responseAPI), "BookVoucherFPTplay isAddPrivate ");
                if (isAddPrivate > 0)
                {
                    transaction.Commit();
                    var bookVoucherOutput = new BookVoucherOutput { eVoucher = bookVoucherFPTplay.data.code, validTo = Convert.ToDateTime(bookVoucherFPTplay.data.expired_date), voucherType = typeData.VoucherTypeName };
                    return new ResponseModel<BookVoucherOutput> { data = bookVoucherOutput, error = null, result = 1 };
                }
                return new ResponseModel<BookVoucherOutput> { data = null, error = "Đã xảy ra lỗi", result = 0 };               

            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(ex.Message), "BookVoucherFPTplay Error ");
                return new ResponseModel<BookVoucherOutput> { data = null, error = ex.Message, result = 0 }; 
            }
        }
        public static string HmacSha256Digest(string message, string secret)
        {
            try
            {
                ASCIIEncoding encoding = new ASCIIEncoding();
                byte[] keyBytes = encoding.GetBytes(secret);
                byte[] messageBytes = encoding.GetBytes(message);
                System.Security.Cryptography.HMACSHA256 cryptographer = new System.Security.Cryptography.HMACSHA256(keyBytes);

                byte[] bytes = cryptographer.ComputeHash(messageBytes);

                return BitConverter.ToString(bytes).Replace("-", "").ToLower();
            }
            catch (Exception ex)
            {
                return "";
            }

        }

        public static string CallAPI_ProXyHMc(string _url, string _data, string logid)
        {
            try
            {
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(""), " start to call ");
                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);
                    client.BaseAddress = new Uri(_url);
                    client.DefaultRequestHeaders.Add("APP-ID", Utility.FptPlay_APP_ID.ToString());
                    client.DefaultRequestHeaders.Add("APP-KEY", Utility.FptPlay_APP_KEY.ToString());
                    var data = new System.Net.Http.StringContent(_data, Encoding.UTF8, "application/json");

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(data), " call API FPt play req ");

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(_url+ "/api/payment/v1/partners/foxgold/codes"), " full url111 ");

                    var response = client.PostAsync("/api/payment/v1/partners/foxgold/codes", data);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) +  " log1");


                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " log2");

                    var response2 = response.Result;

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " log3");

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(response), " sAPI FPt play response2 ");

                    string r = response2.Content.ReadAsStringAsync().Result;

                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(r), " call API FPt play res ");
                    return r;
                }                
            }
            catch (AggregateException ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logid, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(ex.Message + " | StackTrace:" + ex.StackTrace), " call API FPt play error ");

                // Log detailed exception information
                foreach (var innerEx in ex.InnerExceptions)
                {
                    L.Mes(Level.ERROR, string.Format("{0} {1:HH:mm:ss} ", logid, DateTime.Now) + " " + innerEx.Message, " Inner exception");
                }

                return null;
            }

        }
    }
}