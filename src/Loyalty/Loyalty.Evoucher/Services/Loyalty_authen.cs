using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;

namespace Loyalty.Evoucher.Services
{
    public static class Loyalty_authen
    {
        static AuthorizationInfor _auth;
        static AuthorizationInfor getAuthorInforV2(LoginInfor login, string logId)
        {
            
            if (_auth != null)
            {
                long timeExpire = Convert.ToInt32(_auth.expires_in) + Convert.ToInt32(_auth.iat);                
                DateTime datetimeNow = DateTime.Now.AddMinutes(30);                
                long timeNow = ConvertToTimestamp(datetimeNow);
                if (timeExpire > timeNow)
                    return _auth;
            }
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject("start to getAuthorInfor"));
            
            try
            {
                string authInfo = login.username + ":" + login.password;
                authInfo = Convert.ToBase64String(Encoding.Default.GetBytes(authInfo));

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(WebAPIHelper.loyaltyapi + "/auth/oauth/token?grant_type=client_credentials");
                request.Method = "POST";
                request.Accept = "application/json; charset=utf-8";

                request.Headers["Authorization"] = "Basic " + authInfo;

                var response = (HttpWebResponse)request.GetResponse();

                string strResponse = "";
                using (var sr = new StreamReader(response.GetResponseStream()))
                {
                    strResponse = sr.ReadToEnd();
                }
                _auth = JsonConvert.DeserializeObject<AuthorizationInfor>(strResponse);

            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " getAuthorInforV2 Error:");
                return null;
            }
            return _auth;
        }
        private static long ConvertToTimestamp(DateTime value)
        {
            long epoch = (value.Ticks - 621355968000000000) / 10000000;
            return epoch;
        }
        public static AuthorizationInfor GetTokenv2(LoginInfor login, string logId)
        {
            return getAuthorInforV2(login, logId);
        }
    }
}