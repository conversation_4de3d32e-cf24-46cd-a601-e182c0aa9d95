using Loyalty.Evoucher.Models;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using Newtonsoft.Json;
using Voucher.APIHelper.Util;
using System.Configuration;
using Loyalty.Evoucher.Contants;
using System.Data;

namespace Loyalty.Evoucher.Services
{
    public class HifptService
    {
        /*
        public static ResponseModel<BookVoucherOutput> BookVoucherHiFPT(SqlConnection connection, SqlTransaction transaction, BookVoucherModel input, TypeVoucher typeData, Guid logId)
        {
            var res = new ResponseModel<BookVoucherOutput> { data = new BookVoucherOutput(), error = "không tìm thấy", result = 0 };
            try
            {
                BookHiFPTVoucherOP bookHiFPTVoucherOP = new BookHiFPTVoucherOP();
                string url = ConfigurationManager.AppSettings["HiFPT_FG_domain"].ToString()+ "/hi-ecom-promotion-api/v1/partner/exchange-voucher";
                var req = new
                {
                    phone_nb = input.phone,
                    program_id = input.eVoucher,
                    duration_voucher = typeData.ExpiredDay,
                    trans_id = input.transCode,
                    client_id= "EVC"
                };
                LoyaltyCommonServices.WriteToLog(req, "BookVoucherHiFPT req ", logId);

                string datareq = JsonConvert.SerializeObject(req);
                var clientkey = ConfigurationManager.AppSettings["HiFPT_FG_clientkey"].ToString();
                var secretkey = ConfigurationManager.AppSettings["HiFPT_FG_secretkey"].ToString();
                List<HeaderAPI> headers = new List<HeaderAPI> { new HeaderAPI {key="Token",value=string.Format("{0}::{1}{2}", clientkey, secretkey,DateTime.Now.ToString("yyyy-dd-MM")).CreateMD5() } };
                var datares = LoyaltyCommonServices.callapi(url,"POST", datareq, "application/json; charset=utf-8", headers);
                LoyaltyCommonServices.WriteToLog(datares, "BookVoucherHiFPT datares ", logId);
                bookHiFPTVoucherOP = JsonConvert.DeserializeObject<BookHiFPTVoucherOP>(datares);
                if(bookHiFPTVoucherOP.statusCode.Equals(0) && bookHiFPTVoucherOP.data != null)
                {
                    int addCode = AddPrivateCode(connection, transaction, input, bookHiFPTVoucherOP.data,typeData.ID);
                    LoyaltyCommonServices.WriteToLog(addCode, "BookVoucherHiFPT addCode ", logId);
                    if (addCode == 2)
                    {
                        transaction.Commit();
                        res.data = new BookVoucherOutput { eVoucher = bookHiFPTVoucherOP.data.evoucher, validTo = Convert.ToDateTime(bookHiFPTVoucherOP.data.exp_date), voucherType = typeData.VoucherTypeName };
                        res.result = 1;
                        res.error = "";
                    }                    
                }                
            }
            catch (Exception ex)
            {
                res.data = null;
                res.error = ex.Message;
                res.result = -1;
            }
            return res;
        }*/

        public static string GetVoucherType(SqlConnection connection, SqlTransaction transaction, string voucherCode)
        {
            return connection.Query<string>(Contant.OS6_FPTVoucher_LoyaltyHiFPT, new { actionName = "getVoucherType", voucherCode= voucherCode }, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }
        public static int RedeemVoucherDevice(SqlConnection connection, SqlTransaction transaction, UseVoucherModel input)
        {
            return connection.Execute(Contant.OS6_FPTVoucher_LoyaltyHiFPT, 
                new { 
                    actionName = "RedeemVoucherDivice",
                    voucherCode = input.voucherCode ,
                    objid = input.objId,
                    serviceCode=input.serviceCode,
                    valueVoucher = input.valueVoucher
                },commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);
        }

        public static int GetPrivateCodeID(string code)
        {
            int id = 0;
            using (var con = new SqlConnection(SqlHelper.ConnRead()))
            {
                id = con.Query<int>(Contant.OS6_FPTVoucher_LoyaltyHiFPT, new
                {
                    actionName = "GetPrivateCodeID",
                    voucherCode = code
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return id;
        }
        public static int RedeemVoucherNoDevice(SqlConnection connection, SqlTransaction transaction, UseVoucherModel input,string action)
        {
            return connection.Execute(Contant.OS6_FPTVoucher_LoyaltyHiFPT,
                new
                {
                    actionName = action,
                    voucherCode = input.voucherCode,
                    objid = input.objId
                },commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure);
        }

        public static int AddPrivateCode(SqlConnection connection, SqlTransaction transaction, BookVoucherModel input, BookHiFPTVoucherModel voucherBook, int promotionId)
        {
            return connection.Execute(
            Contant.OS6_FPTVoucher_LoyaltyHiFPT,
            new
            {
                actionName = "AddVoucherPrivate",
                voucherCode = voucherBook.evoucher,
                expriteDate = voucherBook.exp_date,
                promotionId = promotionId,
                phone = input.phone,
                trans = input.transCode
            }, commandTimeout: null, transaction: transaction, commandType: CommandType.StoredProcedure) ;
        }

        
    }
}