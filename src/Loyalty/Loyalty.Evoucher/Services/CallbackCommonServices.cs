using Dapper;
using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;

namespace Loyalty.Evoucher.Services
{
    public class CallbackCommonServices
    {
        public static string OS6_FPTVoucher_LoyaltyCallBack = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyCallBack";
        public static List<DataRecall> GetDataToRecall()
        {
            using (var connect = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connect.Query<DataRecall>(OS6_FPTVoucher_LoyaltyCallBack, new
                {
                    actionName = "GetDataToRecall"
                }, commandType: CommandType.StoredProcedure).ToList();
            }
        }
        public static void UpdatePrivateCode(long id)
        {
            using (var connect = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connect.Query(OS6_FPTVoucher_LoyaltyCallBack, new
                {
                    actionName = "UpdatePrivateCode",
                    idPrivateCode = id
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static bool NewChangeStatusLoyaltyV2(SendLoyalty sl, int privateCodeID, string LogId)
        {
            bool flag = false;
            string responeseAPI = "";
            string request = JsonConvert.SerializeObject(sl);
            string exception = "";
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(sl), "ChangeStatusLoyalty req ");
            LoyaltyRedeemOutput lro = new LoyaltyRedeemOutput();
            try
            {
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, LogId);

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(WebAPIHelper.loyaltyapi);
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(WebAPIHelper.loyaltyapi), " NewChangeStatusLoyalty urlAPI " + sl.voucherCode);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", aut.access_token);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(sl), Encoding.UTF8, "application/json");
                    var response = client.PostAsync("/loyalty-services/api/integration-evoucher/redeem-device", data).Result;
                    string result = response.Content.ReadAsStringAsync().Result;
                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(result), " NewChangeStatusLoyalty APIres " + sl.voucherCode);
                    lro = JsonConvert.DeserializeObject<LoyaltyRedeemOutput>(result);
                    responeseAPI = JsonConvert.SerializeObject(lro);
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", LogId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(ex.Message), " NewChangeStatusLoyalty APIres Error " + sl.voucherCode);
                flag = false;
                exception = ex.Message;
            }

            if (lro.statusCode != null)
            {
                if (lro.statusCode.ToUpper().Equals("SUCCESS"))
                {
                    flag = true;
                }
            }
            Insert_LogRequest_LogResponset(privateCodeID, sl.voucherCode, request, flag, responeseAPI, exception);
            return flag;
        }

        public static void Insert_LogRequest_LogResponset(int privareCodeID, string voucherCode, string request, bool isSuccess, string responeseAPI, string exception)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                string endpoint = "/loyalty-services/api/integration-evoucher/redeem-device";
                var idRequest = connection.Query<int>(OS6_FPTVoucher_LoyaltyCallBack, new
                {
                    actionName = "InsertLogRequest",
                    req = request,
                    idPrivateCode = privareCodeID,
                    voucherCode = voucherCode,
                    isSuccess = isSuccess,
                    endpoint = endpoint
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();

                connection.Query(OS6_FPTVoucher_LoyaltyCallBack, new
                {
                    actionName = "InsertLogResponset",
                    res = responeseAPI,
                    idReq = idRequest,
                    exception = exception
                }, commandType: CommandType.StoredProcedure);
            }
        }
        public static int GetPrivateCodeID(string code)
        {
            int id = 0;
            using(var con = new SqlConnection(SqlHelper.ConnRead()))
            {
                id = con.Query<int>(OS6_FPTVoucher_LoyaltyCallBack, new
                {
                    actionName = "GetPrivateCodeID",
                    voucherCode = code
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return id;
        }
        public static async Task processcallbackLoyatylPayment()
        {
            try
            {
                Task taskUpdateVoucherSuccessPayment = new Task
                (
                    (object ob) =>
                    {
                        ReCall_NewChangeStatusLoyalty();
                    }, "taskUpdateVoucherSuccessPayment"
                );
                taskUpdateVoucherSuccessPayment.Start();
                await taskUpdateVoucherSuccessPayment;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "ReCall_NewChangeStatusLoyalty error", Guid.NewGuid());
            }
        }
        public static void ReCall_NewChangeStatusLoyalty()
        {
            var logId = Guid.NewGuid();
            try
            {
                var lst = CallbackCommonServices.GetDataToRecall();
                LoyaltyCommonServices.WriteToLog(lst.Count, "ReCall_NewChangeStatusLoyalty lst ", logId);
                if (lst.Count == 0)
                {
                    return;
                }
                foreach (var item in lst)
                {
                    var nlogId = Guid.NewGuid();
                    var req = JsonConvert.DeserializeObject<SendLoyalty>(item.request);
                    bool isSendLoy = CallbackCommonServices.NewChangeStatusLoyaltyV2(req, item.idPrivateCode, nlogId.ToString());
                    if (isSendLoy == true)
                    {
                        int checkRB = CheckRB(item.idPrivateCode);
                        LoyaltyCommonServices.WriteToLog(checkRB, "ReCall_NewChangeStatusLoyalty checkRB ", nlogId);
                        if (checkRB > 0)
                        {
                            int rb = Rollback_voucherCuoc(item.idPrivateCode, checkRB);
                            LoyaltyCommonServices.WriteToLog(rb, "ReCall_NewChangeStatusLoyalty Rollback_voucherCuoc ", nlogId);
                        }
                        else
                        {
                            CallbackCommonServices.UpdatePrivateCode(item.idPrivateCode);
                        }
                    }
                }

            }
            catch (Exception e)
            {
                LoyaltyCommonServices.WriteToLog(e.Message, "ReCall_NewChangeStatusLoyalty err ", logId);
                return;
            }
            return;
        }

        public static int CheckRB(int idPrivateCode)
        {
            using (var connect = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connect.Query<int>(OS6_FPTVoucher_LoyaltyCallBack, new
                {
                    actionName = "CheckRB",
                    idPrivateCode = idPrivateCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static int Rollback_voucherCuoc(int idPrivateCode, int idSchedulePromotion)
        {
            using (var connect = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connect.Execute(OS6_FPTVoucher_LoyaltyCallBack, new
                {
                    actionName = "Rollback_voucherCuoc",
                    idPrivateCode = idPrivateCode,
                    idSchedulePromotion = idSchedulePromotion
                }, commandType: CommandType.StoredProcedure);
            }
        }
    }
}