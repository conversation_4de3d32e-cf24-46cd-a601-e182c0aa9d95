using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Data;
using System.Threading.Tasks;
using Loyalty.Evoucher.Models;
using System.Configuration;
using Newtonsoft.Json;
using System.Text;
using Loyalty.Services.LoyaltyCampaign;
using System.Net.Http;
using System.Net.Http.Headers;

namespace Loyalty.Evoucher.Services
{
    public class LoyaltyPaymentServices
    {
        public const string OS6_FPTVoucher_LoyaltyPayment = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyPayment";
        public const string OS6_FPTVoucher_CheckCondToApplyVoucher = "PowerInside.dbo.OS6_FPTVoucher_CheckCondToApplyVoucher";
        public const string OS6_CheckIPTVCondToApplyVoucher = "IPTV.dbo.OS6_CheckIPTVCondToApplyVoucher";
        public const string OS6_Fgold_CheckNoPrePaid = "IPTV.dbo.OS6_Fgold_CheckNoPrePaid";
        public const string OS6_FPTVoucher_GetFreePaymentPrepaid = "PowerInside.dbo.OS6_FPTVoucher_GetFreePaymentPrepaid";
        public const string OS6_FPTVoucher_RollbackVoucherByReceipt = "PowerInside.dbo.OS6_FPTVoucher_RollbackVoucherByReceipt";
        public const string OS6_FPTVoucher_Get_VoucherConfig = "PowerInside.dbo.OS6_FPTVoucher_Get_VoucherConfig";
        public const string OS6_FPTVoucher_Campaign_FoxGold_NewCustomer = "PowerInside.dbo.OS6_FPTVoucher_Campaign_FoxGold_NewCustomer";

        public static List<int> arrayServiceToPublishBill = new List<int> { 21, 22, 193, 556 };
        public static List<string> CheckContract(string contracts, Guid logid)
        {
            var lst = new List<string>();
            var lstDataContract = new List<string>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    // kiểm tra hợp đồng có dùng voucher cước trong tháng
                    var lstDataNotUse = connection.Query<string>(OS6_FPTVoucher_LoyaltyPayment,
                        new
                        {
                            actionName = "CheckUseVoucher",
                            contracts = contracts,
                        }, commandType: CommandType.StoredProcedure).ToList();
                    LoyaltyCommonServices.WriteToLog(lstDataNotUse, "CheckContract lstDataNotUse ", logid);
                    if (lstDataNotUse.Count == 0)
                    {
                        LoyaltyCommonServices.WriteToLog("", "CheckContract Nodata ", logid);
                        return new List<string>();
                    }
                    //kiểm tra hợp đồng có nợ cước net, hop dong trả sau net
                    string sContracts = string.Join(",", lstDataNotUse);
                    var lstdata1 = connection.Query<Tuple<string, int>>(OS6_FPTVoucher_CheckCondToApplyVoucher,
                        new
                        {
                            ContractList = sContracts,
                        }, commandType: CommandType.StoredProcedure).ToList();
                    LoyaltyCommonServices.WriteToLog(lstdata1, "CheckContract lstdata1 ", logid);


                    if (lstdata1.Count > 0)
                    {
                        lst.AddRange(lstdata1.Select(x=>x.Item1).ToList());
                    } 
                        

                    // kiểm tra hợp đồng có nợ cước iptv
                    var lstdata2 = CheckPostPaidTV(lstDataNotUse);
                    LoyaltyCommonServices.WriteToLog(lstdata2, "CheckContract lstdata2 ", logid);
                    if (lstdata2.Count > 0) lst.AddRange(lstdata2);

                    var output = lst.GroupBy(contract => contract)
                        .OrderByDescending(group => group.Count())
                        .Select(group => group.Key).ToList();

                    return output;
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "CheckContract Error ", logid);
                return null;
            }
        }

        public static List<datePayment> CheckContractv2(PaymentLoyaltyModelv2 input, StringBuilder sb)
        {
            var lst = new List<datePayment>();
            try
            {
                using (var connection = new SqlConnection(Utility.ConnWrite))
                {
                    connection.Open();
                    // kiểm tra hợp đồng có dùng voucher cước trong tháng
                    foreach (var contract in input.contractPayments)
                    {
                        var contractNotUse = connection.Query<string>(OS6_FPTVoucher_LoyaltyPayment,
                        new
                        {
                            actionName = "CheckUseVoucher",
                            contracts = contract.contract,
                            voucherType = input.voucherType//
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("contractNotUse", contractNotUse));

                        var all_evc_type = get_all_evc_type_payment(connection);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("CheckContractv2 all_evc_type ", all_evc_type));

                        var check_campaign_point = all_evc_type.Where(x => x.evc_type == input.voucherType && x.check_estimate == 0 && !string.IsNullOrEmpty(contractNotUse)).FirstOrDefault();

                        //kiểm tra hợp đồng có nợ cước net, hop dong trả sau net
                        var data1 = connection.Query<string>(OS6_FPTVoucher_CheckCondToApplyVoucher,
                            new
                            {
                                ContractList = contractNotUse,
                            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("data1", data1));

                        // kiểm tra hợp đồng có nợ cước iptv
                        var data2 = CheckPostPaidTVv2(contractNotUse);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("data2", data2));

                        var data3 = EstimateVoucherTypePrepaid(connection, input.voucherType, contractNotUse);

                        if (!string.IsNullOrEmpty(data2) || !string.IsNullOrEmpty(data1) || !string.IsNullOrEmpty(data3) || check_campaign_point != null)
                        {
                            lst.Add(contract);
                        }
                    }
                    return lst;
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("CheckContractv2 er", ex));
                return null;
            }
        }
        public static string CheckPostPaidTVv2(string contract)
        {
            var conStringIpTV = Utility.ConnIPTV;
            
            using (var connection = new SqlConnection(conStringIpTV))
            {
                connection.Open();
                int res = connection.Query<int>(OS6_Fgold_CheckNoPrePaid,
                        new
                        {
                            Contract = contract,
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                if (res == 1)
                {
                    return contract;
                }
            }
            return string.Empty;
        }
                
        public static List<string> CheckPostPaidTV(List<string> sContracts)
        {
            var conStringIpTV = Utility.ConnIPTV;
            List<string> lst = new List<string>();
            using (var connection = new SqlConnection(conStringIpTV))
            {
                connection.Open();
                foreach (var contract in sContracts)
                {
                    // kiêm tra hợp đồng tra sau giá trị =1, trả trước giá trị =0
                    int res = connection.Query<int>(OS6_Fgold_CheckNoPrePaid,
                        new
                        {
                            Contract = contract,
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (res == 1)
                    {
                        lst.Add(contract);
                    }
                }
            }
            return lst;
        }

        public static async Task UpdateVoucherSuccessPayment()
        {
            try
            {
                Task taskUpdateVoucherSuccessPayment = new Task
                (
                    (object ob) =>
                    {
                        UpdateSuccess();
                    }, "TaskCheckdata"
                );
                taskUpdateVoucherSuccessPayment.Start();
                await taskUpdateVoucherSuccessPayment;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "UpdateVoucherSuccessPayment error", Guid.NewGuid());
            }
        }

        public static async Task UpdateVoucherRBPayment()
        {
            try
            {
                Task taskUpdateVoucherRBPayment = new Task
                (
                    (object ob) =>
                    {
                        UpdateRb();
                    }, "TaskCheckdata"
                );
                taskUpdateVoucherRBPayment.Start();
                await taskUpdateVoucherRBPayment;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "UpdateVoucherRBPayment error", Guid.NewGuid());
            }
        }

        public static List<DataSuccessJob> GetDataSuccessJob()
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Query<DataSuccessJob>(OS6_FPTVoucher_LoyaltyPayment,
                new
                {
                    actionName = "getDataJobSuccess"
                },  commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }

        }
        public static List<DataRBJob> GetDataRBJob()
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Query<DataRBJob>(OS6_FPTVoucher_LoyaltyPayment,
                            new
                            {
                                actionName = "getDataJobRB"
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            }

        }
        public static int UpdateVoucherCode(string typeUpdate,int idSd, int idpri )
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    return  connection.Execute(OS6_FPTVoucher_LoyaltyPayment,
                            new
                            {
                                actionName = "UpdatePaymentLoy", 
                                typeUpdate = typeUpdate,
                                id = idSd,
                                idPrivateCode = idpri
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure);
                }
        }
        private static void UpdateSuccess()
        {
            var logId = Guid.NewGuid();
            try
            {
                List<DataSuccessJob> listData = GetDataSuccessJob();
                LoyaltyCommonServices.WriteToLog(listData, "UpdateSuccess GetDataSuccessJob", logId);
                if (listData.Count == 0)
                {
                    return;
                }
                foreach (var data in listData)
                {
                    var req = new SendLoyalty
                    {
                        voucherActualUse = data.voucherActualUse,
                        contractNo = data.Contract,
                        billContent = data.Content,
                        billFromDate = data.FromDate,
                        billNumber = data.BillNumber,
                        billRemain = data.billRemain,
                        billToDate = data.ToDate,
                        billTotalDebt = data.Total,
                        mobileHiFpt = data.Phone,
                        redeemDate = data.RedeemDate,
                        voucherCode = data.Code,
                        voucherPaymentStatus = "SUCCESS"
                    };
                    LoyaltyCommonServices.WriteToLog(req, "UpdateSuccess req success SendLoy ", logId);
                    bool isSendLoy = CallbackCommonServices.NewChangeStatusLoyaltyV2(req, data.VoucherPrivateCodeID, logId.ToString());
                    LoyaltyCommonServices.WriteToLog(isSendLoy, "UpdateSuccess success SendLoy res ", logId);
                    if (isSendLoy)
                    {
                        int UpdateData = UpdateVoucherCode("success", data.ID, data.VoucherPrivateCodeID);
                        LoyaltyCommonServices.WriteToLog(UpdateData, "UpdateSuccess success UpdateData ", logId);
                    }
                }
                
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "UpdateSuccess Error ", logId);
            }
        }

        private static void UpdateRb()
        {
            var logId = Guid.NewGuid();
            try
            {
                List<DataRBJob> listData = GetDataRBJob();
                LoyaltyCommonServices.WriteToLog(listData, "UpdateSuccess GetDataSuccessJob", logId);
                if (listData.Count == 0)
                {
                    return;
                }
                foreach (var data in listData)
                {
                    var req = new SendLoyalty
                    {
                        billNumber = data.BillNumber,
                        mobileHiFpt = data.Phone,
                        voucherCode = data.Code,
                        voucherPaymentStatus = "FAIL"
                    };
                    LoyaltyCommonServices.WriteToLog(req, "UpdateRb req SendLoy ", logId);
                    int idPrivateCode = CallbackCommonServices.GetPrivateCodeID(data.Code);
                    bool isSendLoy = CallbackCommonServices.NewChangeStatusLoyaltyV2(req, idPrivateCode, logId.ToString());
                    LoyaltyCommonServices.WriteToLog(isSendLoy, "UpdateRb fail SendLoy res ", logId);
                    if (isSendLoy)
                    {
                        int UpdateData = UpdateVoucherCode("rollback", data.ID, data.VoucherPrivateCodeID);
                        LoyaltyCommonServices.WriteToLog(UpdateData, "UpdateRb UpdateData ", logId);
                    }
                }

            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "UpdateRb Error ", logId);
            }
        }

        public static ContractBillingModel GetDataContractBilling(datePayment input, StringBuilder sb)
        {
            var res = new ContractBillingModel();
            try
            {
                string apiEndpoint = "{0}/API/BIL/GetContractBilling?contract={1}&billDate={2}";
                string urlBill = Utility.BillPaymentFreeUrl;
                string apiFormanter = string.Format(apiEndpoint,urlBill,input.contract,input.toDate);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("apiFormanter", apiFormanter));

                var resApi = new ContractBilling { data= new ContractBillingModel()};
                string dataResponseAPi = LoyaltyCommonServices.callapi(apiFormanter,"GET");
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("dataResponseAPi", dataResponseAPi));
                resApi = JsonConvert.DeserializeObject<ContractBilling>(dataResponseAPi);
                if (resApi.statusCode.Equals(1))
                {
                    res = resApi.data;
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("dataResponseAPi ex", ex));
                return null;
            }
            return res;
        }

        public static List<EstimatedPayment> GetDataPrepaidBilling(datePayment input, string voucherType, StringBuilder sb)
        {
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetDataPrepaidBilling input", input));
            var res = new List<EstimatedPayment>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    res = connection.Query<EstimatedPayment>(OS6_FPTVoucher_GetFreePaymentPrepaid,
                        new
                        {
                            actionName = "GetBillPrepaid",
                            contract = input.contract,
                            toDate = input.toDate,
                            voucherType = voucherType,
                        }, commandType: CommandType.StoredProcedure).ToList();
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetDataPrepaidBilling res", res));
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetDataPrepaidBilling ex", ex));
                return null;
            }
            return res;
        }

        public static List<VoucherLoyaltyPayment> GetListVoucherPaymentLoy(VoucherBill input,Guid logId)
        {
            LoyaltyListVoucher llv = new LoyaltyListVoucher() { data= new List<VoucherLoyaltyPayment>()};
            try
            {
                int objid = GetObjid(input.contract);
                if (objid == 0) return null;
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, logId.ToString());
                string endpoint = string.Format("/loyalty-services/api/integration-evoucher/invoicepayments?objId={0}&mobile={1}", objid, input.phone);
                List<HeaderAPI> headers= new List<HeaderAPI>();
                headers.Add(new HeaderAPI {key="Authorization",value="Bearer "+aut.access_token});
                string fullAPI = string.Concat(WebAPIHelper.loyaltyapi, endpoint);
                LoyaltyCommonServices.WriteToLog(fullAPI, "GetListVoucherPaymentLoy full API  ", logId);
                string resdataLoy = LoyaltyCommonServices.callapi(fullAPI, "GET",null, "application/json; charset=utf-8", headers);
                LoyaltyCommonServices.WriteToLog(resdataLoy, "GetListVoucherPaymentLoy resdataLoy  ", logId);
                llv = JsonConvert.DeserializeObject<LoyaltyListVoucher>(resdataLoy);
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "GetListVoucherPaymentLoy Error ", logId);
                return null;
            }
            return llv.data;
        }

        public static int GetAmountVoucher(string voucherCode, SqlConnection connection)
        {
            return connection.Query<int>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment, new
            {
                actionName = "GetAmount",
                voucherCode = voucherCode,
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static GetBillDetailOutput GetBillDetail(string receipt, Guid logId)
        {
            try
            {
                var req = new GetBillDetailInput
                {
                    BillNumber = receipt,
                    Source=11
                };
                string data = JsonConvert.SerializeObject(req);
                string domain = LoyaltyCommonServices.GetConfig("onlinereceipt");
                string endpoint = "/api/v2/GetBillDetail";
                string dataDetailVoucher = LoyaltyCommonServices.callapi(string.Concat(domain,endpoint),"POST",data);
                LoyaltyCommonServices.WriteToLog(dataDetailVoucher, "GetBillDetail dataDetailVoucher ", logId);

                GetBillDetailOutput bdo = JsonConvert.DeserializeObject<GetBillDetailOutput>(dataDetailVoucher);

                //fix: 2 dịch vụ trong 1 khoản thu -> lấy cái nhiều tiền nhất
                bdo.Data = bdo.Data.OrderByDescending(x => x.Fee).ToList();

                return bdo;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "GetBillDetail Error ", logId);
                return null;
            }
        }

        public static int RoundTotalMoney(int monyeb4VAT, int vat)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                return connection.Query<int>(OS6_FPTVoucher_LoyaltyPayment,
                    new
                    {
                        actionName = "RoundMoney",
                        valueVoucher = monyeb4VAT,
                        vat = vat
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }

        public static bool PaymentAddBillVoucher(BillDetail input, Tuple<long, int,int,int> tplData,Guid logId)
        {
            try
            {
                int Amount = 0;
                double AmountVAt = 0;
                if (tplData.Item3 > input.Fee)
                {
                    Amount = input.Fee;
                    AmountVAt = RoundTotalMoney(input.Fee, 10);
                }
                else
                {
                    Amount = tplData.Item3;
                    AmountVAt = RoundTotalMoney(tplData.Item3, 10);
                }
                
                var req = new 
                {
                    BillNumber = input.BillNumber,
                    TotalVAT = AmountVAt,
                    Total=Amount,
                    Source = 11,
                    Reference = tplData.Item1,
                    PaidType = 108,
                    StaffID = 0,
                    ReceiptDetailID = input.ID // lấy theo thông tin BA
                };
                LoyaltyCommonServices.WriteToLog(req, "PaymentAddBillVoucher call API req ", logId);
                string data = JsonConvert.SerializeObject(req);
                string domain = Utility.get_appsettings("nextgenapi");
                string endpoint = "/customer/ar/api/v1/receivable/addVoucher";
                string token = getTokenOnlineRecept(logId);
                List<HeaderAPI> headers = new List<HeaderAPI>
                {
                    new HeaderAPI { key = "Authorization", value = "Bearer " + token }
                };
                string dataOutputAddVoucher = LoyaltyCommonServices.callapi(string.Concat(domain, endpoint), "POST", data, "application/json; charset=utf-8", headers);
                LoyaltyCommonServices.WriteToLog(dataOutputAddVoucher, "PaymentAddBillVoucher call API res ", logId);
                dynamic respose = JsonConvert.DeserializeObject<dynamic>(dataOutputAddVoucher);

                if (respose.data.resID == 1)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "PaymentAddBillVoucher Error ", logId);
                return false;
            }
        }

        public static bool UpdatePendingDSC(string voucher, int objid, Guid logId)
        {
            try
            {
                var req = new
                {
                    objId = objid,
                    voucherCode = voucher
                };
                LoginInfor login = new LoginInfor() { username = Login.userName, password = Login.passWord };
                AuthorizationInfor aut = Loyalty_authen.GetTokenv2(login, logId.ToString());

                string data = JsonConvert.SerializeObject(req);
                string endpoint = "/loyalty-services/api/integration-evoucher/apply-invoicepayment";
                List<HeaderAPI> headers= new List<HeaderAPI>();
                headers.Add(new HeaderAPI {key="Authorization",value="Bearer "+aut.access_token});
                string dataOutputAddVoucher = LoyaltyCommonServices.callapi(string.Concat(WebAPIHelper.loyaltyapi, endpoint), "POST", data, "application/json; charset=utf-8", headers);
                LoyaltyCommonServices.WriteToLog(dataOutputAddVoucher, "UpdatePendingDSC call API ", logId);

                dynamic respose = JsonConvert.DeserializeObject<dynamic>(dataOutputAddVoucher);
                if (respose.success == true)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "UpdatePendingDSC Error ", logId);
                return false;
            }
        }
        public static int GetObjid(string contract)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Query<int>(OS6_FPTVoucher_LoyaltyPayment,
                            new
                            {
                                actionName = "Getobjid",
                                contract=contract
                            }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        public static bool CheckPaymentNetTV(GetBillDetailOutput bdo)
        {
            //List<int> arrayService = new List<int> { 21, 22, 193, 556 };

            List<BillDetail> lstDetail = bdo.Data.Where(x => arrayServiceToPublishBill.Contains(x.ServiceType)).ToList();
            if (lstDetail.Count > 0)
            {
                return true;
            }
            return false;
        }
        public static bool CheckObjPendingVoucher(int objid)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                int res = connection.Query<int>(OS6_FPTVoucher_LoyaltyPayment,
                    new
                    {
                        actionName = "CheckObjidPending",
                        objid = objid
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                return (res == 1);
            }
        }

        public static bool CheckVoucherPhone(SqlConnection connection, SqlTransaction transaction,string phone, string voucherCode, Guid logId)
        {
            LoyaltyCommonServices.WriteToLog(voucherCode, "CheckVoucherPhone voucherCode ", logId);
            try
            {
                int data = connection.Query<int>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment,
                    new
                    {
                        actionName = "CheckVoucherPhone",
                        voucherCode = voucherCode,
                        phone = phone,
                    }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
                if (data.Equals(1))
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "CheckVoucherPhone error ", logId);                           
            }
            return false;
        }
        public static ResponseModel<bool> RedeemPaymentHiFPTWithService(SqlConnection connection, SqlTransaction transaction, ObjRedeemHi input, Guid logId, StringBuilder sb)
        {
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("RedeemPaymentHiFPTWithService ", ""));
            var res = new ResponseModel<bool> { data = false, result = 0, error = "Voucher không hợp lệ" };
            try
            {
                GetBillDetailOutput bdo = GetBillDetail(input.bill_receipt, logId);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("bdo ", bdo));
                //bool isTrueNetTV = CheckPaymentNetTV(bdo);
                //if (!isTrueNetTV)
                //{
                //    res.data = false;
                //    res.error = "Khoản thu không phải Net hoặc Pay";
                //    return res;
                //}

                var bdo2 = filter_bdo_redeem(connection, transaction, bdo, input);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("bdo2 ", bdo2));

                //Tuple<long, int,int,int> 1:id privateCode, 2: objid, 3: amount no vat, 4: amount with vat
                Tuple<long, int, int, int> tplData = connection.Query<Tuple<long, int, int, int>>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment,
                    new
                    {
                        actionName = "GetobjidByBill",
                        objid = input.obj_id,
                        voucherCode = input.evc_code,
                    }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("tplData ", tplData));

                var check_rb = update_rollback(connection, transaction, tplData.Item2, tplData.Item1);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("check_rb ", check_rb));

                int addSchedule = connection.Execute(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment,
                    new
                    {
                        actionName = "AddScheduleVoucher",
                        objid = tplData.Item2,
                        idPrivateCode = tplData.Item1,
                        serviceCode = bdo2.ServiceType,
                        ReceiptID = bdo2.ReceiptID
                    }, transaction: transaction, commandType: CommandType.StoredProcedure);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("addSchedule ", addSchedule));
                if (addSchedule > 0)
                {
                    bool updatePending = LoyaltyPaymentServices.UpdatePendingDSC(input.evc_code, tplData.Item2, logId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("UpdatePendingDSC ", updatePending));
                    bool AddVoucher = LoyaltyPaymentServices.PaymentAddBillVoucher(bdo2, tplData, logId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("PaymentAddBillVoucher ", AddVoucher));
                    if (AddVoucher)
                    {
                        transaction.Commit();
                        res.data = true;
                        res.result = 1;
                        res.error = "";
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Exception ", ex));
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            return res;
        }

        public static ResponseModel<bool> RedeemPaymentHiFPTWithNoService(SqlConnection connection, SqlTransaction transaction, ObjRedeemHi input, int id_shedule,Guid logId, StringBuilder sb)
        {
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("RedeemPaymentHiFPTWithNoService", ""));
            var res = new ResponseModel<bool> { data = false, result = 0, error = "Voucher không hợp lệ" };
            try
            {
                GetBillDetailOutput bdo = GetBillDetail(input.bill_receipt, logId);
                //bool isTrueNetTV = LoyaltyPaymentServices.CheckPaymentNetTV(bdo);
                //loc data phu hop co the tinh diem

                //bdo.Data = bdo.Data.Where(x => arrayServiceToPublishBill.Contains(x.ServiceType)).ToList(); 
                //if (bdo.Data.Count == 0)
                //{
                //    res.data = false;
                //    res.error = "Khoản thu không phải Net hoặc Pay";
                //    return res;
                //}

                var bdo2 = filter_bdo_redeem(connection, transaction, bdo, input);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("bdo2", bdo2));

                //Tuple<long, int,int,int> 1:id privateCode, 2: objid, 3: amount no vat, 4: amount with vat
                Tuple<long, int, int, int> tplData = connection.Query<Tuple<long, int, int, int>>(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment,
                    new
                    {
                        actionName = "GetobjidByBill",
                        objid = input.obj_id,
                        voucherCode = input.evc_code
                    }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("tplData", tplData));

                int updateSchedule = connection.Execute(LoyaltyPaymentServices.OS6_FPTVoucher_LoyaltyPayment,
                    new
                    {
                        actionName = "UpdateServiceShedule",
                        objid = tplData.Item2,
                        idPrivateCode = tplData.Item1,
                        serviceCode = bdo2.ServiceType,
                        ReceiptID = bdo2.ReceiptID,
                        id = id_shedule
                    }, transaction: transaction, commandType: CommandType.StoredProcedure);
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("updateSchedule", updateSchedule));
                if (updateSchedule > 0)
                {
                    //bool updatePending = LoyaltyPaymentServices.UpdatePendingDSC(input.evc_code, tplData.Item2, logId);
                    //LoyaltyCommonServices.WriteToLog(updatePending, "RedeemPaymentHiFPTWithService updatePending ", logId);
                    bool AddVoucher = LoyaltyPaymentServices.PaymentAddBillVoucher(bdo2, tplData, logId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("AddVoucher", AddVoucher));
                    if (AddVoucher)
                    {
                        transaction.Commit();
                        res.data = true;
                        res.result = 1;
                        res.error = "";
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("Exception", ex));
                res.data = false;
                res.result = -1;
                res.error = ex.Message;
            }
            return res;
        }

        //khi huy khoan thu co gan voucher -> xoa serviceCode
        private static void RollbackVoucherByReceipt()
        {
            var logId = Guid.NewGuid();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Execute(OS6_FPTVoucher_RollbackVoucherByReceipt,
                        new
                        {
                        }, commandType: CommandType.StoredProcedure);
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "RollbackVoucherByReceipt Error ", logId);
            }
        }
        public static async Task RollbackVoucherByReceipts()
        {
            try
            {
                Task RollbackReceipt = new Task
                (
                    (object ob) =>
                    {
                        RollbackVoucherByReceipt();
                    }, "RollbackVoucherByReceipt"
                );
                RollbackReceipt.Start();
                await RollbackReceipt;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "RollbackVoucherByReceipt error", Guid.NewGuid());
            }
        }

        public static List<Billing> GetBills(SqlConnection connection, List<string> bills)
        {
            var result = new List<Billing>();
            foreach (var bill in bills)
            {
                var billPayment = connection.Query<Billing>(OS6_FPTVoucher_LoyaltyPayment, new
                {
                    actionName = "GetBillings",
                    billNum = bill,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                if (billPayment != null)
                {
                    billPayment.amount = RoundTotalMoney(Convert.ToInt32(billPayment.amount), 10);
                    result.Add(billPayment);
                }
            }

            return result;
        }

        public static List<Receipt> GetReceipts(SqlConnection connection, List<string> receipts)
        {
            var result = new List<Receipt>();
            foreach (string receipt in receipts)
            {
                //receipt là số khoản thu (không phải billNumber)
                var receiptPayment = connection.Query<Receipt>(OS6_FPTVoucher_LoyaltyPayment, new
                {
                    actionName = "GetReceipt",
                    billNum = receipt,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                if (receiptPayment != null)
                {
                    receiptPayment.amount = RoundTotalMoney(Convert.ToInt32(receiptPayment.amount), 10);
                    result.Add(receiptPayment);
                }

            }

            return result;
        }

        public static List<Billing> GetSuccessBills(SqlConnection connection, List<string> bills)
        {
            var result = new List<Billing>();
            foreach (var bill in bills)
            {
                var billSuccess = connection.Query<Billing>(OS6_FPTVoucher_LoyaltyPayment, new
                {
                    actionName = "GetBillingSuccess",
                    billNum = bill,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                if (billSuccess != null)
                {
                    billSuccess.amount = RoundTotalMoney(Convert.ToInt32(billSuccess.amount), 10);
                    result.Add(billSuccess);
                }
            }

            return result;
        }

        public static List<Receipt> GetSuccessReceipts(SqlConnection connection, List<string> receipts)
        {
            var result = new List<Receipt>();
            foreach (string receipt in receipts)
            {
                //receipt là số khoản thu (không phải billNumber)
                var receiptSuccess = connection.Query<Receipt>(OS6_FPTVoucher_LoyaltyPayment, new
                {
                    actionName = "GetReceiptSuccess",
                    billNum = receipt,
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                if (receiptSuccess != null)
                {
                    receiptSuccess.amount = RoundTotalMoney(Convert.ToInt32(receiptSuccess.amount), 10);
                    result.Add(receiptSuccess);
                }

            }

            return result;
        }

        public static List<VoucherInforLoyalty> GetVouchers(SqlConnection connection, string contract)
        {
            var result = new List<VoucherInforLoyalty>();
            result = connection.Query<VoucherInforLoyalty>(OS6_FPTVoucher_LoyaltyPayment, new
            {
                actionName = "GetBillingRedeemed",
                contract = contract
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            return result;
        }

        public static HiFPTVoucherFromLoy GetBillShedule(SqlConnection connection, SqlTransaction transaction, string voucher)
        {
            var result = connection.Query<HiFPTVoucherFromLoy>(OS6_FPTVoucher_LoyaltyPayment, new
            {
                actionName = "GetBillShedule",
                voucherCode = voucher,
            }, transaction: transaction, commandType: CommandType.StoredProcedure).FirstOrDefault();

            return result;
        }

        public static VoucherInforLoyalty GetBillingPending(SqlConnection connection, string contract)
        {
            var result = connection.Query<VoucherInforLoyalty>(OS6_FPTVoucher_LoyaltyPayment, new
            {
                actionName = "GetBillingPending",
                //billNum = input.billNumber,
                contract = contract
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

            return result;
        }

        #region voucher cuoc moi cho kh tra truoc

        public static int checkConfigInvoicePrepaid(string vouchertype)
        {
            try
            {
                using (var conn = new SqlConnection(Utility.ConnRead))
                {
                    return conn.Query<int>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
                    {
                        actionName = "getConfigInvoicePrepaid",
                        voucherType = vouchertype
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            catch (Exception e)
            {
                return 1;
            }
        }


        private static BillDetail filter_bdo_redeem(SqlConnection conn, SqlTransaction trans, GetBillDetailOutput bdo, ObjRedeemHi input)
        {
            var bdo2 = new BillDetail();

            var _bdo_allow = conn.Query<int>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                @actionName = "filter_bdo_redeem",
                @voucherCode = input.evc_code,
                @billnumber_receipt = input.bill_receipt
            }, trans, commandType: CommandType.StoredProcedure).ToList();

            return bdo.Data.Where(x => _bdo_allow.Contains(x.ServiceType)).FirstOrDefault();
        }

        private static string EstimateVoucherTypePrepaid(SqlConnection connection, string voucherType, string contract)
        {
            try
            {
                return connection.Query<string>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
                {
                    @actionName = "EstimateVoucherTypePrepaid",
                    @voucherType = voucherType,
                    @contract = contract
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            catch (Exception)
            {

                return "";
            }
        }

        public static ResponseModels<VoucherInforLoyalty> getVoucherPending(SqlConnection conn, string contract, StringBuilder sb)
        {
            var res = new ResponseModels<VoucherInforLoyalty> { data = new List<VoucherInforLoyalty>(), result = 0, error = "Không tìm thấy" };

            var vil2 = conn.Query<VoucherInforLoyalty>(OS6_FPTVoucher_LoyaltyPayment, new
            {
                actionName = "GetBillingPending",
                contract = contract
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetBillingPending", vil2));

            if (vil2 != null && vil2.Count > 0)
            {
                var item = vil2.FirstOrDefault();
                item.amount = RoundTotalMoney(Convert.ToInt32(item.amount), 10);
                res.data.Add(item);
                res.result = 1;
                res.error = "";
                return res;
            }

            var data = conn.Query<VoucherInforLoyalty>(OS6_FPTVoucher_LoyaltyPayment, new
            {
                actionName = "GetBillingRedeemed",
                contract = contract
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetBillingRedeemed", data));

            if (data != null && data.Count > 0)
            {
                res.data = new List<VoucherInforLoyalty>();
                res.result = 0;
                res.error = "hasRedeemed";
                return res;
            }
            return null;
        }

        public static List<VoucherLoyaltyPayment> filterAllowAddpayment2(SqlConnection conn, List<VoucherLoyaltyPayment> list_evc, List<BillDetail> list_receipt)
        {
            var data = new List<VoucherLoyaltyPayment>();
            var cfig = conn.Query<cf_evc_type>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                actionName = "filterAllowAddpayment2",
            }, commandType: CommandType.StoredProcedure).ToList();

            foreach (var item in list_receipt)
            {
                item.MonthUse = get_receipt_monthuse(conn, item.BillNumber, item.ServiceType);
            }

            foreach (var evc in list_evc)
            {
                var evc_type = get_event_type(conn, evc.eventCode);
                foreach(var c in cfig.Where(x=>x.evc_type==evc_type))
                {
                    if(list_receipt.Where(x=>x.ServiceType == c.service_type && (x.MonthUse == c.month_use || c.month_use == 0)).Any())
                    {
                        if (!data.Where(x => x.voucherCode == evc.voucherCode)?.Any() == true)
                        {
                            data.Add(evc);
                        }
                    }
                }
            }
            return data;
        }

        public static string get_event_type(SqlConnection conn, string eventcode)
        {
            return conn.Query<string>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                @actionName = "get_event_type",
                @eventcode = eventcode
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static int get_receipt_monthuse(SqlConnection conn, string billnumber_receipt, int serviceType)
        {
            return conn.Query<int>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                @actionName = "get_receipt_monthuse",
                @billnumber_receipt = billnumber_receipt,
                @serviceType = serviceType
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static ResponseModels<VoucherInforLoyalty> get_evc_changed(VoucherBill input, string flag)
        {
            var logId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog(flag, input));

            var res = new ResponseModels<VoucherInforLoyalty> { data = new List<VoucherInforLoyalty>(), result = 0, error = "Khoản thu không hợp lệ" };
            try
            {

                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();

                    var checkuse = checkUse(connection, input.contract);
                    if(checkuse == 1)
                    {
                        return res;
                    }

                    var checkPending = LoyaltyPaymentServices.getVoucherPending(connection, input.contract, sb);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("checkPending", checkPending));
                    if (checkPending != null)
                    {
                        return checkPending;
                    }

                    List<VoucherLoyaltyPayment> listVoucherLoy = LoyaltyPaymentServices.GetListVoucherPaymentLoy(input, logId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("listVoucherLoy", listVoucherLoy));
                    if (!listVoucherLoy?.Any() == true)
                    {
                        return res;
                    }

                    GetBillDetailOutput bdo = LoyaltyPaymentServices.GetBillDetail(input.billNumber, logId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetBillDetail", bdo));
                    if (bdo == null)
                    {
                        res.result = 0;
                        res.error = "Đã xảy ra lỗi";
                        return res;
                    }

                    if (bdo != null && bdo.Data.Count > 0)
                    {
                        var listVoucherLoyV2 = LoyaltyPaymentServices.filterAllowAddpayment2(connection, listVoucherLoy, bdo.Data);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("listVoucherLoyV2", listVoucherLoyV2));

                        foreach (var item in listVoucherLoyV2)
                        {
                            res.data.Add(new VoucherInforLoyalty { amount = RoundTotalMoney(GetAmountVoucher(item.voucherCode, connection), 10), evc_code = item.voucherCode, expiry = item.expiredDate, title = item.voucherName, statusCode = "CHANGED" });
                        }
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("listVoucherLoyV2", listVoucherLoyV2));
                        if (res.data.Count > 0)
                        {
                            res.result = 1;
                            res.error = "";
                        }
                        return res;
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetListVoucher ex ", ex));
                return new ResponseModels<VoucherInforLoyalty> { data = null, result = -1, error = ex.Message };
            }
            finally
            {
#if DEBUG
                Utility.LogError("GetListEvoucher", sb.ToString());
#else
                LoggerKafka.WriteLogKafka(sb, flag, logId.ToString());
#endif
            }
            return res;
        }

        private static int checkUse(SqlConnection connection, string contract)
        {
            return connection.Query<int>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                @actionName = "checkUse",
                @contract = contract
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        public static ResponseModel<bool> hi_redeem_evc_loy(ObjRedeemHi input, string flag)
        {
            var logId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("hi_redeem_evc_loy", input));
            var res = new ResponseModel<bool> { data = false, result = 0, error = "Không tìm thấy" };
            try
            {
                using (var connection = new SqlConnection(Utility.ConnWrite))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        bool isValidPhone = CheckVoucherPhone(connection, transaction, input.phone, input.evc_code, logId);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("isValidPhone", isValidPhone));
                        if (!isValidPhone)
                        {
                            res.error = "Voucher không phù hợp với hợp đồng áp dụng";
                            return res;
                        }

                        HiFPTVoucherFromLoy scheduleVocuher = GetBillShedule(connection, transaction, input.evc_code);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("scheduleVocuher", scheduleVocuher));
                        if (scheduleVocuher != null && scheduleVocuher.ServiceCode == 0)
                        {
                            if (input.evc_code != scheduleVocuher.Code)
                            {
                                res.result = 0;
                                res.error = "Voucher không chính xác";
                                return res;
                            }
                            return RedeemPaymentHiFPTWithNoService(connection, transaction, input, scheduleVocuher.ScheduleID, logId, sb);
                        }
                        else if (scheduleVocuher == null)
                        {
                            return RedeemPaymentHiFPTWithService(connection, transaction, input, logId, sb);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("hi_redeem_evc_loy Exception", e));
                return new ResponseModel<bool> { data = false, error = e.Message, result = -1 };
            }
            finally
            {
#if DEBUG
                Utility.LogError("hi_redeem_evc_loy", sb.ToString());
#else
                LoggerKafka.WriteLogKafka(sb, flag, logId.ToString());
#endif
            }
            res.result = 0;
            res.error = "Fail";
            return res;
        }

        public static int get_config_value(string key)
        {
            try
            {
                using (var conn = new SqlConnection(Utility.ConnWrite))
                {
                    return conn.Query<int>(OS6_FPTVoucher_Get_VoucherConfig, new
                    {
                        key = key
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            catch (Exception)
            {
                return 0;
            }
        }

        internal static ResponseModels<VoucherInforLoyalty> get_evc_changed_v2(VoucherBill input, string flag)
        {
            var logId = Guid.NewGuid();
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("get_evc_changed_v2", input));

            var res = new ResponseModels<VoucherInforLoyalty> { data = new List<VoucherInforLoyalty>(), result = 0, error = "Khoản thu không hợp lệ" };
            try
            {

                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();

                    var checkuse = checkUse_v2(connection, input.contract);
                    if (checkuse == 1)
                    {
                        return res;
                    }

                    var checkPending = LoyaltyPaymentServices.getVoucherPending_v2(connection, input.contract, input.billNumber, sb);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("checkPending", checkPending));
                    if (checkPending != null)
                    {
                        return checkPending;
                    }

                    /*
                    // nếu không lấy đc pending nhưng lại đang hẹn giờ -> show rỗng
                    var _check_scheduled = check_scheduled(connection, input.contract);
                    if (_check_scheduled == 1)
                    {
                        return res;
                    }
                    */

                    List<VoucherLoyaltyPayment> listVoucherLoy = LoyaltyPaymentServices.GetListVoucherPaymentLoy(input, logId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("listVoucherLoy", listVoucherLoy));
                    if (listVoucherLoy == null || listVoucherLoy.Count() == 0)
                    {
                        return res;
                    }

                    GetBillDetailOutput bdo = LoyaltyPaymentServices.GetBillDetail(input.billNumber, logId);
                    sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetBillDetail", bdo));
                    if (bdo == null)
                    {
                        res.result = 0;
                        res.error = "Đã xảy ra lỗi";
                        return res;
                    }

                    if (bdo != null && bdo.Data.Count > 0)
                    {
                        var listVoucherLoyV2 = LoyaltyPaymentServices.filterAllowAddpayment2(connection, listVoucherLoy, bdo.Data);
                        sb.AppendLine(LoggerKafka.JoinStringToWriteLog("listVoucherLoyV2", listVoucherLoyV2));

                        var type_dont_used = evc_type_dont_used(connection, input.contract, listVoucherLoyV2, sb);

                        foreach (var item in type_dont_used)
                        {
                            res.data.Add(new VoucherInforLoyalty { amount = RoundTotalMoney(GetAmountVoucher(item.voucherCode, connection), 10), evc_code = item.voucherCode, expiry = item.expiredDate, title = item.voucherName, statusCode = "CHANGED" });
                        }
                        if (res.data.Count > 0)
                        {
                            res.result = 1;
                            res.error = "";
                        }
                        return res;
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetListVoucher ex ", ex));
                return new ResponseModels<VoucherInforLoyalty> { data = null, result = -1, error = ex.Message };
            }
            finally
            {
#if DEBUG
                Utility.LogError("GetListEvoucher", sb.ToString());
#else
                LoggerKafka.WriteLogKafka(sb, flag, logId.ToString());
#endif
            }
            return res;
        }

        public static ResponseModels<VoucherInforLoyalty> getVoucherPending_v2(SqlConnection conn, string contract, string billnumber_receipt, StringBuilder sb)
        {
            var res = new ResponseModels<VoucherInforLoyalty> { data = new List<VoucherInforLoyalty>(), result = 0, error = "Không tìm thấy" };

            var vil2 = conn.Query<VoucherInforLoyalty>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                actionName = "GetBillingPending_v2",
                contract = contract,
                billnumber_receipt = billnumber_receipt
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();

            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetBillingPending_v2", vil2));

            if (vil2 != null && vil2.Count > 0)
            {
                var item = vil2.FirstOrDefault();
                item.amount = RoundTotalMoney(Convert.ToInt32(item.amount), 10);
                res.data.Add(item);
                res.result = 1;
                res.error = "";
                return res;
            }

            var data = conn.Query<VoucherInforLoyalty>(OS6_FPTVoucher_LoyaltyPayment, new
            {
                actionName = "GetBillingRedeemed_v2",
                contract = contract
            }, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("GetBillingRedeemed_v2", data));

            if (data != null && data.Count > 0)
            {
                res.data = new List<VoucherInforLoyalty>();
                res.result = 0;
                res.error = "hasRedeemed";
                return res;
            }
            return null;
        }

        private static int checkUse_v2(SqlConnection connection, string contract)
        {
            return connection.Query<int>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                @actionName = "checkUse_v2",
                @contract = contract
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        private static int check_scheduled(SqlConnection connection, string contract)
        {
            return connection.Query<int>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                @actionName = "check_scheduled",
                @contract = contract
            }, commandType: CommandType.StoredProcedure).FirstOrDefault();
        }

        private static List<VoucherLoyaltyPayment> evc_type_dont_used(SqlConnection connection, string contract, List<VoucherLoyaltyPayment> listVoucherLoyV2, StringBuilder sb)
        {
            var data = new List<VoucherLoyaltyPayment>();
            var xml = Utility.Converter.ToXml(listVoucherLoyV2);
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("evc_type_dont_used xml", xml));
            data = connection.Query<VoucherLoyaltyPayment>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
            {
                @actionName = "evc_type_dont_used",
                @xml = xml,
                @contract = contract
            }, commandType: CommandType.StoredProcedure).ToList();
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("evc_type_dont_used data", data));
            return data == null ? new List<VoucherLoyaltyPayment>() : data;
        }

        private static int update_rollback(SqlConnection conn, SqlTransaction trann, int objid, long privateCode)
        {
            try
            {
                return conn.Execute(OS6_FPTVoucher_LoyaltyPayment, new
                {
                    actionName = "update_rollback_redeem",
                    objid = objid,
                    idPrivateCode = privateCode
                }, trann, commandType: CommandType.StoredProcedure);
            }
            catch (Exception)
            {
                return -1;
            }
        }

        public static string get_evc_type(string evcCode)
        {
            using (SqlConnection conn = new SqlConnection(Utility.ConnRead))
            {
                return conn.Query<string>(OS6_FPTVoucher_GetFreePaymentPrepaid, new
                {
                    @actionName = "get_evc_type",
                    @voucherCode = evcCode
                }, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
        }
        #endregion

        #region bán mới tặng gold
        public static List<lst_contract> check_contract_v3(List<string> contracts, StringBuilder sb)
        {
            var lst = new List<lst_contract>();

            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                var all_evc_type = get_all_evc_type_payment(connection);
                var lst_contracts_dont_use = contracts_dont_use(connection, contracts);
                var contracts_estimate = check_estimate(connection, lst_contracts_dont_use, sb);

                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("all_evc_type", all_evc_type));
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("lst_contracts_dont_use", lst_contracts_dont_use));
                sb.AppendLine(LoggerKafka.JoinStringToWriteLog("contracts_estimate", contracts_estimate));

                foreach (var evc_type in all_evc_type)
                {
                    var item = new lst_contract();
                    item.voucherType = evc_type.evc_type;
                    item.contracts = evc_type.check_estimate == 1 ? contracts_estimate : lst_contracts_dont_use;
                    lst.Add(item);
                }
            }

            return lst;
        }

        public static List<all_evc_type> get_all_evc_type_payment(SqlConnection conn = null)
        {
            bool isConnectionOwner = false;
            if (conn == null)
            {
                string connectionString = Utility.ConnRead;
                conn = new SqlConnection(connectionString);
                conn.Open();
                isConnectionOwner = true;
            }

            try
            {
                var lstdata = conn.Query<all_evc_type>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer, new
                {
                    action_name = "get_all_evc_type_payment"
                }, commandType: CommandType.StoredProcedure).ToList();

                if (lstdata == null)
                {
                    return new List<all_evc_type>();
                }

                return lstdata;
            }
            finally
            {
                if (isConnectionOwner && conn != null)
                {
                    conn.Close();
                    conn.Dispose();
                }
            }
        }


        public static List<string> contracts_dont_use(SqlConnection connection, List<string> contracts)
        {
            var data = connection.Query<string>(OS6_FPTVoucher_Campaign_FoxGold_NewCustomer,
            new
            {
                action_name = "contracts_dont_use",
                contracts = string.Join(",", contracts),
            }, commandType: CommandType.StoredProcedure).ToList();

            if (data == null)
            {
                return new List<string>();
            }
            return data;
        }

        public static List<string> check_contract_prepaid(SqlConnection conn, List<string> contracts)
        {
            var lst = conn.Query<Tuple<string, int>>(OS6_FPTVoucher_CheckCondToApplyVoucher,
            new
            {
                ContractList = string.Join(",",contracts)
            }, commandType: CommandType.StoredProcedure).ToList();

            if (lst == null)
            {
                return new List<string>();
            }

            return lst.Select(x=>x.Item1).ToList();
        }

        public static List<string> check_postpaid_tv(List<string> contracts)
        {
            var conStringIpTV = Utility.ConnIPTV;

            List<string> lst = new List<string>();
            using (var connection = new SqlConnection(conStringIpTV))
            {
                connection.Open();
                foreach (var contract in contracts)
                {
                    // kiêm tra hợp đồng tra sau giá trị =1, trả trước giá trị =0
                    int res = connection.Query<int>(OS6_Fgold_CheckNoPrePaid,
                        new
                        {
                            Contract = contract,
                        }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (res == 1)
                    {
                        lst.Add(contract);
                    }
                }
            }
            return lst;
        }

        public static List<string> check_estimate(SqlConnection conn, List<string> contracts, StringBuilder sb)
        {
            var data = new List<string>();
            var prepaid = check_contract_prepaid(conn, contracts);
            var postpaid = check_postpaid_tv(contracts);
            foreach (var contract in contracts)
            {
                if (prepaid.Contains(contract) || postpaid.Contains(contract))
                {
                    data.Add(contract);
                }
            }

            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("prepaid", prepaid));
            sb.AppendLine(LoggerKafka.JoinStringToWriteLog("postpaid", postpaid));

            return data;
        }
        #endregion

        public static async Task update_privateCode_expired()
        {
            try
            {
                Task private_update = new Task
                (
                    (object ob) =>
                    {
                        LoyaltyCampaignServices.update_voucher();
                    }, "TaskCheckdata"
                );
                private_update.Start();
                await private_update;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "update_privateCode_expired error", Guid.NewGuid());
            }
        }

        #region huyTSD_thu_hoi_diem_loy
        public static async Task CancelActionLoy()
        {
            try
            {
                Task cancelActionTask = Task.Run(() =>
                {
                    LoyaltyCampaignServices.CancelActionLoy();
                });

                await cancelActionTask;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex, "CancelActionLoy error", Guid.NewGuid());
            }
        }
        #endregion

        #region nextgen - khoan thu
        public static string getTokenOnlineRecept(Guid logId)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    var username = Utility.get_appsettings("nextgenapi_user");
                    var password = Utility.get_appsettings("nextgenapi_pass");
                    var request = new HttpRequestMessage
                    {
                        Method = HttpMethod.Post,
                        RequestUri = new Uri(Utility.get_appsettings("nextgenapi") + "/customer/ar/api/authen/getToken"),
                        Content = new StringContent($"{{\"user\": \"{username}\",\"password\": \"{password}\"}}")
                        {
                            Headers =
                            {
                                ContentType = new MediaTypeHeaderValue("application/json")
                            }
                        }
                    };

                    using (var response = client.SendAsync(request).Result)
                    {
                        response.EnsureSuccessStatusCode();
                        var body = response.Content.ReadAsStringAsync().Result;
                        var jsonResponse = JsonConvert.DeserializeObject<dynamic>(body);
                        
                        if (jsonResponse.status == true && jsonResponse.code == 1)
                        {
                            return jsonResponse.data.accessToken;
                        }
                        else
                        {
                            LoyaltyCommonServices.WriteToLog($"getToken Error: {jsonResponse.message}", "getToken Error", logId);
                            return null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "getToken Error",logId);
                return null;
            }
        }

        public static bool ClearVoucherOnlineRecept(string billNumber, int receiptDetailID, Guid logId)
        {
            try
            {
                var req = new
                {
                    billNumber = billNumber,
                    receiptDetailID = receiptDetailID
                };

                string data = JsonConvert.SerializeObject(req);
                string domain = Utility.get_appsettings("nextgenapi");
                string endpoint = "/customer/ar/api/v1/receivable/deleteVoucher";
                string token = getTokenOnlineRecept(logId);
                
                List<HeaderAPI> headers = new List<HeaderAPI>
                {
                    new HeaderAPI { key = "Authorization", value = "Bearer " + token }
                };

                string responseData = LoyaltyCommonServices.callapi(string.Concat(domain, endpoint), "POST", data, "application/json; charset=utf-8", headers);
                LoyaltyCommonServices.WriteToLog(responseData, "DeleteVoucher API response", logId);

                dynamic response = JsonConvert.DeserializeObject<dynamic>(responseData);
                
                if (response.status == true && response.data.resID == 1)
                {
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "DeleteVoucher Error", logId);
                return false;
            }
        }

        public static async Task ProcessRecareBillAddPoint()
        {
            try
            {
                Task recareBillAddPointTask = Task.Run(() =>
                {
                    LoyaltyCampaignServices.PointToBillProcess();
                });

                await recareBillAddPointTask;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex, "ProcessRecareBillAddPoint error", Guid.NewGuid());
            }
        }
        #endregion

    }
}