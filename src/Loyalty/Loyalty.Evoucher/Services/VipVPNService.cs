using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Voucher.APIHelper;
using Dapper;
using System.Net;
using System.Net.Http;
using System.Configuration;
using System.Text;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;
using System.IO;
using Loyalty.Evoucher.Models;
using Voucher.APIHelper.Util;
using Voucher.APIHelper.ShareModel;
using System.Xml.Linq;
using System.Threading.Tasks;

namespace Loyalty.Evoucher.Services
{
    public class VipVPNService
    {
        private const string OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN";

        public static async Task TaskCheckdata()
        {
            try
            {
                Task taskCheckdata = new Task
                (
                    (object ob) =>
                    {
                        CheckStatusVipVPN();
                    }, "TaskCheckdata"
                );
                taskCheckdata.Start();
                await taskCheckdata;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", "", DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "Process TaskCheckdata Error");
            }
        }

        public static async Task TaskActiveLoy()
        {
            try
            {
                Task taskActiveLoy = new Task
                (
                    (object ob) =>
                    {
                        ProcessActiveLoyVPN();
                    }, "TaskActiveLoy"
                );
                taskActiveLoy.Start();
                await taskActiveLoy;
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", "", DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "Process TaskActiveLoy Error");
            }
        }
        private static void CheckStatusVipVPN()
        {
            var logId = Guid.NewGuid();
            try
            {
                List<VipVPNModel> listAccount = GetDataCheck();
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(listAccount.Count), "Process CheckStatusVipVPN lstData Count ");
                if (listAccount.Count == 0)
                {
                    return;
                }

                VipVPNCheckStatusModel vcsm = new VipVPNCheckStatusModel { success = false, data = new List<DataVPNStatusModel>(), message = "" };
                List<string> lstdata = new List<string>();
                lstdata = listAccount.Select(x=>x.Account).ToList();
                foreach (var item in listAccount)
                {
                    lstdata.Add(item.Account);
                }

                string url = Utility.UrlVPN.ToString();
                string endpointStatus = Utility.vpn_endpoint_status.ToString();
                string fileKey = @"~/Keys/VPNkeys/public.key";

                string responseAPI = CallAPIVPN(url, endpointStatus, fileKey, lstdata);
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(responseAPI), "Process_camera CheckStatusVipVPN responseAPI ");
                vcsm = JsonConvert.DeserializeObject<VipVPNCheckStatusModel>(responseAPI);
                var lstDataStatus = new List<DataVPNStatusModel>();
                if (vcsm != null)
                {
                    lstDataStatus = vcsm.data.Where(x => x.activeDate != null).ToList();
                }

                
                if (vcsm.data.Count > 0)
                {
                    var activeVPN = listAccount.Join(vcsm.data, l1 => l1.Account, l2 => l2.username, (l1, l2) => new ActiveVPN { Account = l1.Account, activeDate = l2.activeDate, Code = l1.Code }).Where(a => !string.IsNullOrEmpty(a.activeDate)).ToList();
                    if (activeVPN.Count > 0)
                    {
                        using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                        {
                            connection.Open();
                            using (var transaction = connection.BeginTransaction())
                            {
                                int res = connection.Query<int>(OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN, new
                                {
                                    ActionName = "UpdateRedeemDate",
                                    xml = CreateXMLVPN(activeVPN)
                                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                                transaction.Commit();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(ex.Message), "Process CheckStatusVipVPN Error");
            }
        }

        private static void ProcessActiveLoyVPN()
        {
            List<ActiveVPNModel> listAccount = GetDataActive();
            var logId = Guid.NewGuid();
            try
            {
                foreach (var item in listAccount)
                    {
                        if (!string.IsNullOrEmpty(item.activeDate))
                        {
                            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                            {
                                connection.Open();
                                using (var transaction = connection.BeginTransaction())
                                {
                                    var req = new SendLoyalty()
                                    {
                                        mobileHiFpt = "",
                                        redeemDate = item.activeDate,
                                        shopCode = 0,
                                        shopName = "",
                                        transCode = item.transCode,
                                        voucherActualUse = 0,
                                        voucherCode = item.Code
                                    };
                                    int idPrivateCode = CallbackCommonServices.GetPrivateCodeID(item.Code);
                                    bool changLoyalty = CallbackCommonServices.NewChangeStatusLoyaltyV2(req, idPrivateCode, logId.ToString());
                                    L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                        JsonConvert.SerializeObject(changLoyalty), " ProcessActiveLoyVPN changLoyalty " + item.Code + " ");

                                    if (changLoyalty)
                                    {
                                        int isChangeStatusLoy = updateRedeemDate(connection, transaction, item.Code);
                                        L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                            JsonConvert.SerializeObject(isChangeStatusLoy), " ProcessActiveLoyVPN UpdateStatusLoy " + item.Code + " ");
                                        if (isChangeStatusLoy > 0)
                                        {
                                            transaction.Commit();
                                        }
                                    }
                                    else
                                    {
                                        //gửi mail thông báo trường hợp lỗi
                                        var reqMail = new MailModelInput
                                        {
                                            FromEmail = "<EMAIL>",
                                            Recipients = "<EMAIL> ",
                                            CarbonCopys = "",
                                            BlindCarbonCopys = "",
                                            Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                                            Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công (call Loy) VPN", voucher = item.Code, date = DateTime.Now.ToString() }),
                                            AttachFile = "",
                                            AttachUrl = ""
                                        };
                                        Email.SendMail(reqMail);
                                    }
                                }                                
                            } 
                        }                                              
                    }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                            JsonConvert.SerializeObject(ex.Message), " CheckStatusVipVPN UpdateStatusLoy error: ");
            }
        }
        private static int updateRedeemDate(SqlConnection connection, SqlTransaction transaction,string voucherCode)
        {
            return connection.Execute(OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN, new
            {
                ActionName = "UpdateStatusLoy",
                code = voucherCode
            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
        }
        private static List<VipVPNModel> GetDataCheck()
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<VipVPNModel>(OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN, new
                {
                    ActionName = "GetVPNCodeCheck",
                }, commandType: CommandType.StoredProcedure).ToList();
            }
        }

        private static List<ActiveVPNModel> GetDataActive()
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<ActiveVPNModel>(OS6_FPTVoucher_LoyaltyPromotionEvent_VipVPN, new
                {
                    ActionName = "getVPNactive",
                }, commandType: CommandType.StoredProcedure).ToList();
            }
        }

        private static XElement CreateXMLVPN(List<ActiveVPN> lst)
        {
            var xmlString = new XElement("N",
            from item in lst
            select new XElement("I",
                           new XElement("code", item.Code),
                           new XElement("activeDate", item.activeDate)
                       ));
            return xmlString;
        }
        private static string CallAPIVPN(string _url, string endpoint, string pathFile, List<string> userlist)
        {
            try
            {
                var proxy = new WebProxy
                {
                    Address = new Uri(Utility.http_client_proxy)
                    //Address = new Uri("http://isc-proxy.hcm.fpt.vn:80")
                };

                var httpClientHandler = new HttpClientHandler
                {
                    Proxy = proxy,
                };

                var multiForm = new MultipartFormDataContent();
                foreach (var user in userlist)
                {
                    multiForm.Add(new StringContent(user), "userList");
                }

                FileStream fs = File.OpenRead(System.Web.Hosting.HostingEnvironment.MapPath(pathFile));
                multiForm.Add(new StreamContent(fs), "publicKey", Path.GetFileName(System.Web.Hosting.HostingEnvironment.MapPath(pathFile)));
                using (var client = new HttpClient(handler: httpClientHandler, disposeHandler: true))
                {
                    client.Timeout = TimeSpan.FromSeconds(5);

                    client.DefaultRequestHeaders.Add("token", Utility.vpn_token.ToString());

                    var response = client.PostAsync(_url + endpoint, multiForm).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    return r;
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, JsonConvert.SerializeObject(ex.Message), "CallAPIVPN Error: ");
                return null;
            }
        }


    }
}