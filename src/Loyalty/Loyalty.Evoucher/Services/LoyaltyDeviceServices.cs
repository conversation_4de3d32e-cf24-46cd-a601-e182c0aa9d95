using Loyalty.Evoucher.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using Voucher.APIHelper;
using Voucher.APIHelper.Log4net;
using Dapper;
using System.Net.Http;
using System.Xml.Linq;
using System.Threading.Tasks;
using Voucher.APIHelper.ShareModel;
using Voucher.APIHelper.Util;

namespace Loyalty.Evoucher.Services
{
    public class LoyaltyDeviceServices
    {
        private const string OS6_FPTVoucher_LoyaltyDevicePromotionEvent = "PowerInside.dbo.OS6_FPTVoucher_LoyaltyDevicePromotionEvent";
        private const string OS6_FPTVoucher_CalcAccumulatePointWaranty="PowerInside.dbo.OS6_FPTVoucher_CalcAccumulatePointWaranty";
        private const string OS6_FPTVoucher_AddBillTotalDebtSendLoy = "PowerInside.dbo.OS6_FPTVoucher_AddBillTotalDebtSendLoy";
        private static ResponseModel<DeviceLoyaltyModel> ReturnData(DeviceLoyaltyModel data, int result, string error) 
        {
            return new ResponseModel<DeviceLoyaltyModel> {data=data, result=result, error=error };
        }
        public static ResponseModel<DeviceLoyaltyModel> GetInforVoucher(DeviceVoucherModelInput input, Guid LogId)
        {
            var _reponse = new DeviceLoyaltyModel { Devices = new List<DevicePrice>()};
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    connection.Open();
                    int CheckObjUseVC = connection.Query<int>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                        new
                        {
                            actionName = "CheckObjUseVC",
                            objId = input.objId
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();
                    if (CheckObjUseVC.Equals(1))
                    {
                        return ReturnData(null, 0, "KH đang có evoucher khác chưa sử dụng hoàn tất");
                    }
                    _reponse = connection.Query<DeviceLoyaltyModel>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                        new
                        {
                            actionName = "GetInforDeviceLoyaltyPE",
                            code = input.voucherCode
                        },
                        commandType: CommandType.StoredProcedure).FirstOrDefault();
                    LoyaltyCommonServices.WriteToLog(_reponse, "GetDeviceVoucherLoyalty _reponse ", LogId);
                    if (_reponse == null)
                    {                        
                        return ReturnData(null, 0, "Không tìm thấy thông tin");
                    }
                    //lấy location id là item1, item2 là trạng thái tham gia Loy, 0: chưa tham gia, 1 tham gia rồi, nếu là 0 thì call lại API bên Loy để check
                    Tuple<int, int> tplLocation = connection.Query<Tuple<int, int>>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                    new
                    {
                        actionName = "GetLocationID",
                        objId = input.objId,
                    }, commandType: CommandType.StoredProcedure).FirstOrDefault();
                    LoyaltyCommonServices.WriteToLog(tplLocation, "GetDeviceVoucherLoyalty tplLocation ", LogId);

                    if (tplLocation.Item2.Equals(0))
                    {
                        // check api Loy để kiểm tra
                        bool isObjLoyaltyv = LoyaltyCommonServices.CheckObjLoyalty(input.objId, LogId);
                        LoyaltyCommonServices.WriteToLog(isObjLoyaltyv, "GetDeviceVoucherLoyalty isObjLoyaltyv ", LogId);
                        if (!isObjLoyaltyv)
                        {
                            return ReturnData(null, 0, "Khách chưa tham gia Loyalty");
                        }
                    }
                    // lấy danh sách thiết bị
                    List<Device> Devices = connection.Query<Device>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent, new
                    {
                        actionName = "GetDeviceLoyaltyPE",
                        id = _reponse.privateCodeID                        
                    }, commandType: CommandType.StoredProcedure).ToList();

                    LoyaltyCommonServices.WriteToLog(Devices, "GetDeviceVoucherLoyalty Devices", LogId);

                    if (Devices.Count == 0)
                    {
                        return ReturnData(null, 0, "Không tìm thấy thông tin");
                    }

                    string codeids = string.Join(",", Devices.Select(c => c.DeviceID));
                    List<DeviceInfor> lstData = new List<DeviceInfor>();
                    //foreach (var device in Devices)
                    //{
                    //    List<Device> lst = new List<Device>();
                    //    lst.Add(device);
                    //    var req = new ParapioraInput
                    //    {
                    //        LocationID = tplLocation.Item1,
                    //        CodeID = device.DeviceID.ToString(),
                    //        ReturnStatus = device.ReturnStatus,
                    //        CustomerStatus = device.CustomerStatus
                    //    };
                    //    List<DeviceInfor> lstDevice = GetEFcodeWithPrice(req, lst, LogId);
                    //    if (lstDevice.Count > 0 && lstDevice != null)
                    //    {
                    //        lstData.AddRange(lstDevice);
                    //    }
                    //}
                    Devices.AsParallel().ForAll(device =>
                    {
                        List<Device> lst = new List<Device>();
                        lst.Add(device);
                        var req = new ParapioraInput
                        {
                            LocationID = tplLocation.Item1,
                            CodeID = device.DeviceID.ToString(),
                            ReturnStatus = device.ReturnStatus,
                            CustomerStatus = device.CustomerStatus
                        };
                        List<DeviceInfor> lstDevice = GetEFcodeWithPrice(req, lst, LogId);
                        if (lstDevice.Count > 0 && lstDevice != null)
                        {
                            lstData.AddRange(lstDevice);
                        }
                    });

                    LoyaltyCommonServices.WriteToLog(Devices, "GetDeviceVoucherLoyalty lstDevice", LogId);
                    if (lstData.Count == 0)
                    {
                        return ReturnData(null, 0, "Không tìm thấy thiết bị phù hợp");
                    }
                    _reponse.Devices = lstData.Select(c => new DevicePrice { DeviceID = c.CodeID, Description = c.Name, Price = c.Price, Status = c.Status }).ToList();                    
                }
                return ReturnData(_reponse,1,"");
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "GetDeviceVoucherLoyalty Error ", LogId);
                return ReturnData(null, -1, ex.Message);                
            }            
        }
        public static List<DeviceInfor> GetEFcodeWithPrice(ParapioraInput input, List<Device> devices, Guid LogId)
        {
            List<DeviceInfor> res = new List<DeviceInfor>();
            string url = string.Concat(Utility.parapiora_fpt_api.ToString(), "/api/MobiSale/Get_EFCodePriceList_MobiSale");
            //string url = "http://inside-api-stag.fpt.net/par-transition/transfer/parapiora/api/MobiSale/Get_EFCodePriceList_MobiSale";
            string sJsonData = JsonConvert.SerializeObject(input);

            var httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
            httpWebRequest.ContentType = "application/json";
            httpWebRequest.Method = "POST";
            try
            {
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(sJsonData);
                }
                var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    ParapioraOutput result = new ParapioraOutput { data = new List<Parapiora>() };
                    result = JsonConvert.DeserializeObject<ParapioraOutput>(streamReader.ReadToEnd());

                    

                    foreach (var item in devices)
                    {
                        var lstData = result.data.Where(c => c.Deployment == item.Deployment && c.EFStatus == item.Status
                                                            && c.CustomerStatus == item.CustomerStatus && c.CodeID == item.DeviceID
                                                            && item.PromotionStatus == c.PromotionStatus && item.ReturnStatus == c.ReturnStatus).ToList();                       

                        List<DeviceInfor> lstdevice = lstData.Select(a => new DeviceInfor { CodeID = a.CodeID, Price = a.Price, Name = a.EquipmentName, Status = item.Status }).ToList();
                        LoyaltyCommonServices.WriteToLog(lstdevice, "GetDeviceVoucherLoyalty GetEFcodeWithPrice lstdevice ", LogId);
                        res.AddRange(lstdevice);
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "GetDeviceVoucherLoyalty GetEFcodeWithPrice Error ", LogId);
                return null;
            }
            return res;
        }

        public static bool CheckRedeemVoucher(string voucherCode, int objId)
        {
            int checkRedeem = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                connection.Open();
                checkRedeem = connection.Query<int>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                new
                {
                    actionName = "CheckRedeemVC",
                    objId = objId,
                    code = voucherCode
                },
                commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return (checkRedeem > 0);
        }

        public static int ExcuteStoreActionToolBH(RedeemToolInput input, string action, Guid LogId, string description)
        {
            int res = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                        
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        res = connection.Query<int>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                        new
                        {
                            actionName = action,
                            objId = input.objId,
                            code = input.voucherCode,
                            description = description,
                            billnumber = input.billNumber
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        LoyaltyCommonServices.WriteToLog(res, "RedeemVoucherDeviceLoyalty " + action + " ", LogId);
                        if (res > 0)
                        {
                            transaction.Commit();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "ExcuteStoreAction Error " + action + " ", LogId);
            }
            return res;
        }

        public static int CalcAccumulatePointWaranty(SqlConnection connection, SqlTransaction transaction, int objid, string billNumber)
        {
            return connection.Execute(OS6_FPTVoucher_CalcAccumulatePointWaranty,
                new
                {
                    ObjID = objid,
                    BillNumber = billNumber,
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);            
        }
        public static InforUseVoucher UpdateLoyaltyVoucherToolBH(SqlConnection connection, SqlTransaction transaction, RedeemToolInput input, Guid LogId)
        {
            var res = new InforUseVoucher();
            try
            {
                res = connection.Query<InforUseVoucher>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                new
                {
                    actionName = "UpdateStatusLoyalty",
                    objId = input.objId,
                    code = input.voucherCode,
                    billnumber= input.billNumber
                }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                LoyaltyCommonServices.WriteToLog(res, "UpdateLoyaltyVoucher ", LogId);
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "UpdateLoyaltyVoucher Error ", LogId);
                return null;
            }
            return res;
        }

        public static bool CheckStatusSupport(CheckStatusInput input, Guid LogId)//kiểm tra trạng thái thi công vật tư
        {
            var res = new CheckStatusOutput();
            try
            {

                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(Utility.parapiora_fpt_api);
                    var data = new System.Net.Http.StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
                    var response =  client.PostAsync("/api/v1/RPMaintaince/check-status-support ", data).Result;
                    string r = response.Content.ReadAsStringAsync().Result;
                    LoyaltyCommonServices.WriteToLog(r, "CheckStatusSupport ", LogId);
                    res = JsonConvert.DeserializeObject<CheckStatusOutput>(r);
                }

                if (res.data.status == 1 && res.statusCode == 200)
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "CheckStatusSupport error ", LogId);
                return false;
            }

            return false;
        }

        public static bool CheckRealValueVoucher(List<RealUseVoucher> values)
        {
            bool res = true;
            foreach (var value in values)
            {
                if (value.amount < 0)
                {
                    res = false;
                    break;
                }
            }
            return res;
        }

        public static XElement CreateXMLServiceCode(List<RealUseVoucher> lst, Guid logId)
        {
            var xmlString = new XElement("N",
            from item in lst
            select new XElement("I",
                           new XElement("serviceCode", item.serviceCode),
                           new XElement("amount", item.amount)
                       ));
            LoyaltyCommonServices.WriteToLog(xmlString, "CreateXMLServiceCode ", logId);
            return xmlString;
        }

        public static int KeepLoyaltyVoucherTLS(RedeemTLSInput input, Guid LogId)
        {
            int res = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        res = connection.Query<int>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                        new
                        {
                            actionName = "RedeemDeviceLoyaltyTLS",
                            objId = input.objId,
                            code = input.voucherCode,
                            xml = CreateXMLServiceCode(input.realUse, LogId)
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        LoyaltyCommonServices.WriteToLog(res, "KeepLoyaltyVoucherTLS ", LogId);
                        if (res.Equals(1))
                        {
                            transaction.Commit();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "KeepLoyaltyVoucherTLS Error ", LogId);
                res= 0;
            }
            return res;
        }

        public static bool UpdateLoyaltyVoucherTLS(string voucherCode, int objId)
        {
            var res = new InforUseVoucher();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {

                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        res = connection.Query<InforUseVoucher>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                        new
                        {
                            actionName = "UpdateStatusLoyaltyTLS",
                            objId = objId,
                            code = voucherCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        transaction.Commit();
                    }
                }
            }
            catch (Exception ex)
            {
                return false;
            }
            return true;
        }

        public static InforUseVoucher getInforUseVoucher(string voucherCode)
        {
            var res = new InforUseVoucher();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {

                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        res = connection.Query<InforUseVoucher>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                        new
                        {
                            actionName = "getInforUseVoucher",
                            code = voucherCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        if(res != null)
                        {
                            transaction.Commit();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return null;
            }
            return res;
        }

        public static int ExcuteStoreActionTLS(RedeemTLSInput input, string action, Guid LogId)
        {
            int res = 0;
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {

                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        res = connection.Query<int>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                        new
                        {
                            actionName = action,
                            objId = input.objId,
                            code = input.voucherCode,
                            orderCode=input.orderCode
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                        LoyaltyCommonServices.WriteToLog(res, "RedeemVoucherDeviceLoyalty " + action + " ", LogId);
                        if (res > 0)
                        {
                            transaction.Commit();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "ExcuteStoreAction Error " + action + " ", LogId);
            }
            return res;
        }

        public static async Task MBNdeviceVoucher()
        {
            try
            {
                Task taskMBNdeviceVoucher = new Task
                (
                    (object ob) =>
                    {
                        UpdateStatusVoucherMBN();
                    }, "TaskCheckdata"
                );
                taskMBNdeviceVoucher.Start();
                await taskMBNdeviceVoucher;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "MBNdeviceVoucher error", Guid.NewGuid());
            }
        }

        private static void UpdateStatusVoucherMBN()
        {
            Guid logId1 = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog("", " Start ProcessUpdateStatusVoucherMBN ", logId1);
            try
            {
                List<ObjVoucherMBN> data = GetdataMBNjob();
                if (data.Count == 0)
                {
                    return;
                }
                foreach (var item in data)
                {
                    Guid logId = Guid.NewGuid();
                    LoyaltyCommonServices.WriteToLog(item, "ProcessUpdateStatusVoucherMBN ", logId);
                    try
                    {
                        var input = new CheckStatusInput 
                        {
                            ObjID = item.ObjID,
                            OrderCode = item.OrderCode,
                            OrderType = item.OrderType,
                        };
                        //bool status = CheckStatusSupport(input, logId);
                        //LoyaltyCommonServices.WriteToLog(status, "ProcessUpdateStatusVoucherMBN status ", logId);
                        if (true)
                        {
                            int billTotalDebt = LoyaltyDeviceServices.getBillTotalDebt(item.BillNumber);
                            LoyaltyCommonServices.WriteToLog(billTotalDebt, item.BillNumber + " - billTotalDebt: ", logId);
                            var request = new SendLoyalty()
                            {
                                contractNo = item.Contract,
                                billNumber = item.BillNumber,
                                mobileHiFpt = item.Phone,
                                redeemDate = item.RedeemDate,
                                transCode = item.TransCode,
                                voucherCode = item.Code,
                                billTotalDebt = billTotalDebt
                            };
                            LoyaltyCommonServices.WriteToLog(request, "start to updateLoy request ", logId);
                            bool bSend = CallbackCommonServices.NewChangeStatusLoyaltyV2(request, item.ID, logId.ToString());
                            LoyaltyCommonServices.WriteToLog(bSend, "NewChangeStatusLoyalty bSend ", logId);
                            if (!bSend)
                            {
                                #region gửi mail thông báo
                                var MailContent = new MailModelInput
                                {
                                    FromEmail = "<EMAIL>",
                                    Recipients = "<EMAIL> ",
                                    Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                                    Body = null,
                                    CarbonCopys = "",
                                    BlindCarbonCopys = "",
                                    AttachFile = "",
                                    AttachUrl = ""
                                };
                                MailContent.Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công Change Status Loy = false", voucher = item.Code, date = DateTime.Now.ToString() });
                                try
                                {
                                    Email.SendMail(MailContent);
                                }
                                catch (Exception ex)
                                { LoyaltyCommonServices.WriteToLog(ex.Message, " RedeemVoucherDeviceLoyalty SendMail error ", logId); }
                                #endregion
                            }
                            else
                            {
                                int isUpdate = updateVoucherMBN(item.ID);
                                LoyaltyCommonServices.WriteToLog(isUpdate, "ProcessUpdateStatusVoucherMBN isUpdate ", logId);
                            }
                        }
                    }
                    catch (Exception ex)
                    {                        
                        LoyaltyCommonServices.WriteToLog(ex.Message, "ProcessUpdateStatusVoucherMBN Error ", logId);
                    }
                }
                return;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "ProcessUpdateStatusVoucherMBN Error ", logId1);
                return;
            }
        }
        private static int updateVoucherMBN(int idVoucher)
        {
            using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
            {
                return connection.Execute(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                new
                {
                    actionName = "UpdateStatusMBN",
                    @id = idVoucher
                },
                commandType: CommandType.StoredProcedure);
            }
        }
        private static List<ObjVoucherMBN> GetdataMBNjob()
        {
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                return connection.Query<ObjVoucherMBN>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                new
                {
                    actionName = "GetdataMBNjob"
                },
                commandType: CommandType.StoredProcedure).ToList();
            }
        }

        public static List<InforUseVoucher> GetDataActiveVoucherDevicesJob()
        {
            List<InforUseVoucher> data = new List<InforUseVoucher>();
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnRead()))
                {
                    data = connection.Query<InforUseVoucher>(OS6_FPTVoucher_LoyaltyDevicePromotionEvent,
                    new
                    {
                        actionName = "GetDataActiveVoucherDevicesJob"
                    },
                    commandType: CommandType.StoredProcedure).ToList();
                }
            }
            catch(Exception e)
            {
                return new List<InforUseVoucher>();
            }
            return data;
        }

        public static void ActiveVoucherDevices()
        {
            Guid LogIdss = Guid.NewGuid();
            LoyaltyCommonServices.WriteToLog("", "ProcessTLS start ", LogIdss);
            List<InforUseVoucher> datas = GetDataActiveVoucherDevicesJob();
            LoyaltyCommonServices.WriteToLog(datas, "ProcessTLS GetDataActiveVoucherDevicesJob ", LogIdss);
            foreach (var data in datas)
            {
                #region xác nhận sử dụng voucher
                Guid LogId = Guid.NewGuid();
                bool isRedeem = CheckRedeemVoucher(data.VoucherCode, data.ObjID);
                LoyaltyCommonServices.WriteToLog(isRedeem, "RedeemVoucherDeviceLoyaltyTeleSale CheckRedeemVoucher ", LogId);

                if (!isRedeem)
                {
                    continue;
                }
                //var iuv = new InforUseVoucher();
                //iuv = getInforUseVoucher(data.VoucherCode);
                //LoyaltyCommonServices.WriteToLog(iuv, "RedeemVoucherDeviceLoyaltyTeleSale iuv ", LogId);
                if (data.PrivateCodeID > 0)
                {
                    var MailContent = new MailModelInput
                    {
                        FromEmail = "<EMAIL>",
                        Recipients = "<EMAIL> ",
                        Subject = "[Thông báo] kết quả cập nhậ trạng thái voucher",
                        Body = null,
                        CarbonCopys = "",
                        BlindCarbonCopys = "",
                        AttachFile = "",
                        AttachUrl = ""
                    };
                    int billTotalDebt = getBillTotalDebt(data.BillNumber);
                    LoyaltyCommonServices.WriteToLog(billTotalDebt, data.BillNumber + " - billTotalDebt: ", LogId);
                    var request = new SendLoyalty()
                    {
                        mobileHiFpt = data.Phone,
                        redeemDate = data.RedeemDate,
                        transCode = data.TransCode,
                        billNumber = data.BillNumber,
                        contractNo = data.Contract,
                        voucherCode = data.VoucherCode,
                        billTotalDebt = billTotalDebt
                    };
                    LoyaltyCommonServices.WriteToLog(request, "start to updateLoy req ", LogId);
                    bool bSend = CallbackCommonServices.NewChangeStatusLoyaltyV2(request, data.PrivateCodeID, LogId.ToString());
                    LoyaltyCommonServices.WriteToLog(bSend, "NewChangeStatusLoyalty res ", LogId);
                    if (!bSend)
                    {
                        MailContent.Body = JsonConvert.SerializeObject(new { content = "Cập nhật trạng thái EVC không thành công Change Status Loy = false", voucher = data.VoucherCode, date = DateTime.Now.ToString() });
                        try
                        {
                            Email.SendMail(MailContent);
                        }
                        catch (Exception ex)
                        { LoyaltyCommonServices.WriteToLog(ex.Message, " RedeemVoucherDeviceLoyalty SendMail error ", LogId); }
                    }
                    else
                    {
                        bool isUpdatePrivateCode = UpdateLoyaltyVoucherTLS(data.VoucherCode, data.ObjID);
                        LoyaltyCommonServices.WriteToLog(isUpdatePrivateCode, " RedeemVoucherDeviceLoyalty isUpdatePrivateCode ", LogId);
                    }
                }
                #endregion
            }
        }
        public static async Task processActiveVoucherDevices()
        {
            try
            {
                Task taskActiveVoucherDevices = new Task
                (
                    (object ob) =>
                    {
                        ActiveVoucherDevices();
                    }, "taskActiveVoucherDevices"
                );
                taskActiveVoucherDevices.Start();
                await taskActiveVoucherDevices;
            }
            catch (Exception ex)
            {
                LoyaltyCommonServices.WriteToLog(ex.Message, "ActiveVoucherDevices error", Guid.NewGuid());
            }
        }

        #region add billTotalDebt
        public static int getBillTotalDebt(string billNumber)
        {
            int total = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                total = connection.Query<int>(OS6_FPTVoucher_AddBillTotalDebtSendLoy,
                new
                {
                    actionName = "getBillTotalDebt",
                    billNumber = billNumber
                },
                commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return total;
        }
        #endregion
    }
}