using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Foxpay.Evoucher.Models
{
    public class ApplyPayment : TypeHiFPT
    {
        public string order_id { set; get; }
        public string bill_number { set; get; }
        public string checksum { set; get; }
    }
    public class ApplyPaymentHiFPT
    {
        public string order_id { set; get; }
        public string bill_number { set; get; }
        public string checksum { set; get; }
        public int amount { set; get; }
        public string contract { set; get; }
        public string description { set; get; }
        public int date_created { set; get; }
    }
    public class ApplyPaymentHiFPTInput
    {
        public List<ApplyPaymentHiFPT> ListApplyPayment { get; set; }
        public int typeSource { set; get; }
    }
    public class TypeHiFPT
    {
        public int typeSource { set; get; }
        public string accPayment { set; get; }
    }
    public class ApplyPaymentInput : ApplyPayment
    {       
        public int amount { set; get; }
        public string contract { set; get; }
        public string description { set; get; }
        public int date_created { set; get; }
    }

    public class ApplyPaymentOutput
    {
        public int statusCode { set; get; }
        public string description { set; get; }
    }
    public class BillInfo
    {
        public int TotalFee { set; get; }
        public int TotalPay { set; get; }
        public int TotalDebt { set; get; }
    }

    public class DataBillPayment
    {
        public int Id { set; get; }
        public string BillNumber { set; get; }
        public int Amount { set; get; }
    }
    public class DataBillPaymentHifpt
    {
        public int Id { set; get; }
        public string BillNumber { set; get; }
        public int Amount { set; get; }
        public string AccPayment { set; get; }
    }
}