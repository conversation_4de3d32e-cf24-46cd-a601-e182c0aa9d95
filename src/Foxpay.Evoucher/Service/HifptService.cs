using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Voucher.APIHelper.Util;
using Newtonsoft.Json;
using Voucher.APIHelper.Log4net;
using System.Data;
using System.Configuration;
using Foxpay.Evoucher.Models;
using System.Security.Cryptography;
using System.Text;
using System.Xml.Linq;

namespace Foxpay.Evoucher.Service
{
    public class HifptService
    {
        public const string OS6_FPTVoucher_ApplyPayment_Foxpay = "PowerInside.dbo.OS6_FPTVoucher_ApplyPayment_Foxpay";
        public static ResponseModel<ApplyPaymentOutput> ApplypaymentHifpt(ApplyPaymentHiFPTInput inputs)
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(inputs), "Applypayment req: ");

            // hien tai chi cho hifpt xai function nay
            if (inputs.typeSource != 1 && inputs.typeSource != 2)
            {
                return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 500, description = "Lỗi không xác định" }, result = 0, error = "Thao tác không thành công" };
            }
            try
            {
                string key = "";
                switch (inputs.typeSource)
                {
                    case 0:
                        {
                            key = GetKey("keyFoxPay");
                            break;
                        }
                    case 1:
                        {
                            key = GetKey("keyHiFPT");
                            break;
                        }
                    case 2:
                        {
                            key = GetKey("keyHiFPT");
                            break;
                        }
                }
                int flag = 0;
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        foreach (var input in inputs.ListApplyPayment)
                        {
                            #region code
                            string datainput = input.order_id + input.bill_number + input.amount.ToString() + input.contract + input.description + input.date_created.ToString() + key;
                            string hashData = GetHash(datainput).ToLower();
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                    JsonConvert.SerializeObject(hashData), "Applypayment hashData: ");
                            if (input.checksum != hashData)
                            {
                                return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 500, description = "Lỗi không xác định" }, result = 0, error = "Data không hợp lệ" };
                            }
                            //int billres = connection.Query<int>(OS6_FPTVoucher_ApplyPayment_Foxpay, new
                            //{
                            //    actionName = "BillExist",
                            //    order_id = input.order_id
                            //}, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                            //if (billres.Equals(1))
                            //{
                            //    return new ResponseModel<ApplyPaymentOutput> { data = null, result = 1, error = "order_id không hợp lệ" };
                            //}
                            BillInfo bi = connection.Query<BillInfo>(OS6_FPTVoucher_ApplyPayment_Foxpay, new
                            {
                                actionName = "CheckInforBill",
                                bill_number = input.bill_number
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(bi), "Applypayment bi: ");
                            if (bi.TotalFee.Equals(-1))
                            {
                                return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 3, description = "Không tìm thấy hóa đơn" }, result = 1, error = null };
                            }
                            if (bi.TotalDebt.Equals(0))
                            {
                                return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 2, description = "Hóa đơn đã được gạch nợ" }, result = 1, error = null };
                            }
                            if (bi.TotalDebt < input.amount)
                            {
                                return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 1, description = "Số tiền không hợp lệ" }, result = 1, error = null };
                            }

                            int addRequest = connection.Execute(OS6_FPTVoucher_ApplyPayment_Foxpay, new
                            {
                                actionName = "AddFoxPay",
                                order_id = input.order_id,
                                bill_number = input.bill_number,
                                amount = input.amount,
                                contract = input.contract,
                                description = input.description,
                                date_created = input.date_created,
                                checksum = input.checksum,
                                TypeSource = inputs.typeSource
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                                JsonConvert.SerializeObject(addRequest), "Applypayment addRequest: ");
                            if (addRequest > 0)
                            {
                                flag++;
                            }
                            else
                            {
                                return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 500, description = "Lỗi không xác định" }, result = 0, error = "Thao tác không thành công" };
                            }
                            #endregion
                        }
                        if (flag == inputs.ListApplyPayment.Count)
                        {
                            transaction.Commit();
                            return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 200, description = "Thành công" }, result = 1, error = null };
                        }
                        else
                        {
                            return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 500, description = "Lỗi không xác định" }, result = 0, error = "Thao tác không thành công" };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "Applypayment Error: ");
                return new ResponseModel<ApplyPaymentOutput> { data = new ApplyPaymentOutput { statusCode = 500, description = "Lỗi không xác định" }, result = 0, error = ex.Message };
            }
        }
        public static ResponseModel<bool> ConfirmApplypaymentHifpt(List<ApplyPayment> input)
        {
            var logId = Guid.NewGuid();
            L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                    JsonConvert.SerializeObject(input), "ConfirmApplypayment req: ");
            try
            {
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        List<DataBillPaymentHifpt> lstData = connection.Query<DataBillPaymentHifpt>(OS6_FPTVoucher_ApplyPayment_Foxpay, new
                        {
                            actionName = "ValiRequest",
                            xml = CreateXML(input)
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                        if (lstData == null || lstData.Count() != input.Count())
                        {
                            return new ResponseModel<bool> { data = false, result = 0, error = "dữ liệu không hợp lệ" };
                        }
                        int i = 0;
                        foreach (var data in lstData)
                        {
                            long idpayment = connection.Query<long>(OS6_FPTVoucher_ApplyPayment_Foxpay, new
                            {
                                actionName = "AddPayment",
                                bill_number = data.BillNumber,
                                amount = data.Amount,
                                applyFoxpayid = data.Id,
                                accPayment = data.AccPayment
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                            int updateDataFoxpay = connection.Execute(OS6_FPTVoucher_ApplyPayment_Foxpay, new
                            {
                                actionName = "UpdateFoxpay",
                                PaymentId = idpayment,
                                Id = data.Id
                            }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure);
                            if (idpayment > 0 && updateDataFoxpay > 0)
                            {
                                i++;
                            }
                        }
                        if (i == lstData.Count())
                        {
                            transaction.Commit();
                            return new ResponseModel<bool> { data = true, result = 1, error = "" };
                        }
                        else
                        {
                            return new ResponseModel<bool> { data = false, result = 0, error = "không thể thực hiện đc thao tác" };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.INFO, string.Format("[{0} {1:HH:mm:ss}] ", logId, DateTime.Now) + " " +
                            JsonConvert.SerializeObject(ex.Message), "Applypayment Error: ");
                return new ResponseModel<bool> { data = false, result = 0, error = ex.Message };
            }
        }
        public static string GetHash(string input)
        {
            return string.Join("", (new SHA1Managed().ComputeHash(Encoding.UTF8.GetBytes(input))).Select(x => x.ToString("X2")).ToArray());
        }
        private static XElement CreateXML(List<ApplyPayment> lst)
        {
            var xmlString = new XElement("N",
            from item in lst
            select new XElement("I",
                           new XElement("BillNumber", item.bill_number),
                           new XElement("OrderId", item.order_id),
                           new XElement("Checksum", item.checksum),
                           new XElement("AccPayment", item.accPayment)
                       ));
            return xmlString;
        }
        private static string GetKey(string key)
        {
            string value = "";
            using(var conn = new SqlConnection(SqlHelper.ConnRead()))
            {
                value = conn.Query<string>(OS6_FPTVoucher_ApplyPayment_Foxpay, new
                {
                    actionName = "GetKey",
                    key = key
                }, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
            }
            return value;
        }
    }
}