using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace GetHistoryInviteSuccess.Model
{
    public class HistoryInviteSuccessResponse
    {
        public UserInvited UserInvite { get; set; }
        public List<UserInvited> UserInvied { get; set; }
    }

    public class UserInvited
    {
        public string ContractInvited { get; set; }
        public string FullNameInvited { get; set; }

        public List<PromotionEventNode> PromotionEvent { get; set; }
        public DateTime RegisterDate { get; set; }
        public Boolean RegisterStatus { get; set; }
    }

    public class PromotionEventNode
    {
        public string PromotionEvent { get; set; }
        public string Value { get; set; }
        public int ServiceCode { get; set; }

        public string Description { get; set; }
        [JsonIgnore]
        public int Dismonth { get; set; }
    }
}