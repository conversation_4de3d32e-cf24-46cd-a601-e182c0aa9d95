using GetHistoryInviteSuccess.Constant;
using GetHistoryInviteSuccess.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;
using System.Data.SqlClient;
using Voucher.APIHelper.Log4net;
using Voucher.APIHelper.Util;
using System.Data;
using Newtonsoft.Json;

namespace GetHistoryInviteSuccess.Controllers
{
    public class GetHistoryInviteSuccessController : ApiController
    {
        const string sp= "PowerInside.dbo.OS6_ReferralProgram_InviteHistory_Common";
        [Route("API/ReferralProgram-Member/GetHistoryInviteSuccess")]
        public ResponseModel<HistoryInviteSuccessResponse> post(HistoryInviteSuccessRequest input)
        {
            HistoryInviteSuccessResponse response = new HistoryInviteSuccessResponse();
            try
            {                
                response.UserInvite = GetInfoUserInvite(input.ObjectID);
                response.UserInvied = GetInfoUserInvited(input.ObjectID);
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, ex.Message.ToString(), "GetHistoryInviteSuccess: ");
                return new ResponseModel<HistoryInviteSuccessResponse>() { data = null, error = ex.Message, result = 0 }; 
            }            
            return new ResponseModel<HistoryInviteSuccessResponse>() { data = response, error = null, result = 1 };
        }
        private UserInvited GetInfoUserInvite(int ObjID)
        {
            L.Mes(Level.INFO, "Start to get GetInfoUserInvite obj = " + ObjID.ToString(), "GetHistoryInviteSuccess : ");
            UserInvited instance = new UserInvited();

            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    ContractInfo temp = connection.Query<ContractInfo>(NameStore.InviteSucces, new
                    {
                        ActionName = "GetObjectInvite",
                        ObjIDInvite = ObjID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    L.Mes(Level.INFO, JsonConvert.SerializeObject(temp), "GetInfoUserInvite: ");

                    if (temp == null) return instance;

                    instance.FullNameInvited = temp.FullName;
                    instance.ContractInvited = temp.Contract;
                    instance.RegisterDate = temp.RegDate;
                    instance.RegisterStatus = temp.RegStatus;
                    instance.PromotionEvent = GetItemNode(GetPromotionEventByCode(temp.GenenalCodeInvite));
                }
            }
            return instance;
        }
        private List<UserInvited> GetInfoUserInvited(int ObjID)
        {
            L.Mes(Level.INFO, "Start to get GetInfoUserInvited obj = " + ObjID.ToString(), "GetHistoryInviteSuccess : ");
            List<UserInvited> res =  new List<UserInvited>();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    List<ContractInfo> instance = connection.Query<ContractInfo>(NameStore.InviteSucces, new
                    {
                        ActionName = "GetObjectInvited",
                        ObjIDInvite = ObjID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).ToList();
                    L.Mes(Level.INFO, JsonConvert.SerializeObject(instance), "GetInfoUserInvited: ");
                    if (instance.Count() == 0 || instance == null) return res;

                    for (int i = 0; i < instance.Count(); i++)
                    {
                        var p = GetPromotionEventByCode(instance[i].GenenalCodeInvite);

                        res.Add(new UserInvited() { 
                            ContractInvited = instance[i].Contract,
                            FullNameInvited = instance[i].FullName,
                            RegisterDate = instance[i].RegDate,
                            RegisterStatus = instance[i].RegStatus,
                            PromotionEvent = GetItemNode(p)
                        });
                    }
                }
            }
            return res;
        }
        private PromotionEventModel GetPromotionEventByCode(string Code)
        {
            PromotionEventModel instance = new PromotionEventModel();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    instance = connection.Query<PromotionEventModel>(NameStore.APIMBS, new
                    {
                        ActionName = "GetPromotionEventByEventCode",
                        EventCode = Code,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();
                }
            }
            return instance;
        }
        private List<PromotionEventNode> GetItemNode(PromotionEventModel input)
        {
            List<PromotionEventNode> instance = new List<PromotionEventNode>();

            //Discount TT NET
            if (input.ID1 != 0)
                instance.Add(new PromotionEventNode()
                {
                    PromotionEvent = "DiscountAmountNET",
                    ServiceCode = input.ServiceCode1,
                    Value = input.Value1.ToString(),
                    Description = BuildContentString(TemplateString.DiscountAmountNET, new string[] { input.Value1.ToString("#,##0") }),
                    Dismonth = 0
                });

            if(input.ID7 != 0)
                instance.Add(new PromotionEventNode(){
                    PromotionEvent = "DiscountAmountTV",
                    ServiceCode = input.ServiceCode7,
                    Value = input.Value7.ToString(),
                    Description = BuildContentString(TemplateString.DiscountAmountTV, new string[] { input.Value7.ToString() }),
                    Dismonth = 0
                });



            // X đồng Y tháng NET
            if (input.ID2 != 0)
                instance.Add(new PromotionEventNode()
                {
                    PromotionEvent = "DiscountNET",
                    ServiceCode = input.ServiceCode2,
                    Value = input.Value2.ToString(),
                    Description = BuildContentString(TemplateString.DiscountNET, new string[] { input.Discount2 }),
                    Dismonth = input.DisMonth2
                });


            // X đồng Y tháng TV
            if (input.ID3 != 0)
                instance.Add(new PromotionEventNode()
                {
                    PromotionEvent = "DiscountTV",
                    ServiceCode = input.ServiceCode2,
                    Value = input.Value2.ToString(),
                    Description = BuildContentString(TemplateString.DiscountTV, new string[] { input.Discount3 }),
                    Dismonth = input.DisMonth3
                });


            // CLKM Hòa Mạng NET
            if (input.ID4 != 0)
                instance.Add(new PromotionEventNode() { PromotionEvent = "DiscountNewInterConnNET", ServiceCode = input.ServiceCode4, Value = input.Value4.ToString(), Dismonth = 0, Description = input.Discount4 });
            // CLKM Hòa Mạng TV
            if (input.ID5 != 0)
                instance.Add(new PromotionEventNode() { PromotionEvent = "DiscountNewInterConnTV", ServiceCode = input.ServiceCode5, Value = input.Value5.ToString(), Dismonth = 0, Description = input.Discount5 });
            // Quà Tặng
            if (input.ID6 != 0)
                instance.Add(new PromotionEventNode() { PromotionEvent = "Gift", ServiceCode = 0, Dismonth = 0, Value = input.Discount6 + input.Model, Description = input.Discount6 });
            return instance;
        }

        private string BuildContentString(string Template, string[] stringArray)
        {
            return string.Format(Template, stringArray);
        }
    }
}
