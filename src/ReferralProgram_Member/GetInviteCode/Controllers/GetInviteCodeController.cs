using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Dapper;
using Voucher.APIHelper;
using System.Data;
using GetInviteCodeMember.Models;
using GetInviteCode.Constant;
using System.Globalization;
using Voucher.APIHelper.Log4net;
using Newtonsoft.Json;

namespace GetInviteCodeMember.Controllers
{
    public class GetInviteCodeMemberController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_ReferralProgram_InviteHistory_Common";
        const string spInvite = "PowerInside.dbo.OS6_ReferralProgram_InviteCode";
        [Route("API/ReferralProgram-Member/GetInviteCodeMember")]
        public ResponseModel<InviteCodeResponse> post(InviteCodeRequest input)
        {
            try
            {
                var rs = new InviteCodeResponse();
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        rs = connection.Query<InviteCodeResponse>(sp, new
                        {
                            ActionName = "GetInviteCode",
                            ObjID = input.ObjectID,
                            RowAffected = 0
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                        if (rs == null) return new ResponseModel<InviteCodeResponse>() { data = null, error = null, result = 0 };

                        var InviteCodeInfo = (from row in (connection.Query(spInvite, new
                        {
                            ActionName = "GetInfoPromotionEventByInviteCode",
                            InviteCode = rs.InviteCode,
                            RowAffected = 0
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure))
                                              select (IDictionary<string, object>)row).AsList();

                        if (InviteCodeInfo.Count == 0) return new ResponseModel<InviteCodeResponse>() { data = null, error = null, result = 0 };


                        var temp = new string[] { rs.InviteCode, rs.Shortlink, ConvertTostring(InviteCodeInfo[0]), ((DateTime)InviteCodeInfo[0]["FromDate"]).ToString("dd/MM/yyyy"), ((DateTime)InviteCodeInfo[0]["ToDate"]).ToString("dd/MM/yyyy") };
                        rs.MessageShare = BuildContentString(MessageConstant.MessageShare, temp);


                        var tempMsOnline = new string[] { ConvertTostring(InviteCodeInfo[0]), ((DateTime)InviteCodeInfo[0]["FromDate"]).ToString("dd/MM/yyyy"), ((DateTime)InviteCodeInfo[0]["ToDate"]).ToString("dd/MM/yyyy") };
                        rs.MessageViewOnline = BuildContentString(MessageConstant.MessageViewOnline, tempMsOnline);
                        transaction.Commit();
                    }

                    return new ResponseModel<InviteCodeResponse>() { data = rs, result = 1, error = null };
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, JsonConvert.SerializeObject(ex.ToString()), "ReferralProgram-Member/GetInviteCodeMember");
                return new ResponseModel<InviteCodeResponse>() { data = null, error = ex.Message, result = 0 };
            }
        }
        
        [Route("API/ReferralProgram-Member/fptvn/GetInviteCodeMember")]
        [Authorize(Roles = AuthorizeRole.fptvn)]
        public ResponseModel<InviteCodeResponse> post_fptvn(InviteCodeRequest input)
        {
            try
            {
                var rs = new InviteCodeResponse();
                using (var connection = new SqlConnection(SqlHelper.ConnWrite()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        rs = connection.Query<InviteCodeResponse>(sp, new
                        {
                            ActionName = "GetInviteCode",
                            ObjID = input.ObjectID,
                            RowAffected = 0
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                        if (rs == null) return new ResponseModel<InviteCodeResponse>() { data = null, error = null, result = 0 };

                        var InviteCodeInfo = (from row in (connection.Query(spInvite, new
                        {
                            ActionName = "GetInfoPromotionEventByInviteCode",
                            InviteCode = rs.InviteCode,
                            RowAffected = 0
                        }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure))
                                              select (IDictionary<string, object>)row).AsList();

                        if (InviteCodeInfo.Count == 0) return new ResponseModel<InviteCodeResponse>() { data = null, error = null, result = 0 };


                        var temp = new string[] { rs.InviteCode, rs.Shortlink, ConvertTostring(InviteCodeInfo[0]), ((DateTime)InviteCodeInfo[0]["FromDate"]).ToString("dd/MM/yyyy"), ((DateTime)InviteCodeInfo[0]["ToDate"]).ToString("dd/MM/yyyy") };
                        rs.MessageShare = BuildContentString(MessageConstant.MessageShare, temp);


                        var tempMsOnline = new string[] { ConvertTostring(InviteCodeInfo[0]), ((DateTime)InviteCodeInfo[0]["FromDate"]).ToString("dd/MM/yyyy"), ((DateTime)InviteCodeInfo[0]["ToDate"]).ToString("dd/MM/yyyy") };
                        rs.MessageViewOnline = BuildContentString(MessageConstant.MessageViewOnline, tempMsOnline);
                        transaction.Commit();
                    }

                    rs.Shortlink = "https://fpt.vn/vi?ref=" + rs.InviteCode;
                    return new ResponseModel<InviteCodeResponse>() { data = rs, result = 1, error = null };
                }
            }
            catch (Exception ex)
            {
                L.Mes(Level.ERROR, JsonConvert.SerializeObject(ex.ToString()), "ReferralProgram-Member/GetInviteCodeMember");
                return new ResponseModel<InviteCodeResponse>() { data = null, error = ex.Message, result = 0 };
            }
        }
        private string ConvertTostring(IDictionary<string, object> item)
        {
            string temp = "";
            foreach (KeyValuePair<string, object> entry in item)
            {
                switch (entry.Key)
                {
                    case "Discount1":
                        if (entry.Value == null) break;
                        temp += "Giảm tiền trực tiếp NET: " + ContentResult(entry);
                        temp += ". <br>";
                        break;

                    case "Discount7":
                        if (entry.Value == null) break;
                        temp += "Giảm tiền trực tiếp TV: " + ContentResult(entry);
                        temp += ". <br>";
                        break;


                    case "Discount2":
                        if (entry.Value == null) break;
                        temp += "Giảm phí internet theo tháng: " + ContentResult(entry);
                        temp += ". <br>";
                        break;

                    case "Discount3":
                        if (entry.Value == null) break;
                        temp += "Giảm phí IPTV theo tháng: " + ContentResult(entry);
                        temp += ". <br>";
                        break;
                    case "Discount4":
                        if (entry.Value == null) break;
                        temp += "Giảm phí hòa mạng Internet: " + ContentResult(entry);
                        temp += ". <br>";
                        break;
                    case "Discount5":
                        if (entry.Value == null) break;
                        temp += "Giảm phí hòa mạng IPTV: " + ContentResult(entry);
                        temp += ". <br>";
                        break;
                    case "Discount6":
                        if (entry.Value == null) break;
                        temp += "Quà tặng: " + ContentResult(entry);
                        temp += ". <br>";
                        break;

                    default:
                        if (entry.Value == null) break;
                        if (entry.Key.Contains("Discount"))
                        {
                            temp += entry.Value;
                            temp += ". <br>";
                            break;
                        } break;
                }
            }
            return temp;
        }
        private object ContentResult(KeyValuePair<string, object> input)
        {
            return input.Value == null ? "NULL" : input.Value;
        }

        private string BuildContentString(string Template, string[] stringArray)
        {
            return string.Format(Template, stringArray);
        }
    }
}
