using RegisterInviteUser.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;

namespace RegisterInviteUser.Controllers
{
    public class RegisterInviteUserController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_ReferralProgram_InviteHistory_Common";
        [Route("API/ReferralProgram-Member/RegisterUser")]
        public ResponseModel<bool> post(RegisterInvite input)
        {
            int result = 0;
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    result = connection.Query<int>(sp, new
                    {
                        ActionName = "InsertInviteUser",
                        ObjID = input.ObjectID,
                        InvitePerson = input.InvitePerson,
                        InviteChannel = input.InviteChannel,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (result == 1) { transaction.Commit(); };
                }
                return new ResponseModel<bool>() { data = (result == 1), result = result, error = null };
            }
        }
    }
}
