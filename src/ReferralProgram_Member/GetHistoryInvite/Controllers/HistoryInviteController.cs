using GetHistoryInvite.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Voucher.APIHelper;
using Dapper;

namespace GetHistoryInvite.Controllers
{
    public class HistoryInviteController : ApiController
    {
        const string sp = "PowerInside.dbo.OS6_ReferralProgram_InviteHistory_Common";
        [Route("API/ReferralProgram-Member/GetInvitedHistory")]
        public ResponseModel<HistotyResponse> post(HistotyRequest input)
        {
            var rs = new HistotyResponse();
            using (var connection = new SqlConnection(SqlHelper.ConnRead()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    rs = connection.Query<HistotyResponse>(sp, new
                    {
                        ActionName = "GetHistoty",
                        ObjID = input.ObjectID,
                        RowAffected = 0
                    }, transaction: transaction, commandTimeout: null, commandType: CommandType.StoredProcedure).FirstOrDefault();

                    if (rs == null)
                    {
                        return new ResponseModel<HistotyResponse>() { data = null, error = null, result = 0 };
                    }
                    transaction.Commit();
                }
                return new ResponseModel<HistotyResponse>() { data = rs, result = 1, error = null };
            }
        }
    }
}
